package com.moego.lib.actimvemq.autoconfigure;

import com.moego.lib.common.observability.tracing.Headers;
import com.moego.lib.common.thread.ThreadContextHolder;
import com.moego.lib.common.util.JsonUtil;
import jakarta.jms.Destination;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.Queue;
import jakarta.jms.Topic;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.command.ActiveMQDestination;
import org.springframework.jms.JmsException;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessagePostProcessor;

/**
 * 封装activemq topic和queue消息的发送方法,为了统一在发送前拦截消息并修改消息的destinationName
 *
 * <AUTHOR>
 */
@Slf4j
public class MoeMessageSender {

    private final String prefix;
    private final JmsTemplate queueTemplate;

    private final JmsTemplate topicTemplate;

    public MoeMessageSender(
            final JmsTemplate queueTemplate, final JmsTemplate topicTemplate, final String destinationPrefix) {
        this.prefix = destinationPrefix;
        this.queueTemplate = queueTemplate;
        this.topicTemplate = topicTemplate;
    }

    public String getPrefix() {
        return prefix;
    }

    public void send(final Destination destination, final Object message) throws JmsException {
        ActiveMQDestination d = (ActiveMQDestination) destination;
        d.setPhysicalName(MoeJmsUtils.getDestinationName(prefix, d.getPhysicalName()));
        if (destination instanceof Topic) {
            topicTemplate.convertAndSend(destination, message, new MoeMessagePostProcessor());
        } else if (destination instanceof Queue) {
            queueTemplate.convertAndSend(destination, message, new MoeMessagePostProcessor());
        } else {
            throw new IllegalArgumentException("Destination type is not supported");
        }
    }

    public void setHeader(Message message) {
        try {
            Headers headers = ThreadContextHolder.getContext(Headers.class);
            if (headers != null) {
                Map<String, String> map = headers.getHeaders();
                if (!map.isEmpty()) {
                    message.setStringProperty("moe_headers", JsonUtil.toJson(map));
                }
            }
        } catch (JMSException e) {
            log.error("setHeader error:", e);
        }
    }

    public class MoeMessagePostProcessor implements MessagePostProcessor {

        @Override
        public Message postProcessMessage(Message message) {
            setHeader(message);
            return message;
        }
    }
}
