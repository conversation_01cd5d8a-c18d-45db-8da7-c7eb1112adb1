package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/6/11
 */
@Getter
@Setter
@Builder(toBuilder = true)
public class AssociationIdDTO {
    @Schema(description = "stripe account id")
    private String stripeAccountId;

    @Schema(description = "company id")
    private Long companyId;

    @Schema(description = "business id")
    private Long businessId;
}
