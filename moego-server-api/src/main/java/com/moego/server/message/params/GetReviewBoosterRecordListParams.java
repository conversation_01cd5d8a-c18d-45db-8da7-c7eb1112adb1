package com.moego.server.message.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import java.util.List;
import lombok.Data;
import org.springframework.util.StringUtils;

@Data
public class GetReviewBoosterRecordListParams {

    @JsonIgnore
    private Integer businessId;

    @Schema(description = "query param: customer id")
    @Positive
    private Integer customerId;

    @Schema(description = "query param: source list, 1-sms, 2-grooming report link, 3-pet parent portal")
    private List<@Max(3) @Min(1) Byte> sources;

    @Schema(description = "query param: scores list, 1-5")
    private List<@Max(5) @Min(1) Integer> scores;

    @Schema(description = "query param: staff id list")
    private List<@Positive Integer> staffIds;

    @Schema(description = "query param: only for the replied records")
    private Boolean repliedOnly;

    //    private String startTime;
    //    private String endTime;

    private Integer pageSize;
    private Integer pageNo;
    private String sortBy;
    private String order;

    @JsonIgnore
    private Integer offset;

    @JsonIgnore
    private Integer size;

    private List<String> sortFields =
            List.of("id", "review_time", "appointment_date", "score", "create_time", "update_time");
    private List<String> orderType = List.of("asc", "desc");

    public void validatePageParams() {
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
        }
        if (pageNo == null || pageNo <= 0) {
            pageNo = 1;
        }
        offset = (pageNo - 1) * pageSize;
        size = pageSize;
        if (!StringUtils.hasText(sortBy) || !sortFields.contains(sortBy)) {
            sortBy = "review_time";
        }
        if (!StringUtils.hasText(order) || !orderType.contains(order)) {
            order = "desc";
        }
    }
}
