/*
 * @since 2023-07-06 17:15:21
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.message.vo;

import com.moego.common.utils.Pagination;
import jakarta.annotation.Nonnull;
import java.util.Set;
import lombok.Builder;

@Builder(toBuilder = true)
public record DescribeMessageThreadsVO(
        Set<Integer> ids,
        Set<Integer> businessIds,
        Set<Integer> customerIds,
        boolean includeDeleted,
        @Nonnull Pagination pagination) {}
