package com.moego.server.message.dto;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/11/30
 */
@Data
@Accessors(chain = true)
public class VisibleThreadCustomerDTO {

    /**
     * Visible all customers
     */
    private Boolean isVisibleAll;

    /**
     * Visible specific customer id list
     */
    private List<Integer> visibleCustomerIds;
}
