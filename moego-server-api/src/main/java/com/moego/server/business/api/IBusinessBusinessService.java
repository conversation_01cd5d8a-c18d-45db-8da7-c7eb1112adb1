package com.moego.server.business.api;

import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.dto.CompanyFunctionControlDto;
import com.moego.server.business.dto.BatchQueryBusinessResult;
import com.moego.server.business.dto.BusinessIdWithLevelDto;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.CompanyIdDTO;
import com.moego.server.business.dto.DescribeBusinessesDTO;
import com.moego.server.business.dto.DescribeCompaniesDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.SendDailyEmailDto;
import com.moego.server.business.dto.StaffCompanyDto;
import com.moego.server.business.params.BatchQueryBusinessParams;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.business.params.DescribeBusinessesParams;
import com.moego.server.business.params.DescribeCompaniesParams;
import com.moego.server.business.params.InfoIdParams;
import java.util.List;
import java.util.Map;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessBusinessService {

    /**
     * Get business's invitation code
     *
     * @param businessId business id
     * @return code
     */
    @PostMapping("/service/business/business/getBusinessInvitationCodeById")
    String getBusinessInvitationCodeById(@RequestParam("businessId") Integer businessId);

    /**
     * Get business id by PPP invitation code
     *
     * @param invitationCode code
     * @return business id
     */
    @PostMapping("/service/business/business/getBusinessIdByInvitationCode")
    Integer getBusinessIdByInvitationCode(@RequestBody String invitationCode);

    /**
     * List business id which has PPP invitation code
     *
     * @return business id
     */
    @PostMapping("/service/business/business/listBusinessIdHasInvitationCode")
    List<Integer> listBusinessIdHasInvitationCode();

    @PostMapping("/service/business/business/getBusinessInfoForOB")
    OBBusinessInfoDTO getBusinessInfoForOB(@RequestBody InfoIdParams businessIdParams);

    @PostMapping("/service/business/business/getBusinessInfoListForOB")
    List<OBBusinessInfoDTO> getBusinessInfoListForOB(@RequestBody CommonIdsParams idsParams);

    /**
     * 注意：由于缓存问题，新加的 retailEnable, contactEmail 等字段在这个接口暂时不一定读得到，如有需要使用到，请失效缓存后再调用
     *
     * TODO(account structure): 对于迁移后的 business，部分字段需要取自 company？比如 countryAlpha2Code 等
     */
    @PostMapping("/service/business/business/getBusinessInfo")
    MoeBusinessDto getBusinessInfo(@RequestBody InfoIdParams businessIdParams);

    /**
     * 批量获取：只获取business表和Preference信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/service/business/business/getOnlyBusinessInfoBatch")
    Map<Integer, MoeBusinessDto> getOnlyBusinessInfoBatch(@RequestBody List<Integer> ids);

    /**
     * 注意：由于缓存问题，新加的 retailEnable, contactEmail 等字段在这个接口暂时不一定读得到，如有需要使用到，请失效缓存后再调用
     */
    @PostMapping("/service/business/business/getBusinessInfoWithOwnerEmail")
    MoeBusinessDto getBusinessInfoWithOwnerEmail(@RequestBody InfoIdParams businessIdParams);

    @PostMapping("/service/business/business/getBusinessInfoWithOwnerEmailV2")
    MoeBusinessDto getBusinessInfoWithOwnerEmailV2(@RequestBody InfoIdParams businessIdParams);

    @PostMapping("/service/business/business/getBusinessPreference")
    BusinessPreferenceDto getBusinessPreference(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/business/business/company/all")
    List<StaffCompanyDto> getCompaniesForStaff(@RequestParam("accountId") Integer accountId);

    @GetMapping("/service/business/business/business/queryBusinessIdByCompanyId")
    Map<Integer, BusinessIdWithLevelDto> queryBusinessIdByCompanyId(
            @RequestParam("companyIds") List<Integer> companyIds);

    @GetMapping("/service/business/business/queryCompanyPermissionByCompanyId")
    CompanyFunctionControlDto queryCompanyPermissionByCompanyId(@RequestParam("companyId") Integer companyId);

    @GetMapping("/service/business/business/queryCompanyPermissionByBusinessId")
    CompanyFunctionControlDto queryCompanyPermissionByBusinessId(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/business/getAllBusinessIds")
    List<Integer> getAllBusinessIds(@RequestParam("businessId") Integer businessId);

    /**
     * 根据 franchise 的 business id 获取 franchisor 的 account id
     *
     * @param businessId franchise business id
     * @return franchisor account id
     */
    @GetMapping("/service/business/business/getFranchisorAccountId")
    Integer getFranchisorAccountId(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/business/getAllRelevantBusinessIds")
    List<Integer> getAllRelevantBusinessIds(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/business/getCompanyById")
    CompanyDto getCompanyById(@RequestParam("companyId") Integer companyId);

    @GetMapping("/service/business/business/getCompanyByIdFromMaster")
    CompanyDto getCompanyByIdFromMaster(@RequestParam("companyId") Integer companyId);

    @GetMapping("/service/business/business/getCompanyByBusinessId")
    CompanyDto getCompanyByBusinessId(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/business/getBusinessByCompanyId")
    Map<Integer, MoeBusinessDto> getBusinessByCompanyId(@RequestParam("companyId") Integer companyId);

    /**
     * @param companyId   主键
     * @param locationNum
     * @param vansNum
     * @return
     */
    @PutMapping("/service/business/business/updateCompany")
    Integer updateCompany(
            @RequestParam("companyId") Integer companyId,
            @RequestParam("level") Integer level,
            @RequestParam("locationNum") Integer locationNum,
            @RequestParam("vansNum") Integer vansNum,
            @RequestParam("isAdmin") Boolean isAdmin);

    @GetMapping("/service/business/business/getBusinessShareUpcomingShowServicePrice")
    Boolean getBusinessShareUpcomingShowServicePrice(@RequestParam("tokenBusinessId") Integer tokenBusinessId);

    @GetMapping("/service/business/business/getAllNeedSendDailyBusiness")
    List<SendDailyEmailDto> getAllNeedSendDailyBusiness();

    @GetMapping("/service/business/business/cacheFlush")
    Boolean cacheFlush(@RequestParam("businessId") Integer businessId);

    /**
     * Get all business ids.
     *
     * @return all business ids
     */
    @GetMapping("/service/business/business/getAllBusinessIds2")
    List<Integer> getAllBusinessIds2();

    @GetMapping("/service/business/company/getTargetLevelIds")
    List<Integer> getTargetLevelBusinessIds(
            @RequestParam("filterCompanyIds") List<Integer> filterCompanyIds,
            @RequestParam("startLevel") Integer startLevel,
            @RequestParam("endLevel") Integer endLevel);

    @PostMapping("/service/business/business/updateBizInfoAndFlushCache")
    Boolean updateBizInfoAndFlushCache(@RequestBody OBBusinessInfoDTO businessInfoDTO);

    /**
     * Get information such as the current date, offset of minutes, and time zone of the business
     *
     * @param businessId business id
     * @return business date time information
     */
    @GetMapping("/service/business/business/getBusinessDateTime")
    BusinessDateTimeDTO getBusinessDateTime(@RequestParam("businessId") Integer businessId);

    /**
     * List information such as the current date, offset of minutes, and time zone for businesses.
     *
     * @param businessIds business ids
     * @return key: business id, value: business date time information
     */
    @PostMapping("/service/business/business/listBusinessDateTime")
    Map<Integer, BusinessDateTimeDTO> listBusinessDateTime(@RequestBody List<Integer> businessIds);

    /**
     * @deprecated please use {@link #describeBusinesses(DescribeBusinessesParams)} instead.
     */
    @Deprecated
    @GetMapping("/service/business/business/getUSBusinessId")
    List<Integer> getUSBusinessId();

    /**
     * Search businesses
     */
    @PostMapping("/service/business/business/describeBusinesses")
    DescribeBusinessesDTO describeBusinesses(@RequestBody @Validated DescribeBusinessesParams params);

    @PostMapping("/service/business/business/describeCompanies")
    DescribeCompaniesDTO describeCompanies(@RequestBody @Validated DescribeCompaniesParams params);

    /**
     * Update company blindly, please note this method will update company directly without
     * any check logic, please use it carefully,
     * or try {@link #updateCompany(Integer, Integer, Integer, Integer, Boolean)}.
     */
    @PostMapping("/service/business/business/updateCompanyBlindly")
    CompanyDto updateCompanyBlindly(@RequestBody CompanyDto vo);

    @GetMapping("/service/business/business/getCompanyId")
    CompanyIdDTO getCompanyIdByBusinessId(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/business/business/batchQueryBusiness")
    BatchQueryBusinessResult batchQueryBusiness(@RequestBody BatchQueryBusinessParams params);

    @GetMapping("/service/business/business/isBusinessRetailEnable")
    Boolean isBusinessRetailEnable(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/business/isRetailEnable")
    Map<Integer, Boolean> isRetailEnable(@RequestParam("businessIdList") List<Integer> businessIdList);
}
