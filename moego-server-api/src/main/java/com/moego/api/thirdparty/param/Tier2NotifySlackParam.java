package com.moego.api.thirdparty.param;

import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class Tier2NotifySlackParam {

    // <Sign-up Date>: 注册的年月日具体时间
    String signUpDate;

    // Contact Info
    String ownerName;
    // <Owner Name>: First name + Last name
    // <Company Type>: 来自注册信息<Company Type>
    String companyType;
    // <Tier>
    String tier;
    // <Email>
    String email;
    // <Phone Number>
    String phoneNumber;
    // <Hear from>：来自注册信息：<How did you hear about us?>
    String hearFrom;
    // <Use before>: 来自注册信息：<What did you use before?>
    String useBefore;

    // Company Info
    // <Company Name>
    String companyName;
    // <Country>
    String country;
    // <Pets per month>: 来自注册信息 <How many pets do you take care of per month?>
    String petPerMonth;
    // <Locations/Vans>: 来自注册信息 <How many locations do you have?>、<How many vans do you have?>
    String unitCount;
    // <Address>
    String address;
    // <Website>
    String website;
}
