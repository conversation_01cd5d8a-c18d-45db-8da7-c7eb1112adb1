package com.moego.svc.activitylog.processor;

import com.moego.svc.activitylog.event.enums.ResourceType;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Activity log recorder, this annotation is used to record activity log.
 *
 * <p> This annotation should be used on method of Spring beans.
 * <p> Supported {@code SpEL} expression.
 * <ul>
 *     <li> {@code #xx} xx is the parameter name.</li>
 *     <li> {@code #result} is the return value of the method.</li>
 *     <li> {@code #root.args}/{@code args} is the parameter array of the method.</li>
 *     <li> {@code #root.target}/{@code target} is the target object of the method.</li>
 *     <li> {@code #root.method}/{@code method} is the method.</li>
 *     <li> {@code #root.targetClass}/{@code targetClass} is the target class of the method.</li>
 *     <li> {@code #pX} X is the index of the parameter array.</li>
 *     <li> {@code #aX} X is the index of the parameter array.</li>
 * </ul>
 *
 * <p> Examples:
 *
 * <p> 1. Create a customer:
 * <pre>{@code
 * @ActivityLog(
 *      action = "Create",
 *      resourceType = CUSTOMER,
 *      resourceId = "#result.id", // #result is the return value of the method
 *      details = "#customerParams" // #customerParams is the parameter name of the method
 * )
 * }</pre>
 *
 * <p> 2. Update a customer:
 * <pre>{@code
 * @ActivityLog(
 *      action = "Update",
 *      resourceType = CUSTOMER,
 *      resourceId = "#customerParams.id",
 *      details = "#customerParams"
 * )
 * }</pre>
 *
 * <AUTHOR>
 * @see ActivityLogAspect
 * @see RootObject
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface ActivityLog {

    /**
     * Action, use the naming method with the first letter capitalized, and separate multiple words with spaces.
     *
     * <p> use 'Update' instead of 'update'/'UPDATE'.
     * <p> use 'Batch Create' instead of 'batch create'/'BATCH CREATE'.
     *
     * @return action
     * @see com.moego.svc.activitylog.event.enums.Action
     */
    String action();

    /**
     * Resource type.
     *
     * @return resource type
     * @see com.moego.svc.activitylog.event.enums.ResourceType
     */
    ResourceType resourceType();

    /**
     * Resource id.
     *
     * <p> Supported {@code SpEL} expression.
     *
     * @return resource id
     */
    String resourceId() default "";

    /**
     * Operation details.
     *
     * <p> Supported {@code SpEL} expression.
     *
     * @return details
     */
    String details() default "";

    /**
     * Whether to record the activity log before the method invocation.
     *
     * <p> NOTE: if set to true, {@code #result} will be null.
     *
     * @return true if record the activity log before the method invocation, otherwise false
     */
    boolean beforeInvocation() default false;

    /**
     * Whether current activity log is root activity log.
     *
     * @return true if root activity log, otherwise false
     */
    boolean root() default true;
}
