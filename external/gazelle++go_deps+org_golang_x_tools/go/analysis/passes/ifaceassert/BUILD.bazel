load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "ifaceassert",
    srcs = [
        "doc.go",
        "ifaceassert.go",
    ],
    embedsrcs = ["doc.go"],
    importpath = "golang.org/x/tools/go/analysis/passes/ifaceassert",
    visibility = ["//visibility:public"],
    deps = [
        "//go/analysis",
        "//go/analysis/passes/inspect",
        "//go/analysis/passes/internal/analysisutil",
        "//go/ast/inspector",
        "//internal/typeparams",
    ],
)

alias(
    name = "go_default_library",
    actual = ":ifaceassert",
    visibility = ["//visibility:public"],
)

go_test(
    name = "ifaceassert_test",
    srcs = ["ifaceassert_test.go"],
    deps = [
        ":ifaceassert",
        "//go/analysis/analysistest",
    ],
)
