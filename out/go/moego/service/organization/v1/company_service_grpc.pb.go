// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/organization/v1/company_service.proto

package organizationsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CompanyServiceClient is the client API for CompanyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CompanyServiceClient interface {
	// create company
	CreateCompany(ctx context.Context, in *CreateCompanyRequest, opts ...grpc.CallOption) (*CreateCompanyResponse, error)
	// query companies by ids
	QueryCompaniesByIds(ctx context.Context, in *QueryCompaniesByIdsRequest, opts ...grpc.CallOption) (*QueryCompaniesByIdsResponse, error)
	// is moego pay enable
	IsMoegoPayEnable(ctx context.Context, in *IsMoegoPayEnableRequest, opts ...grpc.CallOption) (*IsMoegoPayEnableResponse, error)
	// update company preference setting
	UpdateCompanyPreferenceSetting(ctx context.Context, in *UpdateCompanyPreferenceSettingRequest, opts ...grpc.CallOption) (*UpdateCompanyPreferenceSettingResponse, error)
	// get company preference setting
	GetCompanyPreferenceSetting(ctx context.Context, in *GetCompanyPreferenceSettingRequest, opts ...grpc.CallOption) (*GetCompanyPreferenceSettingResponse, error)
	// is retail enable
	IsRetailEnable(ctx context.Context, in *IsRetailEnableRequest, opts ...grpc.CallOption) (*IsRetailEnableResponse, error)
	// add tax rule
	// deprecated, use TaxRuleService.CreateTaxRule instead
	AddTaxRule(ctx context.Context, in *AddTaxRuleRequest, opts ...grpc.CallOption) (*AddTaxRuleResponse, error)
	// update tax rule
	// deprecated, use TaxRuleService.UpdateTaxRule instead
	UpdateTaxRule(ctx context.Context, in *UpdateTaxRuleRequest, opts ...grpc.CallOption) (*UpdateTaxRuleResponse, error)
	// delete tax rule
	// deprecated, use TaxRuleService.DeleteTaxRule instead
	DeleteTaxRule(ctx context.Context, in *DeleteTaxRuleRequest, opts ...grpc.CallOption) (*DeleteTaxRuleResponse, error)
	// get tax rule list
	// deprecated, use TaxRuleService.ListTaxRule instead
	GetTaxRuleList(ctx context.Context, in *GetTaxRuleListRequest, opts ...grpc.CallOption) (*GetTaxRuleListResponse, error)
	// get business id for mobile location
	GetBusinessIdForMobile(ctx context.Context, in *GetBusinessIdForMobileRequest, opts ...grpc.CallOption) (*GetBusinessIdForMobileResponse, error)
	// is company migrate
	IsCompanyMigrate(ctx context.Context, in *IsCompanyMigrateRequest, opts ...grpc.CallOption) (*IsCompanyMigrateResponse, error)
	// get clock in/out setting
	GetClockInOutSetting(ctx context.Context, in *GetClockInOutSettingRequest, opts ...grpc.CallOption) (*GetClockInOutSettingResponse, error)
	// update clock in/out setting
	UpdateClockInOutSetting(ctx context.Context, in *UpdateClockInOutSettingRequest, opts ...grpc.CallOption) (*UpdateClockInOutSettingResponse, error)
	// is companies migrate
	IsCompaniesMigrate(ctx context.Context, in *IsCompaniesMigrateRequest, opts ...grpc.CallOption) (*IsCompaniesMigrateResponse, error)
	// set company migrate status
	SetCompanyMigrateStatus(ctx context.Context, in *SetCompanyMigrateStatusRequest, opts ...grpc.CallOption) (*SetCompanyMigrateStatusResponse, error)
	// list companies by enterprise id
	ListCompaniesByEnterpriseId(ctx context.Context, in *ListCompaniesByEnterpriseIdRequest, opts ...grpc.CallOption) (*ListCompaniesByEnterpriseIdResponse, error)
	// query company question status
	GetCompanyQuestionRecord(ctx context.Context, in *CompanyQuestionRecordQueryRequest, opts ...grpc.CallOption) (*CompanyQuestionRecordQueryResponse, error)
	// save company question
	CompanyQuestionRecordSave(ctx context.Context, in *CompanyQuestionRecordRequest, opts ...grpc.CallOption) (*CompanyQuestionRecordResponse, error)
	// sort company sorting
	SortCompany(ctx context.Context, in *SortCompanyRequest, opts ...grpc.CallOption) (*SortCompanyResponse, error)
	// query company list
	ListCompany(ctx context.Context, in *ListCompanyRequest, opts ...grpc.CallOption) (*ListCompanyResponse, error)
	// list company preference setting
	ListCompanyPreferenceSettings(ctx context.Context, in *ListCompanyPreferenceSettingsRequest, opts ...grpc.CallOption) (*ListCompanyPreferenceSettingsResponse, error)
	// update company list
	UpdateCompany(ctx context.Context, in *UpdateCompanyRequest, opts ...grpc.CallOption) (*UpdateCompanyResponse, error)
	// create company from enterprise hub
	CreateCompanyFromEnterpriseHub(ctx context.Context, in *CreateCompanyFromEnterpriseHubRequest, opts ...grpc.CallOption) (*CreateCompanyResponse, error)
}

type companyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCompanyServiceClient(cc grpc.ClientConnInterface) CompanyServiceClient {
	return &companyServiceClient{cc}
}

func (c *companyServiceClient) CreateCompany(ctx context.Context, in *CreateCompanyRequest, opts ...grpc.CallOption) (*CreateCompanyResponse, error) {
	out := new(CreateCompanyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/CreateCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) QueryCompaniesByIds(ctx context.Context, in *QueryCompaniesByIdsRequest, opts ...grpc.CallOption) (*QueryCompaniesByIdsResponse, error) {
	out := new(QueryCompaniesByIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/QueryCompaniesByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) IsMoegoPayEnable(ctx context.Context, in *IsMoegoPayEnableRequest, opts ...grpc.CallOption) (*IsMoegoPayEnableResponse, error) {
	out := new(IsMoegoPayEnableResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/IsMoegoPayEnable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) UpdateCompanyPreferenceSetting(ctx context.Context, in *UpdateCompanyPreferenceSettingRequest, opts ...grpc.CallOption) (*UpdateCompanyPreferenceSettingResponse, error) {
	out := new(UpdateCompanyPreferenceSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/UpdateCompanyPreferenceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetCompanyPreferenceSetting(ctx context.Context, in *GetCompanyPreferenceSettingRequest, opts ...grpc.CallOption) (*GetCompanyPreferenceSettingResponse, error) {
	out := new(GetCompanyPreferenceSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/GetCompanyPreferenceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) IsRetailEnable(ctx context.Context, in *IsRetailEnableRequest, opts ...grpc.CallOption) (*IsRetailEnableResponse, error) {
	out := new(IsRetailEnableResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/IsRetailEnable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) AddTaxRule(ctx context.Context, in *AddTaxRuleRequest, opts ...grpc.CallOption) (*AddTaxRuleResponse, error) {
	out := new(AddTaxRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/AddTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) UpdateTaxRule(ctx context.Context, in *UpdateTaxRuleRequest, opts ...grpc.CallOption) (*UpdateTaxRuleResponse, error) {
	out := new(UpdateTaxRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/UpdateTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) DeleteTaxRule(ctx context.Context, in *DeleteTaxRuleRequest, opts ...grpc.CallOption) (*DeleteTaxRuleResponse, error) {
	out := new(DeleteTaxRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/DeleteTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetTaxRuleList(ctx context.Context, in *GetTaxRuleListRequest, opts ...grpc.CallOption) (*GetTaxRuleListResponse, error) {
	out := new(GetTaxRuleListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/GetTaxRuleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetBusinessIdForMobile(ctx context.Context, in *GetBusinessIdForMobileRequest, opts ...grpc.CallOption) (*GetBusinessIdForMobileResponse, error) {
	out := new(GetBusinessIdForMobileResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/GetBusinessIdForMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) IsCompanyMigrate(ctx context.Context, in *IsCompanyMigrateRequest, opts ...grpc.CallOption) (*IsCompanyMigrateResponse, error) {
	out := new(IsCompanyMigrateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/IsCompanyMigrate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetClockInOutSetting(ctx context.Context, in *GetClockInOutSettingRequest, opts ...grpc.CallOption) (*GetClockInOutSettingResponse, error) {
	out := new(GetClockInOutSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/GetClockInOutSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) UpdateClockInOutSetting(ctx context.Context, in *UpdateClockInOutSettingRequest, opts ...grpc.CallOption) (*UpdateClockInOutSettingResponse, error) {
	out := new(UpdateClockInOutSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/UpdateClockInOutSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) IsCompaniesMigrate(ctx context.Context, in *IsCompaniesMigrateRequest, opts ...grpc.CallOption) (*IsCompaniesMigrateResponse, error) {
	out := new(IsCompaniesMigrateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/IsCompaniesMigrate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) SetCompanyMigrateStatus(ctx context.Context, in *SetCompanyMigrateStatusRequest, opts ...grpc.CallOption) (*SetCompanyMigrateStatusResponse, error) {
	out := new(SetCompanyMigrateStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/SetCompanyMigrateStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) ListCompaniesByEnterpriseId(ctx context.Context, in *ListCompaniesByEnterpriseIdRequest, opts ...grpc.CallOption) (*ListCompaniesByEnterpriseIdResponse, error) {
	out := new(ListCompaniesByEnterpriseIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/ListCompaniesByEnterpriseId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) GetCompanyQuestionRecord(ctx context.Context, in *CompanyQuestionRecordQueryRequest, opts ...grpc.CallOption) (*CompanyQuestionRecordQueryResponse, error) {
	out := new(CompanyQuestionRecordQueryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/GetCompanyQuestionRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) CompanyQuestionRecordSave(ctx context.Context, in *CompanyQuestionRecordRequest, opts ...grpc.CallOption) (*CompanyQuestionRecordResponse, error) {
	out := new(CompanyQuestionRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/CompanyQuestionRecordSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) SortCompany(ctx context.Context, in *SortCompanyRequest, opts ...grpc.CallOption) (*SortCompanyResponse, error) {
	out := new(SortCompanyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/SortCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) ListCompany(ctx context.Context, in *ListCompanyRequest, opts ...grpc.CallOption) (*ListCompanyResponse, error) {
	out := new(ListCompanyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/ListCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) ListCompanyPreferenceSettings(ctx context.Context, in *ListCompanyPreferenceSettingsRequest, opts ...grpc.CallOption) (*ListCompanyPreferenceSettingsResponse, error) {
	out := new(ListCompanyPreferenceSettingsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/ListCompanyPreferenceSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) UpdateCompany(ctx context.Context, in *UpdateCompanyRequest, opts ...grpc.CallOption) (*UpdateCompanyResponse, error) {
	out := new(UpdateCompanyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/UpdateCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) CreateCompanyFromEnterpriseHub(ctx context.Context, in *CreateCompanyFromEnterpriseHubRequest, opts ...grpc.CallOption) (*CreateCompanyResponse, error) {
	out := new(CreateCompanyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.CompanyService/CreateCompanyFromEnterpriseHub", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CompanyServiceServer is the server API for CompanyService service.
// All implementations must embed UnimplementedCompanyServiceServer
// for forward compatibility
type CompanyServiceServer interface {
	// create company
	CreateCompany(context.Context, *CreateCompanyRequest) (*CreateCompanyResponse, error)
	// query companies by ids
	QueryCompaniesByIds(context.Context, *QueryCompaniesByIdsRequest) (*QueryCompaniesByIdsResponse, error)
	// is moego pay enable
	IsMoegoPayEnable(context.Context, *IsMoegoPayEnableRequest) (*IsMoegoPayEnableResponse, error)
	// update company preference setting
	UpdateCompanyPreferenceSetting(context.Context, *UpdateCompanyPreferenceSettingRequest) (*UpdateCompanyPreferenceSettingResponse, error)
	// get company preference setting
	GetCompanyPreferenceSetting(context.Context, *GetCompanyPreferenceSettingRequest) (*GetCompanyPreferenceSettingResponse, error)
	// is retail enable
	IsRetailEnable(context.Context, *IsRetailEnableRequest) (*IsRetailEnableResponse, error)
	// add tax rule
	// deprecated, use TaxRuleService.CreateTaxRule instead
	AddTaxRule(context.Context, *AddTaxRuleRequest) (*AddTaxRuleResponse, error)
	// update tax rule
	// deprecated, use TaxRuleService.UpdateTaxRule instead
	UpdateTaxRule(context.Context, *UpdateTaxRuleRequest) (*UpdateTaxRuleResponse, error)
	// delete tax rule
	// deprecated, use TaxRuleService.DeleteTaxRule instead
	DeleteTaxRule(context.Context, *DeleteTaxRuleRequest) (*DeleteTaxRuleResponse, error)
	// get tax rule list
	// deprecated, use TaxRuleService.ListTaxRule instead
	GetTaxRuleList(context.Context, *GetTaxRuleListRequest) (*GetTaxRuleListResponse, error)
	// get business id for mobile location
	GetBusinessIdForMobile(context.Context, *GetBusinessIdForMobileRequest) (*GetBusinessIdForMobileResponse, error)
	// is company migrate
	IsCompanyMigrate(context.Context, *IsCompanyMigrateRequest) (*IsCompanyMigrateResponse, error)
	// get clock in/out setting
	GetClockInOutSetting(context.Context, *GetClockInOutSettingRequest) (*GetClockInOutSettingResponse, error)
	// update clock in/out setting
	UpdateClockInOutSetting(context.Context, *UpdateClockInOutSettingRequest) (*UpdateClockInOutSettingResponse, error)
	// is companies migrate
	IsCompaniesMigrate(context.Context, *IsCompaniesMigrateRequest) (*IsCompaniesMigrateResponse, error)
	// set company migrate status
	SetCompanyMigrateStatus(context.Context, *SetCompanyMigrateStatusRequest) (*SetCompanyMigrateStatusResponse, error)
	// list companies by enterprise id
	ListCompaniesByEnterpriseId(context.Context, *ListCompaniesByEnterpriseIdRequest) (*ListCompaniesByEnterpriseIdResponse, error)
	// query company question status
	GetCompanyQuestionRecord(context.Context, *CompanyQuestionRecordQueryRequest) (*CompanyQuestionRecordQueryResponse, error)
	// save company question
	CompanyQuestionRecordSave(context.Context, *CompanyQuestionRecordRequest) (*CompanyQuestionRecordResponse, error)
	// sort company sorting
	SortCompany(context.Context, *SortCompanyRequest) (*SortCompanyResponse, error)
	// query company list
	ListCompany(context.Context, *ListCompanyRequest) (*ListCompanyResponse, error)
	// list company preference setting
	ListCompanyPreferenceSettings(context.Context, *ListCompanyPreferenceSettingsRequest) (*ListCompanyPreferenceSettingsResponse, error)
	// update company list
	UpdateCompany(context.Context, *UpdateCompanyRequest) (*UpdateCompanyResponse, error)
	// create company from enterprise hub
	CreateCompanyFromEnterpriseHub(context.Context, *CreateCompanyFromEnterpriseHubRequest) (*CreateCompanyResponse, error)
	mustEmbedUnimplementedCompanyServiceServer()
}

// UnimplementedCompanyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCompanyServiceServer struct {
}

func (UnimplementedCompanyServiceServer) CreateCompany(context.Context, *CreateCompanyRequest) (*CreateCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCompany not implemented")
}
func (UnimplementedCompanyServiceServer) QueryCompaniesByIds(context.Context, *QueryCompaniesByIdsRequest) (*QueryCompaniesByIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCompaniesByIds not implemented")
}
func (UnimplementedCompanyServiceServer) IsMoegoPayEnable(context.Context, *IsMoegoPayEnableRequest) (*IsMoegoPayEnableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsMoegoPayEnable not implemented")
}
func (UnimplementedCompanyServiceServer) UpdateCompanyPreferenceSetting(context.Context, *UpdateCompanyPreferenceSettingRequest) (*UpdateCompanyPreferenceSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCompanyPreferenceSetting not implemented")
}
func (UnimplementedCompanyServiceServer) GetCompanyPreferenceSetting(context.Context, *GetCompanyPreferenceSettingRequest) (*GetCompanyPreferenceSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyPreferenceSetting not implemented")
}
func (UnimplementedCompanyServiceServer) IsRetailEnable(context.Context, *IsRetailEnableRequest) (*IsRetailEnableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsRetailEnable not implemented")
}
func (UnimplementedCompanyServiceServer) AddTaxRule(context.Context, *AddTaxRuleRequest) (*AddTaxRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTaxRule not implemented")
}
func (UnimplementedCompanyServiceServer) UpdateTaxRule(context.Context, *UpdateTaxRuleRequest) (*UpdateTaxRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTaxRule not implemented")
}
func (UnimplementedCompanyServiceServer) DeleteTaxRule(context.Context, *DeleteTaxRuleRequest) (*DeleteTaxRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTaxRule not implemented")
}
func (UnimplementedCompanyServiceServer) GetTaxRuleList(context.Context, *GetTaxRuleListRequest) (*GetTaxRuleListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaxRuleList not implemented")
}
func (UnimplementedCompanyServiceServer) GetBusinessIdForMobile(context.Context, *GetBusinessIdForMobileRequest) (*GetBusinessIdForMobileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessIdForMobile not implemented")
}
func (UnimplementedCompanyServiceServer) IsCompanyMigrate(context.Context, *IsCompanyMigrateRequest) (*IsCompanyMigrateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsCompanyMigrate not implemented")
}
func (UnimplementedCompanyServiceServer) GetClockInOutSetting(context.Context, *GetClockInOutSettingRequest) (*GetClockInOutSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClockInOutSetting not implemented")
}
func (UnimplementedCompanyServiceServer) UpdateClockInOutSetting(context.Context, *UpdateClockInOutSettingRequest) (*UpdateClockInOutSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClockInOutSetting not implemented")
}
func (UnimplementedCompanyServiceServer) IsCompaniesMigrate(context.Context, *IsCompaniesMigrateRequest) (*IsCompaniesMigrateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsCompaniesMigrate not implemented")
}
func (UnimplementedCompanyServiceServer) SetCompanyMigrateStatus(context.Context, *SetCompanyMigrateStatusRequest) (*SetCompanyMigrateStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCompanyMigrateStatus not implemented")
}
func (UnimplementedCompanyServiceServer) ListCompaniesByEnterpriseId(context.Context, *ListCompaniesByEnterpriseIdRequest) (*ListCompaniesByEnterpriseIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCompaniesByEnterpriseId not implemented")
}
func (UnimplementedCompanyServiceServer) GetCompanyQuestionRecord(context.Context, *CompanyQuestionRecordQueryRequest) (*CompanyQuestionRecordQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyQuestionRecord not implemented")
}
func (UnimplementedCompanyServiceServer) CompanyQuestionRecordSave(context.Context, *CompanyQuestionRecordRequest) (*CompanyQuestionRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompanyQuestionRecordSave not implemented")
}
func (UnimplementedCompanyServiceServer) SortCompany(context.Context, *SortCompanyRequest) (*SortCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortCompany not implemented")
}
func (UnimplementedCompanyServiceServer) ListCompany(context.Context, *ListCompanyRequest) (*ListCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCompany not implemented")
}
func (UnimplementedCompanyServiceServer) ListCompanyPreferenceSettings(context.Context, *ListCompanyPreferenceSettingsRequest) (*ListCompanyPreferenceSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCompanyPreferenceSettings not implemented")
}
func (UnimplementedCompanyServiceServer) UpdateCompany(context.Context, *UpdateCompanyRequest) (*UpdateCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCompany not implemented")
}
func (UnimplementedCompanyServiceServer) CreateCompanyFromEnterpriseHub(context.Context, *CreateCompanyFromEnterpriseHubRequest) (*CreateCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCompanyFromEnterpriseHub not implemented")
}
func (UnimplementedCompanyServiceServer) mustEmbedUnimplementedCompanyServiceServer() {}

// UnsafeCompanyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CompanyServiceServer will
// result in compilation errors.
type UnsafeCompanyServiceServer interface {
	mustEmbedUnimplementedCompanyServiceServer()
}

func RegisterCompanyServiceServer(s grpc.ServiceRegistrar, srv CompanyServiceServer) {
	s.RegisterService(&CompanyService_ServiceDesc, srv)
}

func _CompanyService_CreateCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).CreateCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/CreateCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).CreateCompany(ctx, req.(*CreateCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_QueryCompaniesByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCompaniesByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).QueryCompaniesByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/QueryCompaniesByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).QueryCompaniesByIds(ctx, req.(*QueryCompaniesByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_IsMoegoPayEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMoegoPayEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).IsMoegoPayEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/IsMoegoPayEnable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).IsMoegoPayEnable(ctx, req.(*IsMoegoPayEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_UpdateCompanyPreferenceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCompanyPreferenceSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).UpdateCompanyPreferenceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/UpdateCompanyPreferenceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).UpdateCompanyPreferenceSetting(ctx, req.(*UpdateCompanyPreferenceSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetCompanyPreferenceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyPreferenceSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetCompanyPreferenceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/GetCompanyPreferenceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetCompanyPreferenceSetting(ctx, req.(*GetCompanyPreferenceSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_IsRetailEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsRetailEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).IsRetailEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/IsRetailEnable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).IsRetailEnable(ctx, req.(*IsRetailEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_AddTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTaxRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).AddTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/AddTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).AddTaxRule(ctx, req.(*AddTaxRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_UpdateTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTaxRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).UpdateTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/UpdateTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).UpdateTaxRule(ctx, req.(*UpdateTaxRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_DeleteTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTaxRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).DeleteTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/DeleteTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).DeleteTaxRule(ctx, req.(*DeleteTaxRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetTaxRuleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaxRuleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetTaxRuleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/GetTaxRuleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetTaxRuleList(ctx, req.(*GetTaxRuleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetBusinessIdForMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessIdForMobileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetBusinessIdForMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/GetBusinessIdForMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetBusinessIdForMobile(ctx, req.(*GetBusinessIdForMobileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_IsCompanyMigrate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsCompanyMigrateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).IsCompanyMigrate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/IsCompanyMigrate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).IsCompanyMigrate(ctx, req.(*IsCompanyMigrateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetClockInOutSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClockInOutSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetClockInOutSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/GetClockInOutSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetClockInOutSetting(ctx, req.(*GetClockInOutSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_UpdateClockInOutSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClockInOutSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).UpdateClockInOutSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/UpdateClockInOutSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).UpdateClockInOutSetting(ctx, req.(*UpdateClockInOutSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_IsCompaniesMigrate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsCompaniesMigrateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).IsCompaniesMigrate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/IsCompaniesMigrate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).IsCompaniesMigrate(ctx, req.(*IsCompaniesMigrateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_SetCompanyMigrateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCompanyMigrateStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).SetCompanyMigrateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/SetCompanyMigrateStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).SetCompanyMigrateStatus(ctx, req.(*SetCompanyMigrateStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_ListCompaniesByEnterpriseId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCompaniesByEnterpriseIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).ListCompaniesByEnterpriseId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/ListCompaniesByEnterpriseId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).ListCompaniesByEnterpriseId(ctx, req.(*ListCompaniesByEnterpriseIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_GetCompanyQuestionRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompanyQuestionRecordQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).GetCompanyQuestionRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/GetCompanyQuestionRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).GetCompanyQuestionRecord(ctx, req.(*CompanyQuestionRecordQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_CompanyQuestionRecordSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompanyQuestionRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).CompanyQuestionRecordSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/CompanyQuestionRecordSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).CompanyQuestionRecordSave(ctx, req.(*CompanyQuestionRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_SortCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).SortCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/SortCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).SortCompany(ctx, req.(*SortCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_ListCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).ListCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/ListCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).ListCompany(ctx, req.(*ListCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_ListCompanyPreferenceSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCompanyPreferenceSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).ListCompanyPreferenceSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/ListCompanyPreferenceSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).ListCompanyPreferenceSettings(ctx, req.(*ListCompanyPreferenceSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_UpdateCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).UpdateCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/UpdateCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).UpdateCompany(ctx, req.(*UpdateCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_CreateCompanyFromEnterpriseHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCompanyFromEnterpriseHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).CreateCompanyFromEnterpriseHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.CompanyService/CreateCompanyFromEnterpriseHub",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).CreateCompanyFromEnterpriseHub(ctx, req.(*CreateCompanyFromEnterpriseHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CompanyService_ServiceDesc is the grpc.ServiceDesc for CompanyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CompanyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.organization.v1.CompanyService",
	HandlerType: (*CompanyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCompany",
			Handler:    _CompanyService_CreateCompany_Handler,
		},
		{
			MethodName: "QueryCompaniesByIds",
			Handler:    _CompanyService_QueryCompaniesByIds_Handler,
		},
		{
			MethodName: "IsMoegoPayEnable",
			Handler:    _CompanyService_IsMoegoPayEnable_Handler,
		},
		{
			MethodName: "UpdateCompanyPreferenceSetting",
			Handler:    _CompanyService_UpdateCompanyPreferenceSetting_Handler,
		},
		{
			MethodName: "GetCompanyPreferenceSetting",
			Handler:    _CompanyService_GetCompanyPreferenceSetting_Handler,
		},
		{
			MethodName: "IsRetailEnable",
			Handler:    _CompanyService_IsRetailEnable_Handler,
		},
		{
			MethodName: "AddTaxRule",
			Handler:    _CompanyService_AddTaxRule_Handler,
		},
		{
			MethodName: "UpdateTaxRule",
			Handler:    _CompanyService_UpdateTaxRule_Handler,
		},
		{
			MethodName: "DeleteTaxRule",
			Handler:    _CompanyService_DeleteTaxRule_Handler,
		},
		{
			MethodName: "GetTaxRuleList",
			Handler:    _CompanyService_GetTaxRuleList_Handler,
		},
		{
			MethodName: "GetBusinessIdForMobile",
			Handler:    _CompanyService_GetBusinessIdForMobile_Handler,
		},
		{
			MethodName: "IsCompanyMigrate",
			Handler:    _CompanyService_IsCompanyMigrate_Handler,
		},
		{
			MethodName: "GetClockInOutSetting",
			Handler:    _CompanyService_GetClockInOutSetting_Handler,
		},
		{
			MethodName: "UpdateClockInOutSetting",
			Handler:    _CompanyService_UpdateClockInOutSetting_Handler,
		},
		{
			MethodName: "IsCompaniesMigrate",
			Handler:    _CompanyService_IsCompaniesMigrate_Handler,
		},
		{
			MethodName: "SetCompanyMigrateStatus",
			Handler:    _CompanyService_SetCompanyMigrateStatus_Handler,
		},
		{
			MethodName: "ListCompaniesByEnterpriseId",
			Handler:    _CompanyService_ListCompaniesByEnterpriseId_Handler,
		},
		{
			MethodName: "GetCompanyQuestionRecord",
			Handler:    _CompanyService_GetCompanyQuestionRecord_Handler,
		},
		{
			MethodName: "CompanyQuestionRecordSave",
			Handler:    _CompanyService_CompanyQuestionRecordSave_Handler,
		},
		{
			MethodName: "SortCompany",
			Handler:    _CompanyService_SortCompany_Handler,
		},
		{
			MethodName: "ListCompany",
			Handler:    _CompanyService_ListCompany_Handler,
		},
		{
			MethodName: "ListCompanyPreferenceSettings",
			Handler:    _CompanyService_ListCompanyPreferenceSettings_Handler,
		},
		{
			MethodName: "UpdateCompany",
			Handler:    _CompanyService_UpdateCompany_Handler,
		},
		{
			MethodName: "CreateCompanyFromEnterpriseHub",
			Handler:    _CompanyService_CreateCompanyFromEnterpriseHub_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/organization/v1/company_service.proto",
}
