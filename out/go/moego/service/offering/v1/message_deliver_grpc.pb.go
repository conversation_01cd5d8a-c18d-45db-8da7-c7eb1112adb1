// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/offering/v1/message_deliver.proto

package offeringsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MessageDeliverServiceClient is the client API for MessageDeliverService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageDeliverServiceClient interface {
	// create sessions
	TaskSendAllEvent(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type messageDeliverServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageDeliverServiceClient(cc grpc.ClientConnInterface) MessageDeliverServiceClient {
	return &messageDeliverServiceClient{cc}
}

func (c *messageDeliverServiceClient) TaskSendAllEvent(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.MessageDeliverService/TaskSendAllEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageDeliverServiceServer is the server API for MessageDeliverService service.
// All implementations must embed UnimplementedMessageDeliverServiceServer
// for forward compatibility
type MessageDeliverServiceServer interface {
	// create sessions
	TaskSendAllEvent(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedMessageDeliverServiceServer()
}

// UnimplementedMessageDeliverServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMessageDeliverServiceServer struct {
}

func (UnimplementedMessageDeliverServiceServer) TaskSendAllEvent(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskSendAllEvent not implemented")
}
func (UnimplementedMessageDeliverServiceServer) mustEmbedUnimplementedMessageDeliverServiceServer() {}

// UnsafeMessageDeliverServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageDeliverServiceServer will
// result in compilation errors.
type UnsafeMessageDeliverServiceServer interface {
	mustEmbedUnimplementedMessageDeliverServiceServer()
}

func RegisterMessageDeliverServiceServer(s grpc.ServiceRegistrar, srv MessageDeliverServiceServer) {
	s.RegisterService(&MessageDeliverService_ServiceDesc, srv)
}

func _MessageDeliverService_TaskSendAllEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageDeliverServiceServer).TaskSendAllEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.MessageDeliverService/TaskSendAllEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageDeliverServiceServer).TaskSendAllEvent(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageDeliverService_ServiceDesc is the grpc.ServiceDesc for MessageDeliverService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageDeliverService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.offering.v1.MessageDeliverService",
	HandlerType: (*MessageDeliverServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TaskSendAllEvent",
			Handler:    _MessageDeliverService_TaskSendAllEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/offering/v1/message_deliver.proto",
}
