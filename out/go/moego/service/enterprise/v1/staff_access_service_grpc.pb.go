// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/staff_access_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// StaffAccessServiceClient is the client API for StaffAccessService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StaffAccessServiceClient interface {
	// get staff access tenant
	GetStaffAccess(ctx context.Context, in *GetStaffAccessRequest, opts ...grpc.CallOption) (*GetStaffAccessResponse, error)
	// create staff access tenant
	CreateStaffAccess(ctx context.Context, in *CreateStaffAccessRequest, opts ...grpc.CallOption) (*CreateStaffAccessResponse, error)
	// update staff access tenant
	UpdateStaffAccess(ctx context.Context, in *UpdateStaffAccessRequest, opts ...grpc.CallOption) (*UpdateStaffAccessResponse, error)
	// list staff access tenant
	ListStaffAccess(ctx context.Context, in *ListStaffAccessRequest, opts ...grpc.CallOption) (*ListStaffAccessResponse, error)
	// delete staff access tenant
	DeleteStaffAccess(ctx context.Context, in *DeleteStaffAccessRequest, opts ...grpc.CallOption) (*DeleteStaffAccessResponse, error)
}

type staffAccessServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStaffAccessServiceClient(cc grpc.ClientConnInterface) StaffAccessServiceClient {
	return &staffAccessServiceClient{cc}
}

func (c *staffAccessServiceClient) GetStaffAccess(ctx context.Context, in *GetStaffAccessRequest, opts ...grpc.CallOption) (*GetStaffAccessResponse, error) {
	out := new(GetStaffAccessResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.StaffAccessService/GetStaffAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffAccessServiceClient) CreateStaffAccess(ctx context.Context, in *CreateStaffAccessRequest, opts ...grpc.CallOption) (*CreateStaffAccessResponse, error) {
	out := new(CreateStaffAccessResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.StaffAccessService/CreateStaffAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffAccessServiceClient) UpdateStaffAccess(ctx context.Context, in *UpdateStaffAccessRequest, opts ...grpc.CallOption) (*UpdateStaffAccessResponse, error) {
	out := new(UpdateStaffAccessResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.StaffAccessService/UpdateStaffAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffAccessServiceClient) ListStaffAccess(ctx context.Context, in *ListStaffAccessRequest, opts ...grpc.CallOption) (*ListStaffAccessResponse, error) {
	out := new(ListStaffAccessResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.StaffAccessService/ListStaffAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffAccessServiceClient) DeleteStaffAccess(ctx context.Context, in *DeleteStaffAccessRequest, opts ...grpc.CallOption) (*DeleteStaffAccessResponse, error) {
	out := new(DeleteStaffAccessResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.StaffAccessService/DeleteStaffAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StaffAccessServiceServer is the server API for StaffAccessService service.
// All implementations must embed UnimplementedStaffAccessServiceServer
// for forward compatibility
type StaffAccessServiceServer interface {
	// get staff access tenant
	GetStaffAccess(context.Context, *GetStaffAccessRequest) (*GetStaffAccessResponse, error)
	// create staff access tenant
	CreateStaffAccess(context.Context, *CreateStaffAccessRequest) (*CreateStaffAccessResponse, error)
	// update staff access tenant
	UpdateStaffAccess(context.Context, *UpdateStaffAccessRequest) (*UpdateStaffAccessResponse, error)
	// list staff access tenant
	ListStaffAccess(context.Context, *ListStaffAccessRequest) (*ListStaffAccessResponse, error)
	// delete staff access tenant
	DeleteStaffAccess(context.Context, *DeleteStaffAccessRequest) (*DeleteStaffAccessResponse, error)
	mustEmbedUnimplementedStaffAccessServiceServer()
}

// UnimplementedStaffAccessServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStaffAccessServiceServer struct {
}

func (UnimplementedStaffAccessServiceServer) GetStaffAccess(context.Context, *GetStaffAccessRequest) (*GetStaffAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffAccess not implemented")
}
func (UnimplementedStaffAccessServiceServer) CreateStaffAccess(context.Context, *CreateStaffAccessRequest) (*CreateStaffAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaffAccess not implemented")
}
func (UnimplementedStaffAccessServiceServer) UpdateStaffAccess(context.Context, *UpdateStaffAccessRequest) (*UpdateStaffAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffAccess not implemented")
}
func (UnimplementedStaffAccessServiceServer) ListStaffAccess(context.Context, *ListStaffAccessRequest) (*ListStaffAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaffAccess not implemented")
}
func (UnimplementedStaffAccessServiceServer) DeleteStaffAccess(context.Context, *DeleteStaffAccessRequest) (*DeleteStaffAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStaffAccess not implemented")
}
func (UnimplementedStaffAccessServiceServer) mustEmbedUnimplementedStaffAccessServiceServer() {}

// UnsafeStaffAccessServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StaffAccessServiceServer will
// result in compilation errors.
type UnsafeStaffAccessServiceServer interface {
	mustEmbedUnimplementedStaffAccessServiceServer()
}

func RegisterStaffAccessServiceServer(s grpc.ServiceRegistrar, srv StaffAccessServiceServer) {
	s.RegisterService(&StaffAccessService_ServiceDesc, srv)
}

func _StaffAccessService_GetStaffAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffAccessServiceServer).GetStaffAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.StaffAccessService/GetStaffAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffAccessServiceServer).GetStaffAccess(ctx, req.(*GetStaffAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffAccessService_CreateStaffAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStaffAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffAccessServiceServer).CreateStaffAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.StaffAccessService/CreateStaffAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffAccessServiceServer).CreateStaffAccess(ctx, req.(*CreateStaffAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffAccessService_UpdateStaffAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffAccessServiceServer).UpdateStaffAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.StaffAccessService/UpdateStaffAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffAccessServiceServer).UpdateStaffAccess(ctx, req.(*UpdateStaffAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffAccessService_ListStaffAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffAccessServiceServer).ListStaffAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.StaffAccessService/ListStaffAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffAccessServiceServer).ListStaffAccess(ctx, req.(*ListStaffAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffAccessService_DeleteStaffAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStaffAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffAccessServiceServer).DeleteStaffAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.StaffAccessService/DeleteStaffAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffAccessServiceServer).DeleteStaffAccess(ctx, req.(*DeleteStaffAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StaffAccessService_ServiceDesc is the grpc.ServiceDesc for StaffAccessService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StaffAccessService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.StaffAccessService",
	HandlerType: (*StaffAccessServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStaffAccess",
			Handler:    _StaffAccessService_GetStaffAccess_Handler,
		},
		{
			MethodName: "CreateStaffAccess",
			Handler:    _StaffAccessService_CreateStaffAccess_Handler,
		},
		{
			MethodName: "UpdateStaffAccess",
			Handler:    _StaffAccessService_UpdateStaffAccess_Handler,
		},
		{
			MethodName: "ListStaffAccess",
			Handler:    _StaffAccessService_ListStaffAccess_Handler,
		},
		{
			MethodName: "DeleteStaffAccess",
			Handler:    _StaffAccessService_DeleteStaffAccess_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/staff_access_service.proto",
}
