// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/business_customer/v1/business_pet_type_service.proto

package businesscustomersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get pet type request
type GetPetTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *GetPetTypeRequest) Reset() {
	*x = GetPetTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetTypeRequest) ProtoMessage() {}

func (x *GetPetTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetTypeRequest.ProtoReflect.Descriptor instead.
func (*GetPetTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetPetTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPetTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetPetTypeRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// get pet type response
type GetPetTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type
	Type *v1.BusinessPetTypeModel `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *GetPetTypeResponse) Reset() {
	*x = GetPetTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetTypeResponse) ProtoMessage() {}

func (x *GetPetTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetTypeResponse.ProtoReflect.Descriptor instead.
func (*GetPetTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetPetTypeResponse) GetType() *v1.BusinessPetTypeModel {
	if x != nil {
		return x.Type
	}
	return nil
}

// list pet type template request
type ListPetTypeTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetTypeTemplateRequest) Reset() {
	*x = ListPetTypeTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypeTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypeTemplateRequest) ProtoMessage() {}

func (x *ListPetTypeTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypeTemplateRequest.ProtoReflect.Descriptor instead.
func (*ListPetTypeTemplateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{2}
}

// list pet type template response
type ListPetTypeTemplateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type template list
	Types []*v1.BusinessPetTypeModel `protobuf:"bytes,1,rep,name=types,proto3" json:"types,omitempty"`
}

func (x *ListPetTypeTemplateResponse) Reset() {
	*x = ListPetTypeTemplateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypeTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypeTemplateResponse) ProtoMessage() {}

func (x *ListPetTypeTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypeTemplateResponse.ProtoReflect.Descriptor instead.
func (*ListPetTypeTemplateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListPetTypeTemplateResponse) GetTypes() []*v1.BusinessPetTypeModel {
	if x != nil {
		return x.Types
	}
	return nil
}

// list pet type request
type ListPetTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// include unavailable pet types, default is false (only include available pet types)
	IncludeUnavailable bool `protobuf:"varint,3,opt,name=include_unavailable,json=includeUnavailable,proto3" json:"include_unavailable,omitempty"`
}

func (x *ListPetTypeRequest) Reset() {
	*x = ListPetTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypeRequest) ProtoMessage() {}

func (x *ListPetTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypeRequest.ProtoReflect.Descriptor instead.
func (*ListPetTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListPetTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListPetTypeRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *ListPetTypeRequest) GetIncludeUnavailable() bool {
	if x != nil {
		return x.IncludeUnavailable
	}
	return false
}

// list pet type response
type ListPetTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type list
	Types []*v1.BusinessPetTypeModel `protobuf:"bytes,1,rep,name=types,proto3" json:"types,omitempty"`
}

func (x *ListPetTypeResponse) Reset() {
	*x = ListPetTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypeResponse) ProtoMessage() {}

func (x *ListPetTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypeResponse.ProtoReflect.Descriptor instead.
func (*ListPetTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListPetTypeResponse) GetTypes() []*v1.BusinessPetTypeModel {
	if x != nil {
		return x.Types
	}
	return nil
}

// update pet type request
type UpdatePetTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pet type
	Type *v1.BusinessPetTypeUpdateDef `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *UpdatePetTypeRequest) Reset() {
	*x = UpdatePetTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetTypeRequest) ProtoMessage() {}

func (x *UpdatePetTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePetTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdatePetTypeRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *UpdatePetTypeRequest) GetType() *v1.BusinessPetTypeUpdateDef {
	if x != nil {
		return x.Type
	}
	return nil
}

// update pet type response
type UpdatePetTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePetTypeResponse) Reset() {
	*x = UpdatePetTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetTypeResponse) ProtoMessage() {}

func (x *UpdatePetTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetTypeResponse.ProtoReflect.Descriptor instead.
func (*UpdatePetTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{7}
}

// sort pet type request
type SortPetTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type id list, should contain all pet type ids for the company / business
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *SortPetTypeRequest) Reset() {
	*x = SortPetTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetTypeRequest) ProtoMessage() {}

func (x *SortPetTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetTypeRequest.ProtoReflect.Descriptor instead.
func (*SortPetTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{8}
}

func (x *SortPetTypeRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *SortPetTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SortPetTypeRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// sort pet type response
type SortPetTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetTypeResponse) Reset() {
	*x = SortPetTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetTypeResponse) ProtoMessage() {}

func (x *SortPetTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetTypeResponse.ProtoReflect.Descriptor instead.
func (*SortPetTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP(), []int{9}
}

var File_moego_service_business_customer_v1_business_pet_type_service_proto protoreflect.FileDescriptor

var file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDesc = []byte{
	0x0a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x93, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x61, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x1c, 0x0a, 0x1a,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6c, 0x0a, 0x1b, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x05, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x13, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x6e, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x64, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d,
	0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0xf1, 0x01,
	0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x22, 0x17, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x12, 0x53,
	0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x20, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x6f,
	0x72, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x32, 0xb5, 0x05, 0x0a, 0x16, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7b, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x13, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7e, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x0b, 0x53, 0x6f, 0x72,
	0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x72, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x9d, 0x01, 0x0a, 0x2a, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x6d, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescOnce sync.Once
	file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescData = file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDesc
)

func file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescGZIP() []byte {
	file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescOnce.Do(func() {
		file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescData)
	})
	return file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDescData
}

var file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_service_business_customer_v1_business_pet_type_service_proto_goTypes = []interface{}{
	(*GetPetTypeRequest)(nil),           // 0: moego.service.business_customer.v1.GetPetTypeRequest
	(*GetPetTypeResponse)(nil),          // 1: moego.service.business_customer.v1.GetPetTypeResponse
	(*ListPetTypeTemplateRequest)(nil),  // 2: moego.service.business_customer.v1.ListPetTypeTemplateRequest
	(*ListPetTypeTemplateResponse)(nil), // 3: moego.service.business_customer.v1.ListPetTypeTemplateResponse
	(*ListPetTypeRequest)(nil),          // 4: moego.service.business_customer.v1.ListPetTypeRequest
	(*ListPetTypeResponse)(nil),         // 5: moego.service.business_customer.v1.ListPetTypeResponse
	(*UpdatePetTypeRequest)(nil),        // 6: moego.service.business_customer.v1.UpdatePetTypeRequest
	(*UpdatePetTypeResponse)(nil),       // 7: moego.service.business_customer.v1.UpdatePetTypeResponse
	(*SortPetTypeRequest)(nil),          // 8: moego.service.business_customer.v1.SortPetTypeRequest
	(*SortPetTypeResponse)(nil),         // 9: moego.service.business_customer.v1.SortPetTypeResponse
	(*v1.BusinessPetTypeModel)(nil),     // 10: moego.models.business_customer.v1.BusinessPetTypeModel
	(*v1.BusinessPetTypeUpdateDef)(nil), // 11: moego.models.business_customer.v1.BusinessPetTypeUpdateDef
}
var file_moego_service_business_customer_v1_business_pet_type_service_proto_depIdxs = []int32{
	10, // 0: moego.service.business_customer.v1.GetPetTypeResponse.type:type_name -> moego.models.business_customer.v1.BusinessPetTypeModel
	10, // 1: moego.service.business_customer.v1.ListPetTypeTemplateResponse.types:type_name -> moego.models.business_customer.v1.BusinessPetTypeModel
	10, // 2: moego.service.business_customer.v1.ListPetTypeResponse.types:type_name -> moego.models.business_customer.v1.BusinessPetTypeModel
	11, // 3: moego.service.business_customer.v1.UpdatePetTypeRequest.type:type_name -> moego.models.business_customer.v1.BusinessPetTypeUpdateDef
	0,  // 4: moego.service.business_customer.v1.BusinessPetTypeService.GetPetType:input_type -> moego.service.business_customer.v1.GetPetTypeRequest
	2,  // 5: moego.service.business_customer.v1.BusinessPetTypeService.ListPetTypeTemplate:input_type -> moego.service.business_customer.v1.ListPetTypeTemplateRequest
	4,  // 6: moego.service.business_customer.v1.BusinessPetTypeService.ListPetType:input_type -> moego.service.business_customer.v1.ListPetTypeRequest
	6,  // 7: moego.service.business_customer.v1.BusinessPetTypeService.UpdatePetType:input_type -> moego.service.business_customer.v1.UpdatePetTypeRequest
	8,  // 8: moego.service.business_customer.v1.BusinessPetTypeService.SortPetType:input_type -> moego.service.business_customer.v1.SortPetTypeRequest
	1,  // 9: moego.service.business_customer.v1.BusinessPetTypeService.GetPetType:output_type -> moego.service.business_customer.v1.GetPetTypeResponse
	3,  // 10: moego.service.business_customer.v1.BusinessPetTypeService.ListPetTypeTemplate:output_type -> moego.service.business_customer.v1.ListPetTypeTemplateResponse
	5,  // 11: moego.service.business_customer.v1.BusinessPetTypeService.ListPetType:output_type -> moego.service.business_customer.v1.ListPetTypeResponse
	7,  // 12: moego.service.business_customer.v1.BusinessPetTypeService.UpdatePetType:output_type -> moego.service.business_customer.v1.UpdatePetTypeResponse
	9,  // 13: moego.service.business_customer.v1.BusinessPetTypeService.SortPetType:output_type -> moego.service.business_customer.v1.SortPetTypeResponse
	9,  // [9:14] is the sub-list for method output_type
	4,  // [4:9] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_moego_service_business_customer_v1_business_pet_type_service_proto_init() }
func file_moego_service_business_customer_v1_business_pet_type_service_proto_init() {
	if File_moego_service_business_customer_v1_business_pet_type_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypeTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypeTemplateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_business_customer_v1_business_pet_type_service_proto_goTypes,
		DependencyIndexes: file_moego_service_business_customer_v1_business_pet_type_service_proto_depIdxs,
		MessageInfos:      file_moego_service_business_customer_v1_business_pet_type_service_proto_msgTypes,
	}.Build()
	File_moego_service_business_customer_v1_business_pet_type_service_proto = out.File
	file_moego_service_business_customer_v1_business_pet_type_service_proto_rawDesc = nil
	file_moego_service_business_customer_v1_business_pet_type_service_proto_goTypes = nil
	file_moego_service_business_customer_v1_business_pet_type_service_proto_depIdxs = nil
}
