// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/message/v1/template_service.proto

package messagesvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// batch create templates request
type BatchCreateTemplatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// templates.
	Templates []*v1.BatchCreateTemplateItemDef `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
}

func (x *BatchCreateTemplatesRequest) Reset() {
	*x = BatchCreateTemplatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v1_template_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateTemplatesRequest) ProtoMessage() {}

func (x *BatchCreateTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v1_template_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateTemplatesRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v1_template_service_proto_rawDescGZIP(), []int{0}
}

func (x *BatchCreateTemplatesRequest) GetTemplates() []*v1.BatchCreateTemplateItemDef {
	if x != nil {
		return x.Templates
	}
	return nil
}

// batch create templates response
type BatchCreateTemplatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// templates. key is seq num
	Templates map[int64]*v1.TemplateModel `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchCreateTemplatesResponse) Reset() {
	*x = BatchCreateTemplatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v1_template_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateTemplatesResponse) ProtoMessage() {}

func (x *BatchCreateTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v1_template_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateTemplatesResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v1_template_service_proto_rawDescGZIP(), []int{1}
}

func (x *BatchCreateTemplatesResponse) GetTemplates() map[int64]*v1.TemplateModel {
	if x != nil {
		return x.Templates
	}
	return nil
}

// batch get templates by id request
type MGetTemplatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// templates ids.
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *MGetTemplatesRequest) Reset() {
	*x = MGetTemplatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v1_template_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetTemplatesRequest) ProtoMessage() {}

func (x *MGetTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v1_template_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetTemplatesRequest.ProtoReflect.Descriptor instead.
func (*MGetTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v1_template_service_proto_rawDescGZIP(), []int{2}
}

func (x *MGetTemplatesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// batch get templates by id response
type MGetTemplatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// templates
	Templates []*v1.TemplateModel `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
}

func (x *MGetTemplatesResponse) Reset() {
	*x = MGetTemplatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v1_template_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetTemplatesResponse) ProtoMessage() {}

func (x *MGetTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v1_template_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetTemplatesResponse.ProtoReflect.Descriptor instead.
func (*MGetTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v1_template_service_proto_rawDescGZIP(), []int{3}
}

func (x *MGetTemplatesResponse) GetTemplates() []*v1.TemplateModel {
	if x != nil {
		return x.Templates
	}
	return nil
}

var File_moego_service_message_v1_template_service_proto protoreflect.FileDescriptor

var file_moego_service_message_v1_template_service_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x2b, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x7b, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x5c, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10,
	0xf4, 0x03, 0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x22, 0xe9, 0x01,
	0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63,
	0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x1a, 0x64, 0x0a, 0x0e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x33, 0x0a, 0x14, 0x4d, 0x47, 0x65,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xf4, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x5d,
	0x0a, 0x15, 0x4d, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x32, 0x8b, 0x02,
	0x0a, 0x0f, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x85, 0x01, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x0d, 0x4d, 0x47, 0x65,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x20,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76,
	0x31, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_message_v1_template_service_proto_rawDescOnce sync.Once
	file_moego_service_message_v1_template_service_proto_rawDescData = file_moego_service_message_v1_template_service_proto_rawDesc
)

func file_moego_service_message_v1_template_service_proto_rawDescGZIP() []byte {
	file_moego_service_message_v1_template_service_proto_rawDescOnce.Do(func() {
		file_moego_service_message_v1_template_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_message_v1_template_service_proto_rawDescData)
	})
	return file_moego_service_message_v1_template_service_proto_rawDescData
}

var file_moego_service_message_v1_template_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_service_message_v1_template_service_proto_goTypes = []interface{}{
	(*BatchCreateTemplatesRequest)(nil),   // 0: moego.service.message.v1.BatchCreateTemplatesRequest
	(*BatchCreateTemplatesResponse)(nil),  // 1: moego.service.message.v1.BatchCreateTemplatesResponse
	(*MGetTemplatesRequest)(nil),          // 2: moego.service.message.v1.MGetTemplatesRequest
	(*MGetTemplatesResponse)(nil),         // 3: moego.service.message.v1.MGetTemplatesResponse
	nil,                                   // 4: moego.service.message.v1.BatchCreateTemplatesResponse.TemplatesEntry
	(*v1.BatchCreateTemplateItemDef)(nil), // 5: moego.models.message.v1.BatchCreateTemplateItemDef
	(*v1.TemplateModel)(nil),              // 6: moego.models.message.v1.TemplateModel
}
var file_moego_service_message_v1_template_service_proto_depIdxs = []int32{
	5, // 0: moego.service.message.v1.BatchCreateTemplatesRequest.templates:type_name -> moego.models.message.v1.BatchCreateTemplateItemDef
	4, // 1: moego.service.message.v1.BatchCreateTemplatesResponse.templates:type_name -> moego.service.message.v1.BatchCreateTemplatesResponse.TemplatesEntry
	6, // 2: moego.service.message.v1.MGetTemplatesResponse.templates:type_name -> moego.models.message.v1.TemplateModel
	6, // 3: moego.service.message.v1.BatchCreateTemplatesResponse.TemplatesEntry.value:type_name -> moego.models.message.v1.TemplateModel
	0, // 4: moego.service.message.v1.TemplateService.BatchCreateTemplates:input_type -> moego.service.message.v1.BatchCreateTemplatesRequest
	2, // 5: moego.service.message.v1.TemplateService.MGetTemplates:input_type -> moego.service.message.v1.MGetTemplatesRequest
	1, // 6: moego.service.message.v1.TemplateService.BatchCreateTemplates:output_type -> moego.service.message.v1.BatchCreateTemplatesResponse
	3, // 7: moego.service.message.v1.TemplateService.MGetTemplates:output_type -> moego.service.message.v1.MGetTemplatesResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_service_message_v1_template_service_proto_init() }
func file_moego_service_message_v1_template_service_proto_init() {
	if File_moego_service_message_v1_template_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_message_v1_template_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateTemplatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v1_template_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateTemplatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v1_template_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetTemplatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v1_template_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetTemplatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_message_v1_template_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_message_v1_template_service_proto_goTypes,
		DependencyIndexes: file_moego_service_message_v1_template_service_proto_depIdxs,
		MessageInfos:      file_moego_service_message_v1_template_service_proto_msgTypes,
	}.Build()
	File_moego_service_message_v1_template_service_proto = out.File
	file_moego_service_message_v1_template_service_proto_rawDesc = nil
	file_moego_service_message_v1_template_service_proto_goTypes = nil
	file_moego_service_message_v1_template_service_proto_depIdxs = nil
}
