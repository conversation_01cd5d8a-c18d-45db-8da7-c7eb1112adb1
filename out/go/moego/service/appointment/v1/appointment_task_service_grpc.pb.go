// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/appointment_task_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentTaskServiceClient is the client API for AppointmentTaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentTaskServiceClient interface {
	// List appointment tasks by filter
	ListAppointmentTasks(ctx context.Context, in *ListAppointmentTasksRequest, opts ...grpc.CallOption) (*ListAppointmentTasksResponse, error)
	// Count appointment tasks by filter
	CountAppointmentTasks(ctx context.Context, in *CountAppointmentTasksRequest, opts ...grpc.CallOption) (*CountAppointmentTasksResponse, error)
	// Update appointment task, optional field update
	// e.g. assign staff, change status, update note
	PatchAppointmentTask(ctx context.Context, in *PatchAppointmentTaskRequest, opts ...grpc.CallOption) (*PatchAppointmentTaskResponse, error)
	// Batch update appointment task, optional field update
	// e.g. assign staff, change status
	BatchPatchAppointmentTask(ctx context.Context, in *BatchPatchAppointmentTaskRequest, opts ...grpc.CallOption) (*BatchPatchAppointmentTaskResponse, error)
	// Update appointment task, full field update
	UpdateAppointmentTask(ctx context.Context, in *UpdateAppointmentTaskRequest, opts ...grpc.CallOption) (*UpdateAppointmentTaskResponse, error)
	// Get appointment task
	GetAppointmentTask(ctx context.Context, in *GetAppointmentTaskRequest, opts ...grpc.CallOption) (*GetAppointmentTaskResponse, error)
	// List appointment task groups
	ListAppointmentTaskGroups(ctx context.Context, in *ListAppointmentTaskGroupsRequest, opts ...grpc.CallOption) (*ListAppointmentTaskGroupsResponse, error)
	// List appointment task pets by filter
	ListAppointmentTaskPets(ctx context.Context, in *ListAppointmentTaskPetsRequest, opts ...grpc.CallOption) (*ListAppointmentTaskPetsResponse, error)
	// Delete appointment task
	DeleteAppointmentTask(ctx context.Context, in *DeleteAppointmentTaskRequest, opts ...grpc.CallOption) (*DeleteAppointmentTaskResponse, error)
	// Update appointment task, optional field update
	// e.g. assign staff, change status
	PatchTasksByAppointment(ctx context.Context, in *PatchTasksByAppointmentRequest, opts ...grpc.CallOption) (*PatchTasksByAppointmentResponse, error)
	// Sync appointment task
	SyncAppointmentTask(ctx context.Context, in *SyncAppointmentTaskRequest, opts ...grpc.CallOption) (*SyncAppointmentTaskResponse, error)
}

type appointmentTaskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentTaskServiceClient(cc grpc.ClientConnInterface) AppointmentTaskServiceClient {
	return &appointmentTaskServiceClient{cc}
}

func (c *appointmentTaskServiceClient) ListAppointmentTasks(ctx context.Context, in *ListAppointmentTasksRequest, opts ...grpc.CallOption) (*ListAppointmentTasksResponse, error) {
	out := new(ListAppointmentTasksResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/ListAppointmentTasks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) CountAppointmentTasks(ctx context.Context, in *CountAppointmentTasksRequest, opts ...grpc.CallOption) (*CountAppointmentTasksResponse, error) {
	out := new(CountAppointmentTasksResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/CountAppointmentTasks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) PatchAppointmentTask(ctx context.Context, in *PatchAppointmentTaskRequest, opts ...grpc.CallOption) (*PatchAppointmentTaskResponse, error) {
	out := new(PatchAppointmentTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/PatchAppointmentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) BatchPatchAppointmentTask(ctx context.Context, in *BatchPatchAppointmentTaskRequest, opts ...grpc.CallOption) (*BatchPatchAppointmentTaskResponse, error) {
	out := new(BatchPatchAppointmentTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/BatchPatchAppointmentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) UpdateAppointmentTask(ctx context.Context, in *UpdateAppointmentTaskRequest, opts ...grpc.CallOption) (*UpdateAppointmentTaskResponse, error) {
	out := new(UpdateAppointmentTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/UpdateAppointmentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) GetAppointmentTask(ctx context.Context, in *GetAppointmentTaskRequest, opts ...grpc.CallOption) (*GetAppointmentTaskResponse, error) {
	out := new(GetAppointmentTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/GetAppointmentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) ListAppointmentTaskGroups(ctx context.Context, in *ListAppointmentTaskGroupsRequest, opts ...grpc.CallOption) (*ListAppointmentTaskGroupsResponse, error) {
	out := new(ListAppointmentTaskGroupsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/ListAppointmentTaskGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) ListAppointmentTaskPets(ctx context.Context, in *ListAppointmentTaskPetsRequest, opts ...grpc.CallOption) (*ListAppointmentTaskPetsResponse, error) {
	out := new(ListAppointmentTaskPetsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/ListAppointmentTaskPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) DeleteAppointmentTask(ctx context.Context, in *DeleteAppointmentTaskRequest, opts ...grpc.CallOption) (*DeleteAppointmentTaskResponse, error) {
	out := new(DeleteAppointmentTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/DeleteAppointmentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) PatchTasksByAppointment(ctx context.Context, in *PatchTasksByAppointmentRequest, opts ...grpc.CallOption) (*PatchTasksByAppointmentResponse, error) {
	out := new(PatchTasksByAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/PatchTasksByAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) SyncAppointmentTask(ctx context.Context, in *SyncAppointmentTaskRequest, opts ...grpc.CallOption) (*SyncAppointmentTaskResponse, error) {
	out := new(SyncAppointmentTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentTaskService/SyncAppointmentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentTaskServiceServer is the server API for AppointmentTaskService service.
// All implementations must embed UnimplementedAppointmentTaskServiceServer
// for forward compatibility
type AppointmentTaskServiceServer interface {
	// List appointment tasks by filter
	ListAppointmentTasks(context.Context, *ListAppointmentTasksRequest) (*ListAppointmentTasksResponse, error)
	// Count appointment tasks by filter
	CountAppointmentTasks(context.Context, *CountAppointmentTasksRequest) (*CountAppointmentTasksResponse, error)
	// Update appointment task, optional field update
	// e.g. assign staff, change status, update note
	PatchAppointmentTask(context.Context, *PatchAppointmentTaskRequest) (*PatchAppointmentTaskResponse, error)
	// Batch update appointment task, optional field update
	// e.g. assign staff, change status
	BatchPatchAppointmentTask(context.Context, *BatchPatchAppointmentTaskRequest) (*BatchPatchAppointmentTaskResponse, error)
	// Update appointment task, full field update
	UpdateAppointmentTask(context.Context, *UpdateAppointmentTaskRequest) (*UpdateAppointmentTaskResponse, error)
	// Get appointment task
	GetAppointmentTask(context.Context, *GetAppointmentTaskRequest) (*GetAppointmentTaskResponse, error)
	// List appointment task groups
	ListAppointmentTaskGroups(context.Context, *ListAppointmentTaskGroupsRequest) (*ListAppointmentTaskGroupsResponse, error)
	// List appointment task pets by filter
	ListAppointmentTaskPets(context.Context, *ListAppointmentTaskPetsRequest) (*ListAppointmentTaskPetsResponse, error)
	// Delete appointment task
	DeleteAppointmentTask(context.Context, *DeleteAppointmentTaskRequest) (*DeleteAppointmentTaskResponse, error)
	// Update appointment task, optional field update
	// e.g. assign staff, change status
	PatchTasksByAppointment(context.Context, *PatchTasksByAppointmentRequest) (*PatchTasksByAppointmentResponse, error)
	// Sync appointment task
	SyncAppointmentTask(context.Context, *SyncAppointmentTaskRequest) (*SyncAppointmentTaskResponse, error)
	mustEmbedUnimplementedAppointmentTaskServiceServer()
}

// UnimplementedAppointmentTaskServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentTaskServiceServer struct {
}

func (UnimplementedAppointmentTaskServiceServer) ListAppointmentTasks(context.Context, *ListAppointmentTasksRequest) (*ListAppointmentTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentTasks not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) CountAppointmentTasks(context.Context, *CountAppointmentTasksRequest) (*CountAppointmentTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAppointmentTasks not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) PatchAppointmentTask(context.Context, *PatchAppointmentTaskRequest) (*PatchAppointmentTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchAppointmentTask not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) BatchPatchAppointmentTask(context.Context, *BatchPatchAppointmentTaskRequest) (*BatchPatchAppointmentTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchPatchAppointmentTask not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) UpdateAppointmentTask(context.Context, *UpdateAppointmentTaskRequest) (*UpdateAppointmentTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointmentTask not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) GetAppointmentTask(context.Context, *GetAppointmentTaskRequest) (*GetAppointmentTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentTask not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) ListAppointmentTaskGroups(context.Context, *ListAppointmentTaskGroupsRequest) (*ListAppointmentTaskGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentTaskGroups not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) ListAppointmentTaskPets(context.Context, *ListAppointmentTaskPetsRequest) (*ListAppointmentTaskPetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentTaskPets not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) DeleteAppointmentTask(context.Context, *DeleteAppointmentTaskRequest) (*DeleteAppointmentTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAppointmentTask not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) PatchTasksByAppointment(context.Context, *PatchTasksByAppointmentRequest) (*PatchTasksByAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchTasksByAppointment not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) SyncAppointmentTask(context.Context, *SyncAppointmentTaskRequest) (*SyncAppointmentTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncAppointmentTask not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) mustEmbedUnimplementedAppointmentTaskServiceServer() {
}

// UnsafeAppointmentTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentTaskServiceServer will
// result in compilation errors.
type UnsafeAppointmentTaskServiceServer interface {
	mustEmbedUnimplementedAppointmentTaskServiceServer()
}

func RegisterAppointmentTaskServiceServer(s grpc.ServiceRegistrar, srv AppointmentTaskServiceServer) {
	s.RegisterService(&AppointmentTaskService_ServiceDesc, srv)
}

func _AppointmentTaskService_ListAppointmentTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/ListAppointmentTasks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTasks(ctx, req.(*ListAppointmentTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_CountAppointmentTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAppointmentTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).CountAppointmentTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/CountAppointmentTasks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).CountAppointmentTasks(ctx, req.(*CountAppointmentTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_PatchAppointmentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchAppointmentTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).PatchAppointmentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/PatchAppointmentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).PatchAppointmentTask(ctx, req.(*PatchAppointmentTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_BatchPatchAppointmentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchPatchAppointmentTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).BatchPatchAppointmentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/BatchPatchAppointmentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).BatchPatchAppointmentTask(ctx, req.(*BatchPatchAppointmentTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_UpdateAppointmentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).UpdateAppointmentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/UpdateAppointmentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).UpdateAppointmentTask(ctx, req.(*UpdateAppointmentTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_GetAppointmentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).GetAppointmentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/GetAppointmentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).GetAppointmentTask(ctx, req.(*GetAppointmentTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_ListAppointmentTaskGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentTaskGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTaskGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/ListAppointmentTaskGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTaskGroups(ctx, req.(*ListAppointmentTaskGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_ListAppointmentTaskPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentTaskPetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTaskPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/ListAppointmentTaskPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTaskPets(ctx, req.(*ListAppointmentTaskPetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_DeleteAppointmentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAppointmentTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).DeleteAppointmentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/DeleteAppointmentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).DeleteAppointmentTask(ctx, req.(*DeleteAppointmentTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_PatchTasksByAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchTasksByAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).PatchTasksByAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/PatchTasksByAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).PatchTasksByAppointment(ctx, req.(*PatchTasksByAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_SyncAppointmentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncAppointmentTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).SyncAppointmentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentTaskService/SyncAppointmentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).SyncAppointmentTask(ctx, req.(*SyncAppointmentTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentTaskService_ServiceDesc is the grpc.ServiceDesc for AppointmentTaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentTaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.AppointmentTaskService",
	HandlerType: (*AppointmentTaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAppointmentTasks",
			Handler:    _AppointmentTaskService_ListAppointmentTasks_Handler,
		},
		{
			MethodName: "CountAppointmentTasks",
			Handler:    _AppointmentTaskService_CountAppointmentTasks_Handler,
		},
		{
			MethodName: "PatchAppointmentTask",
			Handler:    _AppointmentTaskService_PatchAppointmentTask_Handler,
		},
		{
			MethodName: "BatchPatchAppointmentTask",
			Handler:    _AppointmentTaskService_BatchPatchAppointmentTask_Handler,
		},
		{
			MethodName: "UpdateAppointmentTask",
			Handler:    _AppointmentTaskService_UpdateAppointmentTask_Handler,
		},
		{
			MethodName: "GetAppointmentTask",
			Handler:    _AppointmentTaskService_GetAppointmentTask_Handler,
		},
		{
			MethodName: "ListAppointmentTaskGroups",
			Handler:    _AppointmentTaskService_ListAppointmentTaskGroups_Handler,
		},
		{
			MethodName: "ListAppointmentTaskPets",
			Handler:    _AppointmentTaskService_ListAppointmentTaskPets_Handler,
		},
		{
			MethodName: "DeleteAppointmentTask",
			Handler:    _AppointmentTaskService_DeleteAppointmentTask_Handler,
		},
		{
			MethodName: "PatchTasksByAppointment",
			Handler:    _AppointmentTaskService_PatchTasksByAppointment_Handler,
		},
		{
			MethodName: "SyncAppointmentTask",
			Handler:    _AppointmentTaskService_SyncAppointmentTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/appointment_task_service.proto",
}
