// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/fulfillment/v1/staff_time_slot_service.proto

package fulfillmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list staff time slots request
type ListStaffTimeSLotsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The filter
	Filter *ListStaffTimeSLotsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListStaffTimeSLotsRequest) Reset() {
	*x = ListStaffTimeSLotsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffTimeSLotsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffTimeSLotsRequest) ProtoMessage() {}

func (x *ListStaffTimeSLotsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffTimeSLotsRequest.ProtoReflect.Descriptor instead.
func (*ListStaffTimeSLotsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListStaffTimeSLotsRequest) GetFilter() *ListStaffTimeSLotsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list staff time slots response
type ListStaffTimeSLotsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the staff time slots
	TimeSlots []*v1.StaffTimeSlotModel `protobuf:"bytes,1,rep,name=time_slots,json=timeSlots,proto3" json:"time_slots,omitempty"`
}

func (x *ListStaffTimeSLotsResponse) Reset() {
	*x = ListStaffTimeSLotsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffTimeSLotsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffTimeSLotsResponse) ProtoMessage() {}

func (x *ListStaffTimeSLotsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffTimeSLotsResponse.ProtoReflect.Descriptor instead.
func (*ListStaffTimeSLotsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListStaffTimeSLotsResponse) GetTimeSlots() []*v1.StaffTimeSlotModel {
	if x != nil {
		return x.TimeSlots
	}
	return nil
}

// The filter message
type ListStaffTimeSLotsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fulfillment id
	FulfillmentIds []int64 `protobuf:"varint,1,rep,packed,name=fulfillment_ids,json=fulfillmentIds,proto3" json:"fulfillment_ids,omitempty"`
}

func (x *ListStaffTimeSLotsRequest_Filter) Reset() {
	*x = ListStaffTimeSLotsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffTimeSLotsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffTimeSLotsRequest_Filter) ProtoMessage() {}

func (x *ListStaffTimeSLotsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffTimeSLotsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListStaffTimeSLotsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ListStaffTimeSLotsRequest_Filter) GetFulfillmentIds() []int64 {
	if x != nil {
		return x.FulfillmentIds
	}
	return nil
}

var File_moego_service_fulfillment_v1_staff_time_slot_service_proto protoreflect.FileDescriptor

var file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x01,
	0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x53,
	0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x60, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x44, 0x0a,
	0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x0f, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x73, 0x22, 0x6c, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74,
	0x73, 0x32, 0xa0, 0x01, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x53,
	0x6c, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x12, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x4c, 0x6f, 0x74,
	0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x4c,
	0x6f, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76,
	0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescOnce sync.Once
	file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescData = file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDesc
)

func file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescGZIP() []byte {
	file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescOnce.Do(func() {
		file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescData)
	})
	return file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDescData
}

var file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_service_fulfillment_v1_staff_time_slot_service_proto_goTypes = []interface{}{
	(*ListStaffTimeSLotsRequest)(nil),        // 0: moego.service.fulfillment.v1.ListStaffTimeSLotsRequest
	(*ListStaffTimeSLotsResponse)(nil),       // 1: moego.service.fulfillment.v1.ListStaffTimeSLotsResponse
	(*ListStaffTimeSLotsRequest_Filter)(nil), // 2: moego.service.fulfillment.v1.ListStaffTimeSLotsRequest.Filter
	(*v1.StaffTimeSlotModel)(nil),            // 3: moego.models.fulfillment.v1.StaffTimeSlotModel
}
var file_moego_service_fulfillment_v1_staff_time_slot_service_proto_depIdxs = []int32{
	2, // 0: moego.service.fulfillment.v1.ListStaffTimeSLotsRequest.filter:type_name -> moego.service.fulfillment.v1.ListStaffTimeSLotsRequest.Filter
	3, // 1: moego.service.fulfillment.v1.ListStaffTimeSLotsResponse.time_slots:type_name -> moego.models.fulfillment.v1.StaffTimeSlotModel
	0, // 2: moego.service.fulfillment.v1.StaffTimeSlotService.ListStaffTimeSLots:input_type -> moego.service.fulfillment.v1.ListStaffTimeSLotsRequest
	1, // 3: moego.service.fulfillment.v1.StaffTimeSlotService.ListStaffTimeSLots:output_type -> moego.service.fulfillment.v1.ListStaffTimeSLotsResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_service_fulfillment_v1_staff_time_slot_service_proto_init() }
func file_moego_service_fulfillment_v1_staff_time_slot_service_proto_init() {
	if File_moego_service_fulfillment_v1_staff_time_slot_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffTimeSLotsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffTimeSLotsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffTimeSLotsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_fulfillment_v1_staff_time_slot_service_proto_goTypes,
		DependencyIndexes: file_moego_service_fulfillment_v1_staff_time_slot_service_proto_depIdxs,
		MessageInfos:      file_moego_service_fulfillment_v1_staff_time_slot_service_proto_msgTypes,
	}.Build()
	File_moego_service_fulfillment_v1_staff_time_slot_service_proto = out.File
	file_moego_service_fulfillment_v1_staff_time_slot_service_proto_rawDesc = nil
	file_moego_service_fulfillment_v1_staff_time_slot_service_proto_goTypes = nil
	file_moego_service_fulfillment_v1_staff_time_slot_service_proto_depIdxs = nil
}
