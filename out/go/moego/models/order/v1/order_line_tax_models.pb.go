// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/order_line_tax_models.proto

package orderpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// line tax info
type OrderLineTaxModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// businessId
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// orderId
	OrderId int64 `protobuf:"varint,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// orderItemId
	OrderItemId *int64 `protobuf:"varint,4,opt,name=order_item_id,json=orderItemId,proto3,oneof" json:"order_item_id,omitempty"`
	// applyType
	ApplyType string `protobuf:"bytes,5,opt,name=apply_type,json=applyType,proto3" json:"apply_type,omitempty"`
	// isDeleted
	IsDeleted *bool `protobuf:"varint,6,opt,name=is_deleted,json=isDeleted,proto3,oneof" json:"is_deleted,omitempty"`
	// taxId
	TaxId int64 `protobuf:"varint,7,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// taxRate
	TaxRate float64 `protobuf:"fixed64,8,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// taxAmount
	TaxAmount *float64 `protobuf:"fixed64,9,opt,name=tax_amount,json=taxAmount,proto3,oneof" json:"tax_amount,omitempty"`
	// applyBy
	ApplyBy int64 `protobuf:"varint,10,opt,name=apply_by,json=applyBy,proto3" json:"apply_by,omitempty"`
	// applySequence
	ApplySequence *int32 `protobuf:"varint,11,opt,name=apply_sequence,json=applySequence,proto3,oneof" json:"apply_sequence,omitempty"`
	// create time
	CreateTime *int64 `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`
	// update time
	UpdateTime *int64 `protobuf:"varint,13,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`
	// Tax name.
	TaxName string `protobuf:"bytes,14,opt,name=tax_name,json=taxName,proto3" json:"tax_name,omitempty"`
}

func (x *OrderLineTaxModel) Reset() {
	*x = OrderLineTaxModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_line_tax_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderLineTaxModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderLineTaxModel) ProtoMessage() {}

func (x *OrderLineTaxModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_line_tax_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderLineTaxModel.ProtoReflect.Descriptor instead.
func (*OrderLineTaxModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_line_tax_models_proto_rawDescGZIP(), []int{0}
}

func (x *OrderLineTaxModel) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *OrderLineTaxModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderLineTaxModel) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *OrderLineTaxModel) GetOrderItemId() int64 {
	if x != nil && x.OrderItemId != nil {
		return *x.OrderItemId
	}
	return 0
}

func (x *OrderLineTaxModel) GetApplyType() string {
	if x != nil {
		return x.ApplyType
	}
	return ""
}

func (x *OrderLineTaxModel) GetIsDeleted() bool {
	if x != nil && x.IsDeleted != nil {
		return *x.IsDeleted
	}
	return false
}

func (x *OrderLineTaxModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *OrderLineTaxModel) GetTaxRate() float64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *OrderLineTaxModel) GetTaxAmount() float64 {
	if x != nil && x.TaxAmount != nil {
		return *x.TaxAmount
	}
	return 0
}

func (x *OrderLineTaxModel) GetApplyBy() int64 {
	if x != nil {
		return x.ApplyBy
	}
	return 0
}

func (x *OrderLineTaxModel) GetApplySequence() int32 {
	if x != nil && x.ApplySequence != nil {
		return *x.ApplySequence
	}
	return 0
}

func (x *OrderLineTaxModel) GetCreateTime() int64 {
	if x != nil && x.CreateTime != nil {
		return *x.CreateTime
	}
	return 0
}

func (x *OrderLineTaxModel) GetUpdateTime() int64 {
	if x != nil && x.UpdateTime != nil {
		return *x.UpdateTime
	}
	return 0
}

func (x *OrderLineTaxModel) GetTaxName() string {
	if x != nil {
		return x.TaxName
	}
	return ""
}

var File_moego_models_order_v1_order_line_tax_models_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_order_line_tax_models_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x22, 0xbe, 0x04, 0x0a, 0x11, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52,
	0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a,
	0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x61, 0x78, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x22, 0x0a, 0x0a, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x01, 0x48, 0x03, 0x52, 0x09, 0x74, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x62, 0x79, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x79, 0x12, 0x2a,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x53,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x05, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x24, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x48, 0x06, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x78, 0x4e, 0x61, 0x6d,
	0x65, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69,
	0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x61,
	0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x75, 0x0a, 0x1d, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_order_line_tax_models_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_order_line_tax_models_proto_rawDescData = file_moego_models_order_v1_order_line_tax_models_proto_rawDesc
)

func file_moego_models_order_v1_order_line_tax_models_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_order_line_tax_models_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_order_line_tax_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_order_line_tax_models_proto_rawDescData)
	})
	return file_moego_models_order_v1_order_line_tax_models_proto_rawDescData
}

var file_moego_models_order_v1_order_line_tax_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_order_v1_order_line_tax_models_proto_goTypes = []interface{}{
	(*OrderLineTaxModel)(nil), // 0: moego.models.order.v1.OrderLineTaxModel
}
var file_moego_models_order_v1_order_line_tax_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_order_line_tax_models_proto_init() }
func file_moego_models_order_v1_order_line_tax_models_proto_init() {
	if File_moego_models_order_v1_order_line_tax_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_order_line_tax_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderLineTaxModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v1_order_line_tax_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_order_line_tax_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_order_line_tax_models_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_order_line_tax_models_proto_depIdxs,
		MessageInfos:      file_moego_models_order_v1_order_line_tax_models_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_order_line_tax_models_proto = out.File
	file_moego_models_order_v1_order_line_tax_models_proto_rawDesc = nil
	file_moego_models_order_v1_order_line_tax_models_proto_goTypes = nil
	file_moego_models_order_v1_order_line_tax_models_proto_depIdxs = nil
}
