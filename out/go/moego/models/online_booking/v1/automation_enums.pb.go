// @since 2024-10-11 12:09:44
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/automation_enums.proto

package onlinebookingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob profile update condition
type ProfileUpdateCondition int32

const (
	// unspecified
	ProfileUpdateCondition_PROFILE_UPDATE_CONDITION_UNSPECIFIED ProfileUpdateCondition = 0
	// all
	ProfileUpdateCondition_PROFILE_UPDATE_CONDITION_ALL ProfileUpdateCondition = 1
	// without update
	ProfileUpdateCondition_PROFILE_UPDATE_CONDITION_WITHOUT_UPDATE ProfileUpdateCondition = 2
)

// Enum value maps for ProfileUpdateCondition.
var (
	ProfileUpdateCondition_name = map[int32]string{
		0: "PROFILE_UPDATE_CONDITION_UNSPECIFIED",
		1: "PROFILE_UPDATE_CONDITION_ALL",
		2: "PROFILE_UPDATE_CONDITION_WITHOUT_UPDATE",
	}
	ProfileUpdateCondition_value = map[string]int32{
		"PROFILE_UPDATE_CONDITION_UNSPECIFIED":    0,
		"PROFILE_UPDATE_CONDITION_ALL":            1,
		"PROFILE_UPDATE_CONDITION_WITHOUT_UPDATE": 2,
	}
)

func (x ProfileUpdateCondition) Enum() *ProfileUpdateCondition {
	p := new(ProfileUpdateCondition)
	*p = x
	return p
}

func (x ProfileUpdateCondition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProfileUpdateCondition) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_automation_enums_proto_enumTypes[0].Descriptor()
}

func (ProfileUpdateCondition) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_automation_enums_proto_enumTypes[0]
}

func (x ProfileUpdateCondition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProfileUpdateCondition.Descriptor instead.
func (ProfileUpdateCondition) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_automation_enums_proto_rawDescGZIP(), []int{0}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob vaccine status condition
type VaccineStatusCondition int32

const (
	// unspecified
	VaccineStatusCondition_VACCINE_STATUS_CONDITION_UNSPECIFIED VaccineStatusCondition = 0
	// all
	VaccineStatusCondition_VACCINE_STATUS_CONDITION_ALL VaccineStatusCondition = 1
	// no missing or expired
	VaccineStatusCondition_VACCINE_STATUS_CONDITION_NO_MISSING_OR_EXPIRED VaccineStatusCondition = 2
)

// Enum value maps for VaccineStatusCondition.
var (
	VaccineStatusCondition_name = map[int32]string{
		0: "VACCINE_STATUS_CONDITION_UNSPECIFIED",
		1: "VACCINE_STATUS_CONDITION_ALL",
		2: "VACCINE_STATUS_CONDITION_NO_MISSING_OR_EXPIRED",
	}
	VaccineStatusCondition_value = map[string]int32{
		"VACCINE_STATUS_CONDITION_UNSPECIFIED":           0,
		"VACCINE_STATUS_CONDITION_ALL":                   1,
		"VACCINE_STATUS_CONDITION_NO_MISSING_OR_EXPIRED": 2,
	}
)

func (x VaccineStatusCondition) Enum() *VaccineStatusCondition {
	p := new(VaccineStatusCondition)
	*p = x
	return p
}

func (x VaccineStatusCondition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VaccineStatusCondition) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_automation_enums_proto_enumTypes[1].Descriptor()
}

func (VaccineStatusCondition) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_automation_enums_proto_enumTypes[1]
}

func (x VaccineStatusCondition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VaccineStatusCondition.Descriptor instead.
func (VaccineStatusCondition) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_automation_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_online_booking_v1_automation_enums_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_automation_enums_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2a, 0x91, 0x01, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x24, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c,
	0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x2b,
	0x0a, 0x27, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x4f,
	0x55, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02, 0x2a, 0x98, 0x01, 0x0a, 0x16,
	0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x24, 0x56, 0x41, 0x43, 0x43, 0x49, 0x4e,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x20, 0x0a, 0x1c, 0x56, 0x41, 0x43, 0x43, 0x49, 0x4e, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x4c,
	0x10, 0x01, 0x12, 0x32, 0x0a, 0x2e, 0x56, 0x41, 0x43, 0x43, 0x49, 0x4e, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e,
	0x4f, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x52, 0x5f, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x02, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_automation_enums_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_automation_enums_proto_rawDescData = file_moego_models_online_booking_v1_automation_enums_proto_rawDesc
)

func file_moego_models_online_booking_v1_automation_enums_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_automation_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_automation_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_automation_enums_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_automation_enums_proto_rawDescData
}

var file_moego_models_online_booking_v1_automation_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_online_booking_v1_automation_enums_proto_goTypes = []interface{}{
	(ProfileUpdateCondition)(0), // 0: moego.models.online_booking.v1.ProfileUpdateCondition
	(VaccineStatusCondition)(0), // 1: moego.models.online_booking.v1.VaccineStatusCondition
}
var file_moego_models_online_booking_v1_automation_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_automation_enums_proto_init() }
func file_moego_models_online_booking_v1_automation_enums_proto_init() {
	if File_moego_models_online_booking_v1_automation_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_automation_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_automation_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_automation_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_online_booking_v1_automation_enums_proto_enumTypes,
	}.Build()
	File_moego_models_online_booking_v1_automation_enums_proto = out.File
	file_moego_models_online_booking_v1_automation_enums_proto_rawDesc = nil
	file_moego_models_online_booking_v1_automation_enums_proto_goTypes = nil
	file_moego_models_online_booking_v1_automation_enums_proto_depIdxs = nil
}
