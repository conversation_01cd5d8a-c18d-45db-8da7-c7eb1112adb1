// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/ob_access_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// online booking check identifier request
type OBCheckIdentifierRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*OBCheckIdentifierRequest_Name
	//	*OBCheckIdentifierRequest_Domain
	Anonymous isOBCheckIdentifierRequest_Anonymous `protobuf_oneof:"anonymous"`
	// client uniquely identifies
	//
	// Types that are assignable to Identifier:
	//
	//	*OBCheckIdentifierRequest_PhoneNumber
	//	*OBCheckIdentifierRequest_Email
	Identifier isOBCheckIdentifierRequest_Identifier `protobuf_oneof:"identifier"`
	// include possible clients
	IncludePossibleClients bool `protobuf:"varint,5,opt,name=include_possible_clients,json=includePossibleClients,proto3" json:"include_possible_clients,omitempty"`
}

func (x *OBCheckIdentifierRequest) Reset() {
	*x = OBCheckIdentifierRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBCheckIdentifierRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBCheckIdentifierRequest) ProtoMessage() {}

func (x *OBCheckIdentifierRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBCheckIdentifierRequest.ProtoReflect.Descriptor instead.
func (*OBCheckIdentifierRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{0}
}

func (m *OBCheckIdentifierRequest) GetAnonymous() isOBCheckIdentifierRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *OBCheckIdentifierRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*OBCheckIdentifierRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *OBCheckIdentifierRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*OBCheckIdentifierRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *OBCheckIdentifierRequest) GetIdentifier() isOBCheckIdentifierRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *OBCheckIdentifierRequest) GetPhoneNumber() string {
	if x, ok := x.GetIdentifier().(*OBCheckIdentifierRequest_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return ""
}

func (x *OBCheckIdentifierRequest) GetEmail() string {
	if x, ok := x.GetIdentifier().(*OBCheckIdentifierRequest_Email); ok {
		return x.Email
	}
	return ""
}

func (x *OBCheckIdentifierRequest) GetIncludePossibleClients() bool {
	if x != nil {
		return x.IncludePossibleClients
	}
	return false
}

type isOBCheckIdentifierRequest_Anonymous interface {
	isOBCheckIdentifierRequest_Anonymous()
}

type OBCheckIdentifierRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type OBCheckIdentifierRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*OBCheckIdentifierRequest_Name) isOBCheckIdentifierRequest_Anonymous() {}

func (*OBCheckIdentifierRequest_Domain) isOBCheckIdentifierRequest_Anonymous() {}

type isOBCheckIdentifierRequest_Identifier interface {
	isOBCheckIdentifierRequest_Identifier()
}

type OBCheckIdentifierRequest_PhoneNumber struct {
	// owner phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

type OBCheckIdentifierRequest_Email struct {
	// email
	Email string `protobuf:"bytes,4,opt,name=email,proto3,oneof"`
}

func (*OBCheckIdentifierRequest_PhoneNumber) isOBCheckIdentifierRequest_Identifier() {}

func (*OBCheckIdentifierRequest_Email) isOBCheckIdentifierRequest_Identifier() {}

// online booking check identifier response
type OBCheckIdentifierResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if the identifier exists
	Exist bool `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	// possible clients
	// 如果 request 中的 identifier 可以准确定位到一个 client (即 exist = true)，那么 `possible_clients` 不返回任何东西
	// 如果 request 中的 identifier 无法准确定位到一个 client (即 exist = false)，并且入参设置了 include_possible_clients = true,
	// 那么后端会根据一些规则搜索一些可能匹配的 client 作为 `possible_clients`, 返回给前端选择.
	// 如果匹配不到任何可能的 client, `possible_clients` 为空
	//
	// 目前 possible_clients 的查找规则是:
	// 当 identifier 是 phone number 时, 如果 phone number 无法作为 main contact 直接找到对应的 client,
	// 则将其作为 additional contact 查找, 如果能找到对应的 client, 则返回 (最多不超过 3 个)
	PossibleClients []*OBCheckIdentifierResponse_PossibleClient `protobuf:"bytes,2,rep,name=possible_clients,json=possibleClients,proto3" json:"possible_clients,omitempty"`
}

func (x *OBCheckIdentifierResponse) Reset() {
	*x = OBCheckIdentifierResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBCheckIdentifierResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBCheckIdentifierResponse) ProtoMessage() {}

func (x *OBCheckIdentifierResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBCheckIdentifierResponse.ProtoReflect.Descriptor instead.
func (*OBCheckIdentifierResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{1}
}

func (x *OBCheckIdentifierResponse) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

func (x *OBCheckIdentifierResponse) GetPossibleClients() []*OBCheckIdentifierResponse_PossibleClient {
	if x != nil {
		return x.PossibleClients
	}
	return nil
}

// online booking get verification setting request
type OBGetVerificationSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*OBGetVerificationSettingRequest_Name
	//	*OBGetVerificationSettingRequest_Domain
	Anonymous isOBGetVerificationSettingRequest_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *OBGetVerificationSettingRequest) Reset() {
	*x = OBGetVerificationSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBGetVerificationSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBGetVerificationSettingRequest) ProtoMessage() {}

func (x *OBGetVerificationSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBGetVerificationSettingRequest.ProtoReflect.Descriptor instead.
func (*OBGetVerificationSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{2}
}

func (m *OBGetVerificationSettingRequest) GetAnonymous() isOBGetVerificationSettingRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *OBGetVerificationSettingRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*OBGetVerificationSettingRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *OBGetVerificationSettingRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*OBGetVerificationSettingRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

type isOBGetVerificationSettingRequest_Anonymous interface {
	isOBGetVerificationSettingRequest_Anonymous()
}

type OBGetVerificationSettingRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type OBGetVerificationSettingRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*OBGetVerificationSettingRequest_Name) isOBGetVerificationSettingRequest_Anonymous() {}

func (*OBGetVerificationSettingRequest_Domain) isOBGetVerificationSettingRequest_Anonymous() {}

// online booking get verification setting response
type OBGetVerificationSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// send verification code to existing client
	ExistingClientVerificationCode bool `protobuf:"varint,1,opt,name=existing_client_verification_code,json=existingClientVerificationCode,proto3" json:"existing_client_verification_code,omitempty"`
}

func (x *OBGetVerificationSettingResponse) Reset() {
	*x = OBGetVerificationSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBGetVerificationSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBGetVerificationSettingResponse) ProtoMessage() {}

func (x *OBGetVerificationSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBGetVerificationSettingResponse.ProtoReflect.Descriptor instead.
func (*OBGetVerificationSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{3}
}

func (x *OBGetVerificationSettingResponse) GetExistingClientVerificationCode() bool {
	if x != nil {
		return x.ExistingClientVerificationCode
	}
	return false
}

// online booking send verification code request
type OBSendVerificationCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*OBSendVerificationCodeRequest_Name
	//	*OBSendVerificationCodeRequest_Domain
	Anonymous isOBSendVerificationCodeRequest_Anonymous `protobuf_oneof:"anonymous"`
	// client uniquely identifies
	//
	// Types that are assignable to Identifier:
	//
	//	*OBSendVerificationCodeRequest_PhoneNumber
	//	*OBSendVerificationCodeRequest_PossibleClientId
	//	*OBSendVerificationCodeRequest_AdditionalContact
	Identifier isOBSendVerificationCodeRequest_Identifier `protobuf_oneof:"identifier"`
	// access type, phone number or email, If it is an email, you need to find the associated email if you pass the phone
	AccessType v1.AccessType `protobuf:"varint,8,opt,name=access_type,json=accessType,proto3,enum=moego.models.online_booking.v1.AccessType" json:"access_type,omitempty"`
}

func (x *OBSendVerificationCodeRequest) Reset() {
	*x = OBSendVerificationCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBSendVerificationCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBSendVerificationCodeRequest) ProtoMessage() {}

func (x *OBSendVerificationCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBSendVerificationCodeRequest.ProtoReflect.Descriptor instead.
func (*OBSendVerificationCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{4}
}

func (m *OBSendVerificationCodeRequest) GetAnonymous() isOBSendVerificationCodeRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *OBSendVerificationCodeRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*OBSendVerificationCodeRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *OBSendVerificationCodeRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*OBSendVerificationCodeRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *OBSendVerificationCodeRequest) GetIdentifier() isOBSendVerificationCodeRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *OBSendVerificationCodeRequest) GetPhoneNumber() string {
	if x, ok := x.GetIdentifier().(*OBSendVerificationCodeRequest_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return ""
}

// Deprecated: Do not use.
func (x *OBSendVerificationCodeRequest) GetPossibleClientId() int64 {
	if x, ok := x.GetIdentifier().(*OBSendVerificationCodeRequest_PossibleClientId); ok {
		return x.PossibleClientId
	}
	return 0
}

func (x *OBSendVerificationCodeRequest) GetAdditionalContact() *AdditionalContact {
	if x, ok := x.GetIdentifier().(*OBSendVerificationCodeRequest_AdditionalContact); ok {
		return x.AdditionalContact
	}
	return nil
}

func (x *OBSendVerificationCodeRequest) GetAccessType() v1.AccessType {
	if x != nil {
		return x.AccessType
	}
	return v1.AccessType(0)
}

type isOBSendVerificationCodeRequest_Anonymous interface {
	isOBSendVerificationCodeRequest_Anonymous()
}

type OBSendVerificationCodeRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type OBSendVerificationCodeRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*OBSendVerificationCodeRequest_Name) isOBSendVerificationCodeRequest_Anonymous() {}

func (*OBSendVerificationCodeRequest_Domain) isOBSendVerificationCodeRequest_Anonymous() {}

type isOBSendVerificationCodeRequest_Identifier interface {
	isOBSendVerificationCodeRequest_Identifier()
}

type OBSendVerificationCodeRequest_PhoneNumber struct {
	// owner phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

type OBSendVerificationCodeRequest_PossibleClientId struct {
	// possible client id, send to primary contact
	//
	// Deprecated: Do not use.
	PossibleClientId int64 `protobuf:"varint,4,opt,name=possible_client_id,json=possibleClientId,proto3,oneof"`
}

type OBSendVerificationCodeRequest_AdditionalContact struct {
	// additional contact, send to additional contact
	AdditionalContact *AdditionalContact `protobuf:"bytes,5,opt,name=additional_contact,json=additionalContact,proto3,oneof"`
}

func (*OBSendVerificationCodeRequest_PhoneNumber) isOBSendVerificationCodeRequest_Identifier() {}

func (*OBSendVerificationCodeRequest_PossibleClientId) isOBSendVerificationCodeRequest_Identifier() {}

func (*OBSendVerificationCodeRequest_AdditionalContact) isOBSendVerificationCodeRequest_Identifier() {
}

// The message of additional contact
type AdditionalContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// additional contact phone number
	PhoneNumber string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// related client id
	RelatedClientId int64 `protobuf:"varint,2,opt,name=related_client_id,json=relatedClientId,proto3" json:"related_client_id,omitempty"`
}

func (x *AdditionalContact) Reset() {
	*x = AdditionalContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalContact) ProtoMessage() {}

func (x *AdditionalContact) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalContact.ProtoReflect.Descriptor instead.
func (*AdditionalContact) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{5}
}

func (x *AdditionalContact) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *AdditionalContact) GetRelatedClientId() int64 {
	if x != nil {
		return x.RelatedClientId
	}
	return 0
}

// online booking send verification code to phone response
type OBSendVerificationCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verification code related token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// send verification code success
	Success bool `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *OBSendVerificationCodeResponse) Reset() {
	*x = OBSendVerificationCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBSendVerificationCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBSendVerificationCodeResponse) ProtoMessage() {}

func (x *OBSendVerificationCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBSendVerificationCodeResponse.ProtoReflect.Descriptor instead.
func (*OBSendVerificationCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{6}
}

func (x *OBSendVerificationCodeResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *OBSendVerificationCodeResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// online booking login by phone request
type OBLoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*OBLoginRequest_Name
	//	*OBLoginRequest_Domain
	Anonymous isOBLoginRequest_Anonymous `protobuf_oneof:"anonymous"`
	// login method
	//
	// Types that are assignable to LoginMethod:
	//
	//	*OBLoginRequest_ByVerificationCode
	//	*OBLoginRequest_ByPpp
	LoginMethod isOBLoginRequest_LoginMethod `protobuf_oneof:"login_method"`
	// ob login ads data
	AdsData *OBLoginAdsDataDef `protobuf:"bytes,7,opt,name=ads_data,json=adsData,proto3,oneof" json:"ads_data,omitempty"`
}

func (x *OBLoginRequest) Reset() {
	*x = OBLoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBLoginRequest) ProtoMessage() {}

func (x *OBLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBLoginRequest.ProtoReflect.Descriptor instead.
func (*OBLoginRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{7}
}

func (m *OBLoginRequest) GetAnonymous() isOBLoginRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *OBLoginRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*OBLoginRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *OBLoginRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*OBLoginRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *OBLoginRequest) GetLoginMethod() isOBLoginRequest_LoginMethod {
	if m != nil {
		return m.LoginMethod
	}
	return nil
}

func (x *OBLoginRequest) GetByVerificationCode() *OBLoginByVerificationCodeDef {
	if x, ok := x.GetLoginMethod().(*OBLoginRequest_ByVerificationCode); ok {
		return x.ByVerificationCode
	}
	return nil
}

func (x *OBLoginRequest) GetByPpp() *OBLoginByPPPTokenDef {
	if x, ok := x.GetLoginMethod().(*OBLoginRequest_ByPpp); ok {
		return x.ByPpp
	}
	return nil
}

func (x *OBLoginRequest) GetAdsData() *OBLoginAdsDataDef {
	if x != nil {
		return x.AdsData
	}
	return nil
}

type isOBLoginRequest_Anonymous interface {
	isOBLoginRequest_Anonymous()
}

type OBLoginRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type OBLoginRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*OBLoginRequest_Name) isOBLoginRequest_Anonymous() {}

func (*OBLoginRequest_Domain) isOBLoginRequest_Anonymous() {}

type isOBLoginRequest_LoginMethod interface {
	isOBLoginRequest_LoginMethod()
}

type OBLoginRequest_ByVerificationCode struct {
	// login by verification code
	ByVerificationCode *OBLoginByVerificationCodeDef `protobuf:"bytes,5,opt,name=by_verification_code,json=byVerificationCode,proto3,oneof"`
}

type OBLoginRequest_ByPpp struct {
	// login by ppp token
	ByPpp *OBLoginByPPPTokenDef `protobuf:"bytes,6,opt,name=by_ppp,json=byPpp,proto3,oneof"`
}

func (*OBLoginRequest_ByVerificationCode) isOBLoginRequest_LoginMethod() {}

func (*OBLoginRequest_ByPpp) isOBLoginRequest_LoginMethod() {}

// online booking login by verification code
type OBLoginByVerificationCodeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client uniquely identifies
	//
	// Types that are assignable to Identifier:
	//
	//	*OBLoginByVerificationCodeDef_PhoneNumber
	//	*OBLoginByVerificationCodeDef_PossibleClientId
	//	*OBLoginByVerificationCodeDef_AdditionalContact
	Identifier isOBLoginByVerificationCodeDef_Identifier `protobuf_oneof:"identifier"`
	// access type, phone number or email, If it is an email, you need to find the associated email if you pass the phone
	AccessType v1.AccessType `protobuf:"varint,8,opt,name=access_type,json=accessType,proto3,enum=moego.models.online_booking.v1.AccessType" json:"access_type,omitempty"`
	// verification code
	Code string `protobuf:"bytes,9,opt,name=code,proto3" json:"code,omitempty"`
	// verification code related token
	Token string `protobuf:"bytes,10,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *OBLoginByVerificationCodeDef) Reset() {
	*x = OBLoginByVerificationCodeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBLoginByVerificationCodeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBLoginByVerificationCodeDef) ProtoMessage() {}

func (x *OBLoginByVerificationCodeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBLoginByVerificationCodeDef.ProtoReflect.Descriptor instead.
func (*OBLoginByVerificationCodeDef) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{8}
}

func (m *OBLoginByVerificationCodeDef) GetIdentifier() isOBLoginByVerificationCodeDef_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *OBLoginByVerificationCodeDef) GetPhoneNumber() string {
	if x, ok := x.GetIdentifier().(*OBLoginByVerificationCodeDef_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return ""
}

// Deprecated: Do not use.
func (x *OBLoginByVerificationCodeDef) GetPossibleClientId() int64 {
	if x, ok := x.GetIdentifier().(*OBLoginByVerificationCodeDef_PossibleClientId); ok {
		return x.PossibleClientId
	}
	return 0
}

func (x *OBLoginByVerificationCodeDef) GetAdditionalContact() *AdditionalContact {
	if x, ok := x.GetIdentifier().(*OBLoginByVerificationCodeDef_AdditionalContact); ok {
		return x.AdditionalContact
	}
	return nil
}

func (x *OBLoginByVerificationCodeDef) GetAccessType() v1.AccessType {
	if x != nil {
		return x.AccessType
	}
	return v1.AccessType(0)
}

func (x *OBLoginByVerificationCodeDef) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *OBLoginByVerificationCodeDef) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type isOBLoginByVerificationCodeDef_Identifier interface {
	isOBLoginByVerificationCodeDef_Identifier()
}

type OBLoginByVerificationCodeDef_PhoneNumber struct {
	// owner phone number
	PhoneNumber string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

type OBLoginByVerificationCodeDef_PossibleClientId struct {
	// possible client id
	//
	// Deprecated: Do not use.
	PossibleClientId int64 `protobuf:"varint,2,opt,name=possible_client_id,json=possibleClientId,proto3,oneof"`
}

type OBLoginByVerificationCodeDef_AdditionalContact struct {
	// additional contact
	AdditionalContact *AdditionalContact `protobuf:"bytes,3,opt,name=additional_contact,json=additionalContact,proto3,oneof"`
}

func (*OBLoginByVerificationCodeDef_PhoneNumber) isOBLoginByVerificationCodeDef_Identifier() {}

func (*OBLoginByVerificationCodeDef_PossibleClientId) isOBLoginByVerificationCodeDef_Identifier() {}

func (*OBLoginByVerificationCodeDef_AdditionalContact) isOBLoginByVerificationCodeDef_Identifier() {}

// online booking login by pet parent portal token
type OBLoginByPPPTokenDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client portal token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *OBLoginByPPPTokenDef) Reset() {
	*x = OBLoginByPPPTokenDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBLoginByPPPTokenDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBLoginByPPPTokenDef) ProtoMessage() {}

func (x *OBLoginByPPPTokenDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBLoginByPPPTokenDef.ProtoReflect.Descriptor instead.
func (*OBLoginByPPPTokenDef) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{9}
}

func (x *OBLoginByPPPTokenDef) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// online booking login collect ads data
type OBLoginAdsDataDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// google collect ads data
	GoogleAdsStr string `protobuf:"bytes,1,opt,name=google_ads_str,json=googleAdsStr,proto3" json:"google_ads_str,omitempty"`
}

func (x *OBLoginAdsDataDef) Reset() {
	*x = OBLoginAdsDataDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBLoginAdsDataDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBLoginAdsDataDef) ProtoMessage() {}

func (x *OBLoginAdsDataDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBLoginAdsDataDef.ProtoReflect.Descriptor instead.
func (*OBLoginAdsDataDef) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{10}
}

func (x *OBLoginAdsDataDef) GetGoogleAdsStr() string {
	if x != nil {
		return x.GoogleAdsStr
	}
	return ""
}

// online booking login response
type OBLoginResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OBLoginResponse) Reset() {
	*x = OBLoginResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBLoginResponse) ProtoMessage() {}

func (x *OBLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBLoginResponse.ProtoReflect.Descriptor instead.
func (*OBLoginResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{11}
}

// online booking logout request
type OBLogoutRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*OBLogoutRequest_Name
	//	*OBLogoutRequest_Domain
	Anonymous isOBLogoutRequest_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *OBLogoutRequest) Reset() {
	*x = OBLogoutRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBLogoutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBLogoutRequest) ProtoMessage() {}

func (x *OBLogoutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBLogoutRequest.ProtoReflect.Descriptor instead.
func (*OBLogoutRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{12}
}

func (m *OBLogoutRequest) GetAnonymous() isOBLogoutRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *OBLogoutRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*OBLogoutRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *OBLogoutRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*OBLogoutRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

type isOBLogoutRequest_Anonymous interface {
	isOBLogoutRequest_Anonymous()
}

type OBLogoutRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type OBLogoutRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*OBLogoutRequest_Name) isOBLogoutRequest_Anonymous() {}

func (*OBLogoutRequest_Domain) isOBLogoutRequest_Anonymous() {}

// online booking logout response
type OBLogoutResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OBLogoutResponse) Reset() {
	*x = OBLogoutResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBLogoutResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBLogoutResponse) ProtoMessage() {}

func (x *OBLogoutResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBLogoutResponse.ProtoReflect.Descriptor instead.
func (*OBLogoutResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{13}
}

// online booking get dev mode request
type OBDevModeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OBDevModeRequest) Reset() {
	*x = OBDevModeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBDevModeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBDevModeRequest) ProtoMessage() {}

func (x *OBDevModeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBDevModeRequest.ProtoReflect.Descriptor instead.
func (*OBDevModeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{14}
}

// online booking get dev mode response
type OBDevModeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// dev mode flag
	DevMode bool `protobuf:"varint,1,opt,name=dev_mode,json=devMode,proto3" json:"dev_mode,omitempty"`
}

func (x *OBDevModeResponse) Reset() {
	*x = OBDevModeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBDevModeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBDevModeResponse) ProtoMessage() {}

func (x *OBDevModeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBDevModeResponse.ProtoReflect.Descriptor instead.
func (*OBDevModeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{15}
}

func (x *OBDevModeResponse) GetDevMode() bool {
	if x != nil {
		return x.DevMode
	}
	return false
}

// possible client
type OBCheckIdentifierResponse_PossibleClient struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// masked phone number
	// possible client 不一定是用户本人, 考虑到隐私问题, 返回的 phone number 会被 mask
	MaskedPhoneNumber string `protobuf:"bytes,5,opt,name=masked_phone_number,json=maskedPhoneNumber,proto3" json:"masked_phone_number,omitempty"`
}

func (x *OBCheckIdentifierResponse_PossibleClient) Reset() {
	*x = OBCheckIdentifierResponse_PossibleClient{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBCheckIdentifierResponse_PossibleClient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBCheckIdentifierResponse_PossibleClient) ProtoMessage() {}

func (x *OBCheckIdentifierResponse_PossibleClient) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBCheckIdentifierResponse_PossibleClient.ProtoReflect.Descriptor instead.
func (*OBCheckIdentifierResponse_PossibleClient) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *OBCheckIdentifierResponse_PossibleClient) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OBCheckIdentifierResponse_PossibleClient) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *OBCheckIdentifierResponse_PossibleClient) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *OBCheckIdentifierResponse_PossibleClient) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *OBCheckIdentifierResponse_PossibleClient) GetMaskedPhoneNumber() string {
	if x != nil {
		return x.MaskedPhoneNumber
	}
	return ""
}

var File_moego_api_online_booking_v1_ob_access_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_ob_access_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x34,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x62, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x02,
	0x0a, 0x18, 0x4f, 0x42, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x3e, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x19, 0xfa, 0x42, 0x16, 0x72, 0x14, 0x18, 0x11, 0x32, 0x10, 0x5e, 0x5b, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x34, 0x7d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x48, 0x01, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x32, 0x48, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x38, 0x0a, 0x18, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f,
	0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xd3, 0x02, 0x0a, 0x19, 0x4f,
	0x42, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x12, 0x70,
	0x0a, 0x10, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x0f, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73,
	0x1a, 0xad, 0x01, 0x0a, 0x0e, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d,
	0x61, 0x73, 0x6b, 0x65, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0x63, 0x0a, 0x1f, 0x4f, 0x42, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73,
	0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x6d, 0x0a, 0x20, 0x4f, 0x42, 0x47, 0x65, 0x74, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x21, 0x65, 0x78, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0xa0, 0x03, 0x0a, 0x1d, 0x4f, 0x42, 0x53, 0x65, 0x6e, 0x64, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x3e, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0xfa, 0x42,
	0x16, 0x72, 0x14, 0x18, 0x11, 0x32, 0x10, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d,
	0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x48, 0x01, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x12, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x48, 0x01, 0x52, 0x10, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x5f, 0x0a, 0x12, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x48, 0x01, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x55, 0x0a, 0x0b, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x7d, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x3c, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x19, 0xfa, 0x42, 0x16, 0x72, 0x14, 0x18, 0x11, 0x32, 0x10, 0x5e, 0x5b, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x1e, 0x4f, 0x42, 0x53, 0x65, 0x6e, 0x64,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xff, 0x02, 0x0a, 0x0e, 0x4f, 0x42, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x6d, 0x0a, 0x14, 0x62,
	0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42,
	0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x12, 0x62, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4a, 0x0a, 0x06, 0x62, 0x79,
	0x5f, 0x70, 0x70, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x42, 0x79, 0x50, 0x50, 0x50, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52,
	0x05, 0x62, 0x79, 0x50, 0x70, 0x70, 0x12, 0x4e, 0x0a, 0x08, 0x61, 0x64, 0x73, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x41, 0x64,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x07, 0x61, 0x64, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d,
	0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x13, 0x0a, 0x0c, 0x6c, 0x6f, 0x67, 0x69,
	0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x61, 0x64, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0x92, 0x03, 0x0a, 0x1c, 0x4f,
	0x42, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x66, 0x12, 0x3e, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x19, 0xfa, 0x42, 0x16, 0x72, 0x14, 0x18, 0x11, 0x32, 0x10, 0x5e, 0x5b, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x48, 0x00, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x12, 0x70,
	0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x10, 0x70,
	0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x5f, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x48, 0x00, 0x52, 0x11, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x12, 0x4b, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x06, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0x80, 0x02, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x11, 0x0a, 0x0a,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22,
	0x38, 0x0a, 0x14, 0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42, 0x79, 0x50, 0x50, 0x50, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x20, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18,
	0x80, 0x02, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x39, 0x0a, 0x11, 0x4f, 0x42, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x41, 0x64, 0x73, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x12, 0x24,
	0x0a, 0x0e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x61, 0x64, 0x73, 0x5f, 0x73, 0x74, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x64,
	0x73, 0x53, 0x74, 0x72, 0x22, 0x11, 0x0a, 0x0f, 0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x53, 0x0a, 0x0f, 0x4f, 0x42, 0x4c, 0x6f, 0x67,
	0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x12, 0x0a, 0x10,
	0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x12, 0x0a, 0x10, 0x4f, 0x42, 0x44, 0x65, 0x76, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x2e, 0x0a, 0x11, 0x4f, 0x42, 0x44, 0x65, 0x76, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x76,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x76,
	0x4d, 0x6f, 0x64, 0x65, 0x32, 0xf6, 0x05, 0x0a, 0x0f, 0x4f, 0x42, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x0f, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x42, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x42, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x53, 0x65, 0x6e,
	0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x06, 0x4c, 0x6f, 0x67,
	0x6f, 0x75, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x42, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6b, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x44,
	0x65, 0x76, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x44, 0x65,
	0x76, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01,
	0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_online_booking_v1_ob_access_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_ob_access_api_proto_rawDescData = file_moego_api_online_booking_v1_ob_access_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_ob_access_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_ob_access_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_ob_access_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_ob_access_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_ob_access_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_moego_api_online_booking_v1_ob_access_api_proto_goTypes = []interface{}{
	(*OBCheckIdentifierRequest)(nil),                 // 0: moego.api.online_booking.v1.OBCheckIdentifierRequest
	(*OBCheckIdentifierResponse)(nil),                // 1: moego.api.online_booking.v1.OBCheckIdentifierResponse
	(*OBGetVerificationSettingRequest)(nil),          // 2: moego.api.online_booking.v1.OBGetVerificationSettingRequest
	(*OBGetVerificationSettingResponse)(nil),         // 3: moego.api.online_booking.v1.OBGetVerificationSettingResponse
	(*OBSendVerificationCodeRequest)(nil),            // 4: moego.api.online_booking.v1.OBSendVerificationCodeRequest
	(*AdditionalContact)(nil),                        // 5: moego.api.online_booking.v1.AdditionalContact
	(*OBSendVerificationCodeResponse)(nil),           // 6: moego.api.online_booking.v1.OBSendVerificationCodeResponse
	(*OBLoginRequest)(nil),                           // 7: moego.api.online_booking.v1.OBLoginRequest
	(*OBLoginByVerificationCodeDef)(nil),             // 8: moego.api.online_booking.v1.OBLoginByVerificationCodeDef
	(*OBLoginByPPPTokenDef)(nil),                     // 9: moego.api.online_booking.v1.OBLoginByPPPTokenDef
	(*OBLoginAdsDataDef)(nil),                        // 10: moego.api.online_booking.v1.OBLoginAdsDataDef
	(*OBLoginResponse)(nil),                          // 11: moego.api.online_booking.v1.OBLoginResponse
	(*OBLogoutRequest)(nil),                          // 12: moego.api.online_booking.v1.OBLogoutRequest
	(*OBLogoutResponse)(nil),                         // 13: moego.api.online_booking.v1.OBLogoutResponse
	(*OBDevModeRequest)(nil),                         // 14: moego.api.online_booking.v1.OBDevModeRequest
	(*OBDevModeResponse)(nil),                        // 15: moego.api.online_booking.v1.OBDevModeResponse
	(*OBCheckIdentifierResponse_PossibleClient)(nil), // 16: moego.api.online_booking.v1.OBCheckIdentifierResponse.PossibleClient
	(v1.AccessType)(0),                               // 17: moego.models.online_booking.v1.AccessType
}
var file_moego_api_online_booking_v1_ob_access_api_proto_depIdxs = []int32{
	16, // 0: moego.api.online_booking.v1.OBCheckIdentifierResponse.possible_clients:type_name -> moego.api.online_booking.v1.OBCheckIdentifierResponse.PossibleClient
	5,  // 1: moego.api.online_booking.v1.OBSendVerificationCodeRequest.additional_contact:type_name -> moego.api.online_booking.v1.AdditionalContact
	17, // 2: moego.api.online_booking.v1.OBSendVerificationCodeRequest.access_type:type_name -> moego.models.online_booking.v1.AccessType
	8,  // 3: moego.api.online_booking.v1.OBLoginRequest.by_verification_code:type_name -> moego.api.online_booking.v1.OBLoginByVerificationCodeDef
	9,  // 4: moego.api.online_booking.v1.OBLoginRequest.by_ppp:type_name -> moego.api.online_booking.v1.OBLoginByPPPTokenDef
	10, // 5: moego.api.online_booking.v1.OBLoginRequest.ads_data:type_name -> moego.api.online_booking.v1.OBLoginAdsDataDef
	5,  // 6: moego.api.online_booking.v1.OBLoginByVerificationCodeDef.additional_contact:type_name -> moego.api.online_booking.v1.AdditionalContact
	17, // 7: moego.api.online_booking.v1.OBLoginByVerificationCodeDef.access_type:type_name -> moego.models.online_booking.v1.AccessType
	0,  // 8: moego.api.online_booking.v1.OBAccessService.CheckIdentifier:input_type -> moego.api.online_booking.v1.OBCheckIdentifierRequest
	2,  // 9: moego.api.online_booking.v1.OBAccessService.GetVerificationSetting:input_type -> moego.api.online_booking.v1.OBGetVerificationSettingRequest
	4,  // 10: moego.api.online_booking.v1.OBAccessService.SendVerificationCode:input_type -> moego.api.online_booking.v1.OBSendVerificationCodeRequest
	7,  // 11: moego.api.online_booking.v1.OBAccessService.Login:input_type -> moego.api.online_booking.v1.OBLoginRequest
	12, // 12: moego.api.online_booking.v1.OBAccessService.Logout:input_type -> moego.api.online_booking.v1.OBLogoutRequest
	14, // 13: moego.api.online_booking.v1.OBAccessService.GetDevMode:input_type -> moego.api.online_booking.v1.OBDevModeRequest
	1,  // 14: moego.api.online_booking.v1.OBAccessService.CheckIdentifier:output_type -> moego.api.online_booking.v1.OBCheckIdentifierResponse
	3,  // 15: moego.api.online_booking.v1.OBAccessService.GetVerificationSetting:output_type -> moego.api.online_booking.v1.OBGetVerificationSettingResponse
	6,  // 16: moego.api.online_booking.v1.OBAccessService.SendVerificationCode:output_type -> moego.api.online_booking.v1.OBSendVerificationCodeResponse
	11, // 17: moego.api.online_booking.v1.OBAccessService.Login:output_type -> moego.api.online_booking.v1.OBLoginResponse
	13, // 18: moego.api.online_booking.v1.OBAccessService.Logout:output_type -> moego.api.online_booking.v1.OBLogoutResponse
	15, // 19: moego.api.online_booking.v1.OBAccessService.GetDevMode:output_type -> moego.api.online_booking.v1.OBDevModeResponse
	14, // [14:20] is the sub-list for method output_type
	8,  // [8:14] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_ob_access_api_proto_init() }
func file_moego_api_online_booking_v1_ob_access_api_proto_init() {
	if File_moego_api_online_booking_v1_ob_access_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBCheckIdentifierRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBCheckIdentifierResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBGetVerificationSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBGetVerificationSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBSendVerificationCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBSendVerificationCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBLoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBLoginByVerificationCodeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBLoginByPPPTokenDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBLoginAdsDataDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBLoginResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBLogoutRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBLogoutResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBDevModeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBDevModeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBCheckIdentifierResponse_PossibleClient); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*OBCheckIdentifierRequest_Name)(nil),
		(*OBCheckIdentifierRequest_Domain)(nil),
		(*OBCheckIdentifierRequest_PhoneNumber)(nil),
		(*OBCheckIdentifierRequest_Email)(nil),
	}
	file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*OBGetVerificationSettingRequest_Name)(nil),
		(*OBGetVerificationSettingRequest_Domain)(nil),
	}
	file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*OBSendVerificationCodeRequest_Name)(nil),
		(*OBSendVerificationCodeRequest_Domain)(nil),
		(*OBSendVerificationCodeRequest_PhoneNumber)(nil),
		(*OBSendVerificationCodeRequest_PossibleClientId)(nil),
		(*OBSendVerificationCodeRequest_AdditionalContact)(nil),
	}
	file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*OBLoginRequest_Name)(nil),
		(*OBLoginRequest_Domain)(nil),
		(*OBLoginRequest_ByVerificationCode)(nil),
		(*OBLoginRequest_ByPpp)(nil),
	}
	file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*OBLoginByVerificationCodeDef_PhoneNumber)(nil),
		(*OBLoginByVerificationCodeDef_PossibleClientId)(nil),
		(*OBLoginByVerificationCodeDef_AdditionalContact)(nil),
	}
	file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*OBLogoutRequest_Name)(nil),
		(*OBLogoutRequest_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_ob_access_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_ob_access_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_ob_access_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_ob_access_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_ob_access_api_proto = out.File
	file_moego_api_online_booking_v1_ob_access_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_ob_access_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_ob_access_api_proto_depIdxs = nil
}
