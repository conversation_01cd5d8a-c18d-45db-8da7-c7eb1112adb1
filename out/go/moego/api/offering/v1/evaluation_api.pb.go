// @since 2024-03-12 11:33:59
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/offering/v1/evaluation_api.proto

package offeringapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create evaluation params
type CreateEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation def
	EvaluationDef *v1.EvaluationDef `protobuf:"bytes,1,opt,name=evaluation_def,json=evaluationDef,proto3" json:"evaluation_def,omitempty"`
}

func (x *CreateEvaluationParams) Reset() {
	*x = CreateEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationParams) ProtoMessage() {}

func (x *CreateEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationParams.ProtoReflect.Descriptor instead.
func (*CreateEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateEvaluationParams) GetEvaluationDef() *v1.EvaluationDef {
	if x != nil {
		return x.EvaluationDef
	}
	return nil
}

// create evaluation result
type CreateEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model
	EvaluationModel *v1.EvaluationModel `protobuf:"bytes,1,opt,name=evaluation_model,json=evaluationModel,proto3" json:"evaluation_model,omitempty"`
}

func (x *CreateEvaluationResult) Reset() {
	*x = CreateEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationResult) ProtoMessage() {}

func (x *CreateEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationResult.ProtoReflect.Descriptor instead.
func (*CreateEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateEvaluationResult) GetEvaluationModel() *v1.EvaluationModel {
	if x != nil {
		return x.EvaluationModel
	}
	return nil
}

// update evaluation params
type UpdateEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the evaluation def
	EvaluationDef *v1.EvaluationDef `protobuf:"bytes,2,opt,name=evaluation_def,json=evaluationDef,proto3" json:"evaluation_def,omitempty"`
}

func (x *UpdateEvaluationParams) Reset() {
	*x = UpdateEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationParams) ProtoMessage() {}

func (x *UpdateEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationParams.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateEvaluationParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateEvaluationParams) GetEvaluationDef() *v1.EvaluationDef {
	if x != nil {
		return x.EvaluationDef
	}
	return nil
}

// update evaluation result
type UpdateEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model
	EvaluationModel *v1.EvaluationModel `protobuf:"bytes,1,opt,name=evaluation_model,json=evaluationModel,proto3" json:"evaluation_model,omitempty"`
}

func (x *UpdateEvaluationResult) Reset() {
	*x = UpdateEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationResult) ProtoMessage() {}

func (x *UpdateEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationResult.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateEvaluationResult) GetEvaluationModel() *v1.EvaluationModel {
	if x != nil {
		return x.EvaluationModel
	}
	return nil
}

// delete evaluation params
type DeleteEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteEvaluationParams) Reset() {
	*x = DeleteEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationParams) ProtoMessage() {}

func (x *DeleteEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationParams.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteEvaluationParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete evaluation result
type DeleteEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteEvaluationResult) Reset() {
	*x = DeleteEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationResult) ProtoMessage() {}

func (x *DeleteEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationResult.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{5}
}

// get evaluation
type GetEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetEvaluationParams) Reset() {
	*x = GetEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationParams) ProtoMessage() {}

func (x *GetEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationParams.ProtoReflect.Descriptor instead.
func (*GetEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetEvaluationParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get evaluation
type GetEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model
	Evaluation *v1.EvaluationModel `protobuf:"bytes,1,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *GetEvaluationResult) Reset() {
	*x = GetEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationResult) ProtoMessage() {}

func (x *GetEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationResult.ProtoReflect.Descriptor instead.
func (*GetEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetEvaluationResult) GetEvaluation() *v1.EvaluationModel {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

// get evaluation list
type GetEvaluationListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetEvaluationListParams) Reset() {
	*x = GetEvaluationListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationListParams) ProtoMessage() {}

func (x *GetEvaluationListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationListParams.ProtoReflect.Descriptor instead.
func (*GetEvaluationListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{8}
}

// get evaluation list result
type GetEvaluationListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model list
	Evaluations []*v1.EvaluationModel `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *GetEvaluationListResult) Reset() {
	*x = GetEvaluationListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationListResult) ProtoMessage() {}

func (x *GetEvaluationListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationListResult.ProtoReflect.Descriptor instead.
func (*GetEvaluationListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetEvaluationListResult) GetEvaluations() []*v1.EvaluationModel {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// get applicable evaluation list
type GetApplicableEvaluationListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// service item type
	ServiceItemType *v1.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
}

func (x *GetApplicableEvaluationListParams) Reset() {
	*x = GetApplicableEvaluationListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicableEvaluationListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableEvaluationListParams) ProtoMessage() {}

func (x *GetApplicableEvaluationListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableEvaluationListParams.ProtoReflect.Descriptor instead.
func (*GetApplicableEvaluationListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetApplicableEvaluationListParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetApplicableEvaluationListParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

// get applicable evaluation list result
type GetApplicableEvaluationListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model list
	Evaluations []*v1.EvaluationBriefView `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *GetApplicableEvaluationListResult) Reset() {
	*x = GetApplicableEvaluationListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicableEvaluationListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableEvaluationListResult) ProtoMessage() {}

func (x *GetApplicableEvaluationListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableEvaluationListResult.ProtoReflect.Descriptor instead.
func (*GetApplicableEvaluationListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetApplicableEvaluationListResult) GetEvaluations() []*v1.EvaluationBriefView {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// get business list with applicable evaluation params
type GetBusinessListWithApplicableEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetBusinessListWithApplicableEvaluationParams) Reset() {
	*x = GetBusinessListWithApplicableEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessListWithApplicableEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessListWithApplicableEvaluationParams) ProtoMessage() {}

func (x *GetBusinessListWithApplicableEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessListWithApplicableEvaluationParams.ProtoReflect.Descriptor instead.
func (*GetBusinessListWithApplicableEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{12}
}

// get business list with applicable evaluation result
type GetBusinessListWithApplicableEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the business list
	Businesses []*v11.LocationBriefView `protobuf:"bytes,1,rep,name=businesses,proto3" json:"businesses,omitempty"`
}

func (x *GetBusinessListWithApplicableEvaluationResult) Reset() {
	*x = GetBusinessListWithApplicableEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessListWithApplicableEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessListWithApplicableEvaluationResult) ProtoMessage() {}

func (x *GetBusinessListWithApplicableEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_evaluation_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessListWithApplicableEvaluationResult.ProtoReflect.Descriptor instead.
func (*GetBusinessListWithApplicableEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetBusinessListWithApplicableEvaluationResult) GetBusinesses() []*v11.LocationBriefView {
	if x != nil {
		return x.Businesses
	}
	return nil
}

var File_moego_api_offering_v1_evaluation_api_proto protoreflect.FileDescriptor

var file_moego_api_offering_v1_evaluation_api_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x72, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x58, 0x0a, 0x0e, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x66, 0x22, 0x6e, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a,
	0x10, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x22, 0x8b, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x58, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x66, 0x22, 0x6e, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x10, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x22, 0x31, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2e,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x60,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x49, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x66, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4b, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0xd4, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01,
	0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x74, 0x0a, 0x21, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x4f, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x2f, 0x0a, 0x2d, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x80, 0x01, 0x0a, 0x2d, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x32, 0xa1, 0x07, 0x0a, 0x11, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x72, 0x0a, 0x10, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x72,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0x72, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x75, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x93, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xb7,
	0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_offering_v1_evaluation_api_proto_rawDescOnce sync.Once
	file_moego_api_offering_v1_evaluation_api_proto_rawDescData = file_moego_api_offering_v1_evaluation_api_proto_rawDesc
)

func file_moego_api_offering_v1_evaluation_api_proto_rawDescGZIP() []byte {
	file_moego_api_offering_v1_evaluation_api_proto_rawDescOnce.Do(func() {
		file_moego_api_offering_v1_evaluation_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_offering_v1_evaluation_api_proto_rawDescData)
	})
	return file_moego_api_offering_v1_evaluation_api_proto_rawDescData
}

var file_moego_api_offering_v1_evaluation_api_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_api_offering_v1_evaluation_api_proto_goTypes = []interface{}{
	(*CreateEvaluationParams)(nil),                        // 0: moego.api.offering.v1.CreateEvaluationParams
	(*CreateEvaluationResult)(nil),                        // 1: moego.api.offering.v1.CreateEvaluationResult
	(*UpdateEvaluationParams)(nil),                        // 2: moego.api.offering.v1.UpdateEvaluationParams
	(*UpdateEvaluationResult)(nil),                        // 3: moego.api.offering.v1.UpdateEvaluationResult
	(*DeleteEvaluationParams)(nil),                        // 4: moego.api.offering.v1.DeleteEvaluationParams
	(*DeleteEvaluationResult)(nil),                        // 5: moego.api.offering.v1.DeleteEvaluationResult
	(*GetEvaluationParams)(nil),                           // 6: moego.api.offering.v1.GetEvaluationParams
	(*GetEvaluationResult)(nil),                           // 7: moego.api.offering.v1.GetEvaluationResult
	(*GetEvaluationListParams)(nil),                       // 8: moego.api.offering.v1.GetEvaluationListParams
	(*GetEvaluationListResult)(nil),                       // 9: moego.api.offering.v1.GetEvaluationListResult
	(*GetApplicableEvaluationListParams)(nil),             // 10: moego.api.offering.v1.GetApplicableEvaluationListParams
	(*GetApplicableEvaluationListResult)(nil),             // 11: moego.api.offering.v1.GetApplicableEvaluationListResult
	(*GetBusinessListWithApplicableEvaluationParams)(nil), // 12: moego.api.offering.v1.GetBusinessListWithApplicableEvaluationParams
	(*GetBusinessListWithApplicableEvaluationResult)(nil), // 13: moego.api.offering.v1.GetBusinessListWithApplicableEvaluationResult
	(*v1.EvaluationDef)(nil),                              // 14: moego.models.offering.v1.EvaluationDef
	(*v1.EvaluationModel)(nil),                            // 15: moego.models.offering.v1.EvaluationModel
	(v1.ServiceItemType)(0),                               // 16: moego.models.offering.v1.ServiceItemType
	(*v1.EvaluationBriefView)(nil),                        // 17: moego.models.offering.v1.EvaluationBriefView
	(*v11.LocationBriefView)(nil),                         // 18: moego.models.organization.v1.LocationBriefView
}
var file_moego_api_offering_v1_evaluation_api_proto_depIdxs = []int32{
	14, // 0: moego.api.offering.v1.CreateEvaluationParams.evaluation_def:type_name -> moego.models.offering.v1.EvaluationDef
	15, // 1: moego.api.offering.v1.CreateEvaluationResult.evaluation_model:type_name -> moego.models.offering.v1.EvaluationModel
	14, // 2: moego.api.offering.v1.UpdateEvaluationParams.evaluation_def:type_name -> moego.models.offering.v1.EvaluationDef
	15, // 3: moego.api.offering.v1.UpdateEvaluationResult.evaluation_model:type_name -> moego.models.offering.v1.EvaluationModel
	15, // 4: moego.api.offering.v1.GetEvaluationResult.evaluation:type_name -> moego.models.offering.v1.EvaluationModel
	15, // 5: moego.api.offering.v1.GetEvaluationListResult.evaluations:type_name -> moego.models.offering.v1.EvaluationModel
	16, // 6: moego.api.offering.v1.GetApplicableEvaluationListParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	17, // 7: moego.api.offering.v1.GetApplicableEvaluationListResult.evaluations:type_name -> moego.models.offering.v1.EvaluationBriefView
	18, // 8: moego.api.offering.v1.GetBusinessListWithApplicableEvaluationResult.businesses:type_name -> moego.models.organization.v1.LocationBriefView
	0,  // 9: moego.api.offering.v1.EvaluationService.CreateEvaluation:input_type -> moego.api.offering.v1.CreateEvaluationParams
	2,  // 10: moego.api.offering.v1.EvaluationService.UpdateEvaluation:input_type -> moego.api.offering.v1.UpdateEvaluationParams
	4,  // 11: moego.api.offering.v1.EvaluationService.DeleteEvaluation:input_type -> moego.api.offering.v1.DeleteEvaluationParams
	6,  // 12: moego.api.offering.v1.EvaluationService.GetEvaluation:input_type -> moego.api.offering.v1.GetEvaluationParams
	8,  // 13: moego.api.offering.v1.EvaluationService.GetEvaluationList:input_type -> moego.api.offering.v1.GetEvaluationListParams
	10, // 14: moego.api.offering.v1.EvaluationService.GetApplicableEvaluationList:input_type -> moego.api.offering.v1.GetApplicableEvaluationListParams
	12, // 15: moego.api.offering.v1.EvaluationService.GetBusinessListWithApplicableEvaluation:input_type -> moego.api.offering.v1.GetBusinessListWithApplicableEvaluationParams
	1,  // 16: moego.api.offering.v1.EvaluationService.CreateEvaluation:output_type -> moego.api.offering.v1.CreateEvaluationResult
	3,  // 17: moego.api.offering.v1.EvaluationService.UpdateEvaluation:output_type -> moego.api.offering.v1.UpdateEvaluationResult
	5,  // 18: moego.api.offering.v1.EvaluationService.DeleteEvaluation:output_type -> moego.api.offering.v1.DeleteEvaluationResult
	7,  // 19: moego.api.offering.v1.EvaluationService.GetEvaluation:output_type -> moego.api.offering.v1.GetEvaluationResult
	9,  // 20: moego.api.offering.v1.EvaluationService.GetEvaluationList:output_type -> moego.api.offering.v1.GetEvaluationListResult
	11, // 21: moego.api.offering.v1.EvaluationService.GetApplicableEvaluationList:output_type -> moego.api.offering.v1.GetApplicableEvaluationListResult
	13, // 22: moego.api.offering.v1.EvaluationService.GetBusinessListWithApplicableEvaluation:output_type -> moego.api.offering.v1.GetBusinessListWithApplicableEvaluationResult
	16, // [16:23] is the sub-list for method output_type
	9,  // [9:16] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_api_offering_v1_evaluation_api_proto_init() }
func file_moego_api_offering_v1_evaluation_api_proto_init() {
	if File_moego_api_offering_v1_evaluation_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicableEvaluationListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicableEvaluationListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessListWithApplicableEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_evaluation_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessListWithApplicableEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_offering_v1_evaluation_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_offering_v1_evaluation_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_offering_v1_evaluation_api_proto_goTypes,
		DependencyIndexes: file_moego_api_offering_v1_evaluation_api_proto_depIdxs,
		MessageInfos:      file_moego_api_offering_v1_evaluation_api_proto_msgTypes,
	}.Build()
	File_moego_api_offering_v1_evaluation_api_proto = out.File
	file_moego_api_offering_v1_evaluation_api_proto_rawDesc = nil
	file_moego_api_offering_v1_evaluation_api_proto_goTypes = nil
	file_moego_api_offering_v1_evaluation_api_proto_depIdxs = nil
}
