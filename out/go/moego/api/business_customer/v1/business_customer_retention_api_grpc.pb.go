// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_customer_retention_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerRetentionServiceClient is the client API for BusinessCustomerRetentionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerRetentionServiceClient interface {
	// Fetch retention data
	FetchRetentionData(ctx context.Context, in *FetchRetentionDataParams, opts ...grpc.CallOption) (*FetchRetentionDataResult, error)
}

type businessCustomerRetentionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerRetentionServiceClient(cc grpc.ClientConnInterface) BusinessCustomerRetentionServiceClient {
	return &businessCustomerRetentionServiceClient{cc}
}

func (c *businessCustomerRetentionServiceClient) FetchRetentionData(ctx context.Context, in *FetchRetentionDataParams, opts ...grpc.CallOption) (*FetchRetentionDataResult, error) {
	out := new(FetchRetentionDataResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerRetentionService/FetchRetentionData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerRetentionServiceServer is the server API for BusinessCustomerRetentionService service.
// All implementations must embed UnimplementedBusinessCustomerRetentionServiceServer
// for forward compatibility
type BusinessCustomerRetentionServiceServer interface {
	// Fetch retention data
	FetchRetentionData(context.Context, *FetchRetentionDataParams) (*FetchRetentionDataResult, error)
	mustEmbedUnimplementedBusinessCustomerRetentionServiceServer()
}

// UnimplementedBusinessCustomerRetentionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerRetentionServiceServer struct {
}

func (UnimplementedBusinessCustomerRetentionServiceServer) FetchRetentionData(context.Context, *FetchRetentionDataParams) (*FetchRetentionDataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchRetentionData not implemented")
}
func (UnimplementedBusinessCustomerRetentionServiceServer) mustEmbedUnimplementedBusinessCustomerRetentionServiceServer() {
}

// UnsafeBusinessCustomerRetentionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerRetentionServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerRetentionServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerRetentionServiceServer()
}

func RegisterBusinessCustomerRetentionServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerRetentionServiceServer) {
	s.RegisterService(&BusinessCustomerRetentionService_ServiceDesc, srv)
}

func _BusinessCustomerRetentionService_FetchRetentionData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchRetentionDataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerRetentionServiceServer).FetchRetentionData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerRetentionService/FetchRetentionData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerRetentionServiceServer).FetchRetentionData(ctx, req.(*FetchRetentionDataParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerRetentionService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerRetentionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerRetentionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessCustomerRetentionService",
	HandlerType: (*BusinessCustomerRetentionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FetchRetentionData",
			Handler:    _BusinessCustomerRetentionService_FetchRetentionData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_customer_retention_api.proto",
}
