package com.moego.server.grooming.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Report Card 迁移进度DTO
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportCardMigrateProgressDTO {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 当前进度百分比 (0-100)
     */
    private Integer progressPercent;

    /**
     * 当前正在处理的表
     */
    private String currentTable;

    /**
     * 当前表进度百分比
     */
    private Integer currentTableProgress;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 预计完成时间
     */
    private LocalDateTime estimatedEndTime;

    /**
     * 已完成的表列表
     */
    private List<String> completedTables;

    /**
     * 待处理的表列表
     */
    private List<String> pendingTables;

    /**
     * 当前处理信息
     */
    private String currentMessage;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理速度（记录/秒）
     */
    private Double processingSpeed;

    /**
     * 已处理记录数
     */
    private Long processedRecords;

    /**
     * 总记录数
     */
    private Long totalRecords;

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        /**
         * 等待开始
         */
        PENDING,

        /**
         * 运行中
         */
        RUNNING,

        /**
         * 已完成
         */
        COMPLETED,

        /**
         * 失败
         */
        FAILED,

        /**
         * 已取消
         */
        CANCELLED,

        /**
         * 暂停
         */
        PAUSED
    }
}
