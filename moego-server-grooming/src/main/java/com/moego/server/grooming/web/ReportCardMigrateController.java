package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.report.migrate.ReportCardMigrateService;
import com.moego.server.grooming.web.dto.ReportCardMigrateProgressDTO;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Report Card 数据迁移控制器
 * 负责将 grooming report 和 daily report 数据迁移到 fulfillment 服务
 *
 */
@RestController
@RequestMapping("/scripts/report-card-migrate")
@RequiredArgsConstructor
@Slf4j
@Hidden
@Tag(name = "Report Card Migration", description = "Report Card数据迁移相关接口")
public class ReportCardMigrateController {

    private final ReportCardMigrateService reportCardMigrateService;

    /**
     * 启动完整的Report Card数据迁移
     * 按照依赖顺序迁移所有表：theme_config → template → report → question → send_record
     */
    @PostMapping("/start")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "启动完整数据迁移", description = "按照依赖顺序迁移所有Report Card相关数据")
    public ReportCardMigrateResultDTO startFullMigration(
            @Parameter(description = "指定businessID，为空则迁移所有数据")
            @RequestParam(required = false) Integer businessId) {

        log.info("开始Report Card数据迁移，businessId: {}", businessId);

        try {
            return reportCardMigrateService.startFullMigration(businessId);
        } catch (Exception e) {
            log.error("Report Card数据迁移失败", e);
            throw e;
        }
    }

    /**
     * 迁移主题配置数据
     * moe_grooming_report_theme_config → fulfillment_report_theme_config
     */
    @PostMapping("/theme-config")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移主题配置数据")
    public ReportCardMigrateResultDTO migrateThemeConfig(
            @RequestParam(defaultValue = "false") Boolean forceReplace) {

        log.info("开始迁移主题配置数据，forceReplace: {}", forceReplace);
        return reportCardMigrateService.migrateThemeConfig(forceReplace);
    }

    /**
     * 迁移模板数据
     * moe_grooming_report_template → fulfillment_report_template
     */
    @PostMapping("/template")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移模板数据")
    public ReportCardMigrateResultDTO migrateTemplate(
            @RequestParam(required = false) Integer businessId,
            @RequestParam(defaultValue = "false") Boolean forceReplace) {

        log.info("开始迁移模板数据，businessId: {}, forceReplace: {}", businessId, forceReplace);
        return reportCardMigrateService.migrateTemplate(businessId, forceReplace);
    }

    /**
     * 迁移报告数据
     * moe_grooming_report + daily_report_config → fulfillment_report
     */
    @PostMapping("/report")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移报告数据")
    public ReportCardMigrateResultDTO migrateReport(
            @RequestParam(required = false) Integer businessId,
            @RequestParam(defaultValue = "false") Boolean forceReplace) {

        log.info("开始迁移报告数据，businessId: {}, forceReplace: {}", businessId, forceReplace);
        return reportCardMigrateService.migrateReport(businessId, forceReplace);
    }

    /**
     * 迁移问题数据
     * moe_grooming_report_question → fulfillment_report_question
     */
    @PostMapping("/question")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移问题数据")
    public ReportCardMigrateResultDTO migrateQuestion(
            @RequestParam(required = false) Integer businessId,
            @RequestParam(defaultValue = "false") Boolean forceReplace) {

        log.info("开始迁移问题数据，businessId: {}, forceReplace: {}", businessId, forceReplace);
        return reportCardMigrateService.migrateQuestion(businessId, forceReplace);
    }

    /**
     * 迁移发送记录数据
     * moe_grooming_report_send_log + daily_report_send_log → fulfillment_report_send_record
     */
    @PostMapping("/send-record")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移发送记录数据")
    public ReportCardMigrateResultDTO migrateSendRecord(
            @RequestParam(required = false) Integer businessId,
            @RequestParam(defaultValue = "false") Boolean forceReplace) {

        log.info("开始迁移发送记录数据，businessId: {}, forceReplace: {}", businessId, forceReplace);
        return reportCardMigrateService.migrateSendRecord(businessId, forceReplace);
    }

    /**
     * 获取迁移进度
     */
    @GetMapping("/progress")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "获取迁移进度")
    public ReportCardMigrateProgressDTO getProgress(
            @Parameter(description = "任务ID")
            @RequestParam String taskId) {

        return reportCardMigrateService.getProgress(taskId);
    }

    /**
     * 停止正在进行的迁移任务
     */
    @PostMapping("/stop")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "停止迁移任务")
    public void stopMigration(
            @Parameter(description = "任务ID")
            @RequestParam String taskId) {

        log.info("停止迁移任务，taskId: {}", taskId);
        reportCardMigrateService.stopMigration(taskId);
    }

    /**
     * 数据验证接口
     * 验证迁移后的数据完整性和正确性
     */
    @PostMapping("/validate")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "验证迁移数据")
    public ReportCardMigrateResultDTO validateMigration(
            @RequestParam(required = false) Integer businessId) {

        log.info("开始验证迁移数据，businessId: {}", businessId);
        return reportCardMigrateService.validateMigration(businessId);
    }

    /**
     * 回滚迁移数据
     * 删除已迁移的数据，用于测试或出现问题时的数据清理
     */
    @PostMapping("/rollback")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "回滚迁移数据")
    public ReportCardMigrateResultDTO rollbackMigration(
            @RequestParam(required = false) Integer businessId,
            @Parameter(description = "要回滚的表名，为空则回滚所有表")
            @RequestParam(required = false) String tableName) {

        log.info("开始回滚迁移数据，businessId: {}, tableName: {}", businessId, tableName);
        return reportCardMigrateService.rollbackMigration(businessId, tableName);
    }
}
