package com.moego.server.grooming.service.report.migrate;

import com.moego.idl.service.appointment.v1.DailyReportServiceGrpc;
import com.moego.server.grooming.mapper.MoeGroomingReportSendLogMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingReportSendLog;
import com.moego.server.grooming.mapperbean.MoeGroomingReportSendLogExample;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Report Card 发送记录迁移服务
 * 负责将 moe_grooming_report_send_log 和 daily_report_send_log 数据迁移到 fulfillment_report_send_record
 *
 * 核心原则：
 * 1. 零模式修改：不修改新表结构
 * 2. 引用完整性：保持数据间关联关系
 * 3. 注意表与表之间的id关联，需要直接使用新表的id，在迁移过程中进行新旧表id的替换
 * 4. 数据量较大的表迁移需考虑性能，例如分片处理
 * 5. 可追溯性：完整记录迁移过程
 * 6. 可回滚性：确保问题发生时可快速恢复
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardSendRecordMigrateService {

    private final MoeGroomingReportSendLogMapper groomingSendLogMapper;
    private final DailyReportServiceGrpc.DailyReportServiceBlockingStub dailyReportService;
    // TODO: 注入fulfillment服务客户端
    // private final FulfillmentServiceClient fulfillmentServiceClient;

    /**
     * 迁移发送记录数据
     */
    @Transactional
    public ReportCardMigrateResultDTO migrate(Integer businessId, Boolean forceReplace) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "send_record_" + System.currentTimeMillis();

        log.info("开始迁移发送记录数据，taskId: {}, businessId: {}, forceReplace: {}",
                taskId, businessId, forceReplace);

        try {
            long totalMigrated = 0;
            long totalSkipped = 0;
            long totalFailed = 0;
            long totalSource = 0;

            // 1. 迁移 grooming report send log 数据
            log.info("开始迁移 grooming report send log 数据");
            MigrateResult groomingResult = migrateGroomingSendLogs(businessId, forceReplace);
            totalMigrated += groomingResult.migratedCount;
            totalSkipped += groomingResult.skippedCount;
            totalFailed += groomingResult.failedCount;
            totalSource += groomingResult.sourceCount;

            // 2. 迁移 daily report send log 数据
            log.info("开始迁移 daily report send log 数据");
            MigrateResult dailyResult = migrateDailySendLogs(businessId, forceReplace);
            totalMigrated += dailyResult.migratedCount;
            totalSkipped += dailyResult.skippedCount;
            totalFailed += dailyResult.failedCount;
            totalSource += dailyResult.sourceCount;

            log.info("发送记录数据迁移完成，总源数据: {}, 成功: {}, 跳过: {}, 失败: {}",
                    totalSource, totalMigrated, totalSkipped, totalFailed);

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId, startTime,
                    ReportCardMigrateConfig.TableNames.SEND_RECORD,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_SEND_LOG + "+" + ReportCardMigrateConfig.SourceTables.DAILY_REPORT_SEND_LOG,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_SEND_RECORD,
                    totalSource, totalMigrated, totalSkipped
            );

        } catch (Exception e) {
            log.error("发送记录数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }

    /**
     * 迁移 grooming report send log 数据
     */
    private MigrateResult migrateGroomingSendLogs(Integer businessId, Boolean forceReplace) {
        try {
            // 1. 查询源数据
            MoeGroomingReportSendLogExample example = new MoeGroomingReportSendLogExample();
            MoeGroomingReportSendLogExample.Criteria criteria = example.createCriteria();

            if (businessId != null) {
                criteria.andBusinessIdEqualTo(businessId);
            }

            List<MoeGroomingReportSendLog> sourceSendLogs = groomingSendLogMapper.selectByExample(example);
            log.info("查询到 grooming send log 源数据 {} 条", sourceSendLogs.size());

            if (sourceSendLogs.isEmpty()) {
                return new MigrateResult(0, 0, 0, 0);
            }

            // 2. 分批处理数据
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;

            for (MoeGroomingReportSendLog sourceSendLog : sourceSendLogs) {
                try {
                    // TODO: 检查目标表中是否已存在
                    // boolean exists = fulfillmentServiceClient.checkSendRecordExists(
                    //     sourceSendLog.getBusinessId(), sourceSendLog.getReportId(), sourceSendLog.getSendTime());

                    boolean shouldSkip = false;
                    if (!forceReplace) {
                        // 暂时假设不存在，实际需要调用fulfillment服务检查
                        shouldSkip = false;
                    }

                    if (shouldSkip) {
                        log.debug("发送记录已存在，跳过迁移: businessId={}, reportId={}",
                                sourceSendLog.getBusinessId(), sourceSendLog.getReportId());
                        skippedCount++;
                        continue;
                    }

                    // TODO: 转换数据并通过fulfillment服务接口进行插入或更新
                    // 需要先获取新的report_id
                    // Integer newReportId = ReportCardMigrateUtils.getNewId(
                    //     ReportCardMigrateConfig.TableNames.REPORT, sourceSendLog.getReportId());

                    // FulfillmentReportSendRecordDTO targetSendRecord = convertGroomingSendLog(sourceSendLog, newReportId);
                    // fulfillmentServiceClient.saveOrUpdateSendRecord(targetSendRecord, forceReplace);

                    log.debug("迁移发送记录: businessId={}, reportId={}, sourceType=grooming",
                            sourceSendLog.getBusinessId(), sourceSendLog.getReportId());
                    migratedCount++;

                } catch (Exception e) {
                    log.error("迁移发送记录失败: businessId={}, reportId={}",
                            sourceSendLog.getBusinessId(), sourceSendLog.getReportId(), e);
                    failedCount++;
                }
            }

            return new MigrateResult(sourceSendLogs.size(), migratedCount, skippedCount, failedCount);

        } catch (Exception e) {
            log.error("迁移 grooming send log 数据失败", e);
            throw e;
        }
    }

    /**
     * 迁移 daily report send log 数据
     */
    private MigrateResult migrateDailySendLogs(Integer businessId, Boolean forceReplace) {
        try {
            // TODO: 通过 dailyReportService 查询 daily_report_send_log 数据
            // 这里需要根据实际的 gRPC 接口定义来实现
            /*
            DailyReportSendLogRequest.Builder requestBuilder = DailyReportSendLogRequest.newBuilder();
            if (businessId != null) {
                requestBuilder.setBusinessId(businessId);
            }

            DailyReportSendLogResponse response = dailyReportService.getDailyReportSendLogs(requestBuilder.build());
            List<DailyReportSendLog> dailySendLogs = response.getSendLogsList();
            */

            // 暂时返回空结果，等待实际 gRPC 接口实现
            log.info("daily report send log 数据迁移暂未实现，等待 gRPC 接口定义");
            return new MigrateResult(0, 0, 0, 0);

        } catch (Exception e) {
            log.error("迁移 daily send log 数据失败", e);
            throw e;
        }
    }

    /**
     * 迁移结果内部类
     */
    private static class MigrateResult {
        final long sourceCount;
        final long migratedCount;
        final long skippedCount;
        final long failedCount;

        MigrateResult(long sourceCount, long migratedCount, long skippedCount, long failedCount) {
            this.sourceCount = sourceCount;
            this.migratedCount = migratedCount;
            this.skippedCount = skippedCount;
            this.failedCount = failedCount;
        }
    }
}
