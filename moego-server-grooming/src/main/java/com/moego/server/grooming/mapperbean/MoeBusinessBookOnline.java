package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.dto.BookOnlineDTO.PaymentOption;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_business_book_online
 */
public class MoeBusinessBookOnline {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   0-not  1-yes
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_enable
     *
     * @mbg.generated
     */
    private Byte isEnable;

    /**
     * Database Column Remarks:
     *   service distance range
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.max_available_dist
     *
     * @mbg.generated
     */
    private Integer maxAvailableDist;

    /**
     * Database Column Remarks:
     *   service time range
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.max_available_time
     *
     * @mbg.generated
     */
    private Integer maxAvailableTime;

    /**
     * Database Column Remarks:
     *   service 从最近几天开始 0-Same day,  1-next day,  2-2 days out,  3-3 days out
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.soonest_available
     *
     * @mbg.generated
     */
    private Integer soonestAvailable;

    /**
     * Database Column Remarks:
     *   service从最远可用 service最远可达   1-1months   2-2months 3-3months  6-6months
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.farest_available
     *
     * @mbg.generated
     */
    private Integer farestAvailable;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   0-not  1-yes
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_require_agreement
     *
     * @mbg.generated
     */
    private Byte isRequireAgreement;

    /**
     * Database Column Remarks:
     *   zip_code  商家邮编
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.zip_code
     *
     * @mbg.generated
     */
    private String zipCode;

    /**
     * Database Column Remarks:
     *   place_name;
     *   ;
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.place_name
     *
     * @mbg.generated
     */
    private String placeName;

    /**
     * Database Column Remarks:
     *   state
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.state
     *
     * @mbg.generated
     */
    private String state;

    /**
     * Database Column Remarks:
     *   state_abbreviation
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.state_abbreviation
     *
     * @mbg.generated
     */
    private String stateAbbreviation;

    /**
     * Database Column Remarks:
     *   county
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.county
     *
     * @mbg.generated
     */
    private String county;

    /**
     * Database Column Remarks:
     *   address required for OB: 0-not  1-yes
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_need_address
     *
     * @mbg.generated
     */
    private Byte isNeedAddress;

    /**
     * Database Column Remarks:
     *   need select time for new client 0-not  1-yes
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_need_select_time
     *
     * @mbg.generated
     */
    private Byte isNeedSelectTime;

    /**
     * Database Column Remarks:
     *   0-not  1- 25% 2- 40% 3- 50%
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.fake_it
     *
     * @mbg.generated
     */
    private Byte fakeIt;

    /**
     * Database Column Remarks:
     *   no show protection, 0-closed, 1-required credit card, 2-required prepay, full pay/deposit, depend on prepay_type
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.enable_no_show_fee
     *
     * @mbg.generated
     */
    private Byte enableNoShowFee;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.no_show_fee
     *
     * @mbg.generated
     */
    private BigDecimal noShowFee;

    /**
     * Database Column Remarks:
     *   已弃用，使用timeslot_mins替代。预约间隔时间 1-10mins  2-20mins 3-30mins 4-40mins 5-50mins 6-60mins
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.appointment_interval
     *
     * @mbg.generated
     */
    private Integer appointmentInterval;

    /**
     * Database Column Remarks:
     *   预约间隔时间分钟数，与appointment_interval相同，但用分钟数表示
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.timeslot_mins
     *
     * @mbg.generated
     */
    private Integer timeslotMins;

    /**
     * Database Column Remarks:
     *   时间间隔类型 1 Exact times，2 Arrival windows，3 Date only
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.timeslot_format
     *
     * @mbg.generated
     */
    private Byte timeslotFormat;

    /**
     * Database Column Remarks:
     *   允许的顾客类型  1只允许新顾客  2只允许旧顾客  3两者都允许
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.accept_client
     *
     * @mbg.generated
     */
    private Byte acceptClient;

    /**
     * Database Column Remarks:
     *   book online 的订单，48小时后自动移入waiting list
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.auto_move_wait
     *
     * @mbg.generated
     */
    private Byte autoMoveWait;

    /**
     * Database Column Remarks:
     *   服务区域是否开启 默认关闭
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.service_area_enable
     *
     * @mbg.generated
     */
    private Byte serviceAreaEnable;

    /**
     * Database Column Remarks:
     *   是否打开weight limit提示 是1 否0
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.weight_limit_notify
     *
     * @mbg.generated
     */
    private Byte weightLimitNotify;

    /**
     * Database Column Remarks:
     *   体重限制
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.weight_limit
     *
     * @mbg.generated
     */
    private Integer weightLimit;

    /**
     * Database Column Remarks:
     *   over_limit_tips
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.over_limit_tips
     *
     * @mbg.generated
     */
    private String overLimitTips;

    /**
     * Database Column Remarks:
     *   需要在服务地区内  1开启  2关闭
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.need_within_area
     *
     * @mbg.generated
     */
    private Byte needWithinArea;

    /**
     * Database Column Remarks:
     *   是否检测zipcode
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_by_zipcode
     *
     * @mbg.generated
     */
    private Byte isByZipcode;

    /**
     * Database Column Remarks:
     *   是否检测radius
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_by_radius
     *
     * @mbg.generated
     */
    private Byte isByRadius;

    /**
     * Database Column Remarks:
     *   mobile grooming 内的location
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.setting_location
     *
     * @mbg.generated
     */
    private String settingLocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.setting_lat
     *
     * @mbg.generated
     */
    private String settingLat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.setting_lng
     *
     * @mbg.generated
     */
    private String settingLng;

    /**
     * Database Column Remarks:
     *   是否检测旧client的地址
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_check_existing_client
     *
     * @mbg.generated
     */
    private Byte isCheckExistingClient;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_redirect
     *
     * @mbg.generated
     */
    private Byte isRedirect;

    /**
     * Database Column Remarks:
     *   是否自动同意request  1开  0关
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.auto_accept
     *
     * @mbg.generated
     */
    private Byte autoAccept;

    /**
     * Database Column Remarks:
     *   只显示 一个可用时间 1打开 0关闭
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.show_one_available_time
     *
     * @mbg.generated
     */
    private Byte showOneAvailableTime;

    /**
     * Database Column Remarks:
     *   0-not  1-yes
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.smart_schedule_enable
     *
     * @mbg.generated
     */
    private Byte smartScheduleEnable;

    /**
     * Database Column Remarks:
     *   smart schedule distance range
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.smart_schedule_max_dist
     *
     * @mbg.generated
     */
    private Integer smartScheduleMaxDist;

    /**
     * Database Column Remarks:
     *   smart schedule time range
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.smart_schedule_max_time
     *
     * @mbg.generated
     */
    private Integer smartScheduleMaxTime;

    /**
     * Database Column Remarks:
     *   逗号分隔的zipcode，用于mobile grooming
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.zip_codes
     *
     * @mbg.generated
     */
    private String zipCodes;

    /**
     * Database Column Remarks:
     *   service area list，关联 moe_geoarea
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.service_areas
     *
     * @mbg.generated
     */
    private List<Integer> serviceAreas;

    /**
     * Database Column Remarks:
     *   business book online name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.book_online_name
     *
     * @mbg.generated
     */
    private String bookOnlineName;

    /**
     * Database Column Remarks:
     *   允许不选中time提交
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.allowed_simplify_submit
     *
     * @mbg.generated
     */
    private Byte allowedSimplifySubmit;

    /**
     * Database Column Remarks:
     *   OB available time类型，0-working hour，1-by slot, 2-disable select time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.available_time_type
     *
     * @mbg.generated
     */
    private Byte availableTimeType;

    /**
     * Database Column Remarks:
     *   by slot 时间间隔类型 1 Exact times 2 Arrival windows
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.by_slot_timeslot_format
     *
     * @mbg.generated
     */
    private Byte bySlotTimeslotFormat;

    /**
     * Database Column Remarks:
     *   by slot 时间间隔分钟数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.by_slot_timeslot_mins
     *
     * @mbg.generated
     */
    private Integer bySlotTimeslotMins;

    /**
     * Database Column Remarks:
     *   by slot service 从最近几天开始 0-Same day,  1-next day,  2-2 days out,  3-3 days out
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.by_slot_soonest_available
     *
     * @mbg.generated
     */
    private Integer bySlotSoonestAvailable;

    /**
     * Database Column Remarks:
     *   by slot service最远可达天数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.by_slot_farthest_available
     *
     * @mbg.generated
     */
    private Integer bySlotFarthestAvailable;

    /**
     * Database Column Remarks:
     *   only show applicable service, 0-closed, 1-open
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.service_filter
     *
     * @mbg.generated
     */
    private Byte serviceFilter;

    /**
     * Database Column Remarks:
     *   是否显示categories(1=是，0=否)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_show_categories
     *
     * @mbg.generated
     */
    private Boolean isShowCategories;

    /**
     * Database Column Remarks:
     *   prepay type, 0-full pay, 1-deposit
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.prepay_type
     *
     * @mbg.generated
     */
    private Byte prepayType;

    /**
     * Database Column Remarks:
     *   prepay add-tips enable, 0-disable, 1-enable
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.prepay_tip_enable
     *
     * @mbg.generated
     */
    private Byte prepayTipEnable;

    /**
     * Database Column Remarks:
     *   prepay deposit type, 0-by fixed amount, 1-by percentage
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.deposit_type
     *
     * @mbg.generated
     */
    private Byte depositType;

    /**
     * Database Column Remarks:
     *   deposit percentage, default 30, range: 1-100
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.deposit_percentage
     *
     * @mbg.generated
     */
    private Integer depositPercentage;

    /**
     * Database Column Remarks:
     *   deposit amount, default 20, min: 1, max:1000
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.deposit_amount
     *
     * @mbg.generated
     */
    private BigDecimal depositAmount;

    /**
     * Database Column Remarks:
     *   1-default
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.use_version
     *
     * @mbg.generated
     */
    private Byte useVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.pre_auth_tip_enable
     *
     * @mbg.generated
     */
    private Byte preAuthTipEnable;

    /**
     * Database Column Remarks:
     *   whether refund deposit when auto move to wait list: 0-keep deposit, 1-auto refund
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.auto_refund_deposit
     *
     * @mbg.generated
     */
    private Byte autoRefundDeposit;

    /**
     * Database Column Remarks:
     *   Automatic configuration when a booking request is submitted, auto_accept_all, auto_accept_request, auto_move_waitlist, no_automation
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.request_submitted_auto_type
     *
     * @mbg.generated
     */
    private String requestSubmittedAutoType;

    /**
     * Database Column Remarks:
     *   whether to display staff selection page, default true
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.display_staff_selection_page
     *
     * @mbg.generated
     */
    private Boolean displayStaffSelectionPage;

    /**
     * Database Column Remarks:
     *   当 timeslot_format 配置为 2 时，使用这个配置
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.arrival_window_before_min
     *
     * @mbg.generated
     */
    private Integer arrivalWindowBeforeMin;

    /**
     * Database Column Remarks:
     *   当 timeslot_format 配置为 2 时，使用的配置
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.arrival_window_after_min
     *
     * @mbg.generated
     */
    private Integer arrivalWindowAfterMin;

    /**
     * Database Column Remarks:
     *   从当前日期的偏移日期，默认值为 1，表示 next day
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.booking_range_start_offset
     *
     * @mbg.generated
     */
    private Integer bookingRangeStartOffset;

    /**
     * Database Column Remarks:
     *    1：使用 booking_range_end_offset，2：使用 booking_range_end_date
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.booking_range_end_type
     *
     * @mbg.generated
     */
    private Byte bookingRangeEndType;

    /**
     * Database Column Remarks:
     *   从当前日期的偏移日期，默认值为 180
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.booking_range_end_offset
     *
     * @mbg.generated
     */
    private Integer bookingRangeEndOffset;

    /**
     * Database Column Remarks:
     *   Custom date range 时的结束日期，例如：2023-01-01
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.booking_range_end_date
     *
     * @mbg.generated
     */
    private String bookingRangeEndDate;

    /**
     * Database Column Remarks:
     *   是否需要发送 renew date 通知，只有当 booking_range_end_type 使用 booking_range_end_date 时才会用到该配置
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.is_need_send_renew_notification
     *
     * @mbg.generated
     */
    private Boolean isNeedSendRenewNotification;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_payment_type
     *
     * @mbg.generated
     */
    private Byte groupPaymentType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_filter_rule
     *
     * @mbg.generated
     */
    private String groupFilterRule;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_prepay_type
     *
     * @mbg.generated
     */
    private Byte groupPrepayType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_prepay_tip_enable
     *
     * @mbg.generated
     */
    private Byte groupPrepayTipEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_deposit_type
     *
     * @mbg.generated
     */
    private Byte groupDepositType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_deposit_percentage
     *
     * @mbg.generated
     */
    private Integer groupDepositPercentage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_deposit_amount
     *
     * @mbg.generated
     */
    private BigDecimal groupDepositAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_pre_auth_tip_enable
     *
     * @mbg.generated
     */
    private Byte groupPreAuthTipEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_accept_client
     *
     * @mbg.generated
     */
    private Byte groupAcceptClient;

    /**
     * Database Column Remarks:
     *   0: normal,1: furryland 特殊登陆flow
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.new_client_flow_type
     *
     * @mbg.generated
     */
    private Integer newClientFlowType;

    /**
     * Database Column Remarks:
     *   sync available time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.available_time_sync
     *
     * @mbg.generated
     */
    private Byte availableTimeSync;

    /**
     * Database Column Remarks:
     *   service item type 定制的 prepay amount 配置，数据结构参考：com.moego.server.grooming.dto.BookOnlineDTO#paymentOptionMap。这个字段是个商家定制字段，不要轻易使用！
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.payment_option_map
     *
     * @mbg.generated
     */
    private Map<Integer, PaymentOption> paymentOptionMap;

    /**
     * Database Column Remarks:
     *   only show the next available slot, false-disable, true-enable
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.by_slot_show_one_available_time
     *
     * @mbg.generated
     */
    private Boolean bySlotShowOneAvailableTime;

    /**
     * Database Column Remarks:
     *   Cancellation policy (REQUIRED)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.cancellation_policy
     *
     * @mbg.generated
     */
    private String cancellationPolicy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     * Database Column Remarks:
     *   Prepay policy
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.prepay_policy
     *
     * @mbg.generated
     */
    private String prepayPolicy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.pre_auth_policy
     *
     * @mbg.generated
     */
    private String preAuthPolicy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_pre_auth_policy
     *
     * @mbg.generated
     */
    private String groupPreAuthPolicy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_cancellation_policy
     *
     * @mbg.generated
     */
    private String groupCancellationPolicy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_book_online.group_prepay_policy
     *
     * @mbg.generated
     */
    private String groupPrepayPolicy;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.id
     *
     * @return the value of moe_business_book_online.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.id
     *
     * @param id the value for moe_business_book_online.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.business_id
     *
     * @return the value of moe_business_book_online.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.business_id
     *
     * @param businessId the value for moe_business_book_online.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_enable
     *
     * @return the value of moe_business_book_online.is_enable
     *
     * @mbg.generated
     */
    public Byte getIsEnable() {
        return isEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_enable
     *
     * @param isEnable the value for moe_business_book_online.is_enable
     *
     * @mbg.generated
     */
    public void setIsEnable(Byte isEnable) {
        this.isEnable = isEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.max_available_dist
     *
     * @return the value of moe_business_book_online.max_available_dist
     *
     * @mbg.generated
     */
    public Integer getMaxAvailableDist() {
        return maxAvailableDist;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.max_available_dist
     *
     * @param maxAvailableDist the value for moe_business_book_online.max_available_dist
     *
     * @mbg.generated
     */
    public void setMaxAvailableDist(Integer maxAvailableDist) {
        this.maxAvailableDist = maxAvailableDist;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.max_available_time
     *
     * @return the value of moe_business_book_online.max_available_time
     *
     * @mbg.generated
     */
    public Integer getMaxAvailableTime() {
        return maxAvailableTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.max_available_time
     *
     * @param maxAvailableTime the value for moe_business_book_online.max_available_time
     *
     * @mbg.generated
     */
    public void setMaxAvailableTime(Integer maxAvailableTime) {
        this.maxAvailableTime = maxAvailableTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.soonest_available
     *
     * @return the value of moe_business_book_online.soonest_available
     *
     * @mbg.generated
     */
    public Integer getSoonestAvailable() {
        return soonestAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.soonest_available
     *
     * @param soonestAvailable the value for moe_business_book_online.soonest_available
     *
     * @mbg.generated
     */
    public void setSoonestAvailable(Integer soonestAvailable) {
        this.soonestAvailable = soonestAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.farest_available
     *
     * @return the value of moe_business_book_online.farest_available
     *
     * @mbg.generated
     */
    public Integer getFarestAvailable() {
        return farestAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.farest_available
     *
     * @param farestAvailable the value for moe_business_book_online.farest_available
     *
     * @mbg.generated
     */
    public void setFarestAvailable(Integer farestAvailable) {
        this.farestAvailable = farestAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.create_time
     *
     * @return the value of moe_business_book_online.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.create_time
     *
     * @param createTime the value for moe_business_book_online.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.update_time
     *
     * @return the value of moe_business_book_online.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.update_time
     *
     * @param updateTime the value for moe_business_book_online.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_require_agreement
     *
     * @return the value of moe_business_book_online.is_require_agreement
     *
     * @mbg.generated
     */
    public Byte getIsRequireAgreement() {
        return isRequireAgreement;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_require_agreement
     *
     * @param isRequireAgreement the value for moe_business_book_online.is_require_agreement
     *
     * @mbg.generated
     */
    public void setIsRequireAgreement(Byte isRequireAgreement) {
        this.isRequireAgreement = isRequireAgreement;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.zip_code
     *
     * @return the value of moe_business_book_online.zip_code
     *
     * @mbg.generated
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.zip_code
     *
     * @param zipCode the value for moe_business_book_online.zip_code
     *
     * @mbg.generated
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode == null ? null : zipCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.place_name
     *
     * @return the value of moe_business_book_online.place_name
     *
     * @mbg.generated
     */
    public String getPlaceName() {
        return placeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.place_name
     *
     * @param placeName the value for moe_business_book_online.place_name
     *
     * @mbg.generated
     */
    public void setPlaceName(String placeName) {
        this.placeName = placeName == null ? null : placeName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.state
     *
     * @return the value of moe_business_book_online.state
     *
     * @mbg.generated
     */
    public String getState() {
        return state;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.state
     *
     * @param state the value for moe_business_book_online.state
     *
     * @mbg.generated
     */
    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.state_abbreviation
     *
     * @return the value of moe_business_book_online.state_abbreviation
     *
     * @mbg.generated
     */
    public String getStateAbbreviation() {
        return stateAbbreviation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.state_abbreviation
     *
     * @param stateAbbreviation the value for moe_business_book_online.state_abbreviation
     *
     * @mbg.generated
     */
    public void setStateAbbreviation(String stateAbbreviation) {
        this.stateAbbreviation = stateAbbreviation == null ? null : stateAbbreviation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.county
     *
     * @return the value of moe_business_book_online.county
     *
     * @mbg.generated
     */
    public String getCounty() {
        return county;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.county
     *
     * @param county the value for moe_business_book_online.county
     *
     * @mbg.generated
     */
    public void setCounty(String county) {
        this.county = county == null ? null : county.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_need_address
     *
     * @return the value of moe_business_book_online.is_need_address
     *
     * @mbg.generated
     */
    public Byte getIsNeedAddress() {
        return isNeedAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_need_address
     *
     * @param isNeedAddress the value for moe_business_book_online.is_need_address
     *
     * @mbg.generated
     */
    public void setIsNeedAddress(Byte isNeedAddress) {
        this.isNeedAddress = isNeedAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_need_select_time
     *
     * @return the value of moe_business_book_online.is_need_select_time
     *
     * @mbg.generated
     */
    public Byte getIsNeedSelectTime() {
        return isNeedSelectTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_need_select_time
     *
     * @param isNeedSelectTime the value for moe_business_book_online.is_need_select_time
     *
     * @mbg.generated
     */
    public void setIsNeedSelectTime(Byte isNeedSelectTime) {
        this.isNeedSelectTime = isNeedSelectTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.fake_it
     *
     * @return the value of moe_business_book_online.fake_it
     *
     * @mbg.generated
     */
    public Byte getFakeIt() {
        return fakeIt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.fake_it
     *
     * @param fakeIt the value for moe_business_book_online.fake_it
     *
     * @mbg.generated
     */
    public void setFakeIt(Byte fakeIt) {
        this.fakeIt = fakeIt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.enable_no_show_fee
     *
     * @return the value of moe_business_book_online.enable_no_show_fee
     *
     * @mbg.generated
     */
    public Byte getEnableNoShowFee() {
        return enableNoShowFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.enable_no_show_fee
     *
     * @param enableNoShowFee the value for moe_business_book_online.enable_no_show_fee
     *
     * @mbg.generated
     */
    public void setEnableNoShowFee(Byte enableNoShowFee) {
        this.enableNoShowFee = enableNoShowFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.no_show_fee
     *
     * @return the value of moe_business_book_online.no_show_fee
     *
     * @mbg.generated
     */
    public BigDecimal getNoShowFee() {
        return noShowFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.no_show_fee
     *
     * @param noShowFee the value for moe_business_book_online.no_show_fee
     *
     * @mbg.generated
     */
    public void setNoShowFee(BigDecimal noShowFee) {
        this.noShowFee = noShowFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.appointment_interval
     *
     * @return the value of moe_business_book_online.appointment_interval
     *
     * @mbg.generated
     */
    public Integer getAppointmentInterval() {
        return appointmentInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.appointment_interval
     *
     * @param appointmentInterval the value for moe_business_book_online.appointment_interval
     *
     * @mbg.generated
     */
    public void setAppointmentInterval(Integer appointmentInterval) {
        this.appointmentInterval = appointmentInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.timeslot_mins
     *
     * @return the value of moe_business_book_online.timeslot_mins
     *
     * @mbg.generated
     */
    public Integer getTimeslotMins() {
        return timeslotMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.timeslot_mins
     *
     * @param timeslotMins the value for moe_business_book_online.timeslot_mins
     *
     * @mbg.generated
     */
    public void setTimeslotMins(Integer timeslotMins) {
        this.timeslotMins = timeslotMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.timeslot_format
     *
     * @return the value of moe_business_book_online.timeslot_format
     *
     * @mbg.generated
     */
    public Byte getTimeslotFormat() {
        return timeslotFormat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.timeslot_format
     *
     * @param timeslotFormat the value for moe_business_book_online.timeslot_format
     *
     * @mbg.generated
     */
    public void setTimeslotFormat(Byte timeslotFormat) {
        this.timeslotFormat = timeslotFormat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.accept_client
     *
     * @return the value of moe_business_book_online.accept_client
     *
     * @mbg.generated
     */
    public Byte getAcceptClient() {
        return acceptClient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.accept_client
     *
     * @param acceptClient the value for moe_business_book_online.accept_client
     *
     * @mbg.generated
     */
    public void setAcceptClient(Byte acceptClient) {
        this.acceptClient = acceptClient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.auto_move_wait
     *
     * @return the value of moe_business_book_online.auto_move_wait
     *
     * @mbg.generated
     */
    public Byte getAutoMoveWait() {
        return autoMoveWait;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.auto_move_wait
     *
     * @param autoMoveWait the value for moe_business_book_online.auto_move_wait
     *
     * @mbg.generated
     */
    public void setAutoMoveWait(Byte autoMoveWait) {
        this.autoMoveWait = autoMoveWait;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.service_area_enable
     *
     * @return the value of moe_business_book_online.service_area_enable
     *
     * @mbg.generated
     */
    public Byte getServiceAreaEnable() {
        return serviceAreaEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.service_area_enable
     *
     * @param serviceAreaEnable the value for moe_business_book_online.service_area_enable
     *
     * @mbg.generated
     */
    public void setServiceAreaEnable(Byte serviceAreaEnable) {
        this.serviceAreaEnable = serviceAreaEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.weight_limit_notify
     *
     * @return the value of moe_business_book_online.weight_limit_notify
     *
     * @mbg.generated
     */
    public Byte getWeightLimitNotify() {
        return weightLimitNotify;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.weight_limit_notify
     *
     * @param weightLimitNotify the value for moe_business_book_online.weight_limit_notify
     *
     * @mbg.generated
     */
    public void setWeightLimitNotify(Byte weightLimitNotify) {
        this.weightLimitNotify = weightLimitNotify;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.weight_limit
     *
     * @return the value of moe_business_book_online.weight_limit
     *
     * @mbg.generated
     */
    public Integer getWeightLimit() {
        return weightLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.weight_limit
     *
     * @param weightLimit the value for moe_business_book_online.weight_limit
     *
     * @mbg.generated
     */
    public void setWeightLimit(Integer weightLimit) {
        this.weightLimit = weightLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.over_limit_tips
     *
     * @return the value of moe_business_book_online.over_limit_tips
     *
     * @mbg.generated
     */
    public String getOverLimitTips() {
        return overLimitTips;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.over_limit_tips
     *
     * @param overLimitTips the value for moe_business_book_online.over_limit_tips
     *
     * @mbg.generated
     */
    public void setOverLimitTips(String overLimitTips) {
        this.overLimitTips = overLimitTips == null ? null : overLimitTips.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.need_within_area
     *
     * @return the value of moe_business_book_online.need_within_area
     *
     * @mbg.generated
     */
    public Byte getNeedWithinArea() {
        return needWithinArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.need_within_area
     *
     * @param needWithinArea the value for moe_business_book_online.need_within_area
     *
     * @mbg.generated
     */
    public void setNeedWithinArea(Byte needWithinArea) {
        this.needWithinArea = needWithinArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_by_zipcode
     *
     * @return the value of moe_business_book_online.is_by_zipcode
     *
     * @mbg.generated
     */
    public Byte getIsByZipcode() {
        return isByZipcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_by_zipcode
     *
     * @param isByZipcode the value for moe_business_book_online.is_by_zipcode
     *
     * @mbg.generated
     */
    public void setIsByZipcode(Byte isByZipcode) {
        this.isByZipcode = isByZipcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_by_radius
     *
     * @return the value of moe_business_book_online.is_by_radius
     *
     * @mbg.generated
     */
    public Byte getIsByRadius() {
        return isByRadius;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_by_radius
     *
     * @param isByRadius the value for moe_business_book_online.is_by_radius
     *
     * @mbg.generated
     */
    public void setIsByRadius(Byte isByRadius) {
        this.isByRadius = isByRadius;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.setting_location
     *
     * @return the value of moe_business_book_online.setting_location
     *
     * @mbg.generated
     */
    public String getSettingLocation() {
        return settingLocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.setting_location
     *
     * @param settingLocation the value for moe_business_book_online.setting_location
     *
     * @mbg.generated
     */
    public void setSettingLocation(String settingLocation) {
        this.settingLocation = settingLocation == null ? null : settingLocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.setting_lat
     *
     * @return the value of moe_business_book_online.setting_lat
     *
     * @mbg.generated
     */
    public String getSettingLat() {
        return settingLat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.setting_lat
     *
     * @param settingLat the value for moe_business_book_online.setting_lat
     *
     * @mbg.generated
     */
    public void setSettingLat(String settingLat) {
        this.settingLat = settingLat == null ? null : settingLat.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.setting_lng
     *
     * @return the value of moe_business_book_online.setting_lng
     *
     * @mbg.generated
     */
    public String getSettingLng() {
        return settingLng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.setting_lng
     *
     * @param settingLng the value for moe_business_book_online.setting_lng
     *
     * @mbg.generated
     */
    public void setSettingLng(String settingLng) {
        this.settingLng = settingLng == null ? null : settingLng.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_check_existing_client
     *
     * @return the value of moe_business_book_online.is_check_existing_client
     *
     * @mbg.generated
     */
    public Byte getIsCheckExistingClient() {
        return isCheckExistingClient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_check_existing_client
     *
     * @param isCheckExistingClient the value for moe_business_book_online.is_check_existing_client
     *
     * @mbg.generated
     */
    public void setIsCheckExistingClient(Byte isCheckExistingClient) {
        this.isCheckExistingClient = isCheckExistingClient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_redirect
     *
     * @return the value of moe_business_book_online.is_redirect
     *
     * @mbg.generated
     */
    public Byte getIsRedirect() {
        return isRedirect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_redirect
     *
     * @param isRedirect the value for moe_business_book_online.is_redirect
     *
     * @mbg.generated
     */
    public void setIsRedirect(Byte isRedirect) {
        this.isRedirect = isRedirect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.auto_accept
     *
     * @return the value of moe_business_book_online.auto_accept
     *
     * @mbg.generated
     */
    public Byte getAutoAccept() {
        return autoAccept;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.auto_accept
     *
     * @param autoAccept the value for moe_business_book_online.auto_accept
     *
     * @mbg.generated
     */
    public void setAutoAccept(Byte autoAccept) {
        this.autoAccept = autoAccept;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.show_one_available_time
     *
     * @return the value of moe_business_book_online.show_one_available_time
     *
     * @mbg.generated
     */
    public Byte getShowOneAvailableTime() {
        return showOneAvailableTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.show_one_available_time
     *
     * @param showOneAvailableTime the value for moe_business_book_online.show_one_available_time
     *
     * @mbg.generated
     */
    public void setShowOneAvailableTime(Byte showOneAvailableTime) {
        this.showOneAvailableTime = showOneAvailableTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.smart_schedule_enable
     *
     * @return the value of moe_business_book_online.smart_schedule_enable
     *
     * @mbg.generated
     */
    public Byte getSmartScheduleEnable() {
        return smartScheduleEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.smart_schedule_enable
     *
     * @param smartScheduleEnable the value for moe_business_book_online.smart_schedule_enable
     *
     * @mbg.generated
     */
    public void setSmartScheduleEnable(Byte smartScheduleEnable) {
        this.smartScheduleEnable = smartScheduleEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.smart_schedule_max_dist
     *
     * @return the value of moe_business_book_online.smart_schedule_max_dist
     *
     * @mbg.generated
     */
    public Integer getSmartScheduleMaxDist() {
        return smartScheduleMaxDist;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.smart_schedule_max_dist
     *
     * @param smartScheduleMaxDist the value for moe_business_book_online.smart_schedule_max_dist
     *
     * @mbg.generated
     */
    public void setSmartScheduleMaxDist(Integer smartScheduleMaxDist) {
        this.smartScheduleMaxDist = smartScheduleMaxDist;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.smart_schedule_max_time
     *
     * @return the value of moe_business_book_online.smart_schedule_max_time
     *
     * @mbg.generated
     */
    public Integer getSmartScheduleMaxTime() {
        return smartScheduleMaxTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.smart_schedule_max_time
     *
     * @param smartScheduleMaxTime the value for moe_business_book_online.smart_schedule_max_time
     *
     * @mbg.generated
     */
    public void setSmartScheduleMaxTime(Integer smartScheduleMaxTime) {
        this.smartScheduleMaxTime = smartScheduleMaxTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.zip_codes
     *
     * @return the value of moe_business_book_online.zip_codes
     *
     * @mbg.generated
     */
    public String getZipCodes() {
        return zipCodes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.zip_codes
     *
     * @param zipCodes the value for moe_business_book_online.zip_codes
     *
     * @mbg.generated
     */
    public void setZipCodes(String zipCodes) {
        this.zipCodes = zipCodes == null ? null : zipCodes.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.service_areas
     *
     * @return the value of moe_business_book_online.service_areas
     *
     * @mbg.generated
     */
    public List<Integer> getServiceAreas() {
        return serviceAreas;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.service_areas
     *
     * @param serviceAreas the value for moe_business_book_online.service_areas
     *
     * @mbg.generated
     */
    public void setServiceAreas(List<Integer> serviceAreas) {
        this.serviceAreas = serviceAreas;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.book_online_name
     *
     * @return the value of moe_business_book_online.book_online_name
     *
     * @mbg.generated
     */
    public String getBookOnlineName() {
        return bookOnlineName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.book_online_name
     *
     * @param bookOnlineName the value for moe_business_book_online.book_online_name
     *
     * @mbg.generated
     */
    public void setBookOnlineName(String bookOnlineName) {
        this.bookOnlineName = bookOnlineName == null ? null : bookOnlineName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.allowed_simplify_submit
     *
     * @return the value of moe_business_book_online.allowed_simplify_submit
     *
     * @mbg.generated
     */
    public Byte getAllowedSimplifySubmit() {
        return allowedSimplifySubmit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.allowed_simplify_submit
     *
     * @param allowedSimplifySubmit the value for moe_business_book_online.allowed_simplify_submit
     *
     * @mbg.generated
     */
    public void setAllowedSimplifySubmit(Byte allowedSimplifySubmit) {
        this.allowedSimplifySubmit = allowedSimplifySubmit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.available_time_type
     *
     * @return the value of moe_business_book_online.available_time_type
     *
     * @mbg.generated
     */
    public Byte getAvailableTimeType() {
        return availableTimeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.available_time_type
     *
     * @param availableTimeType the value for moe_business_book_online.available_time_type
     *
     * @mbg.generated
     */
    public void setAvailableTimeType(Byte availableTimeType) {
        this.availableTimeType = availableTimeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.by_slot_timeslot_format
     *
     * @return the value of moe_business_book_online.by_slot_timeslot_format
     *
     * @mbg.generated
     */
    public Byte getBySlotTimeslotFormat() {
        return bySlotTimeslotFormat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.by_slot_timeslot_format
     *
     * @param bySlotTimeslotFormat the value for moe_business_book_online.by_slot_timeslot_format
     *
     * @mbg.generated
     */
    public void setBySlotTimeslotFormat(Byte bySlotTimeslotFormat) {
        this.bySlotTimeslotFormat = bySlotTimeslotFormat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.by_slot_timeslot_mins
     *
     * @return the value of moe_business_book_online.by_slot_timeslot_mins
     *
     * @mbg.generated
     */
    public Integer getBySlotTimeslotMins() {
        return bySlotTimeslotMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.by_slot_timeslot_mins
     *
     * @param bySlotTimeslotMins the value for moe_business_book_online.by_slot_timeslot_mins
     *
     * @mbg.generated
     */
    public void setBySlotTimeslotMins(Integer bySlotTimeslotMins) {
        this.bySlotTimeslotMins = bySlotTimeslotMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.by_slot_soonest_available
     *
     * @return the value of moe_business_book_online.by_slot_soonest_available
     *
     * @mbg.generated
     */
    public Integer getBySlotSoonestAvailable() {
        return bySlotSoonestAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.by_slot_soonest_available
     *
     * @param bySlotSoonestAvailable the value for moe_business_book_online.by_slot_soonest_available
     *
     * @mbg.generated
     */
    public void setBySlotSoonestAvailable(Integer bySlotSoonestAvailable) {
        this.bySlotSoonestAvailable = bySlotSoonestAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.by_slot_farthest_available
     *
     * @return the value of moe_business_book_online.by_slot_farthest_available
     *
     * @mbg.generated
     */
    public Integer getBySlotFarthestAvailable() {
        return bySlotFarthestAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.by_slot_farthest_available
     *
     * @param bySlotFarthestAvailable the value for moe_business_book_online.by_slot_farthest_available
     *
     * @mbg.generated
     */
    public void setBySlotFarthestAvailable(Integer bySlotFarthestAvailable) {
        this.bySlotFarthestAvailable = bySlotFarthestAvailable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.service_filter
     *
     * @return the value of moe_business_book_online.service_filter
     *
     * @mbg.generated
     */
    public Byte getServiceFilter() {
        return serviceFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.service_filter
     *
     * @param serviceFilter the value for moe_business_book_online.service_filter
     *
     * @mbg.generated
     */
    public void setServiceFilter(Byte serviceFilter) {
        this.serviceFilter = serviceFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_show_categories
     *
     * @return the value of moe_business_book_online.is_show_categories
     *
     * @mbg.generated
     */
    public Boolean getIsShowCategories() {
        return isShowCategories;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_show_categories
     *
     * @param isShowCategories the value for moe_business_book_online.is_show_categories
     *
     * @mbg.generated
     */
    public void setIsShowCategories(Boolean isShowCategories) {
        this.isShowCategories = isShowCategories;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.prepay_type
     *
     * @return the value of moe_business_book_online.prepay_type
     *
     * @mbg.generated
     */
    public Byte getPrepayType() {
        return prepayType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.prepay_type
     *
     * @param prepayType the value for moe_business_book_online.prepay_type
     *
     * @mbg.generated
     */
    public void setPrepayType(Byte prepayType) {
        this.prepayType = prepayType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.prepay_tip_enable
     *
     * @return the value of moe_business_book_online.prepay_tip_enable
     *
     * @mbg.generated
     */
    public Byte getPrepayTipEnable() {
        return prepayTipEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.prepay_tip_enable
     *
     * @param prepayTipEnable the value for moe_business_book_online.prepay_tip_enable
     *
     * @mbg.generated
     */
    public void setPrepayTipEnable(Byte prepayTipEnable) {
        this.prepayTipEnable = prepayTipEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.deposit_type
     *
     * @return the value of moe_business_book_online.deposit_type
     *
     * @mbg.generated
     */
    public Byte getDepositType() {
        return depositType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.deposit_type
     *
     * @param depositType the value for moe_business_book_online.deposit_type
     *
     * @mbg.generated
     */
    public void setDepositType(Byte depositType) {
        this.depositType = depositType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.deposit_percentage
     *
     * @return the value of moe_business_book_online.deposit_percentage
     *
     * @mbg.generated
     */
    public Integer getDepositPercentage() {
        return depositPercentage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.deposit_percentage
     *
     * @param depositPercentage the value for moe_business_book_online.deposit_percentage
     *
     * @mbg.generated
     */
    public void setDepositPercentage(Integer depositPercentage) {
        this.depositPercentage = depositPercentage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.deposit_amount
     *
     * @return the value of moe_business_book_online.deposit_amount
     *
     * @mbg.generated
     */
    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.deposit_amount
     *
     * @param depositAmount the value for moe_business_book_online.deposit_amount
     *
     * @mbg.generated
     */
    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.use_version
     *
     * @return the value of moe_business_book_online.use_version
     *
     * @mbg.generated
     */
    public Byte getUseVersion() {
        return useVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.use_version
     *
     * @param useVersion the value for moe_business_book_online.use_version
     *
     * @mbg.generated
     */
    public void setUseVersion(Byte useVersion) {
        this.useVersion = useVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.pre_auth_tip_enable
     *
     * @return the value of moe_business_book_online.pre_auth_tip_enable
     *
     * @mbg.generated
     */
    public Byte getPreAuthTipEnable() {
        return preAuthTipEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.pre_auth_tip_enable
     *
     * @param preAuthTipEnable the value for moe_business_book_online.pre_auth_tip_enable
     *
     * @mbg.generated
     */
    public void setPreAuthTipEnable(Byte preAuthTipEnable) {
        this.preAuthTipEnable = preAuthTipEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.auto_refund_deposit
     *
     * @return the value of moe_business_book_online.auto_refund_deposit
     *
     * @mbg.generated
     */
    public Byte getAutoRefundDeposit() {
        return autoRefundDeposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.auto_refund_deposit
     *
     * @param autoRefundDeposit the value for moe_business_book_online.auto_refund_deposit
     *
     * @mbg.generated
     */
    public void setAutoRefundDeposit(Byte autoRefundDeposit) {
        this.autoRefundDeposit = autoRefundDeposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.request_submitted_auto_type
     *
     * @return the value of moe_business_book_online.request_submitted_auto_type
     *
     * @mbg.generated
     */
    public String getRequestSubmittedAutoType() {
        return requestSubmittedAutoType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.request_submitted_auto_type
     *
     * @param requestSubmittedAutoType the value for moe_business_book_online.request_submitted_auto_type
     *
     * @mbg.generated
     */
    public void setRequestSubmittedAutoType(String requestSubmittedAutoType) {
        this.requestSubmittedAutoType = requestSubmittedAutoType == null ? null : requestSubmittedAutoType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.display_staff_selection_page
     *
     * @return the value of moe_business_book_online.display_staff_selection_page
     *
     * @mbg.generated
     */
    public Boolean getDisplayStaffSelectionPage() {
        return displayStaffSelectionPage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.display_staff_selection_page
     *
     * @param displayStaffSelectionPage the value for moe_business_book_online.display_staff_selection_page
     *
     * @mbg.generated
     */
    public void setDisplayStaffSelectionPage(Boolean displayStaffSelectionPage) {
        this.displayStaffSelectionPage = displayStaffSelectionPage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.arrival_window_before_min
     *
     * @return the value of moe_business_book_online.arrival_window_before_min
     *
     * @mbg.generated
     */
    public Integer getArrivalWindowBeforeMin() {
        return arrivalWindowBeforeMin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.arrival_window_before_min
     *
     * @param arrivalWindowBeforeMin the value for moe_business_book_online.arrival_window_before_min
     *
     * @mbg.generated
     */
    public void setArrivalWindowBeforeMin(Integer arrivalWindowBeforeMin) {
        this.arrivalWindowBeforeMin = arrivalWindowBeforeMin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.arrival_window_after_min
     *
     * @return the value of moe_business_book_online.arrival_window_after_min
     *
     * @mbg.generated
     */
    public Integer getArrivalWindowAfterMin() {
        return arrivalWindowAfterMin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.arrival_window_after_min
     *
     * @param arrivalWindowAfterMin the value for moe_business_book_online.arrival_window_after_min
     *
     * @mbg.generated
     */
    public void setArrivalWindowAfterMin(Integer arrivalWindowAfterMin) {
        this.arrivalWindowAfterMin = arrivalWindowAfterMin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.booking_range_start_offset
     *
     * @return the value of moe_business_book_online.booking_range_start_offset
     *
     * @mbg.generated
     */
    public Integer getBookingRangeStartOffset() {
        return bookingRangeStartOffset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.booking_range_start_offset
     *
     * @param bookingRangeStartOffset the value for moe_business_book_online.booking_range_start_offset
     *
     * @mbg.generated
     */
    public void setBookingRangeStartOffset(Integer bookingRangeStartOffset) {
        this.bookingRangeStartOffset = bookingRangeStartOffset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.booking_range_end_type
     *
     * @return the value of moe_business_book_online.booking_range_end_type
     *
     * @mbg.generated
     */
    public Byte getBookingRangeEndType() {
        return bookingRangeEndType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.booking_range_end_type
     *
     * @param bookingRangeEndType the value for moe_business_book_online.booking_range_end_type
     *
     * @mbg.generated
     */
    public void setBookingRangeEndType(Byte bookingRangeEndType) {
        this.bookingRangeEndType = bookingRangeEndType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.booking_range_end_offset
     *
     * @return the value of moe_business_book_online.booking_range_end_offset
     *
     * @mbg.generated
     */
    public Integer getBookingRangeEndOffset() {
        return bookingRangeEndOffset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.booking_range_end_offset
     *
     * @param bookingRangeEndOffset the value for moe_business_book_online.booking_range_end_offset
     *
     * @mbg.generated
     */
    public void setBookingRangeEndOffset(Integer bookingRangeEndOffset) {
        this.bookingRangeEndOffset = bookingRangeEndOffset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.booking_range_end_date
     *
     * @return the value of moe_business_book_online.booking_range_end_date
     *
     * @mbg.generated
     */
    public String getBookingRangeEndDate() {
        return bookingRangeEndDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.booking_range_end_date
     *
     * @param bookingRangeEndDate the value for moe_business_book_online.booking_range_end_date
     *
     * @mbg.generated
     */
    public void setBookingRangeEndDate(String bookingRangeEndDate) {
        this.bookingRangeEndDate = bookingRangeEndDate == null ? null : bookingRangeEndDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.is_need_send_renew_notification
     *
     * @return the value of moe_business_book_online.is_need_send_renew_notification
     *
     * @mbg.generated
     */
    public Boolean getIsNeedSendRenewNotification() {
        return isNeedSendRenewNotification;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.is_need_send_renew_notification
     *
     * @param isNeedSendRenewNotification the value for moe_business_book_online.is_need_send_renew_notification
     *
     * @mbg.generated
     */
    public void setIsNeedSendRenewNotification(Boolean isNeedSendRenewNotification) {
        this.isNeedSendRenewNotification = isNeedSendRenewNotification;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.company_id
     *
     * @return the value of moe_business_book_online.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.company_id
     *
     * @param companyId the value for moe_business_book_online.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_payment_type
     *
     * @return the value of moe_business_book_online.group_payment_type
     *
     * @mbg.generated
     */
    public Byte getGroupPaymentType() {
        return groupPaymentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_payment_type
     *
     * @param groupPaymentType the value for moe_business_book_online.group_payment_type
     *
     * @mbg.generated
     */
    public void setGroupPaymentType(Byte groupPaymentType) {
        this.groupPaymentType = groupPaymentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_filter_rule
     *
     * @return the value of moe_business_book_online.group_filter_rule
     *
     * @mbg.generated
     */
    public String getGroupFilterRule() {
        return groupFilterRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_filter_rule
     *
     * @param groupFilterRule the value for moe_business_book_online.group_filter_rule
     *
     * @mbg.generated
     */
    public void setGroupFilterRule(String groupFilterRule) {
        this.groupFilterRule = groupFilterRule == null ? null : groupFilterRule.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_prepay_type
     *
     * @return the value of moe_business_book_online.group_prepay_type
     *
     * @mbg.generated
     */
    public Byte getGroupPrepayType() {
        return groupPrepayType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_prepay_type
     *
     * @param groupPrepayType the value for moe_business_book_online.group_prepay_type
     *
     * @mbg.generated
     */
    public void setGroupPrepayType(Byte groupPrepayType) {
        this.groupPrepayType = groupPrepayType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_prepay_tip_enable
     *
     * @return the value of moe_business_book_online.group_prepay_tip_enable
     *
     * @mbg.generated
     */
    public Byte getGroupPrepayTipEnable() {
        return groupPrepayTipEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_prepay_tip_enable
     *
     * @param groupPrepayTipEnable the value for moe_business_book_online.group_prepay_tip_enable
     *
     * @mbg.generated
     */
    public void setGroupPrepayTipEnable(Byte groupPrepayTipEnable) {
        this.groupPrepayTipEnable = groupPrepayTipEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_deposit_type
     *
     * @return the value of moe_business_book_online.group_deposit_type
     *
     * @mbg.generated
     */
    public Byte getGroupDepositType() {
        return groupDepositType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_deposit_type
     *
     * @param groupDepositType the value for moe_business_book_online.group_deposit_type
     *
     * @mbg.generated
     */
    public void setGroupDepositType(Byte groupDepositType) {
        this.groupDepositType = groupDepositType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_deposit_percentage
     *
     * @return the value of moe_business_book_online.group_deposit_percentage
     *
     * @mbg.generated
     */
    public Integer getGroupDepositPercentage() {
        return groupDepositPercentage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_deposit_percentage
     *
     * @param groupDepositPercentage the value for moe_business_book_online.group_deposit_percentage
     *
     * @mbg.generated
     */
    public void setGroupDepositPercentage(Integer groupDepositPercentage) {
        this.groupDepositPercentage = groupDepositPercentage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_deposit_amount
     *
     * @return the value of moe_business_book_online.group_deposit_amount
     *
     * @mbg.generated
     */
    public BigDecimal getGroupDepositAmount() {
        return groupDepositAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_deposit_amount
     *
     * @param groupDepositAmount the value for moe_business_book_online.group_deposit_amount
     *
     * @mbg.generated
     */
    public void setGroupDepositAmount(BigDecimal groupDepositAmount) {
        this.groupDepositAmount = groupDepositAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_pre_auth_tip_enable
     *
     * @return the value of moe_business_book_online.group_pre_auth_tip_enable
     *
     * @mbg.generated
     */
    public Byte getGroupPreAuthTipEnable() {
        return groupPreAuthTipEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_pre_auth_tip_enable
     *
     * @param groupPreAuthTipEnable the value for moe_business_book_online.group_pre_auth_tip_enable
     *
     * @mbg.generated
     */
    public void setGroupPreAuthTipEnable(Byte groupPreAuthTipEnable) {
        this.groupPreAuthTipEnable = groupPreAuthTipEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_accept_client
     *
     * @return the value of moe_business_book_online.group_accept_client
     *
     * @mbg.generated
     */
    public Byte getGroupAcceptClient() {
        return groupAcceptClient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_accept_client
     *
     * @param groupAcceptClient the value for moe_business_book_online.group_accept_client
     *
     * @mbg.generated
     */
    public void setGroupAcceptClient(Byte groupAcceptClient) {
        this.groupAcceptClient = groupAcceptClient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.new_client_flow_type
     *
     * @return the value of moe_business_book_online.new_client_flow_type
     *
     * @mbg.generated
     */
    public Integer getNewClientFlowType() {
        return newClientFlowType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.new_client_flow_type
     *
     * @param newClientFlowType the value for moe_business_book_online.new_client_flow_type
     *
     * @mbg.generated
     */
    public void setNewClientFlowType(Integer newClientFlowType) {
        this.newClientFlowType = newClientFlowType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.available_time_sync
     *
     * @return the value of moe_business_book_online.available_time_sync
     *
     * @mbg.generated
     */
    public Byte getAvailableTimeSync() {
        return availableTimeSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.available_time_sync
     *
     * @param availableTimeSync the value for moe_business_book_online.available_time_sync
     *
     * @mbg.generated
     */
    public void setAvailableTimeSync(Byte availableTimeSync) {
        this.availableTimeSync = availableTimeSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.payment_option_map
     *
     * @return the value of moe_business_book_online.payment_option_map
     *
     * @mbg.generated
     */
    public Map<Integer, PaymentOption> getPaymentOptionMap() {
        return paymentOptionMap;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.payment_option_map
     *
     * @param paymentOptionMap the value for moe_business_book_online.payment_option_map
     *
     * @mbg.generated
     */
    public void setPaymentOptionMap(Map<Integer, PaymentOption> paymentOptionMap) {
        this.paymentOptionMap = paymentOptionMap;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.by_slot_show_one_available_time
     *
     * @return the value of moe_business_book_online.by_slot_show_one_available_time
     *
     * @mbg.generated
     */
    public Boolean getBySlotShowOneAvailableTime() {
        return bySlotShowOneAvailableTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.by_slot_show_one_available_time
     *
     * @param bySlotShowOneAvailableTime the value for moe_business_book_online.by_slot_show_one_available_time
     *
     * @mbg.generated
     */
    public void setBySlotShowOneAvailableTime(Boolean bySlotShowOneAvailableTime) {
        this.bySlotShowOneAvailableTime = bySlotShowOneAvailableTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.cancellation_policy
     *
     * @return the value of moe_business_book_online.cancellation_policy
     *
     * @mbg.generated
     */
    public String getCancellationPolicy() {
        return cancellationPolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.cancellation_policy
     *
     * @param cancellationPolicy the value for moe_business_book_online.cancellation_policy
     *
     * @mbg.generated
     */
    public void setCancellationPolicy(String cancellationPolicy) {
        this.cancellationPolicy = cancellationPolicy == null ? null : cancellationPolicy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.description
     *
     * @return the value of moe_business_book_online.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.description
     *
     * @param description the value for moe_business_book_online.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.prepay_policy
     *
     * @return the value of moe_business_book_online.prepay_policy
     *
     * @mbg.generated
     */
    public String getPrepayPolicy() {
        return prepayPolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.prepay_policy
     *
     * @param prepayPolicy the value for moe_business_book_online.prepay_policy
     *
     * @mbg.generated
     */
    public void setPrepayPolicy(String prepayPolicy) {
        this.prepayPolicy = prepayPolicy == null ? null : prepayPolicy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.pre_auth_policy
     *
     * @return the value of moe_business_book_online.pre_auth_policy
     *
     * @mbg.generated
     */
    public String getPreAuthPolicy() {
        return preAuthPolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.pre_auth_policy
     *
     * @param preAuthPolicy the value for moe_business_book_online.pre_auth_policy
     *
     * @mbg.generated
     */
    public void setPreAuthPolicy(String preAuthPolicy) {
        this.preAuthPolicy = preAuthPolicy == null ? null : preAuthPolicy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_pre_auth_policy
     *
     * @return the value of moe_business_book_online.group_pre_auth_policy
     *
     * @mbg.generated
     */
    public String getGroupPreAuthPolicy() {
        return groupPreAuthPolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_pre_auth_policy
     *
     * @param groupPreAuthPolicy the value for moe_business_book_online.group_pre_auth_policy
     *
     * @mbg.generated
     */
    public void setGroupPreAuthPolicy(String groupPreAuthPolicy) {
        this.groupPreAuthPolicy = groupPreAuthPolicy == null ? null : groupPreAuthPolicy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_cancellation_policy
     *
     * @return the value of moe_business_book_online.group_cancellation_policy
     *
     * @mbg.generated
     */
    public String getGroupCancellationPolicy() {
        return groupCancellationPolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_cancellation_policy
     *
     * @param groupCancellationPolicy the value for moe_business_book_online.group_cancellation_policy
     *
     * @mbg.generated
     */
    public void setGroupCancellationPolicy(String groupCancellationPolicy) {
        this.groupCancellationPolicy = groupCancellationPolicy == null ? null : groupCancellationPolicy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_book_online.group_prepay_policy
     *
     * @return the value of moe_business_book_online.group_prepay_policy
     *
     * @mbg.generated
     */
    public String getGroupPrepayPolicy() {
        return groupPrepayPolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_book_online.group_prepay_policy
     *
     * @param groupPrepayPolicy the value for moe_business_book_online.group_prepay_policy
     *
     * @mbg.generated
     */
    public void setGroupPrepayPolicy(String groupPrepayPolicy) {
        this.groupPrepayPolicy = groupPrepayPolicy == null ? null : groupPrepayPolicy.trim();
    }
}
