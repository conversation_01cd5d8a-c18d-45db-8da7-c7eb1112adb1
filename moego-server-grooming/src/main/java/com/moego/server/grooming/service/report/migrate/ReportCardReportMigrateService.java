package com.moego.server.grooming.service.report.migrate;

import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.idl.service.appointment.v1.DailyReportServiceGrpc;
import com.moego.server.grooming.config.ReportCardMigrateConfig;
import com.moego.server.grooming.mapper.MoeGroomingReportMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportExample;
import com.moego.server.grooming.service.utils.ReportCardMigrateUtils;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Report Card 报告迁移服务
 * 负责将 moe_grooming_report 和 daily_report_config 数据迁移到 fulfillment_report
 *
 * 核心原则：
 * 1. 零模式修改：不修改新表结构
 * 2. 引用完整性：保持数据间关联关系
 * 3. 注意表与表之间的id关联，需要直接使用新表的id，在迁移过程中进行新旧表id的替换
 * 4. 数据量较大的表迁移需考虑性能，例如分片处理
 * 5. 通过调用fulfillment服务接口进行数据操作
 * 6. 可追溯性：完整记录迁移过程
 * 7. 可回滚性：确保问题发生时可快速恢复
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardReportMigrateService {

    private final MoeGroomingReportMapper groomingReportMapper;
    private final DailyReportServiceGrpc.DailyReportServiceBlockingStub dailyReportService;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;

    /**
     * 迁移报告数据
     */
    @Transactional
    public ReportCardMigrateResultDTO migrate(Integer businessId, Boolean forceReplace) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "report_" + System.currentTimeMillis();

        log.info("开始迁移报告数据，taskId: {}, businessId: {}, forceReplace: {}",
                taskId, businessId, forceReplace);

        try {
            long totalMigrated = 0;
            long totalSkipped = 0;
            long totalFailed = 0;
            long totalSource = 0;

            // 1. 迁移 grooming report 数据
            log.info("开始迁移 grooming report 数据");
            MigrateResult groomingResult = migrateGroomingReports(businessId, forceReplace);
            totalMigrated += groomingResult.migratedCount;
            totalSkipped += groomingResult.skippedCount;
            totalFailed += groomingResult.failedCount;
            totalSource += groomingResult.sourceCount;

            // 2. 迁移 daily report 数据
            log.info("开始迁移 daily report 数据");
            MigrateResult dailyResult = migrateDailyReports(businessId, forceReplace);
            totalMigrated += dailyResult.migratedCount;
            totalSkipped += dailyResult.skippedCount;
            totalFailed += dailyResult.failedCount;
            totalSource += dailyResult.sourceCount;

            LocalDateTime endTime = LocalDateTime.now();
            log.info("报告数据迁移完成，总源数据: {}, 成功: {}, 跳过: {}, 失败: {}",
                    totalSource, totalMigrated, totalSkipped, totalFailed);

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId, startTime,
                    ReportCardMigrateConfig.TableNames.REPORT,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT + "+" + ReportCardMigrateConfig.SourceTables.DAILY_REPORT_CONFIG,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT,
                    totalSource, totalMigrated, totalSkipped
            );

        } catch (Exception e) {
            log.error("报告数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }

    /**
     * 迁移 grooming report 数据
     */
    private MigrateResult migrateGroomingReports(Integer businessId, Boolean forceReplace) {
        try {
            // 1. 查询源数据
            MoeGroomingReportExample example = new MoeGroomingReportExample();
            MoeGroomingReportExample.Criteria criteria = example.createCriteria();


            if (businessId != null) {
                criteria.andBusinessIdEqualTo(businessId);
            }

            List<MoeGroomingReport> sourceReports = groomingReportMapper.selectByExample(example);
            log.info("查询到 grooming report 源数据 {} 条", sourceReports.size());

            if (sourceReports.isEmpty()) {
                return new MigrateResult(0, 0, 0, 0);
            }

            // 2. 分批处理数据
            return processBatchMigration(sourceReports, ReportCardMigrateConfig.SourceType.GROOMING, forceReplace);

        } catch (Exception e) {
            log.error("迁移 grooming report 数据失败", e);
            throw e;
        }
    }

    /**
     * 迁移 daily report 数据
     */
    private MigrateResult migrateDailyReports(Integer businessId, Boolean forceReplace) {
        try {
            // TODO: 通过 dailyReportService 查询 daily_report_config 数据
            // 这里需要根据实际的 gRPC 接口定义来实现
            /*
            DailyReportConfigRequest.Builder requestBuilder = DailyReportConfigRequest.newBuilder();
            if (businessId != null) {
                requestBuilder.setBusinessId(businessId);
            }
            requestBuilder.setStatus(0); // 只查询正常状态的数据

            DailyReportConfigResponse response = dailyReportService.getDailyReportConfigs(requestBuilder.build());
            List<DailyReportConfig> dailyReports = response.getConfigsList();
            */

            // 暂时返回空结果，等待实际 gRPC 接口实现
            log.info("daily report 数据迁移暂未实现，等待 gRPC 接口定义");
            return new MigrateResult(0, 0, 0, 0);

        } catch (Exception e) {
            log.error("迁移 daily report 数据失败", e);
            throw e;
        }
    }

    /**
     * 分批处理数据迁移
     */
    private MigrateResult processBatchMigration(List<MoeGroomingReport> sourceReports, String sourceType, Boolean forceReplace) {
        long migratedCount = 0;
        long skippedCount = 0;
        long failedCount = 0;

        // 分批处理
        int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
        for (int i = 0; i < sourceReports.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, sourceReports.size());
            List<MoeGroomingReport> batch = sourceReports.subList(i, endIndex);

            log.debug("处理第 {}-{} 条记录", i + 1, endIndex);

            for (MoeGroomingReport sourceReport : batch) {
                try {
                    // TODO: 检查目标表中是否已存在
                    // boolean exists = fulfillmentServiceClient.checkReportExists(
                    //     sourceReport.getBusinessId(), sourceReport.getAppointmentId(), sourceType);

                    // if (exists && !forceReplace) {
                    //     log.debug("报告已存在，跳过迁移: businessId={}, appointmentId={}",
                    //             sourceReport.getBusinessId(), sourceReport.getAppointmentId());
                    //     skippedCount++;
                    //     continue;
                    // }

                    // TODO: 转换数据并通过fulfillment服务接口进行插入或更新
                    // FulfillmentReportDTO targetReport = convertGroomingReport(sourceReport, sourceType);
                    // Integer newId = fulfillmentServiceClient.saveOrUpdateReport(targetReport, forceReplace);

                    // 存储ID映射关系
                    // ReportCardMigrateUtils.storeIdMapping(
                    //     ReportCardMigrateConfig.TableNames.REPORT, sourceReport.getId(), newId);

                    log.debug("迁移报告: businessId={}, appointmentId={}, sourceType={}",
                            sourceReport.getBusinessId(), sourceReport.getAppointmentId(), sourceType);
                    migratedCount++;

                } catch (Exception e) {
                    log.error("迁移报告失败: businessId={}, appointmentId={}",
                            sourceReport.getBusinessId(), sourceReport.getAppointmentId(), e);
                    failedCount++;
                }
            }
        }

        return new MigrateResult(sourceReports.size(), migratedCount, skippedCount, failedCount);
    }

    /**
     * 转换 grooming report 数据
     */
    /*
    private FulfillmentReportDTO convertGroomingReport(MoeGroomingReport source, String sourceType) {
        FulfillmentReportDTO target = new FulfillmentReportDTO();

        // 基本字段映射
        target.setBusinessId(source.getBusinessId());
        target.setAppointmentId(source.getAppointmentId());
        target.setUserId(source.getUserId());
        target.setPetId(source.getPetId());
        target.setSourceType(sourceType);

        // 获取新的template_id
        Integer newTemplateId = ReportCardMigrateUtils.getNewId(
            ReportCardMigrateConfig.TableNames.TEMPLATE, source.getTemplateId());
        target.setTemplateId(newTemplateId);

        // 字段类型转换
        target.setStatus(ReportCardMigrateUtils.convertTinyintToSmallint(source.getStatus()));
        target.setIsDeleted(ReportCardMigrateUtils.convertTinyintToSmallint(source.getIsDeleted()));

        // JSON字段
        target.setConfigJson(source.getConfigJson());
        target.setExtraJson(source.getExtraJson());

        // 时间字段
        target.setCreateTime(source.getCreateTime());
        target.setUpdateTime(source.getUpdateTime());

        return target;
    }
    */

    /**
     * 迁移结果内部类
     */
    private static class MigrateResult {
        final long sourceCount;
        final long migratedCount;
        final long skippedCount;
        final long failedCount;

        MigrateResult(long sourceCount, long migratedCount, long skippedCount, long failedCount) {
            this.sourceCount = sourceCount;
            this.migratedCount = migratedCount;
            this.skippedCount = skippedCount;
            this.failedCount = failedCount;
        }
    }
}
