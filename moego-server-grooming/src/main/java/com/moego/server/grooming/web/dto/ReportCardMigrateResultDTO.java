package com.moego.server.grooming.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Report Card 迁移结果DTO
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportCardMigrateResultDTO {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 迁移是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 总耗时（毫秒）
     */
    private Long durationMs;

    /**
     * 迁移的业务ID列表
     */
    private List<Integer> businessIds;

    /**
     * 各表迁移详情
     */
    private Map<String, TableMigrateDetail> tableDetails;

    /**
     * 总计数据统计
     */
    private MigrateSummary summary;

    /**
     * 表迁移详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableMigrateDetail {
        /**
         * 表名
         */
        private String tableName;

        /**
         * 源表名
         */
        private String sourceTable;

        /**
         * 目标表名
         */
        private String targetTable;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 源数据总数
         */
        private Long sourceCount;

        /**
         * 成功迁移数量
         */
        private Long migratedCount;

        /**
         * 失败数量
         */
        private Long failedCount;

        /**
         * 跳过数量（已存在）
         */
        private Long skippedCount;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 耗时（毫秒）
         */
        private Long durationMs;
    }

    /**
     * 迁移汇总统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MigrateSummary {
        /**
         * 总表数
         */
        private Integer totalTables;

        /**
         * 成功表数
         */
        private Integer successTables;

        /**
         * 失败表数
         */
        private Integer failedTables;

        /**
         * 总记录数
         */
        private Long totalRecords;

        /**
         * 成功迁移记录数
         */
        private Long migratedRecords;

        /**
         * 失败记录数
         */
        private Long failedRecords;

        /**
         * 跳过记录数
         */
        private Long skippedRecords;
    }
}
