package com.moego.server.grooming.service.utils;

import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Report Card 数据迁移工具类
 * 提供迁移过程中常用的工具方法和数据转换功能
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
public class ReportCardMigrateUtils {

    /**
     * ID映射缓存，用于存储新旧ID的对应关系
     * Key: 表名_旧ID, Value: 新ID
     */
    private static final Map<String, Integer> ID_MAPPING_CACHE = new ConcurrentHashMap<>();

    /**
     * 生成任务ID
     */
    public static String generateTaskId() {
        return "migrate_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 生成带前缀的任务ID
     */
    public static String generateTaskId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 计算耗时（毫秒）
     */
    public static long calculateDuration(LocalDateTime startTime, LocalDateTime endTime) {
        return java.time.Duration.between(startTime, endTime).toMillis();
    }

    /**
     * 构建成功的迁移结果
     */
    public static ReportCardMigrateResultDTO buildSuccessResult(String taskId, LocalDateTime startTime,
                                                               String tableName, String sourceTable, String targetTable,
                                                               long sourceCount, long migratedCount, long skippedCount) {
        LocalDateTime endTime = LocalDateTime.now();

        ReportCardMigrateResultDTO.TableMigrateDetail tableDetail = ReportCardMigrateResultDTO.TableMigrateDetail.builder()
                .tableName(tableName)
                .sourceTable(sourceTable)
                .targetTable(targetTable)
                .success(true)
                .sourceCount(sourceCount)
                .migratedCount(migratedCount)
                .skippedCount(skippedCount)
                .failedCount(0L)
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(calculateDuration(startTime, endTime))
                .build();

        return ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(true)
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(calculateDuration(startTime, endTime))
                .tableDetails(Map.of(tableName, tableDetail))
                .build();
    }

    /**
     * 构建失败的迁移结果
     */
    public static ReportCardMigrateResultDTO buildFailureResult(String taskId, LocalDateTime startTime,
                                                               String errorMessage) {
        LocalDateTime endTime = LocalDateTime.now();
        return ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(false)
                .errorMessage(errorMessage)
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(calculateDuration(startTime, endTime))
                .build();
    }

    /**
     * 构建失败的迁移结果（带表详情）
     */
    public static ReportCardMigrateResultDTO buildFailureResult(String taskId, LocalDateTime startTime,
                                                               String tableName, String sourceTable, String targetTable,
                                                               String errorMessage, long sourceCount, long migratedCount, long failedCount) {
        LocalDateTime endTime = LocalDateTime.now();

        ReportCardMigrateResultDTO.TableMigrateDetail tableDetail = ReportCardMigrateResultDTO.TableMigrateDetail.builder()
                .tableName(tableName)
                .sourceTable(sourceTable)
                .targetTable(targetTable)
                .success(false)
                .errorMessage(errorMessage)
                .sourceCount(sourceCount)
                .migratedCount(migratedCount)
                .failedCount(failedCount)
                .skippedCount(0L)
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(calculateDuration(startTime, endTime))
                .build();

        return ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(false)
                .errorMessage(errorMessage)
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(calculateDuration(startTime, endTime))
                .tableDetails(Map.of(tableName, tableDetail))
                .build();
    }

    /**
     * 数据类型转换：MySQL tinyint -> PostgreSQL smallint
     */
    public static Short convertTinyintToSmallint(Byte value) {
        return value != null ? value.shortValue() : null;
    }

    /**
     * 数据类型转换：MySQL int -> PostgreSQL int
     */
    public static Integer convertInt(Integer value) {
        return value;
    }

    /**
     * 数据类型转换：MySQL boolean -> PostgreSQL boolean
     */
    public static Boolean convertBoolean(Boolean value) {
        return value;
    }

    /**
     * 存储ID映射关系
     */
    public static void storeIdMapping(String tableName, Integer oldId, Integer newId) {
        String key = tableName + "_" + oldId;
        ID_MAPPING_CACHE.put(key, newId);
        log.debug("存储ID映射: {} -> {}", key, newId);
    }

    /**
     * 获取新ID
     */
    public static Integer getNewId(String tableName, Integer oldId) {
        String key = tableName + "_" + oldId;
        Integer newId = ID_MAPPING_CACHE.get(key);
        if (newId == null) {
            log.warn("未找到ID映射: {}", key);
        }
        return newId;
    }

    /**
     * 清除ID映射缓存
     */
    public static void clearIdMappingCache() {
        ID_MAPPING_CACHE.clear();
        log.info("已清除ID映射缓存");
    }

    /**
     * 清除指定表的ID映射缓存
     */
    public static void clearIdMappingCache(String tableName) {
        ID_MAPPING_CACHE.entrySet().removeIf(entry -> entry.getKey().startsWith(tableName + "_"));
        log.info("已清除表 {} 的ID映射缓存", tableName);
    }

    /**
     * 获取ID映射缓存大小
     */
    public static int getIdMappingCacheSize() {
        return ID_MAPPING_CACHE.size();
    }

    /**
     * 验证必填字段
     */
    public static boolean isNotEmpty(String value) {
        return value != null && !value.trim().isEmpty();
    }

    /**
     * 安全的字符串截取
     */
    public static String safeTruncate(String value, int maxLength) {
        if (value == null) {
            return null;
        }
        return value.length() > maxLength ? value.substring(0, maxLength) : value;
    }

    /**
     * 计算进度百分比
     */
    public static int calculateProgressPercent(long processed, long total) {
        if (total == 0) {
            return 100;
        }
        return (int) Math.min(100, (processed * 100) / total);
    }

    /**
     * 格式化数量信息
     */
    public static String formatCountInfo(long processed, long total) {
        return String.format("%d/%d (%.1f%%)", processed, total,
                total > 0 ? (processed * 100.0 / total) : 100.0);
    }

    /**
     * 记录迁移统计信息
     */
    public static void logMigrationStats(String tableName, long sourceCount, long migratedCount,
                                       long skippedCount, long failedCount, long durationMs) {
        log.info("表 {} 迁移统计: 源数据={}, 成功={}, 跳过={}, 失败={}, 耗时={}ms",
                tableName, sourceCount, migratedCount, skippedCount, failedCount, durationMs);
    }
}
