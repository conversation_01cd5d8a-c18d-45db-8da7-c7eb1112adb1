package com.moego.server.grooming.utils;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import lombok.experimental.UtilityClass;

@UtilityClass
public class AppointmentUtils {

    public static boolean isStaffOnly(MoeGroomingAppointment appointment) {
        if (appointment == null || appointment.getServiceTypeInclude() == null) {
            return false;
        }
        return (appointment.getServiceTypeInclude()
                        & (ServiceItemEnum.GROOMING.getBitValue() | ServiceItemEnum.DOG_WALKING.getBitValue()))
                > 0;
    }

    public static boolean isBlock(MoeGroomingAppointment appointment) {
        if (appointment == null) {
            return false;
        }
        return CommonConstant.ENABLE.intValue() == appointment.getIsBlock();
    }
}
