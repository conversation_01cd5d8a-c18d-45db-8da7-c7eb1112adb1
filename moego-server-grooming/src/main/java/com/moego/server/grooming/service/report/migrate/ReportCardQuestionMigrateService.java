package com.moego.server.grooming.service.report.migrate;

import com.moego.server.grooming.config.ReportCardMigrateConfig;
import com.moego.server.grooming.mapper.MoeGroomingReportQuestionMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion;
import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestionExample;
import com.moego.server.grooming.service.utils.ReportCardMigrateUtils;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Report Card 问题迁移服务
 * 负责将 moe_grooming_report_question 数据迁移到 fulfillment_report_question
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardQuestionMigrateService {

    // TODO: 注入相关的mapper和服务客户端
     private final MoeGroomingReportQuestionMapper sourceMapper;
    // private final FulfillmentServiceClient fulfillmentServiceClient;

    /**
     * 迁移问题数据
     * 需要处理template_id的关联关系，使用新表的ID
     */
    @Transactional
    public ReportCardMigrateResultDTO migrate(Integer businessId, Boolean forceReplace) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "question_" + System.currentTimeMillis();

        log.info("开始迁移问题数据，taskId: {}, businessId: {}, forceReplace: {}",
                taskId, businessId, forceReplace);

        try {
            // 1. 查询源数据
            MoeGroomingReportQuestion example = new MoeGroomingReportQuestion();

            List<MoeGroomingReportQuestion> sourceQuestions = sourceMapper.selectByBusinessId(businessId);
            log.info("查询到问题源数据 {} 条", sourceQuestions.size());

            if (sourceQuestions.isEmpty()) {
                return ReportCardMigrateUtils.buildSuccessResult(
                        taskId, startTime,
                        ReportCardMigrateConfig.TableNames.QUESTION,
                        ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_QUESTION,
                        ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_QUESTION,
                        0, 0, 0
                );
            }

            // 2. 分批处理数据迁移
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;

            int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
            for (int i = 0; i < sourceQuestions.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, sourceQuestions.size());
                List<MoeGroomingReportQuestion> batch = sourceQuestions.subList(i, endIndex);

                log.debug("处理第 {}-{} 条问题记录", i + 1, endIndex);

                for (MoeGroomingReportQuestion sourceQuestion : batch) {
                    try {
                        // TODO: 检查目标表中是否已存在
                        // boolean exists = fulfillmentServiceClient.checkQuestionExists(
                        //     sourceQuestion.getBusinessId(), sourceQuestion.getTemplateId(), sourceQuestion.getKey());

                        boolean shouldSkip = false;
                        if (!forceReplace) {
                            // 暂时假设不存在，实际需要调用fulfillment服务检查
                            shouldSkip = false;
                        }

                        if (shouldSkip) {
                            log.debug("问题已存在，跳过迁移: businessId={}, templateId={}, key={}",
                                    sourceQuestion.getBusinessId(), sourceQuestion.getTemplateId(), sourceQuestion.getKey());
                            skippedCount++;
                            continue;
                        }

                        // TODO: 转换数据并通过fulfillment服务接口进行插入或更新
                        // 需要先获取新的template_id
                        // Integer newTemplateId = ReportCardMigrateUtils.getNewId(
                        //     ReportCardMigrateConfig.TableNames.TEMPLATE, sourceQuestion.getTemplateId());

                        // FulfillmentReportQuestionDTO targetQuestion = convertQuestion(sourceQuestion, newTemplateId);
                        // Integer newId = fulfillmentServiceClient.saveOrUpdateQuestion(targetQuestion, forceReplace);

                        log.debug("迁移问题: businessId={}, templateId={}, key={}",
                                sourceQuestion.getBusinessId(), sourceQuestion.getTemplateId(), sourceQuestion.getKey());
                        migratedCount++;

                    } catch (Exception e) {
                        log.error("迁移问题失败: businessId={}, templateId={}, key={}",
                                sourceQuestion.getBusinessId(), sourceQuestion.getTemplateId(), sourceQuestion.getKey(), e);
                        failedCount++;
                    }
                }
            }

            log.info("问题数据迁移完成，总数: {}, 成功: {}, 跳过: {}, 失败: {}",
                    sourceQuestions.size(), migratedCount, skippedCount, failedCount);

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId, startTime,
                    ReportCardMigrateConfig.TableNames.QUESTION,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_QUESTION,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_QUESTION,
                    sourceQuestions.size(), migratedCount, skippedCount
            );

        } catch (Exception e) {
            log.error("问题数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }
}
