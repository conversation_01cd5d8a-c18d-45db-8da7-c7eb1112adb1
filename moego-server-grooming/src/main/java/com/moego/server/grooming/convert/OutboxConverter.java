package com.moego.server.grooming.convert;

import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.server.grooming.mapperbean.AppointmentOutbox;
import java.time.LocalDateTime;
import java.time.ZoneId;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper()
public interface OutboxConverter {
    OutboxConverter INSTANCE = Mappers.getMapper(OutboxConverter.class);

    default AppointmentOutbox toAppointmentOutbox(String topicName, EventRecord<EventData> eventRecord) {
        var po = new AppointmentOutbox();
        po.setTopic(topicName);
        po.setEventId(eventRecord.id());
        po.setEventTime(LocalDateTime.ofInstant(eventRecord.time(), ZoneId.systemDefault()));
        po.setEventKey(eventRecord.key());
        po.setEventType(eventRecord.type());
        po.setEventDetail(JsonUtil.toJson(eventRecord.detail()));
        return po;
    }
}
