package com.moego.server.grooming.service;

import static com.moego.common.enums.QuestionConst.IS_ALLOW_CHANGE_FALSE;
import static com.moego.common.enums.QuestionConst.IS_ALLOW_CHANGE_TRUE;
import static com.moego.common.enums.QuestionConst.IS_ALLOW_DELETE_FALSE;
import static com.moego.common.enums.QuestionConst.IS_ALLOW_EDIT_FALSE;
import static com.moego.common.enums.QuestionConst.IS_REQUIRED_FALSE;
import static com.moego.common.enums.QuestionConst.IS_REQUIRED_TRUE;
import static com.moego.common.enums.QuestionConst.IS_SHOW_FALSE;
import static com.moego.common.enums.QuestionConst.IS_SHOW_TURE;
import static com.moego.common.enums.QuestionConst.QUESTION_TYPE_RADIO;
import static com.moego.common.enums.QuestionConst.QUESTION_TYPE_SHORT;
import static com.moego.common.enums.QuestionConst.TYPE_BOARDING_SERVICE_QUESTION;
import static com.moego.common.enums.QuestionConst.TYPE_DAYCARE_SERVICE_QUESTION;
import static com.moego.common.enums.QuestionConst.TYPE_PET_OWNER_QUESTION;
import static com.moego.common.enums.QuestionConst.TYPE_PET_QUESTION;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.QuestionConst;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.AcceptPetEntryType;
import com.moego.idl.models.online_booking.v1.ExistingClientAccessMode;
import com.moego.idl.models.online_booking.v1.ExistingPetAccessMode;
import com.moego.idl.models.online_booking.v1.NewClientAccessMode;
import com.moego.idl.models.online_booking.v1.NewPetAccessMode;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.QuestionVaccineDTO;
import com.moego.server.grooming.mapper.MoeBookOnlineQuestionMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionExample;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2020-12-15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MoeGroomingQuestionService {

    private static final String PHONE_NUMBER = "Phone number";
    private static final String REFERRAL_SOURCE = "Referral source";

    private final MoeBookOnlineQuestionMapper moeBookOnlineQuestionMapper;

    /**
     * 支持重复调用（重复初始化）
     *
     * @param businessId
     */
    public void initBookingQuestionForBusiness(Integer businessId, Long companyId) {
        if (moeBookOnlineQuestionMapper
                .getListByBusinessId(businessId, QuestionConst.TYPE_PET_QUESTION.intValue())
                .isEmpty()) {
            for (var petQuestion : buildPetQuestions()) {
                petQuestion.setCompanyId(companyId);
                petQuestion.setBusinessId(businessId);
                insert(petQuestion);
            }
            for (var customerQuestion : buildCustomerQuestions()) {
                customerQuestion.setCompanyId(companyId);
                customerQuestion.setBusinessId(businessId);
                insert(customerQuestion);
            }
            for (var boardingQuestion : buildBoardingQuestions()) {
                boardingQuestion.setCompanyId(companyId);
                boardingQuestion.setBusinessId(businessId);
                insert(boardingQuestion);
            }
            for (var daycareQuestion : buildDaycareQuestions()) {
                daycareQuestion.setCompanyId(companyId);
                daycareQuestion.setBusinessId(businessId);
                insert(daycareQuestion);
            }
            log.info("booking question is initialized for {}", businessId);
        } else {
            log.warn("booking question has already been initialized for {}", businessId);
        }
    }

    /**
     * Insert a question, return inserted id.
     *
     * @param entity entity
     * @return insert id
     */
    public int insert(MoeBookOnlineQuestion entity) {

        populate(entity);

        moeBookOnlineQuestionMapper.insertSelective(entity);

        return entity.getId();
    }

    private static void populate(MoeBookOnlineQuestion entity) {
        if (Objects.equals(entity.getType(), TYPE_PET_QUESTION)) {
            if (entity.getNewPetAccessMode() == null && entity.getAcceptPetEntryType() != null) {
                entity.setNewPetAccessMode(getNewPetAccessMode(entity.getAcceptPetEntryType()));
            }
            if (entity.getExistingPetAccessMode() == null && entity.getAcceptPetEntryType() != null) {
                entity.setExistingPetAccessMode(getExistingPetAccessMode(entity.getAcceptPetEntryType()));
            }
            if (entity.getNewPetAccessMode() == null) {
                entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
            }
            if (entity.getExistingPetAccessMode() == null) {
                entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
            }
        }

        if (Objects.equals(entity.getType(), TYPE_PET_OWNER_QUESTION)) {
            if (entity.getAcceptedCustomerType() == null) {
                entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
            }
            if (entity.getNewClientAccessMode() == null && entity.getAcceptedCustomerType() != null) {
                entity.setNewClientAccessMode(getNewClientAccessMode(entity.getAcceptedCustomerType()));
            }
            if (entity.getExistingClientAccessMode() == null && entity.getAcceptedCustomerType() != null) {
                entity.setExistingClientAccessMode(
                        getExistingClientAccessMode(entity.getAcceptedCustomerType(), entity.getQuestion()));
            }
            if (entity.getNewClientAccessMode() == null) {
                entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
            }
            if (entity.getExistingClientAccessMode() == null) {
                entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
            }
        }

        if (entity.getIsRequired() == null) {
            entity.setIsRequired(IS_REQUIRED_FALSE);
        }

        if (entity.getCreateTime() == null) {
            entity.setCreateTime(Instant.now().getEpochSecond());
        }
        if (entity.getUpdateTime() == null) {
            entity.setUpdateTime(Instant.now().getEpochSecond());
        }
    }

    /**
     * Incremental update a question by id, return affected rows.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(MoeBookOnlineQuestion entity) {
        if (entity.getNewPetAccessMode() == null && entity.getAcceptPetEntryType() != null) {
            entity.setNewPetAccessMode(getNewPetAccessMode(entity.getAcceptPetEntryType()));
        }
        if (entity.getExistingPetAccessMode() == null && entity.getAcceptPetEntryType() != null) {
            entity.setExistingPetAccessMode(getExistingPetAccessMode(entity.getAcceptPetEntryType()));
        }
        if (entity.getNewClientAccessMode() == null && entity.getAcceptedCustomerType() != null) {
            entity.setNewClientAccessMode(getNewClientAccessMode(entity.getAcceptedCustomerType()));
        }
        if (entity.getExistingClientAccessMode() == null && entity.getAcceptedCustomerType() != null) {
            entity.setExistingClientAccessMode(getExistingClientAccessMode(
                    entity.getAcceptedCustomerType(), mustGet(entity.getId()).getQuestion()));
        }

        if (entity.getUpdateTime() == null) {
            entity.setUpdateTime(Instant.now().getEpochSecond());
        }

        return moeBookOnlineQuestionMapper.updateByPrimaryKeySelective(entity);
    }

    private MoeBookOnlineQuestion mustGet(int id) {
        var question = moeBookOnlineQuestionMapper.selectByPrimaryKey(id);
        if (question == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Question not found: " + id);
        }
        return question;
    }

    private static ExistingClientAccessMode getExistingClientAccessMode(Integer acceptedCustomerType, String question) {
        return switch (acceptedCustomerType) {
            case AcceptCustomerType.NEW_CUSTOMER_VALUE -> ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_DISABLED;
            case AcceptCustomerType.EXISTING_CUSTOMER_VALUE,
                    AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE -> switch (question) {
                case PHONE_NUMBER, REFERRAL_SOURCE -> ExistingClientAccessMode
                        .EXISTING_CLIENT_ACCESS_MODE_VIEW; // phone number and referral source are not allowed to edit
                default -> ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT;
            };
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid accept customer type: " + acceptedCustomerType);
        };
    }

    private static NewClientAccessMode getNewClientAccessMode(Integer acceptedCustomerType) {
        return switch (acceptedCustomerType) {
            case AcceptCustomerType.NEW_CUSTOMER_VALUE,
                    AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE -> NewClientAccessMode
                    .NEW_CLIENT_ACCESS_MODE_ENABLED;
            case AcceptCustomerType.EXISTING_CUSTOMER_VALUE -> NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_DISABLED;
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid accept customer type: " + acceptedCustomerType);
        };
    }

    private static NewPetAccessMode getNewPetAccessMode(AcceptPetEntryType acceptPetEntryType) {
        return switch (acceptPetEntryType) {
            case NEW, NEW_AND_EXISTING -> NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED;
            case EXISTING -> NewPetAccessMode.NEW_PET_ACCESS_MODE_DISABLED;
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid accept pet entry type: " + acceptPetEntryType);
        };
    }

    private static ExistingPetAccessMode getExistingPetAccessMode(AcceptPetEntryType acceptPetEntryType) {
        return switch (acceptPetEntryType) {
            case EXISTING, NEW_AND_EXISTING -> ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT;
            case NEW -> ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_DISABLED;
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid accept pet entry type: " + acceptPetEntryType);
        };
    }

    public int deleteQuestion(Integer businessId, Integer primaryId) {
        var e = new MoeBookOnlineQuestionExample();
        e.createCriteria().andBusinessIdEqualTo(businessId).andIdEqualTo(primaryId);
        return moeBookOnlineQuestionMapper.deleteByExample(e);
    }

    public void createAdditionalPetOwnerQuestion(Integer businessId, Long companyId, List<String> questionNameList) {
        for (var customerQuestion : buildCustomerQuestions()) {
            if (!questionNameList.contains(customerQuestion.getQuestion())) {
                customerQuestion.setBusinessId(businessId);
                customerQuestion.setCompanyId(companyId);
                insert(customerQuestion);
            }
        }
    }

    public void createBoardingServiceQuestion(Integer businessId, Long companyId) {
        for (var boardingQuestion : buildBoardingQuestions()) {
            boardingQuestion.setCompanyId(companyId);
            boardingQuestion.setBusinessId(businessId);
            insert(boardingQuestion);
        }
    }

    public void createDaycareServiceQuestion(Integer businessId, Long companyId) {
        for (var daycareQuestion : buildDaycareQuestions()) {
            daycareQuestion.setCompanyId(companyId);
            daycareQuestion.setBusinessId(businessId);
            insert(daycareQuestion);
        }
    }

    public void migrateVaccineQuestion(Integer fromBusinessId, Integer toBusinessId) {
        var businessId = fromBusinessId;
        while (toBusinessId == null || businessId < toBusinessId) {
            migrateVaccineQuestion(businessId);
            businessId = moeBookOnlineQuestionMapper.getNextBusinessId(businessId);
            // businessId == null 意味着没有 nextBusinessId 了
            if (businessId == null) {
                break;
            }
        }
    }

    private void migrateVaccineQuestion(int businessId) {
        // 获取 business 的 question
        var questions =
                moeBookOnlineQuestionMapper.getListByBusinessId(businessId, QuestionConst.TYPE_PET_QUESTION.intValue());
        if (CollectionUtils.isEmpty(questions)) {
            log.info("no pet question found for business id {}", businessId);
            return;
        }

        var vaccineQuestion = questions.stream()
                .filter(question -> question.getQuestion().equals("Vaccine"))
                .findFirst()
                .orElse(null);
        if (vaccineQuestion == null) {
            // should not be here
            log.info("vaccineQuestion is null for business id: {}", businessId);
            return;
        }

        var vaccineDocumentQuestion = questions.stream()
                .filter(question -> question.getQuestion().equals("Vaccine document"))
                .findFirst()
                .orElse(null);
        if (vaccineDocumentQuestion == null) {
            // should not be here
            log.info("vaccineDocumentQuestion is null for business id: {}", businessId);
            return;
        }

        var oldExtra = vaccineQuestion.getExtraJson();
        var dto = JsonUtil.toBean(StringUtils.hasText(oldExtra) ? oldExtra : "{}", QuestionVaccineDTO.class);

        if (Objects.equals(CommonConstant.ENABLE, vaccineDocumentQuestion.getIsShow())) {
            dto.setShowVaccineDocument(true);
        }
        if (Objects.equals(CommonConstant.ENABLE, vaccineDocumentQuestion.getIsRequired())) {
            dto.setRequireVaccineDocument(true);
        }
        dto.setShowExpirationDate(true);
        dto.setRequireExpirationDate(true);

        // 填充默认值，避免把 null 值写入到 extraJson
        fillDefaultValue(dto);

        var newExtra = JsonUtil.toJson(dto);
        log.info(
                "migrate vaccine question for business id: {}, oldExtra: {}, newExtra: {}",
                businessId,
                oldExtra,
                newExtra);

        var record = new MoeBookOnlineQuestion();
        record.setId(vaccineQuestion.getId());
        record.setExtraJson(newExtra);
        update(record);
    }

    private void fillDefaultValue(QuestionVaccineDTO dto) {
        if (dto.getSpecificVaccineIds() == null) {
            dto.setSpecificVaccineIds(new ArrayList<>());
        }
        if (dto.getBasedOnVaccineSettings() == null) {
            dto.setBasedOnVaccineSettings(false);
        }
        if (dto.getShowExpirationDate() == null) {
            dto.setShowExpirationDate(false);
        }
        if (dto.getRequireExpirationDate() == null) {
            dto.setRequireExpirationDate(false);
        }
        if (dto.getShowVaccineDocument() == null) {
            dto.setShowVaccineDocument(false);
        }
        if (dto.getRequireVaccineDocument() == null) {
            dto.setRequireVaccineDocument(false);
        }
    }

    private static List<MoeBookOnlineQuestion> buildPetQuestions() {
        var questions = new ArrayList<MoeBookOnlineQuestion>();
        questions.add(buildPetTypeQuestion());
        questions.add(buildPetNameQuestions());
        questions.add(buildPetBreedQuestion());
        questions.add(buildPetWeightQuestion());
        questions.add(buildPetCoatTypeQuestion());
        questions.add(buildPetVaccineQuestion());
        questions.add(buildPetVaccineDocumentQuestion());
        questions.add(buildPetBirthdayQuestion());
        questions.add(buildPetGenderQuestion());
        questions.add(buildPetFixedQuestion());
        questions.add(buildPetImageQuestion());
        questions.add(buildPetBehaviorQuestion());
        questions.add(buildPetVetNameQuestion());
        questions.add(buildPetVetPhoneNumberQuestion());
        questions.add(buildPetVetAddressQuestion());
        questions.add(buildPetHealthIssuesQuestion());
        return questions;
    }

    private static MoeBookOnlineQuestion buildPetTypeQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Pet type");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_TRUE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_FALSE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetNameQuestions() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Pet name");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_TRUE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_FALSE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetBreedQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Pet breed");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_TRUE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_FALSE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetWeightQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Weight");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        // pet weight view and edit
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetCoatTypeQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Coat type");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetVaccineQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Vaccine");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetVaccineDocumentQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Vaccine document");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetBirthdayQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Birthday");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetGenderQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Gender");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetFixedQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Fixed");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetImageQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Pet image");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetBehaviorQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Behavior");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetVetNameQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Vet name");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetVetPhoneNumberQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Vet phone number");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetVetAddressQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Vet address");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildPetHealthIssuesQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Health issues");
        entity.setType(TYPE_PET_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING);
        entity.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static List<MoeBookOnlineQuestion> buildCustomerQuestions() {
        var questions = new ArrayList<MoeBookOnlineQuestion>();
        questions.add(buildCustomerFirstNameQuestion());
        questions.add(buildCustomerLastNameQuestion());
        questions.add(buildCustomerPhoneNumberQuestion());
        questions.add(buildCustomerEmailQuestion());
        questions.add(buildCustomerAddressQuestion());
        questions.add(buildCustomerBirthdayQuestion());
        questions.add(buildCustomerPreferredFrequencyQuestion());
        questions.add(buildCustomerPreferredDayOfWeekQuestion());
        questions.add(buildCustomerPreferredTimeOfDayQuestion());
        questions.add(buildCustomerEmergencyContactQuestion());
        questions.add(buildCustomerPeopleAuthorizedToPickupPetsQuestion());
        questions.add(buildCustomerPreferredGroomerQuestion());
        questions.add(buildCustomerReferralSourceQuestion());
        return questions;
    }

    private static MoeBookOnlineQuestion buildCustomerFirstNameQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("First name");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_TRUE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_FALSE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerLastNameQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Last name");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_TRUE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_FALSE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerPhoneNumberQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion(PHONE_NUMBER);
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_TRUE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_FALSE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerEmailQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Email");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerAddressQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Address");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerBirthdayQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Birthday");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QuestionConst.QUESTION_TYPE_DATE);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerPreferredFrequencyQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Preferred frequency");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerPreferredDayOfWeekQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Preferred day of the week");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerPreferredTimeOfDayQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Preferred time of the day");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerEmergencyContactQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Emergency contact");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerPeopleAuthorizedToPickupPetsQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("People authorized to pickup pets");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerPreferredGroomerQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Preferred groomer");
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_RADIO);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        return entity;
    }

    private static MoeBookOnlineQuestion buildCustomerReferralSourceQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion(REFERRAL_SOURCE);
        entity.setType(TYPE_PET_OWNER_QUESTION);
        entity.setIsShow(IS_SHOW_FALSE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_RADIO);
        entity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        entity.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW);
        return entity;
    }

    private static List<MoeBookOnlineQuestion> buildBoardingQuestions() {
        var questions = new ArrayList<MoeBookOnlineQuestion>();
        questions.add(buildBoardingFeedingScheduleQuestion());
        questions.add(buildBoardingMedicationScheduleQuestion());
        return questions;
    }

    private static List<MoeBookOnlineQuestion> buildDaycareQuestions() {
        var questions = new ArrayList<MoeBookOnlineQuestion>();
        questions.add(buildDaycareFeedingScheduleQuestion());
        questions.add(buildDaycareMedicationScheduleQuestion());
        return questions;
    }

    private static MoeBookOnlineQuestion buildBoardingFeedingScheduleQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Feeding schedule");
        entity.setType(TYPE_BOARDING_SERVICE_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setExtraJson(QuestionConst.DEFAULT_EXTRA_JSON_FOR_FEEDING_SCHEDULE);
        return entity;
    }

    private static MoeBookOnlineQuestion buildBoardingMedicationScheduleQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Medication schedule");
        entity.setType(TYPE_BOARDING_SERVICE_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setExtraJson(QuestionConst.DEFAULT_EXTRA_JSON_FOR_MEDICATION_SCHEDULE);
        return entity;
    }

    private static MoeBookOnlineQuestion buildDaycareFeedingScheduleQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Feeding schedule");
        entity.setType(TYPE_DAYCARE_SERVICE_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setExtraJson(QuestionConst.DEFAULT_EXTRA_JSON_FOR_FEEDING_SCHEDULE);
        return entity;
    }

    private static MoeBookOnlineQuestion buildDaycareMedicationScheduleQuestion() {
        MoeBookOnlineQuestion entity = new MoeBookOnlineQuestion();
        entity.setQuestion("Medication schedule");
        entity.setType(TYPE_DAYCARE_SERVICE_QUESTION);
        entity.setIsShow(IS_SHOW_TURE);
        entity.setIsRequired(IS_REQUIRED_FALSE);
        entity.setIsAllowDelete(IS_ALLOW_DELETE_FALSE);
        entity.setIsAllowChange(IS_ALLOW_CHANGE_TRUE);
        entity.setIsAllowEdit(IS_ALLOW_EDIT_FALSE);
        entity.setQuestionType(QUESTION_TYPE_SHORT);
        entity.setExtraJson(QuestionConst.DEFAULT_EXTRA_JSON_FOR_MEDICATION_SCHEDULE);
        return entity;
    }
}
