package com.moego.server.grooming.config;

/**
 * Report Card 数据迁移配置类
 * 定义迁移过程中使用的常量和配置信息
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public class ReportCardMigrateConfig {

    /**
     * 迁移表名常量
     */
    public static class TableNames {
        public static final String THEME_CONFIG = "theme_config";
        public static final String TEMPLATE = "template";
        public static final String REPORT = "report";
        public static final String QUESTION = "question";
        public static final String SEND_RECORD = "send_record";
    }

    /**
     * 源表名常量
     */
    public static class SourceTables {
        // MySQL 源表
        public static final String MOE_GROOMING_REPORT_THEME_CONFIG = "moe_grooming_report_theme_config";
        public static final String MOE_GROOMING_REPORT_TEMPLATE = "moe_grooming_report_template";
        public static final String MOE_GROOMING_REPORT = "moe_grooming_report";
        public static final String MOE_GROOMING_REPORT_QUESTION = "moe_grooming_report_question";
        public static final String MOE_GROOMING_REPORT_SEND_LOG = "moe_grooming_report_send_log";

        // PostgreSQL 源表
        public static final String DAILY_REPORT_CONFIG = "daily_report_config";
        public static final String DAILY_REPORT_SEND_LOG = "daily_report_send_log";
    }

    /**
     * 目标表名常量
     */
    public static class TargetTables {
        public static final String FULFILLMENT_REPORT_THEME_CONFIG = "fulfillment_report_theme_config";
        public static final String FULFILLMENT_REPORT_TEMPLATE = "fulfillment_report_template";
        public static final String FULFILLMENT_REPORT = "fulfillment_report";
        public static final String FULFILLMENT_REPORT_QUESTION = "fulfillment_report_question";
        public static final String FULFILLMENT_REPORT_SEND_RECORD = "fulfillment_report_send_record";
    }

    /**
     * 数据源类型
     */
    public static class SourceType {
        public static final String GROOMING = "grooming";
        public static final String DAILY = "daily";
    }

    /**
     * 数据库连接配置
     */
    public static class DatabaseConfig {
        // MySQL 数据库配置
        public static final String MYSQL_GROOMING_HOST = "localhost";
        public static final int MYSQL_GROOMING_PORT = 40107;
        public static final String MYSQL_GROOMING_DATABASE = "moe_grooming";

        public static final String MYSQL_MESSAGE_HOST = "localhost";
        public static final int MYSQL_MESSAGE_PORT = 40108;
        public static final String MYSQL_MESSAGE_DATABASE = "moe_message";

        // PostgreSQL 数据库配置
        public static final String POSTGRES_APPOINTMENT_DATABASE = "moego_appointment";
        public static final String POSTGRES_FULFILLMENT_DATABASE = "moego_fulfillment";
    }

    /**
     * 迁移配置参数
     */
    public static class MigrationConfig {
        // 批处理大小
        public static final int BATCH_SIZE = 1000;

        // 最大重试次数
        public static final int MAX_RETRY_COUNT = 3;

        // 超时时间（毫秒）
        public static final long TIMEOUT_MS = 300000; // 5分钟

        // 进度更新间隔（记录数）
        public static final int PROGRESS_UPDATE_INTERVAL = 100;
    }

    /**
     * 状态常量
     */
    public static class Status {
        // 通用状态
        public static final byte ACTIVE = 1;
        public static final byte INACTIVE = 0;
        public static final byte DELETED = 2;

        // PostgreSQL 状态（smallint）
        public static final short ACTIVE_SHORT = 1;
        public static final short INACTIVE_SHORT = 0;
        public static final short DELETED_SHORT = 2;
    }

    /**
     * 错误码常量
     */
    public static class ErrorCode {
        public static final String DATA_NOT_FOUND = "DATA_NOT_FOUND";
        public static final String DUPLICATE_KEY = "DUPLICATE_KEY";
        public static final String FOREIGN_KEY_VIOLATION = "FOREIGN_KEY_VIOLATION";
        public static final String DATA_TYPE_MISMATCH = "DATA_TYPE_MISMATCH";
        public static final String NETWORK_ERROR = "NETWORK_ERROR";
        public static final String TIMEOUT_ERROR = "TIMEOUT_ERROR";
    }

    /**
     * 迁移阶段常量
     */
    public static class MigrationPhase {
        public static final String PREPARE = "PREPARE";
        public static final String EXTRACT = "EXTRACT";
        public static final String TRANSFORM = "TRANSFORM";
        public static final String LOAD = "LOAD";
        public static final String VALIDATE = "VALIDATE";
        public static final String COMPLETE = "COMPLETE";
    }
}
