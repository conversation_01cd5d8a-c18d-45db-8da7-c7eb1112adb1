package com.moego.server.grooming.mapperbean.fulfillment;

import lombok.Data;

import java.util.Date;

/**
 * Fulfillment Report Theme Config Entity
 * 对应 PostgreSQL 表: fulfillment_report_theme_config
 *
 */
@Data
public class FulfillmentReportThemeConfig {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 主题名称
     */
    private String name;

    /**
     * 主题代码
     */
    private String code;

    /**
     * 主题颜色
     */
    private String color;

    /**
     * 浅色主题颜色
     */
    private String lightColor;

    /**
     * 主题图片URL
     */
    private String imgUrl;

    /**
     * 主题图标
     */
    private String icon;

    /**
     * 邮件底部图片URL
     */
    private String emailBottomImgUrl;

    /**
     * 是否推荐
     */
    private Boolean isRecommend;

    /**
     * 状态: 0-inactive, 1-active, 2-hidden
     */
    private Short status;

    /**
     * 可见级别
     */
    private Integer visibleLevel;

    /**
     * 标志: 1-growth+ only
     */
    private Short flag;

    /**
     * 排序值
     */
    private Short sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    @Override
    public String toString() {
        return "FulfillmentReportThemeConfig{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", color='" + color + '\'' +
                ", lightColor='" + lightColor + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", icon='" + icon + '\'' +
                ", emailBottomImgUrl='" + emailBottomImgUrl + '\'' +
                ", isRecommend=" + isRecommend +
                ", status=" + status +
                ", visibleLevel=" + visibleLevel +
                ", flag=" + flag +
                ", sort=" + sort +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
