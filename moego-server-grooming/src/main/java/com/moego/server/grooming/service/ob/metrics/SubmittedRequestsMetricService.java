package com.moego.server.grooming.service.ob.metrics;

import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.CountBookingRequestsRequest;
import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class SubmittedRequestsMetricService implements IOBMetricsService {

    private final AppointmentMapperProxy appointmentMapper;
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestServiceBlockingStub;

    @Override
    public Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return bookingRequestServiceBlockingStub
                .countBookingRequests(CountBookingRequestsRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(timeRangeDTO.companyId())
                                .setBusinessId(timeRangeDTO.businessId())
                                .build())
                        .setFilters(CountBookingRequestsRequest.Filters.newBuilder()
                                .setCreatedTimeRange(Interval.newBuilder()
                                        .setStartTime(Timestamp.newBuilder()
                                                .setSeconds(timeRangeDTO.startTime())
                                                .build())
                                        .setEndTime(Timestamp.newBuilder()
                                                .setSeconds(timeRangeDTO.endTime())
                                                .build())
                                        .build()))
                        .build())
                .getCount();
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.submitted_requests;
    }
}
