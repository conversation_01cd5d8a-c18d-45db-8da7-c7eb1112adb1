package com.moego.server.grooming.mapper.typehandler;

import com.moego.idl.models.appointment.v1.OutboxSendStatus;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

public class OutboxSendStatusHandler extends BaseTypeHandler<OutboxSendStatus> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, OutboxSendStatus parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setInt(i, parameter.getNumber());
    }

    @Override
    public OutboxSendStatus getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getOutboxSendStatus(rs.getInt(columnName), rs.wasNull());
    }

    @Override
    public OutboxSendStatus getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getOutboxSendStatus(rs.getInt(columnIndex), rs.wasNull());
    }

    @Override
    public OutboxSendStatus getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getOutboxSendStatus(cs.getInt(columnIndex), cs.wasNull());
    }

    private OutboxSendStatus getOutboxSendStatus(int number, boolean wasNull) {
        if (number == 0 && wasNull) {
            return OutboxSendStatus.OUTBOX_SEND_STATUS_UNSPECIFIED;
        }

        var type = OutboxSendStatus.forNumber(number);
        return type != null ? type : OutboxSendStatus.UNRECOGNIZED;
    }
}
