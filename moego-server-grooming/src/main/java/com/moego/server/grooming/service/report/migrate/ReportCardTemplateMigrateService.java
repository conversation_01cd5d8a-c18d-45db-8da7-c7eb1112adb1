package com.moego.server.grooming.service.report.migrate;

import com.moego.backend.proto.fulfillment.v1.BatchMigrateTemplatesRequest;
import com.moego.backend.proto.fulfillment.v1.CareType;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse;
import com.moego.server.grooming.config.ReportCardMigrateConfig;
import com.moego.server.grooming.mapper.MoeGroomingReportTemplateMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplateExample;
import com.moego.server.grooming.service.utils.ReportCardMigrateUtils;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Report Card 模板迁移服务
 * 负责将 moe_grooming_report_template 数据迁移到 fulfillment_report_template
 *
 * 核心原则：
 * 1. 零模式修改：不修改新表结构
 * 2. 引用完整性：保持数据间关联关系
 * 3. 注意表与表之间的id关联，需要直接使用新表的id，在迁移过程中进行新旧表id的替换
 * 4. 数据量较大的表迁移需考虑性能，例如分片处理
 * 5. 通过调用fulfillment服务接口进行数据操作
 * 6. 可追溯性：完整记录迁移过程
 * 7. 可回滚性：确保问题发生时可快速恢复
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardTemplateMigrateService {

    private final MoeGroomingReportTemplateMapper sourceMapper;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;

    /**
     * 迁移模板数据
     */
    @Transactional
    public ReportCardMigrateResultDTO migrate(Integer businessId) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "template_" + System.currentTimeMillis();

        log.info("开始迁移模板数据，taskId: {}, businessId: {}",
                taskId, businessId);

        try {
            // 1. 查询源数据
            MoeGroomingReportTemplateExample example = new MoeGroomingReportTemplateExample();
            MoeGroomingReportTemplateExample.Criteria criteria = example.createCriteria(); // 只迁移正常状态的模板

            if (businessId != null) {
                criteria.andBusinessIdEqualTo(businessId);
            }

            List<MoeGroomingReportTemplate> sourceTemplates = sourceMapper.selectByExample(example);
            log.info("查询到模板源数据 {} 条", sourceTemplates.size());

            if (sourceTemplates.isEmpty()) {
                return ReportCardMigrateUtils.buildSuccessResult(
                        taskId, startTime,
                        ReportCardMigrateConfig.TableNames.TEMPLATE,
                        ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_TEMPLATE,
                        ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_TEMPLATE,
                        0, 0, 0
                );
            }

            // 2. 分批处理数据迁移
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;

            for (MoeGroomingReportTemplate sourceTemplate : sourceTemplates) {
                try {
                    FulfillmentReportTemplateUniqueKey uniqueKey = FulfillmentReportTemplateUniqueKey.newBuilder()
                             .setBusinessId(sourceTemplate.getBusinessId())
                             .setCompanyId(sourceTemplate.getCompanyId())
                             .setCareType(CareType.CARE_TYPE_GROOMING)
                             .build();
                    // 检查目标表中是否已存在
                    var templates = fulfillmentServiceClient.getTemplatesByUniqueKeys(
                        GetTemplatesByUniqueKeysRequest.newBuilder()
                        .addAllUniqueKeys(List.of(uniqueKey))
                        .build());
                    if (templates.getTemplatesCount() > 0) {
                        log.debug("template 已存在，跳过迁移: businessId={}, id={}",
                                sourceTemplate.getBusinessId(), sourceTemplate.getId());
                        skippedCount++;
                        continue;
                    }

                    boolean shouldSkip = false;


                    if (shouldSkip) {
                        log.debug("模板已存在，跳过迁移: businessId={}, id={}",
                                sourceTemplate.getBusinessId(), sourceTemplate.getId());
                        skippedCount++;
                        continue;
                    }

                    // TODO: 转换数据并通过fulfillment服务接口进行插入
                    // 需要先获取新的theme_id
                     Integer newThemeConfigId = ReportCardMigrateUtils.getNewId(
                         ReportCardMigrateConfig.TableNames.TEMPLATE, sourceTemplate.getId());

                    // FulfillmentReportTemplateDTO targetTemplate = convertTemplate(sourceTemplate, newThemeConfigId);
                     Integer newId = fulfillmentServiceClient.batchMigrateTemplates( BatchMigrateTemplatesRequest.newBuilder()
                         .build());

                    // 存储ID映射关系
//                     ReportCardMigrateUtils.storeIdMapping(
//                         ReportCardMigrateConfig.TableNames.TEMPLATE, sourceTemplate.getId(), newId);

                    log.debug("迁移模板: businessId={}, id={}",
                            sourceTemplate.getBusinessId(), sourceTemplate.getId());
                    migratedCount++;

                } catch (Exception e) {
                    log.error("迁移模板失败: businessId={}, id={}",
                            sourceTemplate.getBusinessId(), sourceTemplate.getId(), e);
                    failedCount++;
                }
            }

            log.info("模板数据迁移完成，总数: {}, 成功: {}, 跳过: {}, 失败: {}",
                    sourceTemplates.size(), migratedCount, skippedCount, failedCount);

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId, startTime,
                    ReportCardMigrateConfig.TableNames.TEMPLATE,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_TEMPLATE,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_TEMPLATE,
                    sourceTemplates.size(), migratedCount, skippedCount
            );

        } catch (Exception e) {
            log.error("模板数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }
}
