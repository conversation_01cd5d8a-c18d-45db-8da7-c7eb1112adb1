package com.moego.server.grooming.service.report.migrate;

import com.moego.backend.proto.fulfillment.v1.BatchMigrateTemplatesRequest;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateTemplatesResponse;
import com.moego.backend.proto.fulfillment.v1.CareType;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse;
import com.moego.server.grooming.config.ReportCardMigrateConfig;
import com.moego.server.grooming.convert.ReportCardMigrateConverter;
import com.moego.server.grooming.mapper.MoeGroomingReportTemplateMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplateExample;
import com.moego.server.grooming.service.utils.ReportCardMigrateUtils;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Report Card 模板迁移服务
 * 负责将 moe_grooming_report_template 数据迁移到 fulfillment_report_template
 *
 * 核心原则：
 * 1. 零模式修改：不修改新表结构
 * 2. 引用完整性：保持数据间关联关系
 * 3. 注意表与表之间的id关联，需要直接使用新表的id，在迁移过程中进行新旧表id的替换
 * 4. 数据量较大的表迁移需考虑性能，例如分片处理
 * 5. 通过调用fulfillment服务接口进行数据操作
 * 6. 可追溯性：完整记录迁移过程
 * 7. 可回滚性：确保问题发生时可快速恢复
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardTemplateMigrateService {

    private final MoeGroomingReportTemplateMapper sourceMapper;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;
    private final ReportCardMigrateConverter migrateConverter;

    /**
     * 迁移模板数据
     */
    @Transactional
    public ReportCardMigrateResultDTO migrate(Integer businessId) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "template_" + System.currentTimeMillis();

        log.info("开始迁移模板数据，taskId: {}, businessId: {}",
                taskId, businessId);

        try {
            // 1. 查询源数据
            MoeGroomingReportTemplateExample example = new MoeGroomingReportTemplateExample();
            MoeGroomingReportTemplateExample.Criteria criteria = example.createCriteria(); // 只迁移正常状态的模板

            if (businessId != null) {
                criteria.andBusinessIdEqualTo(businessId);
            }

            List<MoeGroomingReportTemplate> sourceTemplates = sourceMapper.selectByExample(example);
            log.info("查询到模板源数据 {} 条", sourceTemplates.size());

            if (sourceTemplates.isEmpty()) {
                return ReportCardMigrateUtils.buildSuccessResult(
                        taskId, startTime,
                        ReportCardMigrateConfig.TableNames.TEMPLATE,
                        ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_TEMPLATE,
                        ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_TEMPLATE,
                        0, 0, 0
                );
            }

            // 2. 分批处理数据迁移
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;

            // 按批次处理数据，提高性能
            int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
            for (int i = 0; i < sourceTemplates.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, sourceTemplates.size());
                List<MoeGroomingReportTemplate> batch = sourceTemplates.subList(i, endIndex);

                log.debug("处理第 {}/{} 批数据，数量: {}",
                         (i / batchSize) + 1, (sourceTemplates.size() + batchSize - 1) / batchSize, batch.size());

                try {
                    // 批量检查是否已存在
                    List<FulfillmentReportTemplateUniqueKey> uniqueKeys = new ArrayList<>();
                    for (MoeGroomingReportTemplate template : batch) {
                        uniqueKeys.add(FulfillmentReportTemplateUniqueKey.newBuilder()
                                .setBusinessId(template.getBusinessId())
                                .setCompanyId(template.getCompanyId())
                                .setCareType(CareType.CARE_TYPE_GROOMING)
                                .build());
                    }

                    GetTemplatesByUniqueKeysResponse existingTemplates = fulfillmentServiceClient.getTemplatesByUniqueKeys(
                            GetTemplatesByUniqueKeysRequest.newBuilder()
                                    .addAllUniqueKeys(uniqueKeys)
                                    .build());

                    // 过滤出需要迁移的模板（不存在的）
                    List<MoeGroomingReportTemplate> templatesToMigrate = new ArrayList<>();
                    for (MoeGroomingReportTemplate template : batch) {
                        boolean exists = existingTemplates.getTemplatesList().stream()
                                .anyMatch(existing ->
                                    existing.getBusinessId() == template.getBusinessId() &&
                                    existing.getCompanyId() == template.getCompanyId());

                        if (exists) {
                            log.debug("模板已存在，跳过迁移: businessId={}, companyId={}, id={}",
                                    template.getBusinessId(), template.getCompanyId(), template.getId());
                            skippedCount++;
                        } else {
                            templatesToMigrate.add(template);
                        }
                    }

                    if (!templatesToMigrate.isEmpty()) {
                        // 转换数据
                        List<FulfillmentReportTemplateSync> convertedTemplates =
                                migrateConverter.convertTemplates(templatesToMigrate);

                        // 验证转换结果
                        List<FulfillmentReportTemplateSync> validTemplates = new ArrayList<>();
                        for (FulfillmentReportTemplateSync converted : convertedTemplates) {
                            if (migrateConverter.validateConvertedTemplate(converted)) {
                                validTemplates.add(converted);
                            } else {
                                log.warn("转换后的模板数据验证失败，跳过");
                                failedCount++;
                            }
                        }

                        if (!validTemplates.isEmpty()) {
                            // 批量迁移
                            BatchMigrateTemplatesRequest request = BatchMigrateTemplatesRequest.newBuilder()
                                    .addAllTemplates(validTemplates)
                                    .build();

                            BatchMigrateTemplatesResponse response = fulfillmentServiceClient.batchMigrateTemplates(request);

                            migratedCount += response.getSuccessCount();
                            skippedCount += response.getSkippedCount();
                            failedCount += response.getFailedCount();

                            log.debug("批量迁移完成: 成功={}, 跳过={}, 失败={}",
                                     response.getSuccessCount(), response.getSkippedCount(), response.getFailedCount());
                        }
                    }

                } catch (Exception e) {
                    log.error("批量迁移模板失败，批次: {}-{}", i, endIndex - 1, e);
                    failedCount += batch.size();
                }
            }

            // 记录转换统计信息
            migrateConverter.logConversionStats(sourceTemplates.size(), (int) migratedCount, (int) failedCount);

            log.info("模板数据迁移完成，总数: {}, 成功: {}, 跳过: {}, 失败: {}",
                    sourceTemplates.size(), migratedCount, skippedCount, failedCount);

            // 记录迁移统计信息
            ReportCardMigrateUtils.logMigrationStats(
                    ReportCardMigrateConfig.TableNames.TEMPLATE,
                    sourceTemplates.size(), migratedCount, skippedCount, failedCount,
                    System.currentTimeMillis() - startTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli()
            );

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId, startTime,
                    ReportCardMigrateConfig.TableNames.TEMPLATE,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_TEMPLATE,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_TEMPLATE,
                    sourceTemplates.size(), migratedCount, skippedCount
            );

        } catch (Exception e) {
            log.error("模板数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }
}
