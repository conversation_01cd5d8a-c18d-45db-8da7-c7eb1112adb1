package com.moego.server.grooming.service.report;

import com.moego.server.grooming.mapper.MoeGroomingReportThemeConfigMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingReportThemeConfig;
import com.moego.server.grooming.mapperbean.MoeGroomingReportThemeConfigExample;
import com.moego.server.grooming.mapperbean.fulfillment.FulfillmentReportThemeConfig;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Report Card 主题配置迁移服务
 * 负责将 moe_grooming_report_theme_config 数据迁移到 fulfillment_report_theme_config
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardThemeConfigMigrateService {

    private final MoeGroomingReportThemeConfigMapper sourceMapper;
    private final FulfillmentReportThemeConfigMapper targetMapper;

    /**
     * 迁移主题配置数据
     */
    @Transactional
    public ReportCardMigrateResultDTO migrate(Boolean forceReplace) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "theme_config_" + System.currentTimeMillis();

        log.info("开始迁移主题配置数据，taskId: {}, forceReplace: {}", taskId, forceReplace);

        try {
            // 1. 查询源数据
            MoeGroomingReportThemeConfigExample example = new MoeGroomingReportThemeConfigExample();
            example.createCriteria().andStatusEqualTo((byte) 1); // 只迁移活跃状态的主题
            List<MoeGroomingReportThemeConfig> sourceConfigs = sourceMapper.selectByExample(example);

            log.info("查询到源主题配置数据 {} 条", sourceConfigs.size());

            if (sourceConfigs.isEmpty()) {
                return buildSuccessResult(taskId, startTime, 0, 0, 0);
            }

            // 2. 数据转换和迁移
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;

            for (MoeGroomingReportThemeConfig sourceConfig : sourceConfigs) {
                try {
                    // 检查目标表中是否已存在
                    FulfillmentReportThemeConfig existingConfig = targetMapper.selectByCode(sourceConfig.getCode());

                    if (existingConfig != null && !forceReplace) {
                        log.debug("主题配置已存在，跳过迁移: code={}", sourceConfig.getCode());
                        skippedCount++;
                        continue;
                    }

                    // 转换数据
                    FulfillmentReportThemeConfig targetConfig = convertThemeConfig(sourceConfig);

                    // 插入或更新
                    if (existingConfig != null && forceReplace) {
                        targetConfig.setId(existingConfig.getId());
                        targetMapper.updateByPrimaryKeySelective(targetConfig);
                        log.debug("更新主题配置: code={}", sourceConfig.getCode());
                    } else {
                        targetMapper.insertSelective(targetConfig);
                        log.debug("插入主题配置: code={}", sourceConfig.getCode());
                    }

                    migratedCount++;

                } catch (Exception e) {
                    log.error("迁移主题配置失败: code={}", sourceConfig.getCode(), e);
                    failedCount++;
                }
            }

            LocalDateTime endTime = LocalDateTime.now();
            log.info("主题配置迁移完成，总数: {}, 成功: {}, 跳过: {}, 失败: {}",
                    sourceConfigs.size(), migratedCount, skippedCount, failedCount);

            return buildSuccessResult(taskId, startTime, sourceConfigs.size(), migratedCount, skippedCount);

        } catch (Exception e) {
            log.error("主题配置迁移过程中发生异常", e);
            return buildFailureResult(taskId, startTime, e.getMessage());
        }
    }

    /**
     * 转换主题配置数据
     */
    private FulfillmentReportThemeConfig convertThemeConfig(MoeGroomingReportThemeConfig source) {
        FulfillmentReportThemeConfig target = new FulfillmentReportThemeConfig();

        // 基本字段映射
        target.setName(source.getName());
        target.setCode(source.getCode());
        target.setColor(source.getColor());
        target.setLightColor(source.getLightColor());
        target.setImgUrl(source.getImgUrl());
        target.setIcon(source.getIcon());
        target.setEmailBottomImgUrl(source.getEmailBottomImgUrl());
        target.setIsRecommend(source.getRecommend());
        target.setVisibleLevel(source.getVisibleLevel());
        target.setFlag(source.getFlag().shortValue());
        target.setSort(source.getSort().shortValue());

        // 状态转换：MySQL tinyint -> PostgreSQL smallint
        target.setStatus(source.getStatus().shortValue());

        // 时间字段
        target.setCreateTime(source.getCreateTime());
        target.setUpdateTime(source.getUpdateTime());

        return target;
    }

    /**
     * 构建成功结果
     */
    private ReportCardMigrateResultDTO buildSuccessResult(String taskId, LocalDateTime startTime,
                                                          long sourceCount, long migratedCount, long skippedCount) {
        LocalDateTime endTime = LocalDateTime.now();
        return ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(true)
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(java.time.Duration.between(startTime, endTime).toMillis())
                .tableDetails(Map.of("theme_config",
                        ReportCardMigrateResultDTO.TableMigrateDetail.builder()
                                .tableName("theme_config")
                                .sourceTable("moe_grooming_report_theme_config")
                                .targetTable("fulfillment_report_theme_config")
                                .success(true)
                                .sourceCount(sourceCount)
                                .migratedCount(migratedCount)
                                .skippedCount(skippedCount)
                                .failedCount(0L)
                                .startTime(startTime)
                                .endTime(endTime)
                                .durationMs(java.time.Duration.between(startTime, endTime).toMillis())
                                .build()))
                .build();
    }

    /**
     * 构建失败结果
     */
    private ReportCardMigrateResultDTO buildFailureResult(String taskId, LocalDateTime startTime, String errorMessage) {
        LocalDateTime endTime = LocalDateTime.now();
        return ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(false)
                .errorMessage(errorMessage)
                .startTime(startTime)
                .endTime(endTime)
                .durationMs(java.time.Duration.between(startTime, endTime).toMillis())
                .build();
    }
}
