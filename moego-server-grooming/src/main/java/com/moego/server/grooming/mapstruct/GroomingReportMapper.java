package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportResourceDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportThemeConfigDTO;
import com.moego.server.grooming.dto.report.AppointmentReportDTO;
import com.moego.server.grooming.dto.report.OperationReportDTO;
import com.moego.server.grooming.dto.report.PetDetailReportDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportResource;
import com.moego.server.grooming.mapperbean.MoeGroomingReportThemeConfig;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import java.util.Date;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GroomingReportMapper {

    GroomingReportMapper INSTANCE = Mappers.getMapper(GroomingReportMapper.class);

    @Mappings({
        @Mapping(source = "submittedTime", target = "submittedTime", qualifiedByName = "toTimestamp"),
        @Mapping(source = "createTime", target = "createTime", qualifiedByName = "toTimestamp"),
        @Mapping(source = "updateTime", target = "updateTime", qualifiedByName = "toTimestamp"),
        @Mapping(source = "templatePublishTime", target = "templatePublishTime", qualifiedByName = "toTimestamp")
    })
    GroomingReportDTO entity2DTO(MoeGroomingReport entity);

    GroomingReportThemeConfigDTO entity2ThemeConfigDTO(MoeGroomingReportThemeConfig entity);

    GroomingReportResourceDTO entity2ResourceDTO(MoeGroomingReportResource entity);

    AppointmentReportDTO toAppointmentReporting(MoeGroomingAppointment entity);

    PetDetailReportDTO toPetDetailReporting(MoeGroomingPetDetail entity);

    OperationReportDTO toOperationReporting(MoeGroomingServiceOperation entity);

    @Named("toTimestamp")
    default Long toTimestamp(Date date) {
        if (date == null) {
            return null;
        }
        return date.getTime() / 1000;
    }
}
