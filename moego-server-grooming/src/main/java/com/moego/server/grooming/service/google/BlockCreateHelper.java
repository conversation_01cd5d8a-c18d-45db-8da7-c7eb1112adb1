package com.moego.server.grooming.service.google;

import com.google.api.client.util.DateTime;
import com.moego.server.grooming.params.AppointmentBlockParams;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;
import org.springframework.util.StringUtils;

public class BlockCreateHelper {

    public static List<AppointmentBlockParams> conventEventToBlock(
            com.google.api.services.calendar.model.Event event, String timezoneName) {
        // AppointmentBlockParams appointmentBlockParams
        String desc = null;
        if (!StringUtils.isEmpty(event.getSummary())) {
            desc = "Event title\n";
            desc += event.getSummary();
        }
        DateTime startTime = event.getStart().getDateTime();
        DateTime endTime = event.getEnd().getDateTime();
        List<AppointmentBlockParams> paramList = new ArrayList<>();
        long startTimeLong = 0;
        long endTimeLong = 0;
        if (startTime == null) {
            startTimeLong = event.getStart().getDate().getValue();
        } else {
            startTimeLong = startTime.getValue();
        }
        if (endTime == null) {
            endTimeLong = event.getEnd().getDate().getValue() - 1;
        } else {
            endTimeLong = endTime.getValue();
        }
        LocalDateTime startDateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(startTimeLong),
                TimeZone.getTimeZone(timezoneName).toZoneId());
        LocalDateTime endDateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(endTimeLong),
                TimeZone.getTimeZone(timezoneName).toZoneId());

        DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 判断年月日是否在同一天
        if (startDateTime.getYear() != endDateTime.getYear()
                || startDateTime.getDayOfYear() != endDateTime.getDayOfYear()) {
            // 跨多天的block，拆分
            Long durationStartDay = Duration.between(LocalDateTime.of(1970, Month.JANUARY, 1, 0, 0), startDateTime)
                    .toDays();
            Long durationEndDay = Duration.between(LocalDateTime.of(1970, Month.JANUARY, 1, 0, 0), endDateTime)
                    .toDays();
            int maxI = (int) (durationEndDay - durationStartDay);
            for (int i = 0; i <= maxI; i++) {
                AppointmentBlockParams param = new AppointmentBlockParams();
                if (i == 0) {
                    // 第一天
                    param.setStartTime(startDateTime.getHour() * 60 + startDateTime.getMinute());
                    param.setEndTime(24 * 60);
                } else if (i != maxI) {
                    // 中间
                    param.setStartTime(0);
                    param.setEndTime(24 * 60);
                } else {
                    // 最后一天
                    param.setStartTime(0);
                    param.setEndTime(endDateTime.getHour() * 60 + endDateTime.getMinute());
                }
                param.setAppointmentDate(startDateTime.plusDays(i).format(sdf));
                param.setDesc(desc);
                paramList.add(param);
            }
        } else {
            AppointmentBlockParams param = new AppointmentBlockParams();
            param.setAppointmentDate(startDateTime.format(sdf));
            param.setStartTime(startDateTime.getHour() * 60 + startDateTime.getMinute());
            param.setEndTime(endDateTime.getHour() * 60 + endDateTime.getMinute());
            param.setDesc(desc);
            paramList.add(param);
        }
        return paramList;
    }
}
