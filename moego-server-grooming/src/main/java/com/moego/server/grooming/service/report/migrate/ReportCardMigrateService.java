package com.moego.server.grooming.service.report.migrate;

import com.moego.server.grooming.web.dto.ReportCardMigrateProgressDTO;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * Report Card 数据迁移服务
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardMigrateService {

    private final ReportCardThemeConfigMigrateService themeConfigMigrateService;
    private final ReportCardTemplateMigrateService templateMigrateService;
    private final ReportCardReportMigrateService reportMigrateService;
    private final ReportCardQuestionMigrateService questionMigrateService;
    private final ReportCardSendRecordMigrateService sendRecordMigrateService;

    @Resource(name = "developScriptExecutorService")
    private ExecutorService executorService;

    // 存储任务进度的Map
    private final Map<String, ReportCardMigrateProgressDTO> taskProgressMap = new ConcurrentHashMap<>();
    private final Map<String, Future<?>> taskFutureMap = new ConcurrentHashMap<>();

    /**
     * 迁移顺序定义
     */
    private static final List<String> MIGRATION_ORDER = Arrays.asList(
            "theme_config",
            "template",
            "report",
            "question",
            "send_record"
    );

    /**
     * 启动完整的数据迁移
     */
    public ReportCardMigrateResultDTO startFullMigration(Integer businessId) {
        String taskId = generateTaskId();
        LocalDateTime startTime = LocalDateTime.now();

        log.info("启动完整数据迁移，taskId: {}, businessId: {}",
                taskId, businessId);

        // 初始化进度跟踪
        ReportCardMigrateProgressDTO progress = ReportCardMigrateProgressDTO.builder()
                .taskId(taskId)
                .status(ReportCardMigrateProgressDTO.TaskStatus.RUNNING)
                .startTime(startTime)
                .progressPercent(0)
                .completedTables(new ArrayList<>())
                .pendingTables(new ArrayList<>(MIGRATION_ORDER))
                .currentMessage("开始数据迁移...")
                .build();
        taskProgressMap.put(taskId, progress);

        // 异步执行迁移任务
        Future<?> future = executorService.submit(() -> {
            try {
                executeFullMigration(taskId, businessId);
            } catch (Exception e) {
                log.error("数据迁移执行失败，taskId: {}", taskId, e);
                updateProgressError(taskId, e.getMessage());
            }
        });
        taskFutureMap.put(taskId, future);

        return ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(true)
                .startTime(startTime)
                .businessIds(businessId != null ? List.of(businessId) : null)
                .build();
    }

    /**
     * 执行完整迁移流程
     */
    private void executeFullMigration(String taskId, Integer businessId) {
        Map<String, ReportCardMigrateResultDTO.TableMigrateDetail> tableDetails = new HashMap<>();
        LocalDateTime overallStartTime = LocalDateTime.now();
        boolean overallSuccess = true;
        String overallErrorMessage = null;

        try {
            // 1. 迁移主题配置
//            updateProgress(taskId, 0, "theme_config", "正在迁移主题配置数据...");
//            ReportCardMigrateResultDTO themeResult = themeConfigMigrateService.migrate(forceReplace);
//            tableDetails.put("theme_config", convertToTableDetail(themeResult, "theme_config"));
//            updateProgress(taskId, 20, null, "主题配置迁移完成");

            // 2. 迁移模板
            updateProgress(taskId, 20, "template", "正在迁移模板数据...");
            ReportCardMigrateResultDTO templateResult = templateMigrateService.migrate(businessId, forceReplace);
            tableDetails.put("template", convertToTableDetail(templateResult, "template"));
            updateProgress(taskId, 40, null, "模板迁移完成");

            // 3. 迁移报告
            updateProgress(taskId, 40, "report", "正在迁移报告数据...");
            ReportCardMigrateResultDTO reportResult = reportMigrateService.migrate(businessId);
            tableDetails.put("report", convertToTableDetail(reportResult, "report"));
            updateProgress(taskId, 60, null, "报告迁移完成");

            // 4. 迁移问题
            updateProgress(taskId, 60, "question", "正在迁移问题数据...");
            ReportCardMigrateResultDTO questionResult = questionMigrateService.migrate(businessId);
            tableDetails.put("question", convertToTableDetail(questionResult, "question"));
            updateProgress(taskId, 80, null, "问题迁移完成");

            // 5. 迁移发送记录
            updateProgress(taskId, 80, "send_record", "正在迁移发送记录数据...");
            ReportCardMigrateResultDTO sendRecordResult = sendRecordMigrateService.migrate(businessId);
            tableDetails.put("send_record", convertToTableDetail(sendRecordResult, "send_record"));
            updateProgress(taskId, 100, null, "所有数据迁移完成");

            // 检查是否有失败的表
            overallSuccess = tableDetails.values().stream().allMatch(ReportCardMigrateResultDTO.TableMigrateDetail::getSuccess);
            if (!overallSuccess) {
                overallErrorMessage = "部分表迁移失败，请查看详细信息";
            }

        } catch (Exception e) {
            log.error("迁移过程中发生异常，taskId: {}", taskId, e);
            overallSuccess = false;
            overallErrorMessage = e.getMessage();
            updateProgressError(taskId, e.getMessage());
        }

        // 更新最终结果
        LocalDateTime endTime = LocalDateTime.now();
        ReportCardMigrateResultDTO finalResult = ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(overallSuccess)
                .errorMessage(overallErrorMessage)
                .startTime(overallStartTime)
                .endTime(endTime)
                .durationMs(java.time.Duration.between(overallStartTime, endTime).toMillis())
                .businessIds(businessId != null ? Arrays.asList(businessId) : null)
                .tableDetails(tableDetails)
                .summary(buildSummary(tableDetails))
                .build();

        // 更新进度为完成状态
        ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
        if (progress != null) {
            progress.setStatus(overallSuccess ?
                    ReportCardMigrateProgressDTO.TaskStatus.COMPLETED :
                    ReportCardMigrateProgressDTO.TaskStatus.FAILED);
            progress.setProgressPercent(100);
            progress.setCurrentMessage(overallSuccess ? "迁移完成" : "迁移失败: " + overallErrorMessage);
            progress.setErrorMessage(overallErrorMessage);
        }

        log.info("完整数据迁移结束，taskId: {}, success: {}, duration: {}ms",
                taskId, overallSuccess, finalResult.getDurationMs());
    }

    /**
     * 迁移主题配置数据
     */
    public ReportCardMigrateResultDTO migrateThemeConfig(Boolean forceReplace) {
        log.info("开始迁移主题配置数据，forceReplace: {}", forceReplace);
        return themeConfigMigrateService.migrate(forceReplace);
    }

    /**
     * 迁移模板数据
     */
    public ReportCardMigrateResultDTO migrateTemplate(Integer businessId, Boolean forceReplace) {
        log.info("开始迁移模板数据，businessId: {}, forceReplace: {}", businessId, forceReplace);
        return templateMigrateService.migrate(businessId, forceReplace);
    }

    /**
     * 迁移报告数据
     */
    public ReportCardMigrateResultDTO migrateReport(Integer businessId, Boolean forceReplace) {
        log.info("开始迁移报告数据，businessId: {}, forceReplace: {}", businessId, forceReplace);
        return reportMigrateService.migrate(businessId, forceReplace);
    }

    /**
     * 迁移问题数据
     */
    public ReportCardMigrateResultDTO migrateQuestion(Integer businessId, Boolean forceReplace) {
        log.info("开始迁移问题数据，businessId: {}, forceReplace: {}", businessId, forceReplace);
        return questionMigrateService.migrate(businessId, forceReplace);
    }

    /**
     * 迁移发送记录数据
     */
    public ReportCardMigrateResultDTO migrateSendRecord(Integer businessId, Boolean forceReplace) {
        log.info("开始迁移发送记录数据，businessId: {}, forceReplace: {}", businessId, forceReplace);
        return sendRecordMigrateService.migrate(businessId, forceReplace);
    }

    /**
     * 获取迁移进度
     */
    public ReportCardMigrateProgressDTO getProgress(String taskId) {
        ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
        if (progress == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }
        return progress;
    }

    /**
     * 停止迁移任务
     */
    public void stopMigration(String taskId) {
        Future<?> future = taskFutureMap.get(taskId);
        if (future != null && !future.isDone()) {
            future.cancel(true);
            updateProgress(taskId, null, null, "任务已取消");
            ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
            if (progress != null) {
                progress.setStatus(ReportCardMigrateProgressDTO.TaskStatus.CANCELLED);
            }
        }
        taskFutureMap.remove(taskId);
    }

    /**
     * 验证迁移数据
     */
    public ReportCardMigrateResultDTO validateMigration(Integer businessId) {
        log.info("开始验证迁移数据，businessId: {}", businessId);
        // TODO: 实现数据验证逻辑
        return ReportCardMigrateResultDTO.builder()
                .success(true)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 回滚迁移数据
     */
    @Transactional
    public ReportCardMigrateResultDTO rollbackMigration(Integer businessId, String tableName) {
        log.info("开始回滚迁移数据，businessId: {}, tableName: {}", businessId, tableName);
        // TODO: 实现回滚逻辑
        return ReportCardMigrateResultDTO.builder()
                .success(true)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    // ==================== 私有辅助方法 ====================

    private String generateTaskId() {
        return "migrate_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    private void updateProgress(String taskId, Integer progressPercent, String currentTable, String message) {
        ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
        if (progress != null) {
            if (progressPercent != null) {
                progress.setProgressPercent(progressPercent);
            }
            if (currentTable != null) {
                progress.setCurrentTable(currentTable);
                progress.getPendingTables().remove(currentTable);
                if (!progress.getCompletedTables().contains(currentTable)) {
                    progress.getCompletedTables().add(currentTable);
                }
            }
            progress.setCurrentMessage(message);
        }
    }

    private void updateProgressError(String taskId, String errorMessage) {
        ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
        if (progress != null) {
            progress.setStatus(ReportCardMigrateProgressDTO.TaskStatus.FAILED);
            progress.setErrorMessage(errorMessage);
            progress.setCurrentMessage("迁移失败: " + errorMessage);
        }
    }

    private ReportCardMigrateResultDTO.TableMigrateDetail convertToTableDetail(
            ReportCardMigrateResultDTO result, String tableName) {
        // 从结果中提取表详情，这里简化处理
        return ReportCardMigrateResultDTO.TableMigrateDetail.builder()
                .tableName(tableName)
                .success(result.getSuccess())
                .errorMessage(result.getErrorMessage())
                .startTime(result.getStartTime())
                .endTime(result.getEndTime())
                .durationMs(result.getDurationMs())
                .build();
    }

    private ReportCardMigrateResultDTO.MigrateSummary buildSummary(
            Map<String, ReportCardMigrateResultDTO.TableMigrateDetail> tableDetails) {
        int totalTables = tableDetails.size();
        int successTables = (int) tableDetails.values().stream().mapToLong(detail -> detail.getSuccess() ? 1 : 0).sum();
        int failedTables = totalTables - successTables;

        return ReportCardMigrateResultDTO.MigrateSummary.builder()
                .totalTables(totalTables)
                .successTables(successTables)
                .failedTables(failedTables)
                .build();
    }
}
