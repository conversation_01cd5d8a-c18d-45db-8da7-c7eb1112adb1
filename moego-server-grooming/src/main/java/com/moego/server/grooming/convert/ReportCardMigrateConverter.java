package com.moego.server.grooming.convert;

import com.google.protobuf.Timestamp;
import com.moego.backend.proto.fulfillment.v1.CareType;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateSync;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Report Card 数据迁移转换器
 * 负责将旧表模型转换为新表迁移接口所需的数据结构
 *
 * 核心功能：
 * 1. MoeGroomingReportTemplate -> FulfillmentReportTemplateSync
 * 2. 数据类型转换和字段映射
 * 3. 批量转换支持
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Component
@Slf4j
public class ReportCardMigrateConverter {

    /**
     * 将 MoeGroomingReportTemplate 转换为 FulfillmentReportTemplateSync
     *
     * @param source 源模板数据
     * @return 转换后的模板数据
     */
    public FulfillmentReportTemplateSync convertTemplate(MoeGroomingReportTemplate source) {
        if (source == null) {
            return null;
        }

        log.debug("转换模板数据: businessId={}, id={}", source.getBusinessId(), source.getId());

        FulfillmentReportTemplateSync.Builder builder = FulfillmentReportTemplateSync.newBuilder()
                // 基础信息
                .setCompanyId(source.getCompanyId() != null ? source.getCompanyId() : 0L)
                .setBusinessId(source.getBusinessId() != null ? source.getBusinessId() : 0L)
                .setCareType(CareType.CARE_TYPE_GROOMING)
                
                // 模板内容
                .setThankYouMessage(source.getThankYouMessage() != null ? source.getThankYouMessage() : "")
                .setThemeColor(source.getThemeColor() != null ? source.getThemeColor() : "")
                .setLightThemeColor(source.getLightThemeColor() != null ? source.getLightThemeColor() : "")
                
                // 显示配置
                .setShowShowcase(source.getShowShowcase() != null ? source.getShowShowcase() : false)
                .setShowOverallFeedback(source.getShowOverallFeedback() != null ? source.getShowOverallFeedback() : false)
                .setRequireBeforePhoto(source.getRequireBeforePhoto() != null ? source.getRequireBeforePhoto() : false)
                .setRequireAfterPhoto(source.getRequireAfterPhoto() != null ? source.getRequireAfterPhoto() : false)
                .setShowPetCondition(source.getShowPetCondition() != null ? source.getShowPetCondition() : false)
                .setShowServiceStaffName(source.getShowServiceStaffName() != null ? source.getShowServiceStaffName() : false)
                .setShowNextAppointment(source.getShowNextAppointment() != null ? source.getShowNextAppointment() : false)
                
                // 评价相关配置
                .setShowReviewBooster(source.getShowReviewBooster() != null ? source.getShowReviewBooster() : false)
                .setShowYelpReview(source.getShowYelpReview() != null ? source.getShowYelpReview() : false)
                .setShowGoogleReview(source.getShowGoogleReview() != null ? source.getShowGoogleReview() : false)
                .setShowFacebookReview(source.getShowFacebookReview() != null ? source.getShowFacebookReview() : false)
                
                // 其他字段
                .setTitle(source.getTitle() != null ? source.getTitle() : "")
                .setUpdateBy(source.getUpdateBy() != null ? source.getUpdateBy() : 0L);

        // 处理下次预约日期格式类型
        if (source.getNextAppointmentDateFormatType() != null) {
            builder.setNextAppointmentDateFormatType(source.getNextAppointmentDateFormatType().intValue());
        }

        // 处理主题代码
        if (source.getThemeCode() != null) {
            builder.setThemeCode(source.getThemeCode());
        }

        // 处理时间字段
        if (source.getLastPublishTime() != null) {
            builder.setLastPublishTime(dateToTimestamp(source.getLastPublishTime()));
        }
        
        if (source.getCreateTime() != null) {
            builder.setCreateTime(dateToTimestamp(source.getCreateTime()));
        }
        
        if (source.getUpdateTime() != null) {
            builder.setUpdateTime(dateToTimestamp(source.getUpdateTime()));
        }

        return builder.build();
    }

    /**
     * 批量转换模板数据
     *
     * @param sourceList 源模板数据列表
     * @return 转换后的模板数据列表
     */
    public List<FulfillmentReportTemplateSync> convertTemplates(List<MoeGroomingReportTemplate> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("批量转换模板数据，数量: {}", sourceList.size());

        List<FulfillmentReportTemplateSync> result = new ArrayList<>(sourceList.size());
        for (MoeGroomingReportTemplate source : sourceList) {
            try {
                FulfillmentReportTemplateSync converted = convertTemplate(source);
                if (converted != null) {
                    result.add(converted);
                }
            } catch (Exception e) {
                log.error("转换模板数据失败: businessId={}, id={}", 
                         source.getBusinessId(), source.getId(), e);
                // 继续处理其他数据，不中断整个批量转换过程
            }
        }

        log.debug("批量转换完成，成功转换: {}/{}", result.size(), sourceList.size());
        return result;
    }

    /**
     * 将 Java Date 转换为 Protobuf Timestamp
     *
     * @param date Java Date对象
     * @return Protobuf Timestamp对象
     */
    private Timestamp dateToTimestamp(Date date) {
        if (date == null) {
            return Timestamp.getDefaultInstance();
        }
        
        long seconds = date.getTime() / 1000;
        int nanos = (int) ((date.getTime() % 1000) * 1_000_000);
        
        return Timestamp.newBuilder()
                .setSeconds(seconds)
                .setNanos(nanos)
                .build();
    }

    /**
     * 验证转换后的数据
     *
     * @param template 转换后的模板数据
     * @return 是否有效
     */
    public boolean validateConvertedTemplate(FulfillmentReportTemplateSync template) {
        if (template == null) {
            log.warn("转换后的模板数据为空");
            return false;
        }

        // 验证必填字段
        if (template.getCompanyId() <= 0) {
            log.warn("公司ID无效: {}", template.getCompanyId());
            return false;
        }

        if (template.getBusinessId() <= 0) {
            log.warn("业务ID无效: {}", template.getBusinessId());
            return false;
        }

        if (template.getCareType() == CareType.CARE_TYPE_UNSPECIFIED) {
            log.warn("护理类型未指定");
            return false;
        }

        return true;
    }

    /**
     * 记录转换统计信息
     *
     * @param sourceCount 源数据数量
     * @param convertedCount 成功转换数量
     * @param failedCount 转换失败数量
     */
    public void logConversionStats(int sourceCount, int convertedCount, int failedCount) {
        log.info("模板数据转换统计: 源数据={}, 成功转换={}, 转换失败={}, 成功率={:.2f}%",
                sourceCount, convertedCount, failedCount,
                sourceCount > 0 ? (convertedCount * 100.0 / sourceCount) : 100.0);
    }
}
