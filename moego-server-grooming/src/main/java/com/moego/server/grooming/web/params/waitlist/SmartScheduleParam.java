package com.moego.server.grooming.web.params.waitlist;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Builder;
import org.hibernate.validator.constraints.Range;

@Builder(toBuilder = true)
public record SmartScheduleParam(
        @Schema(description = "business id", hidden = true) Long businessId,
        @Schema(description = "token staff id", hidden = true) Long tokenStaffId,
        @Range(min = 1) @NotNull @Schema(description = "Wait list id") Long waitListId,
        @Schema(description = "available slot query range start, yyyy-MM-ddTHH:mm:ss") LocalDateTime startDateTime,
        @Schema(description = "available slot range end, yyyy-MM-ddTHH:mm:ss") LocalDateTime endDateTime,
        @Range(min = 1) @NotNull @Schema(description = "Page number") Integer pageNum,
        @Range(min = 1, max = 100) @NotNull @Schema(description = "Page size") Integer pageSize) {}
