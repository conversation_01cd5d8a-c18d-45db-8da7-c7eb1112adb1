package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.intuit.ipp.core.IEntity;
import com.intuit.ipp.data.Account;
import com.intuit.ipp.services.QueryResult;
import com.intuit.oauth2.config.OAuth2Config;
import com.intuit.oauth2.config.Scope;
import com.intuit.oauth2.exception.InvalidRequestException;
import com.moego.common.enums.QuickBooksConst;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.service.metadata.v1.ExtractValuesResponse;
import com.moego.idl.service.metadata.v1.GetKeyResponse;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.ratelimit.v1.RateLimitServiceGrpc;
import com.moego.idl.service.ratelimit.v1.RegisterRulesResponse;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeQbConnectMapper;
import com.moego.server.grooming.mapper.MoeQbSettingMapper;
import com.moego.server.grooming.mapper.MoeQbTaskMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeQbConnect;
import com.moego.server.grooming.mapperbean.MoeQbSetting;
import com.moego.server.grooming.service.dto.QBAccountDto;
import com.moego.server.grooming.service.dto.QBAccountReturnDto;
import com.moego.server.grooming.service.dto.QBBusinessSettingDto;
import com.moego.server.grooming.service.dto.QbQueryGroomingResultDto;
import com.moego.server.grooming.service.intuit.qbo.BusinessDataServiceFactory;
import com.moego.server.grooming.service.intuit.qbo.OAuth2PlatformClientFactory;
import com.moego.server.grooming.web.vo.QBSettingUpdateVo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class QuickBooksServiceTest {

    @Mock
    private MoeQbConnectMapper qbConnectMapper;

    @Mock
    private BusinessDataServiceFactory dataServiceFactory;

    @Mock
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Mock
    private MoeQbSettingMapper qbSettingMapper;

    @Mock
    private MoeQbTaskMapper qbTaskMapper;

    @Mock
    private OrderService orderService;

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private OAuth2PlatformClientFactory factory;

    @Mock
    private QuickBooksSyncService quickBooksSyncService;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private OAuth2Config oAuth2Config;

    @Mock
    private MetadataServiceGrpc.MetadataServiceBlockingStub metadataClient;

    @Mock
    private RateLimitServiceGrpc.RateLimitServiceBlockingStub rateLimitServiceBlockingStub;

    @InjectMocks
    private QuickBooksService quickBooksService;

    private static final Integer BUSINESS_ID = 1;
    private static final Long COMPANY_ID = 1L;
    private static final Integer CONNECT_ID = 1;

    private MoeQbConnect normalQBConnect;
    private MoeQbConnect defaultQBConnect;
    private MoeQbSetting qbSetting;
    private MoeBusinessDto businessDto;
    private Map<String, String> metadataValueMap;

    @BeforeEach
    void setUp() {
        // Setup qbConnect with normal status
        normalQBConnect = new MoeQbConnect();
        normalQBConnect.setId(CONNECT_ID);
        normalQBConnect.setCompanyId(COMPANY_ID);
        normalQBConnect.setConnectStatus(QuickBooksConst.CONNECT_STATUS_NORMAL);

        // Setup qbConnect with default status
        defaultQBConnect = new MoeQbConnect();
        defaultQBConnect.setId(CONNECT_ID);
        defaultQBConnect.setCompanyId(COMPANY_ID);
        defaultQBConnect.setConnectStatus(QuickBooksConst.CONNECT_STATUS_DEFAULT);

        // Setup qbSetting
        qbSetting = new MoeQbSetting();
        qbSetting.setId(1);
        qbSetting.setBusinessId(BUSINESS_ID);
        qbSetting.setCompanyId(COMPANY_ID);
        qbSetting.setConnectId(CONNECT_ID);
        qbSetting.setEnableSync(QuickBooksConst.ENABLE_SYNC_OPEN);
        qbSetting.setStatus(QuickBooksConst.STATUS_NORMAL);
        qbSetting.setSyncBeginDate("2023-02-13");

        // Setup businessDto
        businessDto = new MoeBusinessDto();
        businessDto.setId(BUSINESS_ID);
        businessDto.setCompanyId(COMPANY_ID.intValue());
        businessDto.setTimezoneName("America/New_York");

        // Setup Metadata Value Map for QuickBooks v2 feature flags
        metadataValueMap = Map.of(
                "quickbook_v2_enabled", "true", // QB_METADATA_GRAY_SCALES_TEST_KEY
                "quickbook_v2_pre_onboarding", "auto" // QB_METADATA_USER_OPTIONS_KEY
                );

        // Mock metadata client responses
        //        when(metadataClient.extractValues(any()))
        //                .thenReturn(ExtractValuesResponse.newBuilder()
        //                        .putAllValues(metadataValueMap)
        //                        .build());

        //        // Mock getKey responses
        //        when(metadataClient.getKey(GetKeyRequest.newBuilder()
        //                .setName("quickbook_v2_enabled")
        //                .build()))
        //                .thenReturn(GetKeyResponse.newBuilder()
        //                        .setKey(KeyModel.newBuilder().setId(1).build())
        //                        .build());
        //
        //        when(metadataClient.getKey(GetKeyRequest.newBuilder()
        //                .setName("quickbook_v2_pre_onboarding")
        //                .build()))
        //                .thenReturn(GetKeyResponse.newBuilder()
        //                        .setKey(KeyModel.newBuilder().setId(2).build())
        //                        .build());
    }

    @Test
    void getQbConnectSettingMustAvailable_WhenSettingNotAvailable_ShouldThrowException() {
        // Arrange
        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), any())).thenReturn(null);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);

        // Act & Assert
        assertThrows(CommonException.class, () -> quickBooksService.getQbConnectSettingMustAvailable(BUSINESS_ID));
    }

    @Test
    void getBusinessConnectAccount_WhenValidInput_ShouldReturnAccounts() {
        // Arrange
        Account account = new Account();
        account.setId("1");
        account.setName("Test Account");

        QueryResult queryResult = new QueryResult();
        List<IEntity> entities = new ArrayList<>();
        entities.add(account);
        queryResult.setEntities(entities);

        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), any())).thenReturn(qbSetting);
        when(dataServiceFactory.executeQuery((MoeQbConnect) any(), anyString())).thenReturn(queryResult);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);
        when(qbConnectMapper.selectByPrimaryKey(CONNECT_ID)).thenReturn(normalQBConnect);

        // Act
        QBAccountReturnDto result = quickBooksService.getBusinessConnectAccount(BUSINESS_ID);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getAccountList().size());
        QBAccountDto accountDto = result.getAccountList().get(0);
        assertEquals("1", accountDto.getAccountId());
        assertEquals("Test Account", accountDto.getAccountName());
    }

    @Test
    void getConnectOAuthUrl_WhenValidInput_ShouldReturnUrl() throws InvalidRequestException {
        // Arrange
        String expectedUrl = "https://test.url";
        List<Scope> scopes = Arrays.asList(Scope.Accounting, Scope.Email, Scope.OpenId);

        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);
        when(oAuth2Config.prepareUrl(eq(scopes), any(), eq(BUSINESS_ID.toString())))
                .thenReturn(expectedUrl);

        // Act
        String result = quickBooksService.getConnectOAuthUrl(List.of(BUSINESS_ID));

        // Assert
        assertEquals(expectedUrl, result);
    }

    @Test
    void qbCreateSetting_WhenValidInput_ShouldCreateSetting() {
        // Arrange
        when(qbConnectMapper.selectByPrimaryKey(CONNECT_ID)).thenReturn(defaultQBConnect);
        when(iBusinessBusinessClient.getBusinessInfo(any())).thenReturn(businessDto);
        when(qbSettingMapper.selectTaxSyncTypeByBusinessId(BUSINESS_ID)).thenReturn((byte) 1);

        // Act
        Boolean result = quickBooksService.qbCreateSetting(BUSINESS_ID, COMPANY_ID, CONNECT_ID);

        // Assert
        assertNotNull(result);
    }

    @Test
    void getDefaultSettingDto_ShouldReturnDefaultSetting() {
        // Act
        QBBusinessSettingDto result = quickBooksService.getDefaultSettingDto();

        // Assert
        assertNotNull(result);
        assertEquals(QuickBooksConst.ENABLE_SYNC_CLOSE, result.getEnableSync());
    }

    @Test
    void addRedisSyncGroomingData_WhenValidInput_ShouldAddToRedis() {
        // Arrange
        MoeGroomingInvoice invoice = new MoeGroomingInvoice();
        invoice.setId(1);

        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), any())).thenReturn(qbSetting);
        when(orderService.getOrderByGroomingId(eq(BUSINESS_ID), anyInt())).thenReturn(invoice);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);
        when(qbConnectMapper.selectByPrimaryKey(CONNECT_ID)).thenReturn(normalQBConnect);

        // Act
        quickBooksService.addRedisSyncGroomingData(BUSINESS_ID, 1, "2024-02-11");

        // Verify
        verify(redisUtil).lLeftPush(anyString(), anyString());
    }

    @Test
    void addSyncJobByInvoiceId_WhenValidInput_ShouldAddToRedis() {
        // Arrange
        MoeGroomingInvoice invoice = new MoeGroomingInvoice();
        invoice.setId(1);
        invoice.setBusinessId(BUSINESS_ID);
        invoice.setUpdateTime(System.currentTimeMillis() / 1000);

        when(orderService.getOrderById(eq(BUSINESS_ID), anyInt())).thenReturn(invoice);
        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), any())).thenReturn(qbSetting);
        when(iBusinessBusinessClient.getBusinessInfo(any())).thenReturn(businessDto);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);
        when(qbConnectMapper.selectByPrimaryKey(CONNECT_ID)).thenReturn(normalQBConnect);

        // Act
        quickBooksService.addSyncJobByInvoiceId(BUSINESS_ID, 1);

        // Verify
        verify(redisUtil).lLeftPush(anyString(), anyString());
    }

    @Test
    void addSyncJobByDateRange_WhenValidInput_ShouldAddToRedis() {
        // Arrange
        List<QbQueryGroomingResultDto> groomingResults = new ArrayList<>();
        QbQueryGroomingResultDto dto = new QbQueryGroomingResultDto();
        dto.setGroomingId(1);
        groomingResults.add(dto);

        when(moeGroomingAppointmentMapper.queryAppointmentDateByDateRange(eq(BUSINESS_ID), anyString(), anyString()))
                .thenReturn(groomingResults);
        when(orderService.getListByGroomingIds(eq(BUSINESS_ID), anyList(), any()))
                .thenReturn(List.of(new MoeGroomingInvoice()));
        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), any())).thenReturn(qbSetting);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);

        // Act
        quickBooksService.addSyncJobByDateRange(BUSINESS_ID, "2024-02-11", "2024-02-12");

        // Verify
        verify(redisUtil).lRightPush(anyString(), anyString());
    }

    @Test
    void getQbConnectSetting_WhenValidInput_ShouldReturnSetting() throws InvalidRequestException {
        // Arrange
        String expectedUrl = "https://test.url";
        List<Scope> scopes = Arrays.asList(Scope.Accounting, Scope.Email, Scope.OpenId);

        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), eq(QuickBooksConst.STATUS_NORMAL)))
                .thenReturn(qbSetting);
        when(qbConnectMapper.selectByPrimaryKey(anyInt())).thenReturn(normalQBConnect);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);
        when(oAuth2Config.prepareUrl(eq(scopes), any(), eq(BUSINESS_ID.toString())))
                .thenReturn(expectedUrl);

        // Act
        QBBusinessSettingDto result = quickBooksService.getQbConnectSetting(BUSINESS_ID);

        // Assert
        assertNotNull(result);
        assertEquals(BUSINESS_ID, qbSetting.getBusinessId());
        assertEquals(CONNECT_ID, qbSetting.getConnectId());
        assertEquals(expectedUrl, result.getOAuthUrl());
    }

    @Test
    void getQbConnectSetting_WhenSettingNotFound_ShouldReturnDefaultSetting() throws InvalidRequestException {
        // Arrange
        String expectedUrl = "https://test.url";
        List<Scope> scopes = Arrays.asList(Scope.Accounting, Scope.Email, Scope.OpenId);

        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), eq(QuickBooksConst.STATUS_NORMAL)))
                .thenReturn(null);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);
        when(oAuth2Config.prepareUrl(eq(scopes), any(), eq(BUSINESS_ID.toString())))
                .thenReturn(expectedUrl);

        // Act
        QBBusinessSettingDto result = quickBooksService.getQbConnectSetting(BUSINESS_ID);

        // Assert
        assertNotNull(result);
        assertEquals(QuickBooksConst.ENABLE_SYNC_CLOSE, result.getEnableSync());
    }

    @Test
    void getQbConnectSetting_WhenOAuthConfigFails_ShouldHandleError() {
        // Arrange
        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), eq(QuickBooksConst.STATUS_NORMAL)))
                .thenReturn(qbSetting);
        when(qbConnectMapper.selectByPrimaryKey(anyInt())).thenReturn(normalQBConnect);
        when(factory.getOAuth2Config()).thenThrow(new RuntimeException("OAuth config error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> quickBooksService.getQbConnectSetting(BUSINESS_ID));
    }

    @Test
    void beginTask_WhenValidInput_ShouldReturnTaskStatusRecord() {
        // Arrange
        when(qbSettingMapper.selectAllStatusNormal()).thenReturn(List.of(qbSetting));
        when(rateLimitServiceBlockingStub.registerRules(any()))
                .thenReturn(RegisterRulesResponse.newBuilder().setSucceed(true).build());
        when(metadataClient.extractValues(any()))
                .thenReturn(ExtractValuesResponse.newBuilder()
                        .putAllValues(metadataValueMap)
                        .build());

        // Act
        List<String> result = quickBooksService.beginTask();

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void updateBusinessSetting_WhenValidInput_ShouldUpdateSetting() throws InvalidRequestException {
        // Arrange
        String expectedUrl = "https://test.url";
        List<Scope> scopes = Arrays.asList(Scope.Accounting, Scope.Email, Scope.OpenId);

        QBSettingUpdateVo updateVo = new QBSettingUpdateVo();
        updateVo.setBusinessId(BUSINESS_ID);
        updateVo.setEnableSync(QuickBooksConst.ENABLE_SYNC_CLOSE);

        // Mock selectByBusinessId to return setting
        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), any())).thenReturn(qbSetting);

        when(qbConnectMapper.selectByPrimaryKey(anyInt())).thenReturn(normalQBConnect);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);
        when(oAuth2Config.prepareUrl(eq(scopes), any(), eq(BUSINESS_ID.toString())))
                .thenReturn(expectedUrl);

        // Act
        Boolean result = quickBooksService.updateBusinessSetting(updateVo);

        // Assert
        assertTrue(result);
    }

    @Test
    void updateBusinessSetting_WhenOAuthConfigFails_ShouldHandleError() {
        // Arrange
        QBSettingUpdateVo updateVo = new QBSettingUpdateVo();
        updateVo.setBusinessId(BUSINESS_ID);
        updateVo.setEnableSync(QuickBooksConst.ENABLE_SYNC_CLOSE);

        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), any())).thenReturn(qbSetting);
        when(factory.getOAuth2Config()).thenThrow(new RuntimeException("OAuth config error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> quickBooksService.updateBusinessSetting(updateVo));
    }

    @Test
    void refreshMetadata_WhenValidInput_ShouldRefreshSuccessfully() {
        // Arrange
        when(qbSettingMapper.selectAllStatusNormal()).thenReturn(List.of(qbSetting));
        when(metadataClient.getKey(any()))
                .thenReturn(GetKeyResponse.newBuilder().build());
        when(qbSettingMapper.selectByBusinessId(anyInt(), any())).thenReturn(qbSetting);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);

        // Act
        String result = quickBooksService.refreshMetadata(BUSINESS_ID, QuickBooksConst.QB_REFRESH_METADATA_MODE_NORMAL);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("refresh success"));
    }

    @Test
    void migrationQBDataByBusinessId_WhenValidInput_ShouldStartMigration() {
        // Arrange
        qbSetting.setUserVersion(QuickBooksConst.USER_VERSION_NEW);
        when(qbSettingMapper.selectByBusinessId(eq(BUSINESS_ID), any())).thenReturn(qbSetting);
        when(qbTaskMapper.selectTaskByBusinessIdAndType(anyInt(), any())).thenReturn(new ArrayList<>());
        when(qbTaskMapper.selectBusinessIdTask(anyInt())).thenReturn(null);
        when(iBusinessBusinessClient.getBusinessInfo(any())).thenReturn(businessDto);
        when(factory.getOAuth2Config()).thenReturn(oAuth2Config);

        // Act
        String result = quickBooksService.migrationQBDataByBusinessId(BUSINESS_ID, "2024-02-12");

        // Assert
        assertNotNull(result);
        assertEquals("migration success", result);
    }
}
