package com.moego.server.grooming.util;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.utils.AppointmentUtils;
import org.junit.jupiter.api.Test;

public class AppointmentUtilsTest {

    private MoeGroomingAppointment createMockAppointment(Integer serviceTypeInclude, Integer isBlock) {
        if (serviceTypeInclude == null && isBlock == null) {
            return null;
        }
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setServiceTypeInclude(serviceTypeInclude);
        appointment.setIsBlock(isBlock);
        return appointment;
    }

    @Test
    void isStaffOnly_shouldReturnFalseForNullAppointment() {
        assertFalse(AppointmentUtils.isStaffOnly(null));
    }

    @Test
    void isStaffOnly_shouldReturnFalseForNullServiceTypeInclude() {
        MoeGroomingAppointment appointment = createMockAppointment(null, null);
        assertFalse(AppointmentUtils.isStaffOnly(appointment));
    }

    @Test
    void isStaffOnly_shouldReturnTrueForGroomingService() {
        int groomingBit = ServiceItemEnum.GROOMING.getBitValue();
        MoeGroomingAppointment appointment = createMockAppointment(groomingBit, null);
        assertTrue(AppointmentUtils.isStaffOnly(appointment));
    }

    @Test
    void isStaffOnly_shouldReturnTrueForDogWalkingService() {
        int dogWalkingBit = ServiceItemEnum.DOG_WALKING.getBitValue();
        MoeGroomingAppointment appointment = createMockAppointment(dogWalkingBit, null);
        assertTrue(AppointmentUtils.isStaffOnly(appointment));
    }

    @Test
    void isStaffOnly_shouldReturnTrueForBothServices() {
        int combinedBit = ServiceItemEnum.GROOMING.getBitValue() | ServiceItemEnum.DOG_WALKING.getBitValue();
        MoeGroomingAppointment appointment = createMockAppointment(combinedBit, null);
        assertTrue(AppointmentUtils.isStaffOnly(appointment));
    }

    @Test
    void isStaffOnly_shouldReturnFalseForOtherServices() {
        // Assuming there's another service item not covered by GROOMING or DOG_WALKING
        int otherServiceBit = 1 << 5; // Example: a bit that is not grooming or dog walking
        MoeGroomingAppointment appointment = createMockAppointment(otherServiceBit, null);
        assertFalse(AppointmentUtils.isStaffOnly(appointment));
    }

    @Test
    void isStaffOnly_shouldReturnFalseForZeroServiceTypeInclude() {
        MoeGroomingAppointment appointment = createMockAppointment(0, null);
        assertFalse(AppointmentUtils.isStaffOnly(appointment));
    }

    @Test
    void isBlock_shouldReturnFalseForNullAppointment() {
        assertFalse(AppointmentUtils.isBlock(null));
    }

    @Test
    void isBlock_shouldReturnTrueWhenIsBlockIsEnabled() {
        MoeGroomingAppointment appointment = createMockAppointment(null, CommonConstant.ENABLE.intValue());
        assertTrue(AppointmentUtils.isBlock(appointment));
    }

    @Test
    void isBlock_shouldReturnFalseWhenIsBlockIsDisabled() {
        MoeGroomingAppointment appointment = createMockAppointment(null, CommonConstant.DISABLE.intValue());
        assertFalse(AppointmentUtils.isBlock(appointment));
    }
}
