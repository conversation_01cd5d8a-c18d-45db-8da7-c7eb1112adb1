package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.type.DayOfWeek;
import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.common.enums.SubscriptionConst;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.SlotDailySetting;
import com.moego.idl.models.organization.v1.SlotHourSetting;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.enums.StaffAvailableTypeEnum;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.MoeBookOnlineAvailableStaffMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.dto.OBAvailableDateTimeDTO;
import com.moego.server.grooming.service.dto.ob.OBPetLimitFilterDTO;
import com.moego.server.grooming.service.ob.OBBusinessService;
import com.moego.server.grooming.service.ob.OBBusinessStaffService;
import com.moego.server.grooming.service.ob.OBClientService;
import com.moego.server.grooming.service.ob.OBClientTimeSlotService;
import com.moego.server.grooming.service.ob.OBPetLimitService;
import com.moego.server.grooming.service.ob.SmartScheduleV2Service;
import com.moego.server.grooming.service.remote.ServiceManagementService;
import com.moego.server.grooming.service.remote.SmartSchedulerService;
import jakarta.annotation.Nonnull;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @since 2022/10/24
 */
@ExtendWith(MockitoExtension.class)
public class OBClientTimeSlotServiceQueryTest {

    @InjectMocks
    private OBClientTimeSlotService obClientTimeSlotService;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @Mock
    private MoeBookOnlineAvailableStaffMapper moeBookOnlineAvailableStaffMapper;

    @Mock
    private GroomingServiceService groomingServiceService;

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private SmartScheduleV2Service smartScheduleV2Service;

    @Mock
    private OBBusinessService businessService;

    @Mock
    private OBClientService clientService;

    @Mock
    private CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;

    @Mock
    private OBBusinessStaffService businessStaffService;

    @Mock
    MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    @Mock
    private ServiceManagementService serviceManagementService;

    @Mock
    private CompanyHelper companyHelper;

    @Mock
    private SmartSchedulerService smartSchedulerService;

    @Mock
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Mock
    private StaffTimeSyncService staffTimeSyncService;

    @Mock
    private FeatureFlagApi featureFlagApi;

    @Mock
    private IBusinessClosedDateClient iBusinessClosedDateClient;

    @Mock
    private OBPetLimitService obPetLimitService;

    @Mock
    private ExecutorService smartScheduleExecutorService;

    private final Long COMPANY_ID = 100811L;
    private final Integer BUSINESS_ID = 102786;
    private final Integer STAFF_ID = 119146;
    private final Integer CUSTOMER_ID = 120001;
    private final Set<Integer> staffIdSet = new HashSet<>();
    private OBTimeSlotDTO timeSlotDTO;
    private MoeBusinessBookOnline businessBookOnline;
    private List<MoeGroomingServiceDTO> serviceDTOList;
    private List<OBPetDataDTO> petParamList;
    private OBBusinessInfoDTO obBusinessInfoDTO;
    private BusinessPreferenceDto businessPreference;
    private List<MoeBookOnlineStaffService> moeBookOnlineStaffServices;

    // @BeforeEach
    // public void setUp() {
    //     // 初始化宠物参数列表
    //     petParamList = new ArrayList<>();
    //     OBPetDataDTO petData = new OBPetDataDTO();
    //     petData.setPetId(1);
    //     petData.setPetIndex(0);
    //     petData.setWeight("10.0");
    //     petData.setPetTypeId(1);
    //     petData.setBreed("Golden Retriever");
    //     petParamList.add(petData);
    //
    //     // 初始化基础测试数据
    //     timeSlotDTO = new OBTimeSlotDTO();
    //     timeSlotDTO.setPetParamList(petParamList);
    //     timeSlotDTO.setBusinessId(BUSINESS_ID);
    //     timeSlotDTO.setDate(LocalDate.now().toString());
    //     timeSlotDTO.setCount(42);
    //     timeSlotDTO.setCustomerId(CUSTOMER_ID);
    //     timeSlotDTO.setObName("test-ob");
    //     Map<Integer, List<Integer>> petServices = new HashMap<>();
    //     petServices.put(petData.getPetId(), List.of(100, 200));
    //     timeSlotDTO.setPetServices(petServices);
    //     timeSlotDTO.setServiceIds(List.of(100, 200));
    //     timeSlotDTO.setLat("22.53925");
    //     timeSlotDTO.setLng("113.9431");
    //     timeSlotDTO.setStaffIdList(List.of(STAFF_ID));
    //
    //     staffIdSet.add(STAFF_ID);
    //
    //     // 初始化业务在线预订设置
    //     businessBookOnline = new MoeBusinessBookOnline();
    //     businessBookOnline.setCompanyId(COMPANY_ID);
    //     businessBookOnline.setBusinessId(BUSINESS_ID);
    //     businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
    //     businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
    //     businessBookOnline.setBookingRangeStartOffset(0);
    //     businessBookOnline.setBookingRangeEndOffset(360);
    //     businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
    //
    //     // 初始化服务列表
    //     serviceDTOList = new ArrayList<>();
    //     MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
    //     serviceDTO100.setId(100);
    //     serviceDTO100.setDuration(60);
    //     serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
    //     serviceDTO100.setIsAllStaff(CommonConstant.ENABLE);
    //     serviceDTOList.add(serviceDTO100);
    //     MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
    //     serviceDTO200.setId(200);
    //     serviceDTO200.setDuration(60);
    //     serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
    //     serviceDTO200.setIsAllStaff(CommonConstant.ENABLE);
    //     serviceDTOList.add(serviceDTO200);;
    //     MoeGroomingServiceDTO serviceDTO300 = new MoeGroomingServiceDTO();
    //     serviceDTO300.setId(300);
    //     serviceDTO300.setDuration(60);
    //     serviceDTO300.setType(ServiceEnum.TYPE_SERVICE);
    //     serviceDTO300.setIsAllStaff(CommonConstant.DISABLE);
    //     serviceDTOList.add(serviceDTO300);
    //
    //     obBusinessInfoDTO = new OBBusinessInfoDTO();
    //     obBusinessInfoDTO.setTimezoneName("Asia/Shanghai");
    //     obBusinessInfoDTO.setBusinessMode(SubscriptionConst.BUSINESS_TYPE_SALON);
    //
    //     businessPreference = new BusinessPreferenceDto();
    //     businessPreference.setTimezoneName("Asia/Shanghai");
    //
    //     moeBookOnlineStaffServices = new ArrayList<>();
    //     MoeBookOnlineStaffService moeBookOnlineStaffService = new MoeBookOnlineStaffService();
    //     moeBookOnlineStaffService.setStaffId(STAFF_ID);
    //     moeBookOnlineStaffService.setServiceId(300);
    //     moeBookOnlineStaffServices.add(moeBookOnlineStaffService);
    //
    // }

    @Nested
    @DisplayName("getTimeSlotListV2 方法测试")
    class GetTimeSlotListV2Test {

        @Test
        @DisplayName("正常情况 - AVAILABLE_TIME_TYPE_BY_SLOT 分支测试")
        void testGetTimeSlotListV2_BySlotType_Success() {
            // 初始化宠物参数列表
            petParamList = new ArrayList<>();
            OBPetDataDTO petData = new OBPetDataDTO();
            petData.setPetId(1);
            petData.setPetIndex(0);
            petData.setWeight("10.0");
            petData.setPetTypeId(1);
            petData.setBreed("Golden Retriever");
            petParamList.add(petData);

            // 初始化基础测试数据
            timeSlotDTO = new OBTimeSlotDTO();
            timeSlotDTO.setPetParamList(petParamList);
            timeSlotDTO.setBusinessId(BUSINESS_ID);
            timeSlotDTO.setDate(LocalDate.now().plusDays(1).toString());
            timeSlotDTO.setCount(1);
            timeSlotDTO.setCustomerId(CUSTOMER_ID);
            timeSlotDTO.setObName("test-ob");
            Map<Integer, List<Integer>> petServices = new HashMap<>();
            petServices.put(petData.getPetId(), List.of(100, 200));
            timeSlotDTO.setPetServices(petServices);
            timeSlotDTO.setServiceIds(List.of(100, 200));
            timeSlotDTO.setLat("22.53925");
            timeSlotDTO.setLng("113.9431");
            timeSlotDTO.setStaffIdList(List.of(STAFF_ID));

            staffIdSet.add(STAFF_ID);

            // 初始化业务在线预订设置
            businessBookOnline = new MoeBusinessBookOnline();
            businessBookOnline.setCompanyId(COMPANY_ID);
            businessBookOnline.setBusinessId(BUSINESS_ID);
            businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
            businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
            businessBookOnline.setBookingRangeStartOffset(0);
            businessBookOnline.setBookingRangeEndOffset(1);
            businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
            businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
            businessBookOnline.setBySlotShowOneAvailableTime(false);

            // 初始化服务列表
            serviceDTOList = new ArrayList<>();
            MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
            serviceDTO100.setId(100);
            serviceDTO100.setDuration(60);
            serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
            serviceDTO100.setIsAllStaff(CommonConstant.ENABLE);
            serviceDTOList.add(serviceDTO100);
            MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
            serviceDTO200.setId(200);
            serviceDTO200.setDuration(60);
            serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
            serviceDTO200.setIsAllStaff(CommonConstant.ENABLE);
            serviceDTOList.add(serviceDTO200);
            MoeGroomingServiceDTO serviceDTO300 = new MoeGroomingServiceDTO();
            serviceDTO300.setId(300);
            serviceDTO300.setDuration(60);
            serviceDTO300.setType(ServiceEnum.TYPE_SERVICE);
            serviceDTO300.setIsAllStaff(CommonConstant.DISABLE);
            serviceDTOList.add(serviceDTO300);

            obBusinessInfoDTO = new OBBusinessInfoDTO();
            obBusinessInfoDTO.setTimezoneName("Asia/Shanghai");
            obBusinessInfoDTO.setBusinessMode(SubscriptionConst.BUSINESS_TYPE_SALON);

            businessPreference = new BusinessPreferenceDto();
            businessPreference.setTimezoneName("Asia/Shanghai");

            moeBookOnlineStaffServices = new ArrayList<>();
            MoeBookOnlineStaffService moeBookOnlineStaffService = new MoeBookOnlineStaffService();
            moeBookOnlineStaffService.setStaffId(STAFF_ID);
            moeBookOnlineStaffService.setServiceId(300);
            moeBookOnlineStaffServices.add(moeBookOnlineStaffService);

            // Arrange - 设置测试数据
            businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);

            // Mock 依赖服务的返回值
            when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
            when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                    .thenReturn(false);
            when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
            when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                    .thenReturn(serviceDTOList);
            when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
            when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of(300)))
                    .thenReturn(moeBookOnlineStaffServices);
            List<MoeStaffDto> staffDtoList = new ArrayList<>();
            MoeStaffDto staffDto = new MoeStaffDto();
            staffDto.setId(STAFF_ID);
            staffDto.setStatus(StaffEnum.STATUS_NORMAL);
            staffDtoList.add(staffDto);
            when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);

            Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
            staffAvailabilityMap.put(STAFF_ID, getStaffAvailability());
            when(staffTimeSyncService.queryShiftManagementStaffTime(BUSINESS_ID, COMPANY_ID, staffIdSet))
                    .thenReturn(staffAvailabilityMap);
            when(staffTimeSyncService.queryShiftManagementOverrideStaffTime(BUSINESS_ID, COMPANY_ID, staffIdSet))
                    .thenReturn(new HashMap<>());

            doAnswer(invocation -> {
                        Runnable task = invocation.getArgument(0);
                        task.run(); // 在当前测试线程中同步执行任务
                        return null;
                    })
                    .when(smartScheduleExecutorService)
                    .execute(any(Runnable.class));

            // Mock queryStaffBlocksByDates 方法
            Map<String, List<StaffBlockInfoDTO>> mockBlocksMap = new HashMap<>();
            when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                    .thenReturn(List.of()); // 返回空列表或构造测试数据

            // Mock queryStaffTimeslotPetCount 方法
            Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
            when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(anyInt(), any(), any(), any(), any()))
                    .thenReturn(mockPetCountList);

            // Mock obPetLimitService.generateWithCurrentAppointment 方法
            OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
            when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                    .thenReturn(mockInitPetLimitFilter);

            // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
            OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
            mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0)));
            when(obPetLimitService.fillWithExistingAppointmentV2(any(), any())).thenReturn(mockFinalPetLimitFilter);

            // Mock isSlotCapacityByReservation 方法
            when(featureFlagApi.isOn(any(), any())).thenReturn(false);

            // Mock getAvailableTimeBySlotCapacityV2 的返回值
            OBAvailableDateTimeDTO expectedResult = new OBAvailableDateTimeDTO();
            expectedResult.setAvailableDates(new HashMap<>());

            // 由于 getAvailableTimeBySlotCapacityV2 是私有方法，我们通过反射或者模拟其依赖来测试
            // 这里我们主要验证方法调用流程和分支逻辑

            // Act - 执行测试方法
            OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

            // Assert - 验证结果
            assertThat(result).isNotNull();
            assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NOT_SELECTED);

            // 验证关键方法被调用
            verify(companyHelper).mustGetCompanyId(BUSINESS_ID);
            verify(clientService).validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID);
            verify(businessService).getSettingInfoByBusinessId(BUSINESS_ID);
            verify(groomingServiceService).getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds());
        }

        @Nonnull
        private StaffAvailability getStaffAvailability() {
            return StaffAvailability.newBuilder()
                    .setStaffId(STAFF_ID)
                    .setIsAvailable(true)
                    .setStaffId(STAFF_ID)
                    .setSlotStartSunday("2025-07-13")
                    .setScheduleType(ScheduleType.ONE_WEEK)
                    .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                            .setStaffId(STAFF_ID)
                            .setDayOfWeek(DayOfWeek.MONDAY)
                            .setIsAvailable(true)
                            .setScheduleType(ScheduleType.ONE_WEEK)
                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(1140)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(600)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(600)
                                    .setEndTime(630)
                                    .setCapacity(1)
                                    .build())
                            .build())
                    .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                            .setStaffId(STAFF_ID)
                            .setDayOfWeek(DayOfWeek.TUESDAY)
                            .setIsAvailable(true)
                            .setScheduleType(ScheduleType.ONE_WEEK)
                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(1140)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(600)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(600)
                                    .setEndTime(630)
                                    .setCapacity(1)
                                    .build())
                            .build())
                    .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                            .setStaffId(STAFF_ID)
                            .setDayOfWeek(DayOfWeek.WEDNESDAY)
                            .setIsAvailable(true)
                            .setScheduleType(ScheduleType.ONE_WEEK)
                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(1140)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(600)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(600)
                                    .setEndTime(630)
                                    .setCapacity(1)
                                    .build())
                            .build())
                    .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                            .setStaffId(STAFF_ID)
                            .setDayOfWeek(DayOfWeek.THURSDAY)
                            .setIsAvailable(true)
                            .setScheduleType(ScheduleType.ONE_WEEK)
                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(1140)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(600)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(600)
                                    .setEndTime(630)
                                    .setCapacity(1)
                                    .build())
                            .build())
                    .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                            .setStaffId(STAFF_ID)
                            .setDayOfWeek(DayOfWeek.FRIDAY)
                            .setIsAvailable(true)
                            .setScheduleType(ScheduleType.ONE_WEEK)
                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(1140)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(600)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(600)
                                    .setEndTime(630)
                                    .setCapacity(1)
                                    .build())
                            .build())
                    .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                            .setStaffId(STAFF_ID)
                            .setDayOfWeek(DayOfWeek.SATURDAY)
                            .setIsAvailable(true)
                            .setScheduleType(ScheduleType.ONE_WEEK)
                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(1140)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(600)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(600)
                                    .setEndTime(630)
                                    .setCapacity(1)
                                    .build())
                            .build())
                    .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                            .setStaffId(STAFF_ID)
                            .setDayOfWeek(DayOfWeek.SUNDAY)
                            .setIsAvailable(true)
                            .setScheduleType(ScheduleType.ONE_WEEK)
                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(1140)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(540)
                                    .setEndTime(600)
                                    .setCapacity(1)
                                    .build())
                            .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                    .setStartTime(600)
                                    .setEndTime(630)
                                    .setCapacity(1)
                                    .build())
                            .build())
                    .build();
        }
    }
}
