.idea/
.ijwb/
go.sumtmp/
**/venv/*
**/__pycache__/*
dist/
.DS_Store
_moved_trash_dir
sandbox_stash
tmp/
bazel/out/*
bazel/base/*
bazel/cache/*
bazel/test-report/*
backend/test/api_integration/def/*/
!backend/test/api_integration/def/template/
.vscode/
*.bin
# ignore gradle build output directory
bin

backend/proto/api-linter-report.yaml
# Ignore Gradle project-specific cache directory
.gradle

# Ignore Gradle build output directory
build

# we use yarn
package-lock.json
pnpm-lock.yaml

node_modules/

# For Java source dependency
_java_
**/app/**/server

# proto dependency
backend/proto/deps/
