// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/offering/v1/template_service.proto

package offeringpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 删除服务模板请求
type DeleteServiceTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	ServiceTemplateId int64 `protobuf:"varint,1,opt,name=service_template_id,json=serviceTemplateId,proto3" json:"service_template_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DeleteServiceTemplateRequest) Reset() {
	*x = DeleteServiceTemplateRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceTemplateRequest) ProtoMessage() {}

func (x *DeleteServiceTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceTemplateRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteServiceTemplateRequest) GetServiceTemplateId() int64 {
	if x != nil {
		return x.ServiceTemplateId
	}
	return 0
}

// 删除服务模板响应
type DeleteServiceTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceTemplateResponse) Reset() {
	*x = DeleteServiceTemplateResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceTemplateResponse) ProtoMessage() {}

func (x *DeleteServiceTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceTemplateResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{1}
}

// 更新服务模板请求
type UpdateServiceTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板
	ServiceTemplate *ServiceTemplate `protobuf:"bytes,1,opt,name=service_template,json=serviceTemplate,proto3" json:"service_template,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateServiceTemplateRequest) Reset() {
	*x = UpdateServiceTemplateRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceTemplateRequest) ProtoMessage() {}

func (x *UpdateServiceTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceTemplateRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceTemplateRequest) GetServiceTemplate() *ServiceTemplate {
	if x != nil {
		return x.ServiceTemplate
	}
	return nil
}

// 更新服务模板响应
type UpdateServiceTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceTemplateResponse) Reset() {
	*x = UpdateServiceTemplateResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceTemplateResponse) ProtoMessage() {}

func (x *UpdateServiceTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceTemplateResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{3}
}

// 列出服务模板请求
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: company_id is used as parent in this context --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要page_token --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 不需要page_size，使用现有的分页机制 --)
type ListServiceTemplateByCategoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// 是否包含非活跃状态
	Inactive bool `protobuf:"varint,3,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// 服务类型
	ServiceType ServiceType `protobuf:"varint,4,opt,name=service_type,json=serviceType,proto3,enum=backend.proto.offering.v1.ServiceType" json:"service_type,omitempty"`
	// 护理类型
	CareType      CareType `protobuf:"varint,5,opt,name=care_type,json=careType,proto3,enum=backend.proto.offering.v1.CareType" json:"care_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServiceTemplateByCategoryRequest) Reset() {
	*x = ListServiceTemplateByCategoryRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceTemplateByCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceTemplateByCategoryRequest) ProtoMessage() {}

func (x *ListServiceTemplateByCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceTemplateByCategoryRequest.ProtoReflect.Descriptor instead.
func (*ListServiceTemplateByCategoryRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListServiceTemplateByCategoryRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListServiceTemplateByCategoryRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListServiceTemplateByCategoryRequest) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ListServiceTemplateByCategoryRequest) GetServiceType() ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ListServiceTemplateByCategoryRequest) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

// 列出服务模板响应
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要next_page_token，使用现有的分页机制 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: service_templates字段是必要的响应内容 --)
//
// (-- api-linter: core::0158::response-plural-first-field=disabled
//
//	aip.dev/not-precedent: category_service_list is appropriate for this use case --)
type ListServiceTemplateByCategoryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务工厂列表
	CategoryServiceList []*CategoryService `protobuf:"bytes,1,rep,name=category_service_list,json=categoryServiceList,proto3" json:"category_service_list,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ListServiceTemplateByCategoryResponse) Reset() {
	*x = ListServiceTemplateByCategoryResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceTemplateByCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceTemplateByCategoryResponse) ProtoMessage() {}

func (x *ListServiceTemplateByCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceTemplateByCategoryResponse.ProtoReflect.Descriptor instead.
func (*ListServiceTemplateByCategoryResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListServiceTemplateByCategoryResponse) GetCategoryServiceList() []*CategoryService {
	if x != nil {
		return x.CategoryServiceList
	}
	return nil
}

// 设置服务属性值请求
type SetServiceAttributeValuesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	ServiceTemplateId int64 `protobuf:"varint,1,opt,name=service_template_id,json=serviceTemplateId,proto3" json:"service_template_id,omitempty"`
	// 属性值列表
	Values        []*ServiceAttributeValue `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetServiceAttributeValuesRequest) Reset() {
	*x = SetServiceAttributeValuesRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetServiceAttributeValuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServiceAttributeValuesRequest) ProtoMessage() {}

func (x *SetServiceAttributeValuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServiceAttributeValuesRequest.ProtoReflect.Descriptor instead.
func (*SetServiceAttributeValuesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{6}
}

func (x *SetServiceAttributeValuesRequest) GetServiceTemplateId() int64 {
	if x != nil {
		return x.ServiceTemplateId
	}
	return 0
}

func (x *SetServiceAttributeValuesRequest) GetValues() []*ServiceAttributeValue {
	if x != nil {
		return x.Values
	}
	return nil
}

// 设置服务属性值响应
type SetServiceAttributeValuesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetServiceAttributeValuesResponse) Reset() {
	*x = SetServiceAttributeValuesResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetServiceAttributeValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServiceAttributeValuesResponse) ProtoMessage() {}

func (x *SetServiceAttributeValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServiceAttributeValuesResponse.ProtoReflect.Descriptor instead.
func (*SetServiceAttributeValuesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{7}
}

// 创建服务类型请求
// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: 不需要ServiceType字段，直接使用各个字段 --)
type CreateServiceTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 类型
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// 描述
	Description   string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceTypeRequest) Reset() {
	*x = CreateServiceTypeRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTypeRequest) ProtoMessage() {}

func (x *CreateServiceTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTypeRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceTypeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateServiceTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateServiceTypeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateServiceTypeRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateServiceTypeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 创建服务类型响应
type CreateServiceTypeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务类型ID
	ServiceTypeId int64 `protobuf:"varint,1,opt,name=service_type_id,json=serviceTypeId,proto3" json:"service_type_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceTypeResponse) Reset() {
	*x = CreateServiceTypeResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTypeResponse) ProtoMessage() {}

func (x *CreateServiceTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTypeResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceTypeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{9}
}

func (x *CreateServiceTypeResponse) GetServiceTypeId() int64 {
	if x != nil {
		return x.ServiceTypeId
	}
	return 0
}

// 设置服务类型属性请求
type SetServiceTypeAttributeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务类型ID
	ServiceTypeId int64 `protobuf:"varint,1,opt,name=service_type_id,json=serviceTypeId,proto3" json:"service_type_id,omitempty"`
	// 服务属性值列表
	ServiceAttributeValues []*ServiceAttributeValue `protobuf:"bytes,2,rep,name=service_attribute_values,json=serviceAttributeValues,proto3" json:"service_attribute_values,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *SetServiceTypeAttributeRequest) Reset() {
	*x = SetServiceTypeAttributeRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetServiceTypeAttributeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServiceTypeAttributeRequest) ProtoMessage() {}

func (x *SetServiceTypeAttributeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServiceTypeAttributeRequest.ProtoReflect.Descriptor instead.
func (*SetServiceTypeAttributeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{10}
}

func (x *SetServiceTypeAttributeRequest) GetServiceTypeId() int64 {
	if x != nil {
		return x.ServiceTypeId
	}
	return 0
}

func (x *SetServiceTypeAttributeRequest) GetServiceAttributeValues() []*ServiceAttributeValue {
	if x != nil {
		return x.ServiceAttributeValues
	}
	return nil
}

// 设置服务类型属性响应
type SetServiceTypeAttributeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetServiceTypeAttributeResponse) Reset() {
	*x = SetServiceTypeAttributeResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetServiceTypeAttributeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServiceTypeAttributeResponse) ProtoMessage() {}

func (x *SetServiceTypeAttributeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServiceTypeAttributeResponse.ProtoReflect.Descriptor instead.
func (*SetServiceTypeAttributeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{11}
}

// 创建服务工厂请求
type CreateServiceTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务工厂
	ServiceTemplate *ServiceTemplate `protobuf:"bytes,1,opt,name=service_template,json=serviceTemplate,proto3" json:"service_template,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateServiceTemplateRequest) Reset() {
	*x = CreateServiceTemplateRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTemplateRequest) ProtoMessage() {}

func (x *CreateServiceTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{12}
}

func (x *CreateServiceTemplateRequest) GetServiceTemplate() *ServiceTemplate {
	if x != nil {
		return x.ServiceTemplate
	}
	return nil
}

// 创建服务工厂响应
type CreateServiceTemplateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	// (-- api-linter: core::0141::forbidden-types=disabled
	//
	//	aip.dev/not-precedent: uint64 is appropriate for ID field --)
	ServiceTemplateId uint64 `protobuf:"varint,1,opt,name=service_template_id,json=serviceTemplateId,proto3" json:"service_template_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateServiceTemplateResponse) Reset() {
	*x = CreateServiceTemplateResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTemplateResponse) ProtoMessage() {}

func (x *CreateServiceTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTemplateResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{13}
}

func (x *CreateServiceTemplateResponse) GetServiceTemplateId() uint64 {
	if x != nil {
		return x.ServiceTemplateId
	}
	return 0
}

// 获取服务工厂请求
type GetServiceTemplateByIDsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID列表
	ServiceTemplateIds []int64 `protobuf:"varint,3,rep,packed,name=service_template_ids,json=serviceTemplateIds,proto3" json:"service_template_ids,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetServiceTemplateByIDsRequest) Reset() {
	*x = GetServiceTemplateByIDsRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceTemplateByIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceTemplateByIDsRequest) ProtoMessage() {}

func (x *GetServiceTemplateByIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceTemplateByIDsRequest.ProtoReflect.Descriptor instead.
func (*GetServiceTemplateByIDsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetServiceTemplateByIDsRequest) GetServiceTemplateIds() []int64 {
	if x != nil {
		return x.ServiceTemplateIds
	}
	return nil
}

// 获取服务工厂响应
type GetServiceTemplateByIDsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务工厂列表
	ServiceTemplateList []*ServiceTemplate `protobuf:"bytes,1,rep,name=service_template_list,json=serviceTemplateList,proto3" json:"service_template_list,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *GetServiceTemplateByIDsResponse) Reset() {
	*x = GetServiceTemplateByIDsResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceTemplateByIDsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceTemplateByIDsResponse) ProtoMessage() {}

func (x *GetServiceTemplateByIDsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceTemplateByIDsResponse.ProtoReflect.Descriptor instead.
func (*GetServiceTemplateByIDsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetServiceTemplateByIDsResponse) GetServiceTemplateList() []*ServiceTemplate {
	if x != nil {
		return x.ServiceTemplateList
	}
	return nil
}

// 获取服务类型属性请求
type GetServiceTypeAttributesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务类型ID列表
	ServiceTypeIds []int64 `protobuf:"varint,1,rep,packed,name=service_type_ids,json=serviceTypeIds,proto3" json:"service_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetServiceTypeAttributesRequest) Reset() {
	*x = GetServiceTypeAttributesRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceTypeAttributesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceTypeAttributesRequest) ProtoMessage() {}

func (x *GetServiceTypeAttributesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceTypeAttributesRequest.ProtoReflect.Descriptor instead.
func (*GetServiceTypeAttributesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetServiceTypeAttributesRequest) GetServiceTypeIds() []int64 {
	if x != nil {
		return x.ServiceTypeIds
	}
	return nil
}

// 获取服务类型属性响应
type GetServiceTypeAttributesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// key: service_type_id, value: 该类型下的属性值列表
	ServiceTypeAttributes map[int64]*ServiceTypeAttributes `protobuf:"bytes,1,rep,name=service_type_attributes,json=serviceTypeAttributes,proto3" json:"service_type_attributes,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetServiceTypeAttributesResponse) Reset() {
	*x = GetServiceTypeAttributesResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceTypeAttributesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceTypeAttributesResponse) ProtoMessage() {}

func (x *GetServiceTypeAttributesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceTypeAttributesResponse.ProtoReflect.Descriptor instead.
func (*GetServiceTypeAttributesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetServiceTypeAttributesResponse) GetServiceTypeAttributes() map[int64]*ServiceTypeAttributes {
	if x != nil {
		return x.ServiceTypeAttributes
	}
	return nil
}

// 用于包装属性值列表
type ServiceTypeAttributes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务属性值列表
	ServiceAttributeValues []*ServiceAttributeValue `protobuf:"bytes,1,rep,name=service_attribute_values,json=serviceAttributeValues,proto3" json:"service_attribute_values,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ServiceTypeAttributes) Reset() {
	*x = ServiceTypeAttributes{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceTypeAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTypeAttributes) ProtoMessage() {}

func (x *ServiceTypeAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTypeAttributes.ProtoReflect.Descriptor instead.
func (*ServiceTypeAttributes) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{18}
}

func (x *ServiceTypeAttributes) GetServiceAttributeValues() []*ServiceAttributeValue {
	if x != nil {
		return x.ServiceAttributeValues
	}
	return nil
}

// 获取服务属性值请求
type GetServiceAttributeValuesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID列表
	ServiceTemplateIds []int64 `protobuf:"varint,1,rep,packed,name=service_template_ids,json=serviceTemplateIds,proto3" json:"service_template_ids,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetServiceAttributeValuesRequest) Reset() {
	*x = GetServiceAttributeValuesRequest{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceAttributeValuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceAttributeValuesRequest) ProtoMessage() {}

func (x *GetServiceAttributeValuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceAttributeValuesRequest.ProtoReflect.Descriptor instead.
func (*GetServiceAttributeValuesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetServiceAttributeValuesRequest) GetServiceTemplateIds() []int64 {
	if x != nil {
		return x.ServiceTemplateIds
	}
	return nil
}

// 获取服务属性值响应
type GetServiceAttributeValuesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// key: service_template_id, value: 该工厂下的属性值列表
	ServiceAttributeValues map[int64]*ServiceAttributeValues `protobuf:"bytes,1,rep,name=service_attribute_values,json=serviceAttributeValues,proto3" json:"service_attribute_values,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *GetServiceAttributeValuesResponse) Reset() {
	*x = GetServiceAttributeValuesResponse{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceAttributeValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceAttributeValuesResponse) ProtoMessage() {}

func (x *GetServiceAttributeValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceAttributeValuesResponse.ProtoReflect.Descriptor instead.
func (*GetServiceAttributeValuesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetServiceAttributeValuesResponse) GetServiceAttributeValues() map[int64]*ServiceAttributeValues {
	if x != nil {
		return x.ServiceAttributeValues
	}
	return nil
}

// 用于包装属性值列表
type ServiceAttributeValues struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 属性值列表
	Values        []*ServiceAttributeValue `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceAttributeValues) Reset() {
	*x = ServiceAttributeValues{}
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAttributeValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAttributeValues) ProtoMessage() {}

func (x *ServiceAttributeValues) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_template_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAttributeValues.ProtoReflect.Descriptor instead.
func (*ServiceAttributeValues) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_template_service_proto_rawDescGZIP(), []int{21}
}

func (x *ServiceAttributeValues) GetValues() []*ServiceAttributeValue {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_backend_proto_offering_v1_template_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_template_service_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/offering/v1/template_service.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17validate/validate.proto\x1a(backend/proto/offering/v1/template.proto\x1a&backend/proto/offering/v1/common.proto\"N\n" +
	"\x1cDeleteServiceTemplateRequest\x12.\n" +
	"\x13service_template_id\x18\x01 \x01(\x03R\x11serviceTemplateId\"\x1f\n" +
	"\x1dDeleteServiceTemplateResponse\"u\n" +
	"\x1cUpdateServiceTemplateRequest\x12U\n" +
	"\x10service_template\x18\x01 \x01(\v2*.backend.proto.offering.v1.ServiceTemplateR\x0fserviceTemplate\"\x1f\n" +
	"\x1dUpdateServiceTemplateResponse\"\xa4\x02\n" +
	"$ListServiceTemplateByCategoryRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12+\n" +
	"\fbusiness_ids\x18\x02 \x03(\x03B\b\xfaB\x05\x92\x01\x02\b\x01R\vbusinessIds\x12\x1a\n" +
	"\binactive\x18\x03 \x01(\bR\binactive\x12I\n" +
	"\fservice_type\x18\x04 \x01(\x0e2&.backend.proto.offering.v1.ServiceTypeR\vserviceType\x12@\n" +
	"\tcare_type\x18\x05 \x01(\x0e2#.backend.proto.offering.v1.CareTypeR\bcareType\"\x87\x01\n" +
	"%ListServiceTemplateByCategoryResponse\x12^\n" +
	"\x15category_service_list\x18\x01 \x03(\v2*.backend.proto.offering.v1.CategoryServiceR\x13categoryServiceList\"\x9c\x01\n" +
	" SetServiceAttributeValuesRequest\x12.\n" +
	"\x13service_template_id\x18\x01 \x01(\x03R\x11serviceTemplateId\x12H\n" +
	"\x06values\x18\x02 \x03(\v20.backend.proto.offering.v1.ServiceAttributeValueR\x06values\"#\n" +
	"!SetServiceAttributeValuesResponse\"\x90\x01\n" +
	"\x18CreateServiceTypeRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"C\n" +
	"\x19CreateServiceTypeResponse\x12&\n" +
	"\x0fservice_type_id\x18\x01 \x01(\x03R\rserviceTypeId\"\xb4\x01\n" +
	"\x1eSetServiceTypeAttributeRequest\x12&\n" +
	"\x0fservice_type_id\x18\x01 \x01(\x03R\rserviceTypeId\x12j\n" +
	"\x18service_attribute_values\x18\x02 \x03(\v20.backend.proto.offering.v1.ServiceAttributeValueR\x16serviceAttributeValues\"!\n" +
	"\x1fSetServiceTypeAttributeResponse\"u\n" +
	"\x1cCreateServiceTemplateRequest\x12U\n" +
	"\x10service_template\x18\x01 \x01(\v2*.backend.proto.offering.v1.ServiceTemplateR\x0fserviceTemplate\"O\n" +
	"\x1dCreateServiceTemplateResponse\x12.\n" +
	"\x13service_template_id\x18\x01 \x01(\x04R\x11serviceTemplateId\"R\n" +
	"\x1eGetServiceTemplateByIDsRequest\x120\n" +
	"\x14service_template_ids\x18\x03 \x03(\x03R\x12serviceTemplateIds\"\x81\x01\n" +
	"\x1fGetServiceTemplateByIDsResponse\x12^\n" +
	"\x15service_template_list\x18\x01 \x03(\v2*.backend.proto.offering.v1.ServiceTemplateR\x13serviceTemplateList\"K\n" +
	"\x1fGetServiceTypeAttributesRequest\x12(\n" +
	"\x10service_type_ids\x18\x01 \x03(\x03R\x0eserviceTypeIds\"\xaf\x02\n" +
	" GetServiceTypeAttributesResponse\x12\x8e\x01\n" +
	"\x17service_type_attributes\x18\x01 \x03(\v2V.backend.proto.offering.v1.GetServiceTypeAttributesResponse.ServiceTypeAttributesEntryR\x15serviceTypeAttributes\x1az\n" +
	"\x1aServiceTypeAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12F\n" +
	"\x05value\x18\x02 \x01(\v20.backend.proto.offering.v1.ServiceTypeAttributesR\x05value:\x028\x01\"\x83\x01\n" +
	"\x15ServiceTypeAttributes\x12j\n" +
	"\x18service_attribute_values\x18\x01 \x03(\v20.backend.proto.offering.v1.ServiceAttributeValueR\x16serviceAttributeValues\"T\n" +
	" GetServiceAttributeValuesRequest\x120\n" +
	"\x14service_template_ids\x18\x01 \x03(\x03R\x12serviceTemplateIds\"\xb6\x02\n" +
	"!GetServiceAttributeValuesResponse\x12\x92\x01\n" +
	"\x18service_attribute_values\x18\x01 \x03(\v2X.backend.proto.offering.v1.GetServiceAttributeValuesResponse.ServiceAttributeValuesEntryR\x16serviceAttributeValues\x1a|\n" +
	"\x1bServiceAttributeValuesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12G\n" +
	"\x05value\x18\x02 \x01(\v21.backend.proto.offering.v1.ServiceAttributeValuesR\x05value:\x028\x01\"b\n" +
	"\x16ServiceAttributeValues\x12H\n" +
	"\x06values\x18\x01 \x03(\v20.backend.proto.offering.v1.ServiceAttributeValueR\x06values2\xf7\x05\n" +
	"\x16ServiceTemplateService\x12\x8a\x01\n" +
	"\x15CreateServiceTemplate\x127.backend.proto.offering.v1.CreateServiceTemplateRequest\x1a8.backend.proto.offering.v1.CreateServiceTemplateResponse\x12\x90\x01\n" +
	"\x17GetServiceTemplateByIDs\x129.backend.proto.offering.v1.GetServiceTemplateByIDsRequest\x1a:.backend.proto.offering.v1.GetServiceTemplateByIDsResponse\x12\xa2\x01\n" +
	"\x1dListServiceTemplateByCategory\x12?.backend.proto.offering.v1.ListServiceTemplateByCategoryRequest\<EMAIL>\x12\x8a\x01\n" +
	"\x15UpdateServiceTemplate\x127.backend.proto.offering.v1.UpdateServiceTemplateRequest\x1a8.backend.proto.offering.v1.UpdateServiceTemplateResponse\x12\x8a\x01\n" +
	"\x15DeleteServiceTemplate\x127.backend.proto.offering.v1.DeleteServiceTemplateRequest\x1a8.backend.proto.offering.v1.DeleteServiceTemplateResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_template_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_template_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_template_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_template_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_template_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_template_service_proto_rawDesc), len(file_backend_proto_offering_v1_template_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_template_service_proto_rawDescData
}

var file_backend_proto_offering_v1_template_service_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_backend_proto_offering_v1_template_service_proto_goTypes = []any{
	(*DeleteServiceTemplateRequest)(nil),          // 0: backend.proto.offering.v1.DeleteServiceTemplateRequest
	(*DeleteServiceTemplateResponse)(nil),         // 1: backend.proto.offering.v1.DeleteServiceTemplateResponse
	(*UpdateServiceTemplateRequest)(nil),          // 2: backend.proto.offering.v1.UpdateServiceTemplateRequest
	(*UpdateServiceTemplateResponse)(nil),         // 3: backend.proto.offering.v1.UpdateServiceTemplateResponse
	(*ListServiceTemplateByCategoryRequest)(nil),  // 4: backend.proto.offering.v1.ListServiceTemplateByCategoryRequest
	(*ListServiceTemplateByCategoryResponse)(nil), // 5: backend.proto.offering.v1.ListServiceTemplateByCategoryResponse
	(*SetServiceAttributeValuesRequest)(nil),      // 6: backend.proto.offering.v1.SetServiceAttributeValuesRequest
	(*SetServiceAttributeValuesResponse)(nil),     // 7: backend.proto.offering.v1.SetServiceAttributeValuesResponse
	(*CreateServiceTypeRequest)(nil),              // 8: backend.proto.offering.v1.CreateServiceTypeRequest
	(*CreateServiceTypeResponse)(nil),             // 9: backend.proto.offering.v1.CreateServiceTypeResponse
	(*SetServiceTypeAttributeRequest)(nil),        // 10: backend.proto.offering.v1.SetServiceTypeAttributeRequest
	(*SetServiceTypeAttributeResponse)(nil),       // 11: backend.proto.offering.v1.SetServiceTypeAttributeResponse
	(*CreateServiceTemplateRequest)(nil),          // 12: backend.proto.offering.v1.CreateServiceTemplateRequest
	(*CreateServiceTemplateResponse)(nil),         // 13: backend.proto.offering.v1.CreateServiceTemplateResponse
	(*GetServiceTemplateByIDsRequest)(nil),        // 14: backend.proto.offering.v1.GetServiceTemplateByIDsRequest
	(*GetServiceTemplateByIDsResponse)(nil),       // 15: backend.proto.offering.v1.GetServiceTemplateByIDsResponse
	(*GetServiceTypeAttributesRequest)(nil),       // 16: backend.proto.offering.v1.GetServiceTypeAttributesRequest
	(*GetServiceTypeAttributesResponse)(nil),      // 17: backend.proto.offering.v1.GetServiceTypeAttributesResponse
	(*ServiceTypeAttributes)(nil),                 // 18: backend.proto.offering.v1.ServiceTypeAttributes
	(*GetServiceAttributeValuesRequest)(nil),      // 19: backend.proto.offering.v1.GetServiceAttributeValuesRequest
	(*GetServiceAttributeValuesResponse)(nil),     // 20: backend.proto.offering.v1.GetServiceAttributeValuesResponse
	(*ServiceAttributeValues)(nil),                // 21: backend.proto.offering.v1.ServiceAttributeValues
	nil,                                           // 22: backend.proto.offering.v1.GetServiceTypeAttributesResponse.ServiceTypeAttributesEntry
	nil,                                           // 23: backend.proto.offering.v1.GetServiceAttributeValuesResponse.ServiceAttributeValuesEntry
	(*ServiceTemplate)(nil),                       // 24: backend.proto.offering.v1.ServiceTemplate
	(ServiceType)(0),                              // 25: backend.proto.offering.v1.ServiceType
	(CareType)(0),                                 // 26: backend.proto.offering.v1.CareType
	(*CategoryService)(nil),                       // 27: backend.proto.offering.v1.CategoryService
	(*ServiceAttributeValue)(nil),                 // 28: backend.proto.offering.v1.ServiceAttributeValue
}
var file_backend_proto_offering_v1_template_service_proto_depIdxs = []int32{
	24, // 0: backend.proto.offering.v1.UpdateServiceTemplateRequest.service_template:type_name -> backend.proto.offering.v1.ServiceTemplate
	25, // 1: backend.proto.offering.v1.ListServiceTemplateByCategoryRequest.service_type:type_name -> backend.proto.offering.v1.ServiceType
	26, // 2: backend.proto.offering.v1.ListServiceTemplateByCategoryRequest.care_type:type_name -> backend.proto.offering.v1.CareType
	27, // 3: backend.proto.offering.v1.ListServiceTemplateByCategoryResponse.category_service_list:type_name -> backend.proto.offering.v1.CategoryService
	28, // 4: backend.proto.offering.v1.SetServiceAttributeValuesRequest.values:type_name -> backend.proto.offering.v1.ServiceAttributeValue
	28, // 5: backend.proto.offering.v1.SetServiceTypeAttributeRequest.service_attribute_values:type_name -> backend.proto.offering.v1.ServiceAttributeValue
	24, // 6: backend.proto.offering.v1.CreateServiceTemplateRequest.service_template:type_name -> backend.proto.offering.v1.ServiceTemplate
	24, // 7: backend.proto.offering.v1.GetServiceTemplateByIDsResponse.service_template_list:type_name -> backend.proto.offering.v1.ServiceTemplate
	22, // 8: backend.proto.offering.v1.GetServiceTypeAttributesResponse.service_type_attributes:type_name -> backend.proto.offering.v1.GetServiceTypeAttributesResponse.ServiceTypeAttributesEntry
	28, // 9: backend.proto.offering.v1.ServiceTypeAttributes.service_attribute_values:type_name -> backend.proto.offering.v1.ServiceAttributeValue
	23, // 10: backend.proto.offering.v1.GetServiceAttributeValuesResponse.service_attribute_values:type_name -> backend.proto.offering.v1.GetServiceAttributeValuesResponse.ServiceAttributeValuesEntry
	28, // 11: backend.proto.offering.v1.ServiceAttributeValues.values:type_name -> backend.proto.offering.v1.ServiceAttributeValue
	18, // 12: backend.proto.offering.v1.GetServiceTypeAttributesResponse.ServiceTypeAttributesEntry.value:type_name -> backend.proto.offering.v1.ServiceTypeAttributes
	21, // 13: backend.proto.offering.v1.GetServiceAttributeValuesResponse.ServiceAttributeValuesEntry.value:type_name -> backend.proto.offering.v1.ServiceAttributeValues
	12, // 14: backend.proto.offering.v1.ServiceTemplateService.CreateServiceTemplate:input_type -> backend.proto.offering.v1.CreateServiceTemplateRequest
	14, // 15: backend.proto.offering.v1.ServiceTemplateService.GetServiceTemplateByIDs:input_type -> backend.proto.offering.v1.GetServiceTemplateByIDsRequest
	4,  // 16: backend.proto.offering.v1.ServiceTemplateService.ListServiceTemplateByCategory:input_type -> backend.proto.offering.v1.ListServiceTemplateByCategoryRequest
	2,  // 17: backend.proto.offering.v1.ServiceTemplateService.UpdateServiceTemplate:input_type -> backend.proto.offering.v1.UpdateServiceTemplateRequest
	0,  // 18: backend.proto.offering.v1.ServiceTemplateService.DeleteServiceTemplate:input_type -> backend.proto.offering.v1.DeleteServiceTemplateRequest
	13, // 19: backend.proto.offering.v1.ServiceTemplateService.CreateServiceTemplate:output_type -> backend.proto.offering.v1.CreateServiceTemplateResponse
	15, // 20: backend.proto.offering.v1.ServiceTemplateService.GetServiceTemplateByIDs:output_type -> backend.proto.offering.v1.GetServiceTemplateByIDsResponse
	5,  // 21: backend.proto.offering.v1.ServiceTemplateService.ListServiceTemplateByCategory:output_type -> backend.proto.offering.v1.ListServiceTemplateByCategoryResponse
	3,  // 22: backend.proto.offering.v1.ServiceTemplateService.UpdateServiceTemplate:output_type -> backend.proto.offering.v1.UpdateServiceTemplateResponse
	1,  // 23: backend.proto.offering.v1.ServiceTemplateService.DeleteServiceTemplate:output_type -> backend.proto.offering.v1.DeleteServiceTemplateResponse
	19, // [19:24] is the sub-list for method output_type
	14, // [14:19] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_template_service_proto_init() }
func file_backend_proto_offering_v1_template_service_proto_init() {
	if File_backend_proto_offering_v1_template_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_template_proto_init()
	file_backend_proto_offering_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_template_service_proto_rawDesc), len(file_backend_proto_offering_v1_template_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_template_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_template_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_template_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_template_service_proto = out.File
	file_backend_proto_offering_v1_template_service_proto_goTypes = nil
	file_backend_proto_offering_v1_template_service_proto_depIdxs = nil
}
