// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/v1/template_service.proto

package offeringpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ServiceTemplateService_CreateServiceTemplate_FullMethodName         = "/backend.proto.offering.v1.ServiceTemplateService/CreateServiceTemplate"
	ServiceTemplateService_GetServiceTemplateByIDs_FullMethodName       = "/backend.proto.offering.v1.ServiceTemplateService/GetServiceTemplateByIDs"
	ServiceTemplateService_ListServiceTemplateByCategory_FullMethodName = "/backend.proto.offering.v1.ServiceTemplateService/ListServiceTemplateByCategory"
	ServiceTemplateService_UpdateServiceTemplate_FullMethodName         = "/backend.proto.offering.v1.ServiceTemplateService/UpdateServiceTemplate"
	ServiceTemplateService_DeleteServiceTemplate_FullMethodName         = "/backend.proto.offering.v1.ServiceTemplateService/DeleteServiceTemplate"
)

// ServiceTemplateServiceClient is the client API for ServiceTemplateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 服务模板服务
type ServiceTemplateServiceClient interface {
	// 创建服务类型
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateServiceTypeResponse is appropriate for this use case --)
	//
	// rpc CreateServiceType(CreateServiceTypeRequest) returns (CreateServiceTypeResponse);
	// 创建一个服务
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateServiceTemplateResponse is appropriate for this use case --)
	CreateServiceTemplate(ctx context.Context, in *CreateServiceTemplateRequest, opts ...grpc.CallOption) (*CreateServiceTemplateResponse, error)
	// 设置service的attribute值
	// (-- api-linter: core::0134::synonyms=disabled
	//
	//	aip.dev/not-precedent: Set is more appropriate for this use case --)
	//
	// rpc SetServiceAttributeValues(SetServiceAttributeValuesRequest) returns (SetServiceAttributeValuesResponse);
	// 设置service_type的attribute
	// (-- api-linter: core::0134::synonyms=disabled
	//
	//	aip.dev/not-precedent: Set is more appropriate for this use case --)
	//
	// rpc SetServiceTypeAttribute(SetServiceTypeAttributeRequest) returns (SetServiceTypeAttributeResponse);
	// 通过 repeated service_template_id 获取 ServiceTemplate 列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceTemplateByIDsResponse is appropriate for this use case --)
	//
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetServiceTemplateByIDs is appropriate for this use case --)
	GetServiceTemplateByIDs(ctx context.Context, in *GetServiceTemplateByIDsRequest, opts ...grpc.CallOption) (*GetServiceTemplateByIDsResponse, error)
	// 通过 repeated service_type_id 获取 ServiceTypeAttribute（属性值）列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceTypeAttributesResponse is appropriate for this use case --)
	//
	// rpc GetServiceTypeAttributes(GetServiceTypeAttributesRequest) returns (GetServiceTypeAttributesResponse);
	// 通过 repeated service_template_id 获取 ServiceAttributeValues 列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceAttributeValuesResponse is appropriate for this use case --)
	//
	// rpc GetServiceAttributeValues(GetServiceAttributeValuesRequest) returns (GetServiceAttributeValuesResponse);
	// 列出服务模板
	// (-- api-linter: core::0158::request-page_token-field=disabled
	// aip.dev/not-precedent: 不需要page_token --)
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: ListServiceTemplateByCategory is clear and appropriate --)
	ListServiceTemplateByCategory(ctx context.Context, in *ListServiceTemplateByCategoryRequest, opts ...grpc.CallOption) (*ListServiceTemplateByCategoryResponse, error)
	// 更新服务模板
	// (-- api-linter: core::0134::response-message-name=disabled
	//
	//	aip.dev/not-precedent: UpdateServiceTemplateResponse is appropriate for this use case --)
	UpdateServiceTemplate(ctx context.Context, in *UpdateServiceTemplateRequest, opts ...grpc.CallOption) (*UpdateServiceTemplateResponse, error)
	// 删除服务模板
	// (-- api-linter: core::0135::response-message-name=disabled
	//
	//	aip.dev/not-precedent: DeleteServiceTemplateResponse is appropriate for this use case --)
	DeleteServiceTemplate(ctx context.Context, in *DeleteServiceTemplateRequest, opts ...grpc.CallOption) (*DeleteServiceTemplateResponse, error)
}

type serviceTemplateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceTemplateServiceClient(cc grpc.ClientConnInterface) ServiceTemplateServiceClient {
	return &serviceTemplateServiceClient{cc}
}

func (c *serviceTemplateServiceClient) CreateServiceTemplate(ctx context.Context, in *CreateServiceTemplateRequest, opts ...grpc.CallOption) (*CreateServiceTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceTemplateResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_CreateServiceTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) GetServiceTemplateByIDs(ctx context.Context, in *GetServiceTemplateByIDsRequest, opts ...grpc.CallOption) (*GetServiceTemplateByIDsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceTemplateByIDsResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_GetServiceTemplateByIDs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) ListServiceTemplateByCategory(ctx context.Context, in *ListServiceTemplateByCategoryRequest, opts ...grpc.CallOption) (*ListServiceTemplateByCategoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServiceTemplateByCategoryResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_ListServiceTemplateByCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) UpdateServiceTemplate(ctx context.Context, in *UpdateServiceTemplateRequest, opts ...grpc.CallOption) (*UpdateServiceTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateServiceTemplateResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_UpdateServiceTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) DeleteServiceTemplate(ctx context.Context, in *DeleteServiceTemplateRequest, opts ...grpc.CallOption) (*DeleteServiceTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteServiceTemplateResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_DeleteServiceTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceTemplateServiceServer is the server API for ServiceTemplateService service.
// All implementations must embed UnimplementedServiceTemplateServiceServer
// for forward compatibility.
//
// 服务模板服务
type ServiceTemplateServiceServer interface {
	// 创建服务类型
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateServiceTypeResponse is appropriate for this use case --)
	//
	// rpc CreateServiceType(CreateServiceTypeRequest) returns (CreateServiceTypeResponse);
	// 创建一个服务
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateServiceTemplateResponse is appropriate for this use case --)
	CreateServiceTemplate(context.Context, *CreateServiceTemplateRequest) (*CreateServiceTemplateResponse, error)
	// 设置service的attribute值
	// (-- api-linter: core::0134::synonyms=disabled
	//
	//	aip.dev/not-precedent: Set is more appropriate for this use case --)
	//
	// rpc SetServiceAttributeValues(SetServiceAttributeValuesRequest) returns (SetServiceAttributeValuesResponse);
	// 设置service_type的attribute
	// (-- api-linter: core::0134::synonyms=disabled
	//
	//	aip.dev/not-precedent: Set is more appropriate for this use case --)
	//
	// rpc SetServiceTypeAttribute(SetServiceTypeAttributeRequest) returns (SetServiceTypeAttributeResponse);
	// 通过 repeated service_template_id 获取 ServiceTemplate 列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceTemplateByIDsResponse is appropriate for this use case --)
	//
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetServiceTemplateByIDs is appropriate for this use case --)
	GetServiceTemplateByIDs(context.Context, *GetServiceTemplateByIDsRequest) (*GetServiceTemplateByIDsResponse, error)
	// 通过 repeated service_type_id 获取 ServiceTypeAttribute（属性值）列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceTypeAttributesResponse is appropriate for this use case --)
	//
	// rpc GetServiceTypeAttributes(GetServiceTypeAttributesRequest) returns (GetServiceTypeAttributesResponse);
	// 通过 repeated service_template_id 获取 ServiceAttributeValues 列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceAttributeValuesResponse is appropriate for this use case --)
	//
	// rpc GetServiceAttributeValues(GetServiceAttributeValuesRequest) returns (GetServiceAttributeValuesResponse);
	// 列出服务模板
	// (-- api-linter: core::0158::request-page_token-field=disabled
	// aip.dev/not-precedent: 不需要page_token --)
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: ListServiceTemplateByCategory is clear and appropriate --)
	ListServiceTemplateByCategory(context.Context, *ListServiceTemplateByCategoryRequest) (*ListServiceTemplateByCategoryResponse, error)
	// 更新服务模板
	// (-- api-linter: core::0134::response-message-name=disabled
	//
	//	aip.dev/not-precedent: UpdateServiceTemplateResponse is appropriate for this use case --)
	UpdateServiceTemplate(context.Context, *UpdateServiceTemplateRequest) (*UpdateServiceTemplateResponse, error)
	// 删除服务模板
	// (-- api-linter: core::0135::response-message-name=disabled
	//
	//	aip.dev/not-precedent: DeleteServiceTemplateResponse is appropriate for this use case --)
	DeleteServiceTemplate(context.Context, *DeleteServiceTemplateRequest) (*DeleteServiceTemplateResponse, error)
	mustEmbedUnimplementedServiceTemplateServiceServer()
}

// UnimplementedServiceTemplateServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedServiceTemplateServiceServer struct{}

func (UnimplementedServiceTemplateServiceServer) CreateServiceTemplate(context.Context, *CreateServiceTemplateRequest) (*CreateServiceTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceTemplate not implemented")
}
func (UnimplementedServiceTemplateServiceServer) GetServiceTemplateByIDs(context.Context, *GetServiceTemplateByIDsRequest) (*GetServiceTemplateByIDsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceTemplateByIDs not implemented")
}
func (UnimplementedServiceTemplateServiceServer) ListServiceTemplateByCategory(context.Context, *ListServiceTemplateByCategoryRequest) (*ListServiceTemplateByCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceTemplateByCategory not implemented")
}
func (UnimplementedServiceTemplateServiceServer) UpdateServiceTemplate(context.Context, *UpdateServiceTemplateRequest) (*UpdateServiceTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServiceTemplate not implemented")
}
func (UnimplementedServiceTemplateServiceServer) DeleteServiceTemplate(context.Context, *DeleteServiceTemplateRequest) (*DeleteServiceTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServiceTemplate not implemented")
}
func (UnimplementedServiceTemplateServiceServer) mustEmbedUnimplementedServiceTemplateServiceServer() {
}
func (UnimplementedServiceTemplateServiceServer) testEmbeddedByValue() {}

// UnsafeServiceTemplateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceTemplateServiceServer will
// result in compilation errors.
type UnsafeServiceTemplateServiceServer interface {
	mustEmbedUnimplementedServiceTemplateServiceServer()
}

func RegisterServiceTemplateServiceServer(s grpc.ServiceRegistrar, srv ServiceTemplateServiceServer) {
	// If the following call pancis, it indicates UnimplementedServiceTemplateServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ServiceTemplateService_ServiceDesc, srv)
}

func _ServiceTemplateService_CreateServiceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).CreateServiceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_CreateServiceTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).CreateServiceTemplate(ctx, req.(*CreateServiceTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_GetServiceTemplateByIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceTemplateByIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).GetServiceTemplateByIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_GetServiceTemplateByIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).GetServiceTemplateByIDs(ctx, req.(*GetServiceTemplateByIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_ListServiceTemplateByCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceTemplateByCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).ListServiceTemplateByCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_ListServiceTemplateByCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).ListServiceTemplateByCategory(ctx, req.(*ListServiceTemplateByCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_UpdateServiceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).UpdateServiceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_UpdateServiceTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).UpdateServiceTemplate(ctx, req.(*UpdateServiceTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_DeleteServiceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).DeleteServiceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_DeleteServiceTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).DeleteServiceTemplate(ctx, req.(*DeleteServiceTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceTemplateService_ServiceDesc is the grpc.ServiceDesc for ServiceTemplateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceTemplateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.v1.ServiceTemplateService",
	HandlerType: (*ServiceTemplateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateServiceTemplate",
			Handler:    _ServiceTemplateService_CreateServiceTemplate_Handler,
		},
		{
			MethodName: "GetServiceTemplateByIDs",
			Handler:    _ServiceTemplateService_GetServiceTemplateByIDs_Handler,
		},
		{
			MethodName: "ListServiceTemplateByCategory",
			Handler:    _ServiceTemplateService_ListServiceTemplateByCategory_Handler,
		},
		{
			MethodName: "UpdateServiceTemplate",
			Handler:    _ServiceTemplateService_UpdateServiceTemplate_Handler,
		},
		{
			MethodName: "DeleteServiceTemplate",
			Handler:    _ServiceTemplateService_DeleteServiceTemplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/v1/template_service.proto",
}
