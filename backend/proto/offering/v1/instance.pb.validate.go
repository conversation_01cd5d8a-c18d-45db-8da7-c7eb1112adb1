// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/offering/v1/instance.proto

package offeringpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ServiceInstance with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServiceInstance) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceInstance with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceInstanceMultiError, or nil if none found.
func (m *ServiceInstance) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceInstance) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for BusinessId

	// no validation rules for CustomerId

	// no validation rules for CompanyId

	// no validation rules for AppointmentId

	// no validation rules for PetId

	// no validation rules for CareType

	// no validation rules for DateType

	// no validation rules for ServiceFactoryId

	// no validation rules for ParentId

	// no validation rules for RootId

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceInstanceValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceInstanceValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceInstanceValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceInstanceValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceInstanceValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceInstanceValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceInstanceValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceInstanceValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceInstanceValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceInstanceValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceInstanceValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceInstanceValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ServiceInstanceMultiError(errors)
	}

	return nil
}

// ServiceInstanceMultiError is an error wrapping multiple validation errors
// returned by ServiceInstance.ValidateAll() if the designated constraints
// aren't met.
type ServiceInstanceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceInstanceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceInstanceMultiError) AllErrors() []error { return m }

// ServiceInstanceValidationError is the validation error returned by
// ServiceInstance.Validate if the designated constraints aren't met.
type ServiceInstanceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceInstanceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceInstanceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceInstanceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceInstanceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceInstanceValidationError) ErrorName() string { return "ServiceInstanceValidationError" }

// Error satisfies the builtin error interface
func (e ServiceInstanceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceInstance.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceInstanceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceInstanceValidationError{}

// Validate checks the field values on ServiceInstanceFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceInstanceFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceInstanceFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceInstanceFilterMultiError, or nil if none found.
func (m *ServiceInstanceFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceInstanceFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ServiceInstanceFilterMultiError(errors)
	}

	return nil
}

// ServiceInstanceFilterMultiError is an error wrapping multiple validation
// errors returned by ServiceInstanceFilter.ValidateAll() if the designated
// constraints aren't met.
type ServiceInstanceFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceInstanceFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceInstanceFilterMultiError) AllErrors() []error { return m }

// ServiceInstanceFilterValidationError is the validation error returned by
// ServiceInstanceFilter.Validate if the designated constraints aren't met.
type ServiceInstanceFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceInstanceFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceInstanceFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceInstanceFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceInstanceFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceInstanceFilterValidationError) ErrorName() string {
	return "ServiceInstanceFilterValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceInstanceFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceInstanceFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceInstanceFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceInstanceFilterValidationError{}
