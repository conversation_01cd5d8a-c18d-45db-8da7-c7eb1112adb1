// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/offering/v1/template_service.proto

package offeringpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DeleteServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteServiceTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteServiceTemplateRequestMultiError, or nil if none found.
func (m *DeleteServiceTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteServiceTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceTemplateId

	if len(errors) > 0 {
		return DeleteServiceTemplateRequestMultiError(errors)
	}

	return nil
}

// DeleteServiceTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteServiceTemplateRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteServiceTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteServiceTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteServiceTemplateRequestMultiError) AllErrors() []error { return m }

// DeleteServiceTemplateRequestValidationError is the validation error returned
// by DeleteServiceTemplateRequest.Validate if the designated constraints
// aren't met.
type DeleteServiceTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteServiceTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteServiceTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteServiceTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteServiceTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteServiceTemplateRequestValidationError) ErrorName() string {
	return "DeleteServiceTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteServiceTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteServiceTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteServiceTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteServiceTemplateRequestValidationError{}

// Validate checks the field values on DeleteServiceTemplateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteServiceTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteServiceTemplateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteServiceTemplateResponseMultiError, or nil if none found.
func (m *DeleteServiceTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteServiceTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteServiceTemplateResponseMultiError(errors)
	}

	return nil
}

// DeleteServiceTemplateResponseMultiError is an error wrapping multiple
// validation errors returned by DeleteServiceTemplateResponse.ValidateAll()
// if the designated constraints aren't met.
type DeleteServiceTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteServiceTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteServiceTemplateResponseMultiError) AllErrors() []error { return m }

// DeleteServiceTemplateResponseValidationError is the validation error
// returned by DeleteServiceTemplateResponse.Validate if the designated
// constraints aren't met.
type DeleteServiceTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteServiceTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteServiceTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteServiceTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteServiceTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteServiceTemplateResponseValidationError) ErrorName() string {
	return "DeleteServiceTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteServiceTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteServiceTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteServiceTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteServiceTemplateResponseValidationError{}

// Validate checks the field values on UpdateServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateServiceTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateServiceTemplateRequestMultiError, or nil if none found.
func (m *UpdateServiceTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateServiceTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetServiceTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateServiceTemplateRequestValidationError{
					field:  "ServiceTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateServiceTemplateRequestValidationError{
					field:  "ServiceTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServiceTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateServiceTemplateRequestValidationError{
				field:  "ServiceTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateServiceTemplateRequestMultiError(errors)
	}

	return nil
}

// UpdateServiceTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateServiceTemplateRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateServiceTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateServiceTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateServiceTemplateRequestMultiError) AllErrors() []error { return m }

// UpdateServiceTemplateRequestValidationError is the validation error returned
// by UpdateServiceTemplateRequest.Validate if the designated constraints
// aren't met.
type UpdateServiceTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateServiceTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateServiceTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateServiceTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateServiceTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateServiceTemplateRequestValidationError) ErrorName() string {
	return "UpdateServiceTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateServiceTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateServiceTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateServiceTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateServiceTemplateRequestValidationError{}

// Validate checks the field values on UpdateServiceTemplateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateServiceTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateServiceTemplateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateServiceTemplateResponseMultiError, or nil if none found.
func (m *UpdateServiceTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateServiceTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateServiceTemplateResponseMultiError(errors)
	}

	return nil
}

// UpdateServiceTemplateResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateServiceTemplateResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateServiceTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateServiceTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateServiceTemplateResponseMultiError) AllErrors() []error { return m }

// UpdateServiceTemplateResponseValidationError is the validation error
// returned by UpdateServiceTemplateResponse.Validate if the designated
// constraints aren't met.
type UpdateServiceTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateServiceTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateServiceTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateServiceTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateServiceTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateServiceTemplateResponseValidationError) ErrorName() string {
	return "UpdateServiceTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateServiceTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateServiceTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateServiceTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateServiceTemplateResponseValidationError{}

// Validate checks the field values on ListServiceTemplateByCategoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListServiceTemplateByCategoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListServiceTemplateByCategoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListServiceTemplateByCategoryRequestMultiError, or nil if none found.
func (m *ListServiceTemplateByCategoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListServiceTemplateByCategoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := ListServiceTemplateByCategoryRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetBusinessIds()) < 1 {
		err := ListServiceTemplateByCategoryRequestValidationError{
			field:  "BusinessIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Inactive

	// no validation rules for ServiceType

	// no validation rules for CareType

	if len(errors) > 0 {
		return ListServiceTemplateByCategoryRequestMultiError(errors)
	}

	return nil
}

// ListServiceTemplateByCategoryRequestMultiError is an error wrapping multiple
// validation errors returned by
// ListServiceTemplateByCategoryRequest.ValidateAll() if the designated
// constraints aren't met.
type ListServiceTemplateByCategoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListServiceTemplateByCategoryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListServiceTemplateByCategoryRequestMultiError) AllErrors() []error { return m }

// ListServiceTemplateByCategoryRequestValidationError is the validation error
// returned by ListServiceTemplateByCategoryRequest.Validate if the designated
// constraints aren't met.
type ListServiceTemplateByCategoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListServiceTemplateByCategoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListServiceTemplateByCategoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListServiceTemplateByCategoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListServiceTemplateByCategoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListServiceTemplateByCategoryRequestValidationError) ErrorName() string {
	return "ListServiceTemplateByCategoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListServiceTemplateByCategoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListServiceTemplateByCategoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListServiceTemplateByCategoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListServiceTemplateByCategoryRequestValidationError{}

// Validate checks the field values on ListServiceTemplateByCategoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListServiceTemplateByCategoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListServiceTemplateByCategoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListServiceTemplateByCategoryResponseMultiError, or nil if none found.
func (m *ListServiceTemplateByCategoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListServiceTemplateByCategoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCategoryServiceList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListServiceTemplateByCategoryResponseValidationError{
						field:  fmt.Sprintf("CategoryServiceList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListServiceTemplateByCategoryResponseValidationError{
						field:  fmt.Sprintf("CategoryServiceList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListServiceTemplateByCategoryResponseValidationError{
					field:  fmt.Sprintf("CategoryServiceList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListServiceTemplateByCategoryResponseMultiError(errors)
	}

	return nil
}

// ListServiceTemplateByCategoryResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListServiceTemplateByCategoryResponse.ValidateAll() if the designated
// constraints aren't met.
type ListServiceTemplateByCategoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListServiceTemplateByCategoryResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListServiceTemplateByCategoryResponseMultiError) AllErrors() []error { return m }

// ListServiceTemplateByCategoryResponseValidationError is the validation error
// returned by ListServiceTemplateByCategoryResponse.Validate if the
// designated constraints aren't met.
type ListServiceTemplateByCategoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListServiceTemplateByCategoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListServiceTemplateByCategoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListServiceTemplateByCategoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListServiceTemplateByCategoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListServiceTemplateByCategoryResponseValidationError) ErrorName() string {
	return "ListServiceTemplateByCategoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListServiceTemplateByCategoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListServiceTemplateByCategoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListServiceTemplateByCategoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListServiceTemplateByCategoryResponseValidationError{}

// Validate checks the field values on SetServiceAttributeValuesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SetServiceAttributeValuesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetServiceAttributeValuesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SetServiceAttributeValuesRequestMultiError, or nil if none found.
func (m *SetServiceAttributeValuesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetServiceAttributeValuesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceTemplateId

	for idx, item := range m.GetValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetServiceAttributeValuesRequestValidationError{
						field:  fmt.Sprintf("Values[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetServiceAttributeValuesRequestValidationError{
						field:  fmt.Sprintf("Values[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetServiceAttributeValuesRequestValidationError{
					field:  fmt.Sprintf("Values[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetServiceAttributeValuesRequestMultiError(errors)
	}

	return nil
}

// SetServiceAttributeValuesRequestMultiError is an error wrapping multiple
// validation errors returned by
// SetServiceAttributeValuesRequest.ValidateAll() if the designated
// constraints aren't met.
type SetServiceAttributeValuesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetServiceAttributeValuesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetServiceAttributeValuesRequestMultiError) AllErrors() []error { return m }

// SetServiceAttributeValuesRequestValidationError is the validation error
// returned by SetServiceAttributeValuesRequest.Validate if the designated
// constraints aren't met.
type SetServiceAttributeValuesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetServiceAttributeValuesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetServiceAttributeValuesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetServiceAttributeValuesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetServiceAttributeValuesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetServiceAttributeValuesRequestValidationError) ErrorName() string {
	return "SetServiceAttributeValuesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetServiceAttributeValuesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetServiceAttributeValuesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetServiceAttributeValuesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetServiceAttributeValuesRequestValidationError{}

// Validate checks the field values on SetServiceAttributeValuesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SetServiceAttributeValuesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetServiceAttributeValuesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SetServiceAttributeValuesResponseMultiError, or nil if none found.
func (m *SetServiceAttributeValuesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetServiceAttributeValuesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SetServiceAttributeValuesResponseMultiError(errors)
	}

	return nil
}

// SetServiceAttributeValuesResponseMultiError is an error wrapping multiple
// validation errors returned by
// SetServiceAttributeValuesResponse.ValidateAll() if the designated
// constraints aren't met.
type SetServiceAttributeValuesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetServiceAttributeValuesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetServiceAttributeValuesResponseMultiError) AllErrors() []error { return m }

// SetServiceAttributeValuesResponseValidationError is the validation error
// returned by SetServiceAttributeValuesResponse.Validate if the designated
// constraints aren't met.
type SetServiceAttributeValuesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetServiceAttributeValuesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetServiceAttributeValuesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetServiceAttributeValuesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetServiceAttributeValuesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetServiceAttributeValuesResponseValidationError) ErrorName() string {
	return "SetServiceAttributeValuesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetServiceAttributeValuesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetServiceAttributeValuesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetServiceAttributeValuesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetServiceAttributeValuesResponseValidationError{}

// Validate checks the field values on CreateServiceTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceTypeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateServiceTypeRequestMultiError, or nil if none found.
func (m *CreateServiceTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for Type

	// no validation rules for Description

	if len(errors) > 0 {
		return CreateServiceTypeRequestMultiError(errors)
	}

	return nil
}

// CreateServiceTypeRequestMultiError is an error wrapping multiple validation
// errors returned by CreateServiceTypeRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateServiceTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceTypeRequestMultiError) AllErrors() []error { return m }

// CreateServiceTypeRequestValidationError is the validation error returned by
// CreateServiceTypeRequest.Validate if the designated constraints aren't met.
type CreateServiceTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceTypeRequestValidationError) ErrorName() string {
	return "CreateServiceTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceTypeRequestValidationError{}

// Validate checks the field values on CreateServiceTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceTypeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateServiceTypeResponseMultiError, or nil if none found.
func (m *CreateServiceTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceTypeId

	if len(errors) > 0 {
		return CreateServiceTypeResponseMultiError(errors)
	}

	return nil
}

// CreateServiceTypeResponseMultiError is an error wrapping multiple validation
// errors returned by CreateServiceTypeResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateServiceTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceTypeResponseMultiError) AllErrors() []error { return m }

// CreateServiceTypeResponseValidationError is the validation error returned by
// CreateServiceTypeResponse.Validate if the designated constraints aren't met.
type CreateServiceTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceTypeResponseValidationError) ErrorName() string {
	return "CreateServiceTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceTypeResponseValidationError{}

// Validate checks the field values on SetServiceTypeAttributeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetServiceTypeAttributeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetServiceTypeAttributeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SetServiceTypeAttributeRequestMultiError, or nil if none found.
func (m *SetServiceTypeAttributeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetServiceTypeAttributeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceTypeId

	for idx, item := range m.GetServiceAttributeValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetServiceTypeAttributeRequestValidationError{
						field:  fmt.Sprintf("ServiceAttributeValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetServiceTypeAttributeRequestValidationError{
						field:  fmt.Sprintf("ServiceAttributeValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetServiceTypeAttributeRequestValidationError{
					field:  fmt.Sprintf("ServiceAttributeValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetServiceTypeAttributeRequestMultiError(errors)
	}

	return nil
}

// SetServiceTypeAttributeRequestMultiError is an error wrapping multiple
// validation errors returned by SetServiceTypeAttributeRequest.ValidateAll()
// if the designated constraints aren't met.
type SetServiceTypeAttributeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetServiceTypeAttributeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetServiceTypeAttributeRequestMultiError) AllErrors() []error { return m }

// SetServiceTypeAttributeRequestValidationError is the validation error
// returned by SetServiceTypeAttributeRequest.Validate if the designated
// constraints aren't met.
type SetServiceTypeAttributeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetServiceTypeAttributeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetServiceTypeAttributeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetServiceTypeAttributeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetServiceTypeAttributeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetServiceTypeAttributeRequestValidationError) ErrorName() string {
	return "SetServiceTypeAttributeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetServiceTypeAttributeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetServiceTypeAttributeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetServiceTypeAttributeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetServiceTypeAttributeRequestValidationError{}

// Validate checks the field values on SetServiceTypeAttributeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetServiceTypeAttributeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetServiceTypeAttributeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SetServiceTypeAttributeResponseMultiError, or nil if none found.
func (m *SetServiceTypeAttributeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetServiceTypeAttributeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SetServiceTypeAttributeResponseMultiError(errors)
	}

	return nil
}

// SetServiceTypeAttributeResponseMultiError is an error wrapping multiple
// validation errors returned by SetServiceTypeAttributeResponse.ValidateAll()
// if the designated constraints aren't met.
type SetServiceTypeAttributeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetServiceTypeAttributeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetServiceTypeAttributeResponseMultiError) AllErrors() []error { return m }

// SetServiceTypeAttributeResponseValidationError is the validation error
// returned by SetServiceTypeAttributeResponse.Validate if the designated
// constraints aren't met.
type SetServiceTypeAttributeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetServiceTypeAttributeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetServiceTypeAttributeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetServiceTypeAttributeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetServiceTypeAttributeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetServiceTypeAttributeResponseValidationError) ErrorName() string {
	return "SetServiceTypeAttributeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetServiceTypeAttributeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetServiceTypeAttributeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetServiceTypeAttributeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetServiceTypeAttributeResponseValidationError{}

// Validate checks the field values on CreateServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateServiceTemplateRequestMultiError, or nil if none found.
func (m *CreateServiceTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetServiceTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateServiceTemplateRequestValidationError{
					field:  "ServiceTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateServiceTemplateRequestValidationError{
					field:  "ServiceTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServiceTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateServiceTemplateRequestValidationError{
				field:  "ServiceTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateServiceTemplateRequestMultiError(errors)
	}

	return nil
}

// CreateServiceTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by CreateServiceTemplateRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateServiceTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceTemplateRequestMultiError) AllErrors() []error { return m }

// CreateServiceTemplateRequestValidationError is the validation error returned
// by CreateServiceTemplateRequest.Validate if the designated constraints
// aren't met.
type CreateServiceTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceTemplateRequestValidationError) ErrorName() string {
	return "CreateServiceTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceTemplateRequestValidationError{}

// Validate checks the field values on CreateServiceTemplateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceTemplateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateServiceTemplateResponseMultiError, or nil if none found.
func (m *CreateServiceTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceTemplateId

	if len(errors) > 0 {
		return CreateServiceTemplateResponseMultiError(errors)
	}

	return nil
}

// CreateServiceTemplateResponseMultiError is an error wrapping multiple
// validation errors returned by CreateServiceTemplateResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateServiceTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceTemplateResponseMultiError) AllErrors() []error { return m }

// CreateServiceTemplateResponseValidationError is the validation error
// returned by CreateServiceTemplateResponse.Validate if the designated
// constraints aren't met.
type CreateServiceTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceTemplateResponseValidationError) ErrorName() string {
	return "CreateServiceTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceTemplateResponseValidationError{}

// Validate checks the field values on GetServiceTemplateByIDsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceTemplateByIDsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceTemplateByIDsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceTemplateByIDsRequestMultiError, or nil if none found.
func (m *GetServiceTemplateByIDsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceTemplateByIDsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetServiceTemplateByIDsRequestMultiError(errors)
	}

	return nil
}

// GetServiceTemplateByIDsRequestMultiError is an error wrapping multiple
// validation errors returned by GetServiceTemplateByIDsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetServiceTemplateByIDsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceTemplateByIDsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceTemplateByIDsRequestMultiError) AllErrors() []error { return m }

// GetServiceTemplateByIDsRequestValidationError is the validation error
// returned by GetServiceTemplateByIDsRequest.Validate if the designated
// constraints aren't met.
type GetServiceTemplateByIDsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceTemplateByIDsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceTemplateByIDsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceTemplateByIDsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceTemplateByIDsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceTemplateByIDsRequestValidationError) ErrorName() string {
	return "GetServiceTemplateByIDsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceTemplateByIDsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceTemplateByIDsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceTemplateByIDsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceTemplateByIDsRequestValidationError{}

// Validate checks the field values on GetServiceTemplateByIDsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceTemplateByIDsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceTemplateByIDsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceTemplateByIDsResponseMultiError, or nil if none found.
func (m *GetServiceTemplateByIDsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceTemplateByIDsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetServiceTemplateList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetServiceTemplateByIDsResponseValidationError{
						field:  fmt.Sprintf("ServiceTemplateList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetServiceTemplateByIDsResponseValidationError{
						field:  fmt.Sprintf("ServiceTemplateList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetServiceTemplateByIDsResponseValidationError{
					field:  fmt.Sprintf("ServiceTemplateList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetServiceTemplateByIDsResponseMultiError(errors)
	}

	return nil
}

// GetServiceTemplateByIDsResponseMultiError is an error wrapping multiple
// validation errors returned by GetServiceTemplateByIDsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetServiceTemplateByIDsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceTemplateByIDsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceTemplateByIDsResponseMultiError) AllErrors() []error { return m }

// GetServiceTemplateByIDsResponseValidationError is the validation error
// returned by GetServiceTemplateByIDsResponse.Validate if the designated
// constraints aren't met.
type GetServiceTemplateByIDsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceTemplateByIDsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceTemplateByIDsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceTemplateByIDsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceTemplateByIDsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceTemplateByIDsResponseValidationError) ErrorName() string {
	return "GetServiceTemplateByIDsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceTemplateByIDsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceTemplateByIDsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceTemplateByIDsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceTemplateByIDsResponseValidationError{}

// Validate checks the field values on GetServiceTypeAttributesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceTypeAttributesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceTypeAttributesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceTypeAttributesRequestMultiError, or nil if none found.
func (m *GetServiceTypeAttributesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceTypeAttributesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetServiceTypeAttributesRequestMultiError(errors)
	}

	return nil
}

// GetServiceTypeAttributesRequestMultiError is an error wrapping multiple
// validation errors returned by GetServiceTypeAttributesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetServiceTypeAttributesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceTypeAttributesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceTypeAttributesRequestMultiError) AllErrors() []error { return m }

// GetServiceTypeAttributesRequestValidationError is the validation error
// returned by GetServiceTypeAttributesRequest.Validate if the designated
// constraints aren't met.
type GetServiceTypeAttributesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceTypeAttributesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceTypeAttributesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceTypeAttributesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceTypeAttributesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceTypeAttributesRequestValidationError) ErrorName() string {
	return "GetServiceTypeAttributesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceTypeAttributesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceTypeAttributesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceTypeAttributesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceTypeAttributesRequestValidationError{}

// Validate checks the field values on GetServiceTypeAttributesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetServiceTypeAttributesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceTypeAttributesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceTypeAttributesResponseMultiError, or nil if none found.
func (m *GetServiceTypeAttributesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceTypeAttributesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]int64, len(m.GetServiceTypeAttributes()))
		i := 0
		for key := range m.GetServiceTypeAttributes() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetServiceTypeAttributes()[key]
			_ = val

			// no validation rules for ServiceTypeAttributes[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetServiceTypeAttributesResponseValidationError{
							field:  fmt.Sprintf("ServiceTypeAttributes[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetServiceTypeAttributesResponseValidationError{
							field:  fmt.Sprintf("ServiceTypeAttributes[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetServiceTypeAttributesResponseValidationError{
						field:  fmt.Sprintf("ServiceTypeAttributes[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetServiceTypeAttributesResponseMultiError(errors)
	}

	return nil
}

// GetServiceTypeAttributesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetServiceTypeAttributesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetServiceTypeAttributesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceTypeAttributesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceTypeAttributesResponseMultiError) AllErrors() []error { return m }

// GetServiceTypeAttributesResponseValidationError is the validation error
// returned by GetServiceTypeAttributesResponse.Validate if the designated
// constraints aren't met.
type GetServiceTypeAttributesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceTypeAttributesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceTypeAttributesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceTypeAttributesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceTypeAttributesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceTypeAttributesResponseValidationError) ErrorName() string {
	return "GetServiceTypeAttributesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceTypeAttributesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceTypeAttributesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceTypeAttributesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceTypeAttributesResponseValidationError{}

// Validate checks the field values on ServiceTypeAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceTypeAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceTypeAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceTypeAttributesMultiError, or nil if none found.
func (m *ServiceTypeAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceTypeAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetServiceAttributeValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceTypeAttributesValidationError{
						field:  fmt.Sprintf("ServiceAttributeValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceTypeAttributesValidationError{
						field:  fmt.Sprintf("ServiceAttributeValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceTypeAttributesValidationError{
					field:  fmt.Sprintf("ServiceAttributeValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ServiceTypeAttributesMultiError(errors)
	}

	return nil
}

// ServiceTypeAttributesMultiError is an error wrapping multiple validation
// errors returned by ServiceTypeAttributes.ValidateAll() if the designated
// constraints aren't met.
type ServiceTypeAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceTypeAttributesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceTypeAttributesMultiError) AllErrors() []error { return m }

// ServiceTypeAttributesValidationError is the validation error returned by
// ServiceTypeAttributes.Validate if the designated constraints aren't met.
type ServiceTypeAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceTypeAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceTypeAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceTypeAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceTypeAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceTypeAttributesValidationError) ErrorName() string {
	return "ServiceTypeAttributesValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceTypeAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceTypeAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceTypeAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceTypeAttributesValidationError{}

// Validate checks the field values on GetServiceAttributeValuesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetServiceAttributeValuesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceAttributeValuesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceAttributeValuesRequestMultiError, or nil if none found.
func (m *GetServiceAttributeValuesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceAttributeValuesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetServiceAttributeValuesRequestMultiError(errors)
	}

	return nil
}

// GetServiceAttributeValuesRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetServiceAttributeValuesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetServiceAttributeValuesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceAttributeValuesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceAttributeValuesRequestMultiError) AllErrors() []error { return m }

// GetServiceAttributeValuesRequestValidationError is the validation error
// returned by GetServiceAttributeValuesRequest.Validate if the designated
// constraints aren't met.
type GetServiceAttributeValuesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceAttributeValuesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceAttributeValuesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceAttributeValuesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceAttributeValuesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceAttributeValuesRequestValidationError) ErrorName() string {
	return "GetServiceAttributeValuesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceAttributeValuesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceAttributeValuesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceAttributeValuesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceAttributeValuesRequestValidationError{}

// Validate checks the field values on GetServiceAttributeValuesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetServiceAttributeValuesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceAttributeValuesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetServiceAttributeValuesResponseMultiError, or nil if none found.
func (m *GetServiceAttributeValuesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceAttributeValuesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]int64, len(m.GetServiceAttributeValues()))
		i := 0
		for key := range m.GetServiceAttributeValues() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetServiceAttributeValues()[key]
			_ = val

			// no validation rules for ServiceAttributeValues[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetServiceAttributeValuesResponseValidationError{
							field:  fmt.Sprintf("ServiceAttributeValues[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetServiceAttributeValuesResponseValidationError{
							field:  fmt.Sprintf("ServiceAttributeValues[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetServiceAttributeValuesResponseValidationError{
						field:  fmt.Sprintf("ServiceAttributeValues[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetServiceAttributeValuesResponseMultiError(errors)
	}

	return nil
}

// GetServiceAttributeValuesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetServiceAttributeValuesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetServiceAttributeValuesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceAttributeValuesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceAttributeValuesResponseMultiError) AllErrors() []error { return m }

// GetServiceAttributeValuesResponseValidationError is the validation error
// returned by GetServiceAttributeValuesResponse.Validate if the designated
// constraints aren't met.
type GetServiceAttributeValuesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceAttributeValuesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceAttributeValuesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceAttributeValuesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceAttributeValuesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceAttributeValuesResponseValidationError) ErrorName() string {
	return "GetServiceAttributeValuesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceAttributeValuesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceAttributeValuesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceAttributeValuesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceAttributeValuesResponseValidationError{}

// Validate checks the field values on ServiceAttributeValues with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceAttributeValues) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceAttributeValues with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceAttributeValuesMultiError, or nil if none found.
func (m *ServiceAttributeValues) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceAttributeValues) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceAttributeValuesValidationError{
						field:  fmt.Sprintf("Values[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceAttributeValuesValidationError{
						field:  fmt.Sprintf("Values[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceAttributeValuesValidationError{
					field:  fmt.Sprintf("Values[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ServiceAttributeValuesMultiError(errors)
	}

	return nil
}

// ServiceAttributeValuesMultiError is an error wrapping multiple validation
// errors returned by ServiceAttributeValues.ValidateAll() if the designated
// constraints aren't met.
type ServiceAttributeValuesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceAttributeValuesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceAttributeValuesMultiError) AllErrors() []error { return m }

// ServiceAttributeValuesValidationError is the validation error returned by
// ServiceAttributeValues.Validate if the designated constraints aren't met.
type ServiceAttributeValuesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceAttributeValuesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceAttributeValuesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceAttributeValuesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceAttributeValuesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceAttributeValuesValidationError) ErrorName() string {
	return "ServiceAttributeValuesValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceAttributeValuesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceAttributeValues.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceAttributeValuesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceAttributeValuesValidationError{}
