// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/offering/v1/common.proto

package offeringpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 排序方式
type SortType int32

const (
	// SORT_TYPE_UNSPECIFIED 默认排序类型
	SortType_SORT_TYPE_UNSPECIFIED SortType = 0
	// SORT_TYPE_START_TIME 按服务开始时间
	SortType_SORT_TYPE_START_TIME SortType = 1
)

// Enum value maps for SortType.
var (
	SortType_name = map[int32]string{
		0: "SORT_TYPE_UNSPECIFIED",
		1: "SORT_TYPE_START_TIME",
	}
	SortType_value = map[string]int32{
		"SORT_TYPE_UNSPECIFIED": 0,
		"SORT_TYPE_START_TIME":  1,
	}
)

func (x SortType) Enum() *SortType {
	p := new(SortType)
	*p = x
	return p
}

func (x SortType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[0].Descriptor()
}

func (SortType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[0]
}

func (x SortType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortType.Descriptor instead.
func (SortType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{0}
}

// 日期类型
type DateType int32

const (
	// 未指定日期类型
	DateType_DATE_TYPE_UNSPECIFIED DateType = 0
	// 每日日期
	DateType_DATE_TYPE_DATE_EVERYDAY DateType = 1
	// 特定日期
	DateType_DATE_TYPE_SPECIFIC_DATE DateType = 2
	// 日期点
	DateType_DATE_TYPE_DATE_POINT DateType = 3
	// 每日包含退房日
	DateType_DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY DateType = 4
	// 每日排除入住日
	DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY DateType = 5
	// 最后一天
	DateType_DATE_TYPE_LAST_DAY DateType = 6
	// 第一天
	DateType_DATE_TYPE_FIRST_DAY DateType = 7
)

// Enum value maps for DateType.
var (
	DateType_name = map[int32]string{
		0: "DATE_TYPE_UNSPECIFIED",
		1: "DATE_TYPE_DATE_EVERYDAY",
		2: "DATE_TYPE_SPECIFIC_DATE",
		3: "DATE_TYPE_DATE_POINT",
		4: "DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY",
		5: "DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY",
		6: "DATE_TYPE_LAST_DAY",
		7: "DATE_TYPE_FIRST_DAY",
	}
	DateType_value = map[string]int32{
		"DATE_TYPE_UNSPECIFIED":                   0,
		"DATE_TYPE_DATE_EVERYDAY":                 1,
		"DATE_TYPE_SPECIFIC_DATE":                 2,
		"DATE_TYPE_DATE_POINT":                    3,
		"DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY": 4,
		"DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY":   5,
		"DATE_TYPE_LAST_DAY":                      6,
		"DATE_TYPE_FIRST_DAY":                     7,
	}
)

func (x DateType) Enum() *DateType {
	p := new(DateType)
	*p = x
	return p
}

func (x DateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DateType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[1].Descriptor()
}

func (DateType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[1]
}

func (x DateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DateType.Descriptor instead.
func (DateType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{1}
}

// 服务类型
type CareType int32

const (
	// 未知护理类型
	CareType_CARE_TYPE_UNSPECIFIED CareType = 0
	// GROOMING类型
	CareType_CARE_TYPE_GROOMING CareType = 1
	// BOARDING类型
	CareType_CARE_TYPE_BOARDING CareType = 2
	// DAYCARE类型
	CareType_CARE_TYPE_DAYCARE CareType = 3
	// EVALUATION类型
	CareType_CARE_TYPE_EVALUATION CareType = 4
	// DOG_WALKING类型
	CareType_CARE_TYPE_DOG_WALKING CareType = 5
	// GROUP_CLASS类型
	CareType_CARE_TYPE_GROUP_CLASS CareType = 6
)

// Enum value maps for CareType.
var (
	CareType_name = map[int32]string{
		0: "CARE_TYPE_UNSPECIFIED",
		1: "CARE_TYPE_GROOMING",
		2: "CARE_TYPE_BOARDING",
		3: "CARE_TYPE_DAYCARE",
		4: "CARE_TYPE_EVALUATION",
		5: "CARE_TYPE_DOG_WALKING",
		6: "CARE_TYPE_GROUP_CLASS",
	}
	CareType_value = map[string]int32{
		"CARE_TYPE_UNSPECIFIED": 0,
		"CARE_TYPE_GROOMING":    1,
		"CARE_TYPE_BOARDING":    2,
		"CARE_TYPE_DAYCARE":     3,
		"CARE_TYPE_EVALUATION":  4,
		"CARE_TYPE_DOG_WALKING": 5,
		"CARE_TYPE_GROUP_CLASS": 6,
	}
)

func (x CareType) Enum() *CareType {
	p := new(CareType)
	*p = x
	return p
}

func (x CareType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CareType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[2].Descriptor()
}

func (CareType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[2]
}

func (x CareType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CareType.Descriptor instead.
func (CareType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{2}
}

// 服务类型枚举
type ServiceType int32

const (
	// 未指定的服务类型
	ServiceType_SERVICE_TYPE_UNSPECIFIED ServiceType = 0
	// 服务类型
	ServiceType_SERVICE_TYPE_SERVICE ServiceType = 1
	// 选项类型
	ServiceType_SERVICE_TYPE_OPTION ServiceType = 2
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNSPECIFIED",
		1: "SERVICE_TYPE_SERVICE",
		2: "SERVICE_TYPE_OPTION",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNSPECIFIED": 0,
		"SERVICE_TYPE_SERVICE":     1,
		"SERVICE_TYPE_OPTION":      2,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[3].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[3]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{3}
}

// 价格单位, 1-per session, 2-per night, 3-per hour, 4-per day
type PriceUnit int32

const (
	// 未指定
	PriceUnit_PRICE_UNIT_UNSPECIFIED PriceUnit = 0
	// 每次服务
	PriceUnit_PRICE_UNIT_PER_SESSION PriceUnit = 1
	// 每晚
	PriceUnit_PRICE_UNIT_PER_NIGHT PriceUnit = 2
	// 每小时
	PriceUnit_PRICE_UNIT_PER_HOUR PriceUnit = 3
	// 每天
	PriceUnit_PRICE_UNIT_PER_DAY PriceUnit = 4
)

// Enum value maps for PriceUnit.
var (
	PriceUnit_name = map[int32]string{
		0: "PRICE_UNIT_UNSPECIFIED",
		1: "PRICE_UNIT_PER_SESSION",
		2: "PRICE_UNIT_PER_NIGHT",
		3: "PRICE_UNIT_PER_HOUR",
		4: "PRICE_UNIT_PER_DAY",
	}
	PriceUnit_value = map[string]int32{
		"PRICE_UNIT_UNSPECIFIED": 0,
		"PRICE_UNIT_PER_SESSION": 1,
		"PRICE_UNIT_PER_NIGHT":   2,
		"PRICE_UNIT_PER_HOUR":    3,
		"PRICE_UNIT_PER_DAY":     4,
	}
)

func (x PriceUnit) Enum() *PriceUnit {
	p := new(PriceUnit)
	*p = x
	return p
}

func (x PriceUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[4].Descriptor()
}

func (PriceUnit) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[4]
}

func (x PriceUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceUnit.Descriptor instead.
func (PriceUnit) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{4}
}

// 服务来源
type ServiceSource int32

const (
	// 未指定
	ServiceSource_SERVICE_SOURCE_UNSPECIFIED ServiceSource = 0
	// 平台
	ServiceSource_SERVICE_SOURCE_PLATFORM ServiceSource = 1
	// 企业
	ServiceSource_SERVICE_SOURCE_ENTERPRISE ServiceSource = 2
)

// Enum value maps for ServiceSource.
var (
	ServiceSource_name = map[int32]string{
		0: "SERVICE_SOURCE_UNSPECIFIED",
		1: "SERVICE_SOURCE_PLATFORM",
		2: "SERVICE_SOURCE_ENTERPRISE",
	}
	ServiceSource_value = map[string]int32{
		"SERVICE_SOURCE_UNSPECIFIED": 0,
		"SERVICE_SOURCE_PLATFORM":    1,
		"SERVICE_SOURCE_ENTERPRISE":  2,
	}
)

func (x ServiceSource) Enum() *ServiceSource {
	p := new(ServiceSource)
	*p = x
	return p
}

func (x ServiceSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceSource) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_common_proto_enumTypes[5].Descriptor()
}

func (ServiceSource) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_common_proto_enumTypes[5]
}

func (x ServiceSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceSource.Descriptor instead.
func (ServiceSource) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{5}
}

// 分页信息
type PaginationRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 偏移量，默认0
	Offset int32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// 每页数量，默认200
	Limit         int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRef) Reset() {
	*x = PaginationRef{}
	mi := &file_backend_proto_offering_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRef) ProtoMessage() {}

func (x *PaginationRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRef.ProtoReflect.Descriptor instead.
func (*PaginationRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *PaginationRef) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *PaginationRef) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

var File_backend_proto_offering_v1_common_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_common_proto_rawDesc = "" +
	"\n" +
	"&backend/proto/offering/v1/common.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17validate/validate.proto\"=\n" +
	"\rPaginationRef\x12\x16\n" +
	"\x06offset\x18\x01 \x01(\x05R\x06offset\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit*?\n" +
	"\bSortType\x12\x19\n" +
	"\x15SORT_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14SORT_TYPE_START_TIME\x10\x01*\x82\x02\n" +
	"\bDateType\x12\x19\n" +
	"\x15DATE_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17DATE_TYPE_DATE_EVERYDAY\x10\x01\x12\x1b\n" +
	"\x17DATE_TYPE_SPECIFIC_DATE\x10\x02\x12\x18\n" +
	"\x14DATE_TYPE_DATE_POINT\x10\x03\x12+\n" +
	"'DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY\x10\x04\x12)\n" +
	"%DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY\x10\x05\x12\x16\n" +
	"\x12DATE_TYPE_LAST_DAY\x10\x06\x12\x17\n" +
	"\x13DATE_TYPE_FIRST_DAY\x10\a*\xbc\x01\n" +
	"\bCareType\x12\x19\n" +
	"\x15CARE_TYPE_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12CARE_TYPE_GROOMING\x10\x01\x12\x16\n" +
	"\x12CARE_TYPE_BOARDING\x10\x02\x12\x15\n" +
	"\x11CARE_TYPE_DAYCARE\x10\x03\x12\x18\n" +
	"\x14CARE_TYPE_EVALUATION\x10\x04\x12\x19\n" +
	"\x15CARE_TYPE_DOG_WALKING\x10\x05\x12\x19\n" +
	"\x15CARE_TYPE_GROUP_CLASS\x10\x06*^\n" +
	"\vServiceType\x12\x1c\n" +
	"\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14SERVICE_TYPE_SERVICE\x10\x01\x12\x17\n" +
	"\x13SERVICE_TYPE_OPTION\x10\x02*\x8e\x01\n" +
	"\tPriceUnit\x12\x1a\n" +
	"\x16PRICE_UNIT_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16PRICE_UNIT_PER_SESSION\x10\x01\x12\x18\n" +
	"\x14PRICE_UNIT_PER_NIGHT\x10\x02\x12\x17\n" +
	"\x13PRICE_UNIT_PER_HOUR\x10\x03\x12\x16\n" +
	"\x12PRICE_UNIT_PER_DAY\x10\x04*k\n" +
	"\rServiceSource\x12\x1e\n" +
	"\x1aSERVICE_SOURCE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17SERVICE_SOURCE_PLATFORM\x10\x01\x12\x1d\n" +
	"\x19SERVICE_SOURCE_ENTERPRISE\x10\x02Bk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_common_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_common_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_common_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_common_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_common_proto_rawDesc), len(file_backend_proto_offering_v1_common_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_common_proto_rawDescData
}

var file_backend_proto_offering_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_backend_proto_offering_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_offering_v1_common_proto_goTypes = []any{
	(SortType)(0),         // 0: backend.proto.offering.v1.SortType
	(DateType)(0),         // 1: backend.proto.offering.v1.DateType
	(CareType)(0),         // 2: backend.proto.offering.v1.CareType
	(ServiceType)(0),      // 3: backend.proto.offering.v1.ServiceType
	(PriceUnit)(0),        // 4: backend.proto.offering.v1.PriceUnit
	(ServiceSource)(0),    // 5: backend.proto.offering.v1.ServiceSource
	(*PaginationRef)(nil), // 6: backend.proto.offering.v1.PaginationRef
}
var file_backend_proto_offering_v1_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_common_proto_init() }
func file_backend_proto_offering_v1_common_proto_init() {
	if File_backend_proto_offering_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_common_proto_rawDesc), len(file_backend_proto_offering_v1_common_proto_rawDesc)),
			NumEnums:      6,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_common_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_common_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_common_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_common_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_common_proto = out.File
	file_backend_proto_offering_v1_common_proto_goTypes = nil
	file_backend_proto_offering_v1_common_proto_depIdxs = nil
}
