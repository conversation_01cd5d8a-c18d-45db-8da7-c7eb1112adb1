// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/offering/v1/template.proto

package offeringpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ServiceAttributeValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceAttributeValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceAttributeValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceAttributeValueMultiError, or nil if none found.
func (m *ServiceAttributeValue) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceAttributeValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TemplateId

	// no validation rules for Attribute

	switch v := m.Value.(type) {
	case *ServiceAttributeValue_TextValue:
		if v == nil {
			err := ServiceAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TextValue
	case *ServiceAttributeValue_IntValue:
		if v == nil {
			err := ServiceAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for IntValue
	case *ServiceAttributeValue_DecimalValue:
		if v == nil {
			err := ServiceAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DecimalValue
	case *ServiceAttributeValue_BoolValue:
		if v == nil {
			err := ServiceAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for BoolValue
	case *ServiceAttributeValue_JsonValue:
		if v == nil {
			err := ServiceAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for JsonValue
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ServiceAttributeValueMultiError(errors)
	}

	return nil
}

// ServiceAttributeValueMultiError is an error wrapping multiple validation
// errors returned by ServiceAttributeValue.ValidateAll() if the designated
// constraints aren't met.
type ServiceAttributeValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceAttributeValueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceAttributeValueMultiError) AllErrors() []error { return m }

// ServiceAttributeValueValidationError is the validation error returned by
// ServiceAttributeValue.Validate if the designated constraints aren't met.
type ServiceAttributeValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceAttributeValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceAttributeValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceAttributeValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceAttributeValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceAttributeValueValidationError) ErrorName() string {
	return "ServiceAttributeValueValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceAttributeValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceAttributeValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceAttributeValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceAttributeValueValidationError{}

// Validate checks the field values on ServiceAvailability with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceAvailability) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceAvailability with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceAvailabilityMultiError, or nil if none found.
func (m *ServiceAvailability) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceAvailability) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Inactive

	// no validation rules for IsAllBreed

	// no validation rules for IsAllWeightRange

	// no validation rules for WeightDownLimit

	// no validation rules for WeightUpLimit

	// no validation rules for IsAllCoatFilter

	// no validation rules for ServiceFilter

	// no validation rules for IsAllPetSize

	// no validation rules for AllowedPetSizeList

	// no validation rules for IsAllLodging

	if len(errors) > 0 {
		return ServiceAvailabilityMultiError(errors)
	}

	return nil
}

// ServiceAvailabilityMultiError is an error wrapping multiple validation
// errors returned by ServiceAvailability.ValidateAll() if the designated
// constraints aren't met.
type ServiceAvailabilityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceAvailabilityMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceAvailabilityMultiError) AllErrors() []error { return m }

// ServiceAvailabilityValidationError is the validation error returned by
// ServiceAvailability.Validate if the designated constraints aren't met.
type ServiceAvailabilityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceAvailabilityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceAvailabilityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceAvailabilityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceAvailabilityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceAvailabilityValidationError) ErrorName() string {
	return "ServiceAvailabilityValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceAvailabilityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceAvailability.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceAvailabilityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceAvailabilityValidationError{}

// Validate checks the field values on Pricing with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Pricing) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Pricing with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PricingMultiError, or nil if none found.
func (m *Pricing) ValidateAll() error {
	return m.validate(true)
}

func (m *Pricing) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaxId

	// no validation rules for Price

	// no validation rules for PriceUnit

	// no validation rules for AddCommission

	// no validation rules for CanTip

	if len(errors) > 0 {
		return PricingMultiError(errors)
	}

	return nil
}

// PricingMultiError is an error wrapping multiple validation errors returned
// by Pricing.ValidateAll() if the designated constraints aren't met.
type PricingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PricingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PricingMultiError) AllErrors() []error { return m }

// PricingValidationError is the validation error returned by Pricing.Validate
// if the designated constraints aren't met.
type PricingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PricingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PricingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PricingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PricingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PricingValidationError) ErrorName() string { return "PricingValidationError" }

// Error satisfies the builtin error interface
func (e PricingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPricing.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PricingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PricingValidationError{}

// Validate checks the field values on ServiceTemplate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServiceTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceTemplateMultiError, or nil if none found.
func (m *ServiceTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CompanyId

	// no validation rules for CategoryId

	// no validation rules for CareType

	// no validation rules for Type

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Duration

	// no validation rules for MaxDuration

	// no validation rules for TaxId

	// no validation rules for ColorCode

	// no validation rules for IsActive

	// no validation rules for Price

	// no validation rules for PriceUint

	// no validation rules for CurrencyCode

	// no validation rules for Source

	// no validation rules for IsRequirePrerequisiteClass

	if len(errors) > 0 {
		return ServiceTemplateMultiError(errors)
	}

	return nil
}

// ServiceTemplateMultiError is an error wrapping multiple validation errors
// returned by ServiceTemplate.ValidateAll() if the designated constraints
// aren't met.
type ServiceTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceTemplateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceTemplateMultiError) AllErrors() []error { return m }

// ServiceTemplateValidationError is the validation error returned by
// ServiceTemplate.Validate if the designated constraints aren't met.
type ServiceTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceTemplateValidationError) ErrorName() string { return "ServiceTemplateValidationError" }

// Error satisfies the builtin error interface
func (e ServiceTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceTemplateValidationError{}

// Validate checks the field values on ServiceOptionTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceOptionTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceOptionTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceOptionTemplateMultiError, or nil if none found.
func (m *ServiceOptionTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceOptionTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Price

	// no validation rules for Duration

	// no validation rules for Sort

	// no validation rules for CategoryId

	// no validation rules for IsActive

	// no validation rules for IsRequired

	// no validation rules for MaxSelectable

	if len(errors) > 0 {
		return ServiceOptionTemplateMultiError(errors)
	}

	return nil
}

// ServiceOptionTemplateMultiError is an error wrapping multiple validation
// errors returned by ServiceOptionTemplate.ValidateAll() if the designated
// constraints aren't met.
type ServiceOptionTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceOptionTemplateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceOptionTemplateMultiError) AllErrors() []error { return m }

// ServiceOptionTemplateValidationError is the validation error returned by
// ServiceOptionTemplate.Validate if the designated constraints aren't met.
type ServiceOptionTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceOptionTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceOptionTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceOptionTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceOptionTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceOptionTemplateValidationError) ErrorName() string {
	return "ServiceOptionTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceOptionTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceOptionTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceOptionTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceOptionTemplateValidationError{}

// Validate checks the field values on CategoryService with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CategoryService) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CategoryService with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CategoryServiceMultiError, or nil if none found.
func (m *CategoryService) ValidateAll() error {
	return m.validate(true)
}

func (m *CategoryService) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CategoryId

	// no validation rules for Name

	for idx, item := range m.GetServiceTemplateList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CategoryServiceValidationError{
						field:  fmt.Sprintf("ServiceTemplateList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CategoryServiceValidationError{
						field:  fmt.Sprintf("ServiceTemplateList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CategoryServiceValidationError{
					field:  fmt.Sprintf("ServiceTemplateList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CategoryServiceMultiError(errors)
	}

	return nil
}

// CategoryServiceMultiError is an error wrapping multiple validation errors
// returned by CategoryService.ValidateAll() if the designated constraints
// aren't met.
type CategoryServiceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CategoryServiceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CategoryServiceMultiError) AllErrors() []error { return m }

// CategoryServiceValidationError is the validation error returned by
// CategoryService.Validate if the designated constraints aren't met.
type CategoryServiceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CategoryServiceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CategoryServiceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CategoryServiceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CategoryServiceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CategoryServiceValidationError) ErrorName() string { return "CategoryServiceValidationError" }

// Error satisfies the builtin error interface
func (e CategoryServiceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCategoryService.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CategoryServiceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CategoryServiceValidationError{}
