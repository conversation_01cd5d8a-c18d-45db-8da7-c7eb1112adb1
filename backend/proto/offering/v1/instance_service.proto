syntax = "proto3";

package backend.proto.offering.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "backend/proto/offering/v1/instance.proto";
import "backend/proto/offering/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// 服务实例服务
service InstanceService {
  // 列出服务实例
  rpc ListServiceInstance(ListServiceInstanceRequest) returns (ListServiceInstanceResponse);

  // 获取服务实例
  // (-- api-linter: core::0136::prepositions=disabled
  //     aip.dev/not-precedent: GetServiceInstanceByIDs is clear and appropriate --)
  // (-- api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: GetServiceInstanceByIDsResponse is appropriate for this use case --)
  rpc GetServiceInstanceByIDs(GetServiceInstanceByIDsRequest) returns (GetServiceInstanceByIDsResponse);
}

// 获取服务实例请求
message GetServiceInstanceByIDsRequest {
  // 服务实例ID列表
  repeated int64 service_instance_ids = 1;
}

// 获取服务实例响应
message GetServiceInstanceByIDsResponse {
  // 服务实例列表
  repeated ServiceInstance service_instances = 1;
}

// 列出服务实例请求
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: company_id is used as parent in this context --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 不需要page_size，使用现有的分页机制 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 不需要page_token，使用现有的分页机制 --)
message ListServiceInstanceRequest {
  // 公司ID
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // 商家ID
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // 查询开始时间
  google.protobuf.Timestamp start_time = 3;
  // 查询结束时间
  google.protobuf.Timestamp end_time = 4;
  // 过滤条件
  // (-- api-linter: core::0132::request-field-types=disabled
  //     aip.dev/not-precedent: 打平成string不利用协议理解 --)
  ServiceInstanceFilter filter = 5;
  // 排序类型
  SortType sort_type = 6;
  // 分页信息
  PaginationRef pagination = 7;
}

// 列出服务实例响应
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 不需要next_page_token字段，使用现有的分页机制 --)
message ListServiceInstanceResponse {
  // 服务实例列表
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  repeated ServiceInstance service_instances = 1;
  // 分页信息
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  PaginationRef pagination = 2;
  // 是否最后一页
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  bool is_end = 3;
  // 总条数
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  int32 total = 4;
} 