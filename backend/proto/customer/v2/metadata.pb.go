// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/customer/v2/metadata.proto

package customerpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	money "google.golang.org/genproto/googleapis/type/money"
	phone_number "google.golang.org/genproto/googleapis/type/phone_number"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// customer type
type CustomerType int32

const (
	// 0 is reserved for unspecified
	CustomerType_CUSTOMER_TYPE_UNSPECIFIED CustomerType = 0
	// lead
	CustomerType_LEAD CustomerType = 1
	// customer
	CustomerType_CUSTOMER CustomerType = 2
)

// Enum value maps for CustomerType.
var (
	CustomerType_name = map[int32]string{
		0: "CUSTOMER_TYPE_UNSPECIFIED",
		1: "LEAD",
		2: "CUSTOMER",
	}
	CustomerType_value = map[string]int32{
		"CUSTOMER_TYPE_UNSPECIFIED": 0,
		"LEAD":                      1,
		"CUSTOMER":                  2,
	}
)

func (x CustomerType) Enum() *CustomerType {
	p := new(CustomerType)
	*p = x
	return p
}

func (x CustomerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[0].Descriptor()
}

func (CustomerType) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[0]
}

func (x CustomerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerType.Descriptor instead.
func (CustomerType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{0}
}

// state enumeration
type Customer_State int32

const (
	// 0 is reserved for unspecified
	Customer_STATE_UNSPECIFIED Customer_State = 0
	// active
	Customer_ACTIVE Customer_State = 1
	// inactive
	Customer_INACTIVE Customer_State = 2
	// deleted
	Customer_DELETED Customer_State = 3
)

// Enum value maps for Customer_State.
var (
	Customer_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Customer_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Customer_State) Enum() *Customer_State {
	p := new(Customer_State)
	*p = x
	return p
}

func (x Customer_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Customer_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[1].Descriptor()
}

func (Customer_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[1]
}

func (x Customer_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Customer_State.Descriptor instead.
func (Customer_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{0, 0}
}

// state enumeration
type Lead_State int32

const (
	// 0 is reserved for unspecified
	Lead_STATE_UNSPECIFIED Lead_State = 0
	// active
	Lead_ACTIVE Lead_State = 1
	// inactive
	Lead_INACTIVE Lead_State = 2
	// deleted
	Lead_DELETED Lead_State = 3
)

// Enum value maps for Lead_State.
var (
	Lead_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Lead_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Lead_State) Enum() *Lead_State {
	p := new(Lead_State)
	*p = x
	return p
}

func (x Lead_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lead_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[2].Descriptor()
}

func (Lead_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[2]
}

func (x Lead_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lead_State.Descriptor instead.
func (Lead_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{1, 0}
}

// state enumeration
type Contact_State int32

const (
	// 0 is reserved for unspecified
	Contact_STATE_UNSPECIFIED Contact_State = 0
	// active
	Contact_ACTIVE Contact_State = 1
	// inactive
	Contact_INACTIVE Contact_State = 2
	// deleted
	Contact_DELETED Contact_State = 3
)

// Enum value maps for Contact_State.
var (
	Contact_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Contact_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Contact_State) Enum() *Contact_State {
	p := new(Contact_State)
	*p = x
	return p
}

func (x Contact_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Contact_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[3].Descriptor()
}

func (Contact_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[3]
}

func (x Contact_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Contact_State.Descriptor instead.
func (Contact_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{2, 0}
}

// state enumeration
type ContactTag_State int32

const (
	// 0 is reserved for unspecified
	ContactTag_STATE_UNSPECIFIED ContactTag_State = 0
	// active
	ContactTag_ACTIVE ContactTag_State = 1
	// inactive
	ContactTag_INACTIVE ContactTag_State = 2
	// deleted
	ContactTag_DELETED ContactTag_State = 3
)

// Enum value maps for ContactTag_State.
var (
	ContactTag_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	ContactTag_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x ContactTag_State) Enum() *ContactTag_State {
	p := new(ContactTag_State)
	*p = x
	return p
}

func (x ContactTag_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContactTag_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[4].Descriptor()
}

func (ContactTag_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[4]
}

func (x ContactTag_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContactTag_State.Descriptor instead.
func (ContactTag_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{3, 0}
}

// type
type ContactTag_Type int32

const (
	// 0 is reserved for unspecified
	ContactTag_TYPE_UNSPECIFIED ContactTag_Type = 0
	// custom
	ContactTag_CUSTOM ContactTag_Type = 1
	// 以下都是内置标签
	// EMERGENCY
	ContactTag_EMERGENCY ContactTag_Type = 2
	// PICKUP
	ContactTag_PICKUP ContactTag_Type = 3
	// COMMUNITY
	ContactTag_COMMUNITY ContactTag_Type = 4
	// PRIMARY
	ContactTag_PRIMARY ContactTag_Type = 5
)

// Enum value maps for ContactTag_Type.
var (
	ContactTag_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CUSTOM",
		2: "EMERGENCY",
		3: "PICKUP",
		4: "COMMUNITY",
		5: "PRIMARY",
	}
	ContactTag_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CUSTOM":           1,
		"EMERGENCY":        2,
		"PICKUP":           3,
		"COMMUNITY":        4,
		"PRIMARY":          5,
	}
)

func (x ContactTag_Type) Enum() *ContactTag_Type {
	p := new(ContactTag_Type)
	*p = x
	return p
}

func (x ContactTag_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContactTag_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[5].Descriptor()
}

func (ContactTag_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[5]
}

func (x ContactTag_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContactTag_Type.Descriptor instead.
func (ContactTag_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{3, 1}
}

// state enumeration
type Address_State int32

const (
	// 0 is reserved for unspecified
	Address_STATE_UNSPECIFIED Address_State = 0
	// active
	Address_ACTIVE Address_State = 1
	// inactive
	Address_INACTIVE Address_State = 2
	// deleted
	Address_DELETED Address_State = 3
)

// Enum value maps for Address_State.
var (
	Address_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Address_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Address_State) Enum() *Address_State {
	p := new(Address_State)
	*p = x
	return p
}

func (x Address_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Address_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[6].Descriptor()
}

func (Address_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[6]
}

func (x Address_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Address_State.Descriptor instead.
func (Address_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{4, 0}
}

// field state enumeration
type CustomField_State int32

const (
	// 0 is reserved for unspecified
	CustomField_STATE_UNSPECIFIED CustomField_State = 0
	// active
	CustomField_ACTIVE CustomField_State = 1
	// deleted
	CustomField_DELETED CustomField_State = 2
)

// Enum value maps for CustomField_State.
var (
	CustomField_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "DELETED",
	}
	CustomField_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"DELETED":           2,
	}
)

func (x CustomField_State) Enum() *CustomField_State {
	p := new(CustomField_State)
	*p = x
	return p
}

func (x CustomField_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomField_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[7].Descriptor()
}

func (CustomField_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[7]
}

func (x CustomField_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomField_State.Descriptor instead.
func (CustomField_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5, 0}
}

// field type enumeration
type CustomField_Type int32

const (
	// 0 is reserved for unspecified
	CustomField_TYPE_UNSPECIFIED CustomField_Type = 0
	// text
	CustomField_TEXT CustomField_Type = 1
	// number
	CustomField_NUMBER CustomField_Type = 2
	// date
	CustomField_DATE CustomField_Type = 3
	// boolean
	CustomField_BOOLEAN CustomField_Type = 4
	// currency
	CustomField_CURRENCY CustomField_Type = 5
	// select
	CustomField_SELECT CustomField_Type = 6
	// multi select
	CustomField_MULTI_SELECT CustomField_Type = 7
	// relation
	CustomField_RELATION CustomField_Type = 8
	// money
	CustomField_MONEY CustomField_Type = 9
)

// Enum value maps for CustomField_Type.
var (
	CustomField_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "TEXT",
		2: "NUMBER",
		3: "DATE",
		4: "BOOLEAN",
		5: "CURRENCY",
		6: "SELECT",
		7: "MULTI_SELECT",
		8: "RELATION",
		9: "MONEY",
	}
	CustomField_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"TEXT":             1,
		"NUMBER":           2,
		"DATE":             3,
		"BOOLEAN":          4,
		"CURRENCY":         5,
		"SELECT":           6,
		"MULTI_SELECT":     7,
		"RELATION":         8,
		"MONEY":            9,
	}
)

func (x CustomField_Type) Enum() *CustomField_Type {
	p := new(CustomField_Type)
	*p = x
	return p
}

func (x CustomField_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomField_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[8].Descriptor()
}

func (CustomField_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[8]
}

func (x CustomField_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomField_Type.Descriptor instead.
func (CustomField_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5, 1}
}

// 实体类型
type CustomField_Value_Relation_Entity int32

const (
	// 0 is reserved for unspecified
	CustomField_Value_Relation_ENTITY_UNSPECIFIED CustomField_Value_Relation_Entity = 0
	// customer
	CustomField_Value_Relation_CUSTOMER CustomField_Value_Relation_Entity = 1
	// lead
	CustomField_Value_Relation_LEAD CustomField_Value_Relation_Entity = 2
)

// Enum value maps for CustomField_Value_Relation_Entity.
var (
	CustomField_Value_Relation_Entity_name = map[int32]string{
		0: "ENTITY_UNSPECIFIED",
		1: "CUSTOMER",
		2: "LEAD",
	}
	CustomField_Value_Relation_Entity_value = map[string]int32{
		"ENTITY_UNSPECIFIED": 0,
		"CUSTOMER":           1,
		"LEAD":               2,
	}
)

func (x CustomField_Value_Relation_Entity) Enum() *CustomField_Value_Relation_Entity {
	p := new(CustomField_Value_Relation_Entity)
	*p = x
	return p
}

func (x CustomField_Value_Relation_Entity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomField_Value_Relation_Entity) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[9].Descriptor()
}

func (CustomField_Value_Relation_Entity) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[9]
}

func (x CustomField_Value_Relation_Entity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomField_Value_Relation_Entity.Descriptor instead.
func (CustomField_Value_Relation_Entity) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5, 0, 0, 0}
}

// customer
type Customer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// organization reference
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// core identifier information
	// 名 (最大长度255字符)
	GivenName string `protobuf:"bytes,3,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓 (最大长度255字符)
	FamilyName string `protobuf:"bytes,4,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// basic classification
	// 实体状态（活跃/非活跃/删除）
	State Customer_State `protobuf:"varint,11,opt,name=state,proto3,enum=backend.proto.customer.v2.Customer_State" json:"state,omitempty"`
	// lifecycle
	LifecycleId int64 `protobuf:"varint,12,opt,name=lifecycle_id,json=lifecycleId,proto3" json:"lifecycle_id,omitempty"`
	// relationship
	// 负责人 id (staff id)
	OwnerStaffId int64 `protobuf:"varint,13,opt,name=owner_staff_id,json=ownerStaffId,proto3" json:"owner_staff_id,omitempty"`
	// extended fields
	// 自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,14,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Customer) Reset() {
	*x = Customer{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer) ProtoMessage() {}

func (x *Customer) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer.ProtoReflect.Descriptor instead.
func (*Customer) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{0}
}

func (x *Customer) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Customer) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *Customer) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Customer) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Customer) GetState() Customer_State {
	if x != nil {
		return x.State
	}
	return Customer_STATE_UNSPECIFIED
}

func (x *Customer) GetLifecycleId() int64 {
	if x != nil {
		return x.LifecycleId
	}
	return 0
}

func (x *Customer) GetOwnerStaffId() int64 {
	if x != nil {
		return x.OwnerStaffId
	}
	return 0
}

func (x *Customer) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *Customer) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Customer) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Customer) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// lead
type Lead struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// lead ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// organization reference
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// core identifier information
	// 名 (最大长度255字符)
	GivenName string `protobuf:"bytes,3,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓 (最大长度255字符)
	FamilyName string `protobuf:"bytes,4,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 实体状态（活跃/非活跃/删除）
	State Lead_State `protobuf:"varint,11,opt,name=state,proto3,enum=backend.proto.customer.v2.Lead_State" json:"state,omitempty"`
	// lifecycle
	LifecycleId int64 `protobuf:"varint,12,opt,name=lifecycle_id,json=lifecycleId,proto3" json:"lifecycle_id,omitempty"`
	// relationship
	// 负责人 id (staff id)
	OwnerStaffId int64 `protobuf:"varint,13,opt,name=owner_staff_id,json=ownerStaffId,proto3" json:"owner_staff_id,omitempty"`
	// extended fields
	// 自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,14,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Lead) Reset() {
	*x = Lead{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Lead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lead) ProtoMessage() {}

func (x *Lead) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lead.ProtoReflect.Descriptor instead.
func (*Lead) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{1}
}

func (x *Lead) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Lead) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *Lead) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Lead) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Lead) GetState() Lead_State {
	if x != nil {
		return x.State
	}
	return Lead_STATE_UNSPECIFIED
}

func (x *Lead) GetLifecycleId() int64 {
	if x != nil {
		return x.LifecycleId
	}
	return 0
}

func (x *Lead) GetOwnerStaffId() int64 {
	if x != nil {
		return x.OwnerStaffId
	}
	return 0
}

func (x *Lead) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *Lead) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Lead) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Lead) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// contact
type Contact struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// basic information
	// 名字
	GivenName string `protobuf:"bytes,3,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓
	FamilyName string `protobuf:"bytes,4,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 邮箱地址 (必须是有效的邮箱格式)
	Email string `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	// 电话号码
	Phone *phone_number.PhoneNumber `protobuf:"bytes,6,opt,name=phone,proto3" json:"phone,omitempty"`
	// 是否是自己创建的
	IsSelf bool `protobuf:"varint,7,opt,name=is_self,json=isSelf,proto3" json:"is_self,omitempty"`
	// state information
	State Contact_State `protobuf:"varint,10,opt,name=state,proto3,enum=backend.proto.customer.v2.Contact_State" json:"state,omitempty"`
	// 关联的标签列表
	// 1.如果创建时没有指定, 就是默认空列表
	// 2. 如果创建时仅指定id, 会根据id查询标签列表, 并做关联
	// 3. 如果创建时传完整Tag, 会以传入的Tag创建新Tag, 并做关联
	// 注: 如果创建时传id和完整Tag, 会以id查询内容为准
	// --
	// 所以, 如果创建Contact时绑定已有标签, 这里应该只传id
	// 如果创建Contact时还需要同时创建新标签, 这里应该传完整Tag
	Tags []*ContactTag `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Contact) Reset() {
	*x = Contact{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Contact) ProtoMessage() {}

func (x *Contact) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Contact.ProtoReflect.Descriptor instead.
func (*Contact) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{2}
}

func (x *Contact) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Contact) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Contact) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Contact) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Contact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Contact) GetPhone() *phone_number.PhoneNumber {
	if x != nil {
		return x.Phone
	}
	return nil
}

func (x *Contact) GetIsSelf() bool {
	if x != nil {
		return x.IsSelf
	}
	return false
}

func (x *Contact) GetState() Contact_State {
	if x != nil {
		return x.State
	}
	return Contact_STATE_UNSPECIFIED
}

func (x *Contact) GetTags() []*ContactTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Contact) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Contact) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Contact) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// contact tag
type ContactTag struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// organization reference
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// 标签名称 (最大长度100字符)
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 标签描述 (最大长度500字符)
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// 标签颜色 (十六进制颜色代码，如 #FF5733)
	Color string `protobuf:"bytes,5,opt,name=color,proto3" json:"color,omitempty"`
	// 排序顺序
	SortOrder int32 `protobuf:"varint,6,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 实体状态（活跃/非活跃/删除）
	State ContactTag_State `protobuf:"varint,7,opt,name=state,proto3,enum=backend.proto.customer.v2.ContactTag_State" json:"state,omitempty"`
	// 标签类型
	Type ContactTag_Type `protobuf:"varint,8,opt,name=type,proto3,enum=backend.proto.customer.v2.ContactTag_Type" json:"type,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContactTag) Reset() {
	*x = ContactTag{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactTag) ProtoMessage() {}

func (x *ContactTag) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactTag.ProtoReflect.Descriptor instead.
func (*ContactTag) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{3}
}

func (x *ContactTag) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ContactTag) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ContactTag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ContactTag) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ContactTag) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *ContactTag) GetSortOrder() int32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *ContactTag) GetState() ContactTag_State {
	if x != nil {
		return x.State
	}
	return ContactTag_STATE_UNSPECIFIED
}

func (x *ContactTag) GetType() ContactTag_Type {
	if x != nil {
		return x.Type
	}
	return ContactTag_TYPE_UNSPECIFIED
}

func (x *ContactTag) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ContactTag) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ContactTag) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// address
type Address struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// address information
	// 完整地址信息
	Address *postaladdress.PostalAddress `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// geographic coordinates
	// 纬度
	Latlng *latlng.LatLng `protobuf:"bytes,5,opt,name=latlng,proto3" json:"latlng,omitempty"`
	// state information
	State Address_State `protobuf:"varint,18,opt,name=state,proto3,enum=backend.proto.customer.v2.Address_State" json:"state,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Address) Reset() {
	*x = Address{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{4}
}

func (x *Address) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Address) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Address) GetAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *Address) GetLatlng() *latlng.LatLng {
	if x != nil {
		return x.Latlng
	}
	return nil
}

func (x *Address) GetState() Address_State {
	if x != nil {
		return x.State
	}
	return Address_STATE_UNSPECIFIED
}

func (x *Address) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Address) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Address) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// custom field definition
type CustomField struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段定义ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// field basic information
	// 实体类型
	Owner *EntityRelation `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
	// 字段标识
	Key string `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	// 字段标签
	Label string `protobuf:"bytes,4,opt,name=label,proto3" json:"label,omitempty"`
	// 字段类型
	Type CustomField_Type `protobuf:"varint,5,opt,name=type,proto3,enum=backend.proto.customer.v2.CustomField_Type" json:"type,omitempty"`
	// field configuration
	// 是否必填
	IsRequired bool `protobuf:"varint,6,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// 实体状态（活跃/删除）
	State CustomField_State `protobuf:"varint,7,opt,name=state,proto3,enum=backend.proto.customer.v2.CustomField_State" json:"state,omitempty"`
	// 默认值
	DefaultValue *CustomField_Value `protobuf:"bytes,8,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`
	// option configuration
	// 字段选项列表
	Options []*CustomField_Option `protobuf:"bytes,9,rep,name=options,proto3" json:"options,omitempty"`
	// validation rules
	// 验证规则
	ValidationRules *structpb.Struct `protobuf:"bytes,10,opt,name=validation_rules,json=validationRules,proto3" json:"validation_rules,omitempty"`
	// display configuration
	// 显示顺序
	DisplayOrder int32 `protobuf:"varint,11,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	// 帮助文本
	HelpText string `protobuf:"bytes,12,opt,name=help_text,json=helpText,proto3" json:"help_text,omitempty"`
	// tenant isolation
	// 租户ID
	TenantId int64 `protobuf:"varint,13,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField) Reset() {
	*x = CustomField{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField) ProtoMessage() {}

func (x *CustomField) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField.ProtoReflect.Descriptor instead.
func (*CustomField) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5}
}

func (x *CustomField) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomField) GetOwner() *EntityRelation {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *CustomField) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *CustomField) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CustomField) GetType() CustomField_Type {
	if x != nil {
		return x.Type
	}
	return CustomField_TYPE_UNSPECIFIED
}

func (x *CustomField) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *CustomField) GetState() CustomField_State {
	if x != nil {
		return x.State
	}
	return CustomField_STATE_UNSPECIFIED
}

func (x *CustomField) GetDefaultValue() *CustomField_Value {
	if x != nil {
		return x.DefaultValue
	}
	return nil
}

func (x *CustomField) GetOptions() []*CustomField_Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *CustomField) GetValidationRules() *structpb.Struct {
	if x != nil {
		return x.ValidationRules
	}
	return nil
}

func (x *CustomField) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *CustomField) GetHelpText() string {
	if x != nil {
		return x.HelpText
	}
	return ""
}

func (x *CustomField) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *CustomField) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CustomField) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *CustomField) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// Custom field value, can be of any supported type.
type CustomField_Value struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 实际的值，oneof 只会有一个生效
	//
	// Types that are valid to be assigned to Value:
	//
	//	*CustomField_Value_String_
	//	*CustomField_Value_DoubleValue
	//	*CustomField_Value_Int64
	//	*CustomField_Value_Bool
	//	*CustomField_Value_Money
	//	*CustomField_Value_TimestampTime
	//	*CustomField_Value_Relation_
	Value         isCustomField_Value_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField_Value) Reset() {
	*x = CustomField_Value{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField_Value) ProtoMessage() {}

func (x *CustomField_Value) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField_Value.ProtoReflect.Descriptor instead.
func (*CustomField_Value) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5, 0}
}

func (x *CustomField_Value) GetValue() isCustomField_Value_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *CustomField_Value) GetString_() string {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_String_); ok {
			return x.String_
		}
	}
	return ""
}

func (x *CustomField_Value) GetDoubleValue() float64 {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_DoubleValue); ok {
			return x.DoubleValue
		}
	}
	return 0
}

func (x *CustomField_Value) GetInt64() int64 {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_Int64); ok {
			return x.Int64
		}
	}
	return 0
}

func (x *CustomField_Value) GetBool() bool {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_Bool); ok {
			return x.Bool
		}
	}
	return false
}

func (x *CustomField_Value) GetMoney() *money.Money {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_Money); ok {
			return x.Money
		}
	}
	return nil
}

func (x *CustomField_Value) GetTimestampTime() *timestamppb.Timestamp {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_TimestampTime); ok {
			return x.TimestampTime
		}
	}
	return nil
}

func (x *CustomField_Value) GetRelation() *CustomField_Value_Relation {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_Relation_); ok {
			return x.Relation
		}
	}
	return nil
}

type isCustomField_Value_Value interface {
	isCustomField_Value_Value()
}

type CustomField_Value_String_ struct {
	// 字符串类型
	String_ string `protobuf:"bytes,2,opt,name=string,proto3,oneof"`
}

type CustomField_Value_DoubleValue struct {
	// 浮点数类型
	DoubleValue float64 `protobuf:"fixed64,3,opt,name=double_value,json=doubleValue,proto3,oneof"`
}

type CustomField_Value_Int64 struct {
	// 整型类型
	Int64 int64 `protobuf:"varint,4,opt,name=int64,proto3,oneof"`
}

type CustomField_Value_Bool struct {
	// 布尔类型
	Bool bool `protobuf:"varint,6,opt,name=bool,proto3,oneof"`
}

type CustomField_Value_Money struct {
	// 金额类型
	Money *money.Money `protobuf:"bytes,7,opt,name=money,proto3,oneof"`
}

type CustomField_Value_TimestampTime struct {
	// 时间戳类型
	TimestampTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=timestamp_time,json=timestampTime,proto3,oneof"`
}

type CustomField_Value_Relation_ struct {
	// 关联关系类型
	Relation *CustomField_Value_Relation `protobuf:"bytes,9,opt,name=relation,proto3,oneof"`
}

func (*CustomField_Value_String_) isCustomField_Value_Value() {}

func (*CustomField_Value_DoubleValue) isCustomField_Value_Value() {}

func (*CustomField_Value_Int64) isCustomField_Value_Value() {}

func (*CustomField_Value_Bool) isCustomField_Value_Value() {}

func (*CustomField_Value_Money) isCustomField_Value_Value() {}

func (*CustomField_Value_TimestampTime) isCustomField_Value_Value() {}

func (*CustomField_Value_Relation_) isCustomField_Value_Value() {}

// 字段选项
type CustomField_Option struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段值
	Value *CustomField_Value `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// 选项标签
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	// 排序顺序
	SortOrder int32 `protobuf:"varint,3,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 实体状态（活跃/删除）
	State         CustomField_State `protobuf:"varint,4,opt,name=state,proto3,enum=backend.proto.customer.v2.CustomField_State" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField_Option) Reset() {
	*x = CustomField_Option{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField_Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField_Option) ProtoMessage() {}

func (x *CustomField_Option) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField_Option.ProtoReflect.Descriptor instead.
func (*CustomField_Option) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5, 1}
}

func (x *CustomField_Option) GetValue() *CustomField_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *CustomField_Option) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CustomField_Option) GetSortOrder() int32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *CustomField_Option) GetState() CustomField_State {
	if x != nil {
		return x.State
	}
	return CustomField_STATE_UNSPECIFIED
}

// Relation to another entity (customer/lead)
type CustomField_Value_Relation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 关联实体类型
	Entity CustomField_Value_Relation_Entity `protobuf:"varint,1,opt,name=entity,proto3,enum=backend.proto.customer.v2.CustomField_Value_Relation_Entity" json:"entity,omitempty"`
	// 关联实体ID
	Id            int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField_Value_Relation) Reset() {
	*x = CustomField_Value_Relation{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField_Value_Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField_Value_Relation) ProtoMessage() {}

func (x *CustomField_Value_Relation) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField_Value_Relation.ProtoReflect.Descriptor instead.
func (*CustomField_Value_Relation) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5, 0, 0}
}

func (x *CustomField_Value_Relation) GetEntity() CustomField_Value_Relation_Entity {
	if x != nil {
		return x.Entity
	}
	return CustomField_Value_Relation_ENTITY_UNSPECIFIED
}

func (x *CustomField_Value_Relation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_backend_proto_customer_v2_metadata_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_metadata_proto_rawDesc = "" +
	"\n" +
	"(backend/proto/customer/v2/metadata.proto\x12\x19backend.proto.customer.v2\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17google/type/money.proto\x1a\x1egoogle/type/phone_number.proto\x1a google/type/postal_address.proto\x1a\x18google/type/latlng.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x17validate/validate.proto\x1a&backend/proto/customer/v2/common.proto\"\x89\x05\n" +
	"\bCustomer\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12'\n" +
	"\n" +
	"given_name\x18\x03 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\tgivenName\x12)\n" +
	"\vfamily_name\x18\x04 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\n" +
	"familyName\x12D\n" +
	"\x05state\x18\v \x01(\x0e2).backend.proto.customer.v2.Customer.StateB\x03\xe0A\x03R\x05state\x12!\n" +
	"\flifecycle_id\x18\f \x01(\x03R\vlifecycleId\x12$\n" +
	"\x0eowner_staff_id\x18\r \x01(\x03R\fownerStaffId\x12<\n" +
	"\rcustom_fields\x18\x0e \x01(\v2\x17.google.protobuf.StructR\fcustomFields\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"\x81\x05\n" +
	"\x04Lead\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12'\n" +
	"\n" +
	"given_name\x18\x03 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\tgivenName\x12)\n" +
	"\vfamily_name\x18\x04 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\n" +
	"familyName\x12@\n" +
	"\x05state\x18\v \x01(\x0e2%.backend.proto.customer.v2.Lead.StateB\x03\xe0A\x03R\x05state\x12!\n" +
	"\flifecycle_id\x18\f \x01(\x03R\vlifecycleId\x12$\n" +
	"\x0eowner_staff_id\x18\r \x01(\x03R\fownerStaffId\x12<\n" +
	"\rcustom_fields\x18\x0e \x01(\v2\x17.google.protobuf.StructR\fcustomFields\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"\xf7\x04\n" +
	"\aContact\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12'\n" +
	"\n" +
	"given_name\x18\x03 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\tgivenName\x12)\n" +
	"\vfamily_name\x18\x04 \x01(\tB\b\xfaB\x05r\x03\x18\xff\x01R\n" +
	"familyName\x12 \n" +
	"\x05email\x18\x05 \x01(\tB\n" +
	"\xfaB\ar\x05\x18\xff\x01`\x01R\x05email\x12.\n" +
	"\x05phone\x18\x06 \x01(\v2\x18.google.type.PhoneNumberR\x05phone\x12\x17\n" +
	"\ais_self\x18\a \x01(\bR\x06isSelf\x12C\n" +
	"\x05state\x18\n" +
	" \x01(\x0e2(.backend.proto.customer.v2.Contact.StateB\x03\xe0A\x03R\x05state\x129\n" +
	"\x04tags\x18\v \x03(\v2%.backend.proto.customer.v2.ContactTagR\x04tags\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"\xda\x05\n" +
	"\n" +
	"ContactTag\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12\x1b\n" +
	"\x04name\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x18dR\x04name\x12*\n" +
	"\vdescription\x18\x04 \x01(\tB\b\xfaB\x05r\x03\x18\xf4\x03R\vdescription\x12\x1d\n" +
	"\x05color\x18\x05 \x01(\tB\a\xfaB\x04r\x02\x18\aR\x05color\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x06 \x01(\x05R\tsortOrder\x12F\n" +
	"\x05state\x18\a \x01(\x0e2+.backend.proto.customer.v2.ContactTag.StateB\x03\xe0A\x03R\x05state\x12>\n" +
	"\x04type\x18\b \x01(\x0e2*.backend.proto.customer.v2.ContactTag.TypeR\x04type\x12;\n" +
	"\vcreate_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"_\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06CUSTOM\x10\x01\x12\r\n" +
	"\tEMERGENCY\x10\x02\x12\n" +
	"\n" +
	"\x06PICKUP\x10\x03\x12\r\n" +
	"\tCOMMUNITY\x10\x04\x12\v\n" +
	"\aPRIMARY\x10\x05\"\xe0\x03\n" +
	"\aAddress\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x124\n" +
	"\aaddress\x18\x04 \x01(\v2\x1a.google.type.PostalAddressR\aaddress\x12+\n" +
	"\x06latlng\x18\x05 \x01(\v2\x13.google.type.LatLngR\x06latlng\x12C\n" +
	"\x05state\x18\x12 \x01(\x0e2(.backend.proto.customer.v2.Address.StateB\x03\xe0A\x03R\x05state\x12;\n" +
	"\vcreate_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"\xb1\r\n" +
	"\vCustomField\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12?\n" +
	"\x05owner\x18\x02 \x01(\v2).backend.proto.customer.v2.EntityRelationR\x05owner\x12\x10\n" +
	"\x03key\x18\x03 \x01(\tR\x03key\x12\x14\n" +
	"\x05label\x18\x04 \x01(\tR\x05label\x12?\n" +
	"\x04type\x18\x05 \x01(\x0e2+.backend.proto.customer.v2.CustomField.TypeR\x04type\x12\x1f\n" +
	"\vis_required\x18\x06 \x01(\bR\n" +
	"isRequired\x12G\n" +
	"\x05state\x18\a \x01(\x0e2,.backend.proto.customer.v2.CustomField.StateB\x03\xe0A\x03R\x05state\x12Q\n" +
	"\rdefault_value\x18\b \x01(\v2,.backend.proto.customer.v2.CustomField.ValueR\fdefaultValue\x12G\n" +
	"\aoptions\x18\t \x03(\v2-.backend.proto.customer.v2.CustomField.OptionR\aoptions\x12B\n" +
	"\x10validation_rules\x18\n" +
	" \x01(\v2\x17.google.protobuf.StructR\x0fvalidationRules\x12#\n" +
	"\rdisplay_order\x18\v \x01(\x05R\fdisplayOrder\x12\x1b\n" +
	"\thelp_text\x18\f \x01(\tR\bhelpText\x12\x1b\n" +
	"\ttenant_id\x18\r \x01(\x03R\btenantId\x12;\n" +
	"\vcreate_time\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\x1a\xf0\x03\n" +
	"\x05Value\x12\x18\n" +
	"\x06string\x18\x02 \x01(\tH\x00R\x06string\x12#\n" +
	"\fdouble_value\x18\x03 \x01(\x01H\x00R\vdoubleValue\x12\x16\n" +
	"\x05int64\x18\x04 \x01(\x03H\x00R\x05int64\x12\x14\n" +
	"\x04bool\x18\x06 \x01(\bH\x00R\x04bool\x12*\n" +
	"\x05money\x18\a \x01(\v2\x12.google.type.MoneyH\x00R\x05money\x12C\n" +
	"\x0etimestamp_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x00R\rtimestampTime\x12S\n" +
	"\brelation\x18\t \x01(\v25.backend.proto.customer.v2.CustomField.Value.RelationH\x00R\brelation\x1a\xaa\x01\n" +
	"\bRelation\x12T\n" +
	"\x06entity\x18\x01 \x01(\x0e2<.backend.proto.customer.v2.CustomField.Value.Relation.EntityR\x06entity\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\"8\n" +
	"\x06Entity\x12\x16\n" +
	"\x12ENTITY_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bCUSTOMER\x10\x01\x12\b\n" +
	"\x04LEAD\x10\x02B\a\n" +
	"\x05value\x1a\xca\x01\n" +
	"\x06Option\x12B\n" +
	"\x05value\x18\x01 \x01(\v2,.backend.proto.customer.v2.CustomField.ValueR\x05value\x12\x14\n" +
	"\x05label\x18\x02 \x01(\tR\x05label\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x03 \x01(\x05R\tsortOrder\x12G\n" +
	"\x05state\x18\x04 \x01(\x0e2,.backend.proto.customer.v2.CustomField.StateB\x03\xe0A\x03R\x05state\"7\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\v\n" +
	"\aDELETED\x10\x02\"\x8e\x01\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04TEXT\x10\x01\x12\n" +
	"\n" +
	"\x06NUMBER\x10\x02\x12\b\n" +
	"\x04DATE\x10\x03\x12\v\n" +
	"\aBOOLEAN\x10\x04\x12\f\n" +
	"\bCURRENCY\x10\x05\x12\n" +
	"\n" +
	"\x06SELECT\x10\x06\x12\x10\n" +
	"\fMULTI_SELECT\x10\a\x12\f\n" +
	"\bRELATION\x10\b\x12\t\n" +
	"\x05MONEY\x10\t*E\n" +
	"\fCustomerType\x12\x1d\n" +
	"\x19CUSTOMER_TYPE_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04LEAD\x10\x01\x12\f\n" +
	"\bCUSTOMER\x10\x02Bk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_metadata_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_metadata_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_metadata_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_metadata_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_metadata_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_metadata_proto_rawDesc), len(file_backend_proto_customer_v2_metadata_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_metadata_proto_rawDescData
}

var file_backend_proto_customer_v2_metadata_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_backend_proto_customer_v2_metadata_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_backend_proto_customer_v2_metadata_proto_goTypes = []any{
	(CustomerType)(0),                      // 0: backend.proto.customer.v2.CustomerType
	(Customer_State)(0),                    // 1: backend.proto.customer.v2.Customer.State
	(Lead_State)(0),                        // 2: backend.proto.customer.v2.Lead.State
	(Contact_State)(0),                     // 3: backend.proto.customer.v2.Contact.State
	(ContactTag_State)(0),                  // 4: backend.proto.customer.v2.ContactTag.State
	(ContactTag_Type)(0),                   // 5: backend.proto.customer.v2.ContactTag.Type
	(Address_State)(0),                     // 6: backend.proto.customer.v2.Address.State
	(CustomField_State)(0),                 // 7: backend.proto.customer.v2.CustomField.State
	(CustomField_Type)(0),                  // 8: backend.proto.customer.v2.CustomField.Type
	(CustomField_Value_Relation_Entity)(0), // 9: backend.proto.customer.v2.CustomField.Value.Relation.Entity
	(*Customer)(nil),                       // 10: backend.proto.customer.v2.Customer
	(*Lead)(nil),                           // 11: backend.proto.customer.v2.Lead
	(*Contact)(nil),                        // 12: backend.proto.customer.v2.Contact
	(*ContactTag)(nil),                     // 13: backend.proto.customer.v2.ContactTag
	(*Address)(nil),                        // 14: backend.proto.customer.v2.Address
	(*CustomField)(nil),                    // 15: backend.proto.customer.v2.CustomField
	(*CustomField_Value)(nil),              // 16: backend.proto.customer.v2.CustomField.Value
	(*CustomField_Option)(nil),             // 17: backend.proto.customer.v2.CustomField.Option
	(*CustomField_Value_Relation)(nil),     // 18: backend.proto.customer.v2.CustomField.Value.Relation
	(*OrganizationRef)(nil),                // 19: backend.proto.customer.v2.OrganizationRef
	(*structpb.Struct)(nil),                // 20: google.protobuf.Struct
	(*timestamppb.Timestamp)(nil),          // 21: google.protobuf.Timestamp
	(*phone_number.PhoneNumber)(nil),       // 22: google.type.PhoneNumber
	(*postaladdress.PostalAddress)(nil),    // 23: google.type.PostalAddress
	(*latlng.LatLng)(nil),                  // 24: google.type.LatLng
	(*EntityRelation)(nil),                 // 25: backend.proto.customer.v2.EntityRelation
	(*money.Money)(nil),                    // 26: google.type.Money
}
var file_backend_proto_customer_v2_metadata_proto_depIdxs = []int32{
	19, // 0: backend.proto.customer.v2.Customer.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	1,  // 1: backend.proto.customer.v2.Customer.state:type_name -> backend.proto.customer.v2.Customer.State
	20, // 2: backend.proto.customer.v2.Customer.custom_fields:type_name -> google.protobuf.Struct
	21, // 3: backend.proto.customer.v2.Customer.create_time:type_name -> google.protobuf.Timestamp
	21, // 4: backend.proto.customer.v2.Customer.update_time:type_name -> google.protobuf.Timestamp
	21, // 5: backend.proto.customer.v2.Customer.delete_time:type_name -> google.protobuf.Timestamp
	19, // 6: backend.proto.customer.v2.Lead.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	2,  // 7: backend.proto.customer.v2.Lead.state:type_name -> backend.proto.customer.v2.Lead.State
	20, // 8: backend.proto.customer.v2.Lead.custom_fields:type_name -> google.protobuf.Struct
	21, // 9: backend.proto.customer.v2.Lead.create_time:type_name -> google.protobuf.Timestamp
	21, // 10: backend.proto.customer.v2.Lead.update_time:type_name -> google.protobuf.Timestamp
	21, // 11: backend.proto.customer.v2.Lead.delete_time:type_name -> google.protobuf.Timestamp
	22, // 12: backend.proto.customer.v2.Contact.phone:type_name -> google.type.PhoneNumber
	3,  // 13: backend.proto.customer.v2.Contact.state:type_name -> backend.proto.customer.v2.Contact.State
	13, // 14: backend.proto.customer.v2.Contact.tags:type_name -> backend.proto.customer.v2.ContactTag
	21, // 15: backend.proto.customer.v2.Contact.create_time:type_name -> google.protobuf.Timestamp
	21, // 16: backend.proto.customer.v2.Contact.update_time:type_name -> google.protobuf.Timestamp
	21, // 17: backend.proto.customer.v2.Contact.delete_time:type_name -> google.protobuf.Timestamp
	19, // 18: backend.proto.customer.v2.ContactTag.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	4,  // 19: backend.proto.customer.v2.ContactTag.state:type_name -> backend.proto.customer.v2.ContactTag.State
	5,  // 20: backend.proto.customer.v2.ContactTag.type:type_name -> backend.proto.customer.v2.ContactTag.Type
	21, // 21: backend.proto.customer.v2.ContactTag.create_time:type_name -> google.protobuf.Timestamp
	21, // 22: backend.proto.customer.v2.ContactTag.update_time:type_name -> google.protobuf.Timestamp
	21, // 23: backend.proto.customer.v2.ContactTag.delete_time:type_name -> google.protobuf.Timestamp
	23, // 24: backend.proto.customer.v2.Address.address:type_name -> google.type.PostalAddress
	24, // 25: backend.proto.customer.v2.Address.latlng:type_name -> google.type.LatLng
	6,  // 26: backend.proto.customer.v2.Address.state:type_name -> backend.proto.customer.v2.Address.State
	21, // 27: backend.proto.customer.v2.Address.create_time:type_name -> google.protobuf.Timestamp
	21, // 28: backend.proto.customer.v2.Address.update_time:type_name -> google.protobuf.Timestamp
	21, // 29: backend.proto.customer.v2.Address.delete_time:type_name -> google.protobuf.Timestamp
	25, // 30: backend.proto.customer.v2.CustomField.owner:type_name -> backend.proto.customer.v2.EntityRelation
	8,  // 31: backend.proto.customer.v2.CustomField.type:type_name -> backend.proto.customer.v2.CustomField.Type
	7,  // 32: backend.proto.customer.v2.CustomField.state:type_name -> backend.proto.customer.v2.CustomField.State
	16, // 33: backend.proto.customer.v2.CustomField.default_value:type_name -> backend.proto.customer.v2.CustomField.Value
	17, // 34: backend.proto.customer.v2.CustomField.options:type_name -> backend.proto.customer.v2.CustomField.Option
	20, // 35: backend.proto.customer.v2.CustomField.validation_rules:type_name -> google.protobuf.Struct
	21, // 36: backend.proto.customer.v2.CustomField.create_time:type_name -> google.protobuf.Timestamp
	21, // 37: backend.proto.customer.v2.CustomField.update_time:type_name -> google.protobuf.Timestamp
	21, // 38: backend.proto.customer.v2.CustomField.delete_time:type_name -> google.protobuf.Timestamp
	26, // 39: backend.proto.customer.v2.CustomField.Value.money:type_name -> google.type.Money
	21, // 40: backend.proto.customer.v2.CustomField.Value.timestamp_time:type_name -> google.protobuf.Timestamp
	18, // 41: backend.proto.customer.v2.CustomField.Value.relation:type_name -> backend.proto.customer.v2.CustomField.Value.Relation
	16, // 42: backend.proto.customer.v2.CustomField.Option.value:type_name -> backend.proto.customer.v2.CustomField.Value
	7,  // 43: backend.proto.customer.v2.CustomField.Option.state:type_name -> backend.proto.customer.v2.CustomField.State
	9,  // 44: backend.proto.customer.v2.CustomField.Value.Relation.entity:type_name -> backend.proto.customer.v2.CustomField.Value.Relation.Entity
	45, // [45:45] is the sub-list for method output_type
	45, // [45:45] is the sub-list for method input_type
	45, // [45:45] is the sub-list for extension type_name
	45, // [45:45] is the sub-list for extension extendee
	0,  // [0:45] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_metadata_proto_init() }
func file_backend_proto_customer_v2_metadata_proto_init() {
	if File_backend_proto_customer_v2_metadata_proto != nil {
		return
	}
	file_backend_proto_customer_v2_common_proto_init()
	file_backend_proto_customer_v2_metadata_proto_msgTypes[6].OneofWrappers = []any{
		(*CustomField_Value_String_)(nil),
		(*CustomField_Value_DoubleValue)(nil),
		(*CustomField_Value_Int64)(nil),
		(*CustomField_Value_Bool)(nil),
		(*CustomField_Value_Money)(nil),
		(*CustomField_Value_TimestampTime)(nil),
		(*CustomField_Value_Relation_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_metadata_proto_rawDesc), len(file_backend_proto_customer_v2_metadata_proto_rawDesc)),
			NumEnums:      10,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_customer_v2_metadata_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_metadata_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_metadata_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_metadata_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_metadata_proto = out.File
	file_backend_proto_customer_v2_metadata_proto_goTypes = nil
	file_backend_proto_customer_v2_metadata_proto_depIdxs = nil
}
