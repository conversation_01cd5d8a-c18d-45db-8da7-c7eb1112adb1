// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/customer/v2/activity.proto

package customerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LifeCycle with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LifeCycle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LifeCycle with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LifeCycleMultiError, or nil
// if none found.
func (m *LifeCycle) ValidateAll() error {
	return m.validate(true)
}

func (m *LifeCycle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Sort

	// no validation rules for IsDefault

	if len(errors) > 0 {
		return LifeCycleMultiError(errors)
	}

	return nil
}

// LifeCycleMultiError is an error wrapping multiple validation errors returned
// by LifeCycle.ValidateAll() if the designated constraints aren't met.
type LifeCycleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LifeCycleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LifeCycleMultiError) AllErrors() []error { return m }

// LifeCycleValidationError is the validation error returned by
// LifeCycle.Validate if the designated constraints aren't met.
type LifeCycleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LifeCycleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LifeCycleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LifeCycleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LifeCycleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LifeCycleValidationError) ErrorName() string { return "LifeCycleValidationError" }

// Error satisfies the builtin error interface
func (e LifeCycleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLifeCycle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LifeCycleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LifeCycleValidationError{}

// Validate checks the field values on ActionState with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActionState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionState with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActionStateMultiError, or
// nil if none found.
func (m *ActionState) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Sort

	// no validation rules for Color

	if len(errors) > 0 {
		return ActionStateMultiError(errors)
	}

	return nil
}

// ActionStateMultiError is an error wrapping multiple validation errors
// returned by ActionState.ValidateAll() if the designated constraints aren't met.
type ActionStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionStateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionStateMultiError) AllErrors() []error { return m }

// ActionStateValidationError is the validation error returned by
// ActionState.Validate if the designated constraints aren't met.
type ActionStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionStateValidationError) ErrorName() string { return "ActionStateValidationError" }

// Error satisfies the builtin error interface
func (e ActionStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionStateValidationError{}

// Validate checks the field values on Task with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Task with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TaskMultiError, or nil if none found.
func (m *Task) ValidateAll() error {
	return m.validate(true)
}

func (m *Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for State

	if m.AllocateStaffId != nil {
		// no validation rules for AllocateStaffId
	}

	if m.CompleteTime != nil {

		if all {
			switch v := interface{}(m.GetCompleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskValidationError{
					field:  "CompleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TaskMultiError(errors)
	}

	return nil
}

// TaskMultiError is an error wrapping multiple validation errors returned by
// Task.ValidateAll() if the designated constraints aren't met.
type TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskMultiError) AllErrors() []error { return m }

// TaskValidationError is the validation error returned by Task.Validate if the
// designated constraints aren't met.
type TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskValidationError) ErrorName() string { return "TaskValidationError" }

// Error satisfies the builtin error interface
func (e TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskValidationError{}

// Validate checks the field values on ActivityLog with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActivityLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityLog with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActivityLogMultiError, or
// nil if none found.
func (m *ActivityLog) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CustomerId

	// no validation rules for CustomerName

	// no validation rules for CustomerPhoneNumber

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivityLogValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivityLogValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivityLogValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivityLogValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivityLogValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivityLogValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivityLogValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivityLogValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivityLogValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActivityLogMultiError(errors)
	}

	return nil
}

// ActivityLogMultiError is an error wrapping multiple validation errors
// returned by ActivityLog.ValidateAll() if the designated constraints aren't met.
type ActivityLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityLogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityLogMultiError) AllErrors() []error { return m }

// ActivityLogValidationError is the validation error returned by
// ActivityLog.Validate if the designated constraints aren't met.
type ActivityLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityLogValidationError) ErrorName() string { return "ActivityLogValidationError" }

// Error satisfies the builtin error interface
func (e ActivityLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityLogValidationError{}

// Validate checks the field values on Note with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Note) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Note with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NoteMultiError, or nil if none found.
func (m *Note) ValidateAll() error {
	return m.validate(true)
}

func (m *Note) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetCreateSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NoteValidationError{
					field:  "CreateSource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NoteValidationError{
					field:  "CreateSource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NoteValidationError{
				field:  "CreateSource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NoteValidationError{
					field:  "UpdateSource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NoteValidationError{
					field:  "UpdateSource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NoteValidationError{
				field:  "UpdateSource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NoteValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NoteValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NoteValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NoteValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NoteValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NoteValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NoteMultiError(errors)
	}

	return nil
}

// NoteMultiError is an error wrapping multiple validation errors returned by
// Note.ValidateAll() if the designated constraints aren't met.
type NoteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoteMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoteMultiError) AllErrors() []error { return m }

// NoteValidationError is the validation error returned by Note.Validate if the
// designated constraints aren't met.
type NoteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoteValidationError) ErrorName() string { return "NoteValidationError" }

// Error satisfies the builtin error interface
func (e NoteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNote.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoteValidationError{}

// Validate checks the field values on Tag with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Tag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Tag with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TagMultiError, or nil if none found.
func (m *Tag) ValidateAll() error {
	return m.validate(true)
}

func (m *Tag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Sort

	if len(errors) > 0 {
		return TagMultiError(errors)
	}

	return nil
}

// TagMultiError is an error wrapping multiple validation errors returned by
// Tag.ValidateAll() if the designated constraints aren't met.
type TagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TagMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TagMultiError) AllErrors() []error { return m }

// TagValidationError is the validation error returned by Tag.Validate if the
// designated constraints aren't met.
type TagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TagValidationError) ErrorName() string { return "TagValidationError" }

// Error satisfies the builtin error interface
func (e TagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TagValidationError{}

// Validate checks the field values on Source with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Source) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SourceMultiError, or nil if none found.
func (m *Source) ValidateAll() error {
	return m.validate(true)
}

func (m *Source) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Sort

	if len(errors) > 0 {
		return SourceMultiError(errors)
	}

	return nil
}

// SourceMultiError is an error wrapping multiple validation errors returned by
// Source.ValidateAll() if the designated constraints aren't met.
type SourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourceMultiError) AllErrors() []error { return m }

// SourceValidationError is the validation error returned by Source.Validate if
// the designated constraints aren't met.
type SourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourceValidationError) ErrorName() string { return "SourceValidationError" }

// Error satisfies the builtin error interface
func (e SourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourceValidationError{}

// Validate checks the field values on SystemSource with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SystemSource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemSource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SystemSourceMultiError, or
// nil if none found.
func (m *SystemSource) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemSource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Source

	// no validation rules for SourceId

	// no validation rules for SourceName

	if len(errors) > 0 {
		return SystemSourceMultiError(errors)
	}

	return nil
}

// SystemSourceMultiError is an error wrapping multiple validation errors
// returned by SystemSource.ValidateAll() if the designated constraints aren't met.
type SystemSourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemSourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemSourceMultiError) AllErrors() []error { return m }

// SystemSourceValidationError is the validation error returned by
// SystemSource.Validate if the designated constraints aren't met.
type SystemSourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemSourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemSourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemSourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemSourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemSourceValidationError) ErrorName() string { return "SystemSourceValidationError" }

// Error satisfies the builtin error interface
func (e SystemSourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemSource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemSourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemSourceValidationError{}

// Validate checks the field values on ActivityLog_Message with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivityLog_Message) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityLog_Message with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityLog_MessageMultiError, or nil if none found.
func (m *ActivityLog_Message) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityLog_Message) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MessageId

	// no validation rules for Text

	// no validation rules for State

	// no validation rules for FailReason

	// no validation rules for Direction

	if len(errors) > 0 {
		return ActivityLog_MessageMultiError(errors)
	}

	return nil
}

// ActivityLog_MessageMultiError is an error wrapping multiple validation
// errors returned by ActivityLog_Message.ValidateAll() if the designated
// constraints aren't met.
type ActivityLog_MessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityLog_MessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityLog_MessageMultiError) AllErrors() []error { return m }

// ActivityLog_MessageValidationError is the validation error returned by
// ActivityLog_Message.Validate if the designated constraints aren't met.
type ActivityLog_MessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityLog_MessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityLog_MessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityLog_MessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityLog_MessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityLog_MessageValidationError) ErrorName() string {
	return "ActivityLog_MessageValidationError"
}

// Error satisfies the builtin error interface
func (e ActivityLog_MessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityLog_Message.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityLog_MessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityLog_MessageValidationError{}

// Validate checks the field values on ActivityLog_Call with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActivityLog_Call) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityLog_Call with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityLog_CallMultiError, or nil if none found.
func (m *ActivityLog_Call) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityLog_Call) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CallId

	// no validation rules for Text

	// no validation rules for State

	// no validation rules for FailReason

	// no validation rules for Direction

	if len(errors) > 0 {
		return ActivityLog_CallMultiError(errors)
	}

	return nil
}

// ActivityLog_CallMultiError is an error wrapping multiple validation errors
// returned by ActivityLog_Call.ValidateAll() if the designated constraints
// aren't met.
type ActivityLog_CallMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityLog_CallMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityLog_CallMultiError) AllErrors() []error { return m }

// ActivityLog_CallValidationError is the validation error returned by
// ActivityLog_Call.Validate if the designated constraints aren't met.
type ActivityLog_CallValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityLog_CallValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityLog_CallValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityLog_CallValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityLog_CallValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityLog_CallValidationError) ErrorName() string { return "ActivityLog_CallValidationError" }

// Error satisfies the builtin error interface
func (e ActivityLog_CallValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityLog_Call.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityLog_CallValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityLog_CallValidationError{}

// Validate checks the field values on ActivityLog_Note with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActivityLog_Note) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityLog_Note with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityLog_NoteMultiError, or nil if none found.
func (m *ActivityLog_Note) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityLog_Note) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	if len(errors) > 0 {
		return ActivityLog_NoteMultiError(errors)
	}

	return nil
}

// ActivityLog_NoteMultiError is an error wrapping multiple validation errors
// returned by ActivityLog_Note.ValidateAll() if the designated constraints
// aren't met.
type ActivityLog_NoteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityLog_NoteMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityLog_NoteMultiError) AllErrors() []error { return m }

// ActivityLog_NoteValidationError is the validation error returned by
// ActivityLog_Note.Validate if the designated constraints aren't met.
type ActivityLog_NoteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityLog_NoteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityLog_NoteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityLog_NoteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityLog_NoteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityLog_NoteValidationError) ErrorName() string { return "ActivityLog_NoteValidationError" }

// Error satisfies the builtin error interface
func (e ActivityLog_NoteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityLog_Note.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityLog_NoteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityLog_NoteValidationError{}

// Validate checks the field values on ActivityLog_Task with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActivityLog_Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityLog_Task with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityLog_TaskMultiError, or nil if none found.
func (m *ActivityLog_Task) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityLog_Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetTask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivityLog_TaskValidationError{
					field:  "Task",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivityLog_TaskValidationError{
					field:  "Task",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivityLog_TaskValidationError{
				field:  "Task",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActivityLog_TaskMultiError(errors)
	}

	return nil
}

// ActivityLog_TaskMultiError is an error wrapping multiple validation errors
// returned by ActivityLog_Task.ValidateAll() if the designated constraints
// aren't met.
type ActivityLog_TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityLog_TaskMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityLog_TaskMultiError) AllErrors() []error { return m }

// ActivityLog_TaskValidationError is the validation error returned by
// ActivityLog_Task.Validate if the designated constraints aren't met.
type ActivityLog_TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityLog_TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityLog_TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityLog_TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityLog_TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityLog_TaskValidationError) ErrorName() string { return "ActivityLog_TaskValidationError" }

// Error satisfies the builtin error interface
func (e ActivityLog_TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityLog_Task.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityLog_TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityLog_TaskValidationError{}

// Validate checks the field values on ActivityLog_Convert with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivityLog_Convert) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityLog_Convert with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityLog_ConvertMultiError, or nil if none found.
func (m *ActivityLog_Convert) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityLog_Convert) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OriginType

	// no validation rules for SourceId

	// no validation rules for TargetType

	// no validation rules for TargetId

	if len(errors) > 0 {
		return ActivityLog_ConvertMultiError(errors)
	}

	return nil
}

// ActivityLog_ConvertMultiError is an error wrapping multiple validation
// errors returned by ActivityLog_Convert.ValidateAll() if the designated
// constraints aren't met.
type ActivityLog_ConvertMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityLog_ConvertMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityLog_ConvertMultiError) AllErrors() []error { return m }

// ActivityLog_ConvertValidationError is the validation error returned by
// ActivityLog_Convert.Validate if the designated constraints aren't met.
type ActivityLog_ConvertValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityLog_ConvertValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityLog_ConvertValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityLog_ConvertValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityLog_ConvertValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityLog_ConvertValidationError) ErrorName() string {
	return "ActivityLog_ConvertValidationError"
}

// Error satisfies the builtin error interface
func (e ActivityLog_ConvertValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityLog_Convert.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityLog_ConvertValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityLog_ConvertValidationError{}

// Validate checks the field values on ActivityLog_Create with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivityLog_Create) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityLog_Create with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityLog_CreateMultiError, or nil if none found.
func (m *ActivityLog_Create) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityLog_Create) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ActivityLog_CreateMultiError(errors)
	}

	return nil
}

// ActivityLog_CreateMultiError is an error wrapping multiple validation errors
// returned by ActivityLog_Create.ValidateAll() if the designated constraints
// aren't met.
type ActivityLog_CreateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityLog_CreateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityLog_CreateMultiError) AllErrors() []error { return m }

// ActivityLog_CreateValidationError is the validation error returned by
// ActivityLog_Create.Validate if the designated constraints aren't met.
type ActivityLog_CreateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityLog_CreateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityLog_CreateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityLog_CreateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityLog_CreateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityLog_CreateValidationError) ErrorName() string {
	return "ActivityLog_CreateValidationError"
}

// Error satisfies the builtin error interface
func (e ActivityLog_CreateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityLog_Create.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityLog_CreateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityLog_CreateValidationError{}

// Validate checks the field values on ActivityLog_Action with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivityLog_Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityLog_Action with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityLog_ActionMultiError, or nil if none found.
func (m *ActivityLog_Action) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityLog_Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Action.(type) {
	case *ActivityLog_Action_Message:
		if v == nil {
			err := ActivityLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMessage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Message",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Message",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityLog_ActionValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActivityLog_Action_Call:
		if v == nil {
			err := ActivityLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCall()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Call",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Call",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCall()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityLog_ActionValidationError{
					field:  "Call",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActivityLog_Action_Note:
		if v == nil {
			err := ActivityLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNote()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Note",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Note",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNote()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityLog_ActionValidationError{
					field:  "Note",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActivityLog_Action_Task:
		if v == nil {
			err := ActivityLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTask()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Task",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Task",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTask()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityLog_ActionValidationError{
					field:  "Task",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActivityLog_Action_Convert:
		if v == nil {
			err := ActivityLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetConvert()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Convert",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Convert",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetConvert()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityLog_ActionValidationError{
					field:  "Convert",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActivityLog_Action_Create:
		if v == nil {
			err := ActivityLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Create",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityLog_ActionValidationError{
						field:  "Create",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityLog_ActionValidationError{
					field:  "Create",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ActivityLog_ActionMultiError(errors)
	}

	return nil
}

// ActivityLog_ActionMultiError is an error wrapping multiple validation errors
// returned by ActivityLog_Action.ValidateAll() if the designated constraints
// aren't met.
type ActivityLog_ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityLog_ActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityLog_ActionMultiError) AllErrors() []error { return m }

// ActivityLog_ActionValidationError is the validation error returned by
// ActivityLog_Action.Validate if the designated constraints aren't met.
type ActivityLog_ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityLog_ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityLog_ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityLog_ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityLog_ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityLog_ActionValidationError) ErrorName() string {
	return "ActivityLog_ActionValidationError"
}

// Error satisfies the builtin error interface
func (e ActivityLog_ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityLog_Action.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityLog_ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityLog_ActionValidationError{}
