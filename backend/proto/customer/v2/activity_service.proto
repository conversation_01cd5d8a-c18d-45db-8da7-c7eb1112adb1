// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0148::human-names=disabled
//     aip.dev/not-precedent: 使用first_name和last_name作为字段名 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用customer_id作为标识符 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义响应字段 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求字段结构 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)syntax = "proto3";
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 使用空resp易于拓展 --)syntax = "proto3";
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 不使用behavior --)syntax = "proto3";
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent:  使用自定义分页方式 --)syntax = "proto3";

syntax = "proto3";

package backend.proto.customer.v2;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";

import "backend/proto/customer/v2/activity.proto";
import "google/protobuf/timestamp.proto";

// ActivityService 提供客户活动服务
service ActivityService {
  // ActivityLog
  // CreateCustomerActivityLog 创建客户活动日志
  rpc CreateCustomerActivityLog(CreateCustomerActivityLogRequest) returns (CreateCustomerActivityLogResponse);
  // ListCustomerActivityLogs 获取客户历史记录
  rpc UpdateCustomerActivityLog(UpdateCustomerActivityLogRequest) returns (UpdateCustomerActivityLogResponse);
  // ListCustomerActivityLogs 获取客户历史记录
  rpc ListCustomerActivityLogs(ListCustomerActivityLogsRequest) returns (ListCustomerActivityLogsResponse);

  // Convert
  // ConvertCustomer 转换客户状态
  rpc ConvertCustomer(ConvertCustomerRequest) returns (ConvertCustomerResponse);
  // ConvertCustomersAttribute 批量转换客户属性
  rpc ConvertCustomersAttribute(ConvertCustomersAttributeRequest) returns (ConvertCustomersAttributeResponse);

  // Task
  // CreateCustomerTask 创建客户任务
  rpc CreateCustomerTask(CreateCustomerTaskRequest) returns (CreateCustomerTaskResponse);
  // UpdateCustomerTask 更新客户任务
  rpc UpdateCustomerTask(UpdateCustomerTaskRequest) returns (UpdateCustomerTaskResponse);
  // ListCustomerTasks 获取客户任务列表
  rpc ListCustomerTasks(ListCustomerTasksRequest) returns (ListCustomerTasksResponse);
  // DeleteCustomerTask 删除客户任务列表
  rpc DeleteCustomerTask(DeleteCustomerTaskRequest) returns (DeleteCustomerTaskResponse);


  // LifeCycle
  // CreateLifeCycle 创建生命周期
  rpc CreateLifeCycle(CreateLifeCycleRequest) returns (CreateLifeCycleResponse);
  // UpdateLifeCycle 更新生命周期
  rpc UpdateLifeCycles(UpdateLifeCyclesRequest) returns (UpdateLifeCyclesResponse);
  // ListLifeCycles 获取生命周期列表
  rpc ListLifeCycles(ListLifeCyclesRequest) returns (ListLifeCyclesResponse);
  // DeleteLifeCycle 删除生命周期
  rpc DeleteLifeCycle(DeleteLifeCycleRequest) returns (DeleteLifeCycleResponse);

  // ActionState
  // CreateActionState 创建行动状态
  rpc CreateActionState(CreateActionStateRequest) returns (CreateActionStateResponse);
  // UpdateActionState 更新行动状态
  rpc UpdateActionStates(UpdateActionStatesRequest) returns (UpdateActionsStatesResponse);
  // ListActionStates 获取行动状态列表
  rpc ListActionStates(ListActionStatesRequest) returns (ListActionStatesResponse);
  // DeleteActionState 删除行动状态
  rpc DeleteActionState(DeleteActionStateRequest) returns (DeleteActionStateResponse);

  // Tag
  // CreateTag 创建标签
  rpc CreateTag(CreateTagRequest) returns (CreateTagResponse);
  // UpdateTags 批量更新标签
  rpc UpdateTags(UpdateTagsRequest) returns (UpdateTagsResponse);
  // ListTags 获取标签列表
  rpc ListTags(ListTagsRequest) returns (ListTagsResponse);
  // DeleteTag 删除标签
  rpc DeleteTag(DeleteTagRequest) returns (DeleteTagResponse);
  // CoverCustomerTag 覆盖客户标签
  rpc CoverCustomerTag(CoverCustomerTagRequest) returns (CoverCustomerTagResponse);

  // Note
  // CreateNote 创建备注
  rpc CreateNote(CreateNoteRequest) returns (CreateNoteResponse);
  // UpdateNotes 批量更新备注
  rpc UpdateNotes(UpdateNotesRequest) returns (UpdateNotesResponse);
  // ListNotes 获取备注列表
  rpc ListNotes(ListNotesRequest) returns (ListNotesResponse);
  // DeleteNote 删除备注
  rpc DeleteNote(DeleteNoteRequest) returns (DeleteNoteResponse);

  // Source
  // CreateSource 创建来源
  rpc CreateSource(CreateSourceRequest) returns (CreateSourceResponse);
  // UpdateSources 批量更新来源
  rpc UpdateSources(UpdateSourcesRequest) returns (UpdateSourcesResponse);
  // ListSources 获取来源列表
  rpc ListSources(ListSourcesRequest) returns (ListSourcesResponse);
  // DeleteSource 删除来源
  rpc DeleteSource(DeleteSourceRequest) returns (DeleteSourceResponse);

}

// CreateCustomerRequest 创建用户活动日志
message CreateCustomerActivityLogRequest {
  // 客户ID
  int64 customer_id = 1;
  // 客户名称
  string customer_name = 9;
  // 客户电话
  string customer_phone_number = 10;
  // 互动数据
  ActivityLog.Action action = 2;
  // 公司名
  int64 company_id = 3;
  // 门店名
  int64 business_id = 4;
  // 记录来源
  optional SystemSource source = 5;
}

// CreateCustomerActivityLogResponse 创建用户活动日志
message CreateCustomerActivityLogResponse {
  // 客户活动日志记录ID
  int64 log_id = 1;
}

// UpdateCustomerActivityLogRequest 更新用户活动日志
message UpdateCustomerActivityLogRequest {
  // 客户活动日志记录ID
  int64 log_id = 1;
  // 操作员工ID
  int64 staff_id = 2;

  // 互动数据
  optional ActivityLog.Action action = 10;
}

// UpdateCustomerActivityLogResponse 创建用户活动日志
message UpdateCustomerActivityLogResponse {}

// ListCustomerActivityLogsRequest 获取客户历史记录请求
message ListCustomerActivityLogsRequest {
  // Filter 过滤器
  message Filter {
    // 客户ID
    optional int64 customer_id = 1;
    // 记录类型过滤
    optional ActivityLog.Type type = 2;
    // 公司ID
    optional int64 company_id = 3;
  }
  // 过滤条件
  optional Filter filter = 1;
  // 每页数量，允许为0
  optional int32 page_size = 2;
  // 页码，从1开始
  optional int32 page_num = 3;
}

// ListCustomerActivityLogsResponse 获取客户历史记录响应
message ListCustomerActivityLogsResponse {
  // 历史记录列表
  repeated ActivityLog activity_logs = 1;
  // 总数
  int32 total = 2;
}


// ConvertCustomerRequest 转换客户状态请求
message ConvertCustomerRequest {
  // 客户ID
  int64 customer_id = 1;
}

// ConvertCustomerResponse 转换客户状态响应
message ConvertCustomerResponse {}

// ConvertCustomersAttributeRequest 批量转换客户属性请求
message ConvertCustomersAttributeRequest{
  // 客户IDs
  repeated int64 customer_ids = 1;
  // 生命周期
  optional int64 customize_life_cycle_id = 2;
  // 行动状态
  optional int64 customize_action_state_id = 3;
}

// ConvertCustomersAttributeRequest 批量转换客户属性响应
message ConvertCustomersAttributeResponse{
}

// CreateCustomerTaskRequest 创建客户任务列表
message CreateCustomerTaskRequest {
  // 客户ID
  int64 customer_id = 1;
  // 公司ID
  int64 company_id = 2;
  // 门店ID
  int64 business_id = 3;
  // 操作员工ID
  int64 staff_id = 4;

  // 任务名称
  string name = 10;
  // 任务分配员工
  optional int64 allocate_staff_id = 11;
  // 任务预期完成的时间
  optional google.protobuf.Timestamp complete_time = 12;
}

// CreateCustomerTaskResponse 创建客户任务列表
message CreateCustomerTaskResponse {
  // 任务ID
  int64 task_id = 1;
}

// UpdateCustomerTaskRequest 更新客户任务
message UpdateCustomerTaskRequest {
  // 任务ID
  int64 task_id = 1;
  // 操作员工ID
  int64 staff_id = 2;

  // 任务名称
  optional string name = 10;
  // 任务分配员工
  optional int64 allocate_staff_id = 11;
  // 任务预期完成的时间
  optional google.protobuf.Timestamp complete_time = 12;
  // 任务状态
  optional Task.State state = 13;
}

// UpdateCustomerTaskResponse 更新客户任务
message UpdateCustomerTaskResponse {}

// ListCustomerTasksRequest 获取客户任务列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
message ListCustomerTasksRequest {
  // 客户ID
  int64 customer_id = 1;
}

// ListCustomerTasksResponse 获取客户任务列表响应
message ListCustomerTasksResponse {
  // 任务列表
  repeated Task tasks = 1;
}

// DeleteCustomerTaskRequest 删除客户任务列表请求
message DeleteCustomerTaskRequest {
  // 任务ID
  int64 task_id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// DeleteCustomerTaskResponse 删除客户任务列表响应
message DeleteCustomerTaskResponse {
}


// CreateLifeCycleRequest 创建生命周期请求
message CreateLifeCycleRequest {
  // 公司ID
  int64 company_id = 1;
  // 门店ID
  int64 business_id = 2;
  // 操作员工ID
  int64 staff_id = 3;

  // 名称
  string name = 10;
  // 排序
  int32 sort = 11;
}

// CreateLifeCycleResponse 创建生命周期响应
message CreateLifeCycleResponse {
  // LifeCycle ID
  int64 id = 1;
}

// UpdateLifeCyclesRequest 批量更新生命周期请求
message UpdateLifeCyclesRequest {
  // 批量更新结构
  message UpdateLifeCycle {
    // ID
    int64 id = 1;
    // 名称
    optional string name = 2;
    // 排序
    optional int32 sort = 3;
  }

  // 批量更新数据
  repeated UpdateLifeCycle updates = 1;
  // 操作员工ID
  int64 staff_id = 2;
  // 公司ID
  int64 company_id = 3;
}

// UpdateLifeCyclesResponse 批量更更新生命周期响应
message UpdateLifeCyclesResponse {}

// ListLifeCyclesRequest 获取生命周期列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
message ListLifeCyclesRequest {
  // 公司ID
  int64 company_id = 1;
}

// ListLifeCyclesResponse 获取生命周期列表响应
message ListLifeCyclesResponse {
  // 生命周期列表响应
  repeated LifeCycle life_cycles = 1;
}

// DeleteLifeCycleRequest 删除生命周期请求
message DeleteLifeCycleRequest {
  // ID
  int64 id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// DeleteLifeCycleResponse 删除生命周期响应
message DeleteLifeCycleResponse {
}

// CreateActionStateRequest 创建行动状态请求
message CreateActionStateRequest {
  // 公司ID
  int64 company_id = 1;
  // 门店ID
  int64 business_id = 2;
  // 操作员工ID
  int64 staff_id = 3;

  // 名称
  string name = 10;
  // 排序
  int32 sort = 11;
  // 颜色
  string color = 12;
}

// CreateActionStateResponse 创建行动状态响应
message CreateActionStateResponse {
  // ActionState ID
  int64 id = 1;
}

// UpdateActionStatesRequest 批量更新行动状态请求
message UpdateActionStatesRequest {
  // 批量更新结构
  message UpdateActionState {
    // ID
    int64 id = 1;
    // 名称
    optional string name = 2;
    // 排序
    optional int32 sort = 3;
    // 颜色
    optional string color = 4;
  }

  // 批量更新数据
  repeated UpdateActionState updates = 1;
  // 操作员工ID
  int64 staff_id = 2;
  // 公司ID
  int64 company_id = 3;
}

// UpdateActionStatesResponse 批量更新行动状态响应
message UpdateActionsStatesResponse {}

// ListActionStatesRequest 获取行动状态列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
message ListActionStatesRequest {
  // 公司ID
  int64 company_id = 1;
}

// ListActionStatesResponse 获取行动状态列表响应
message ListActionStatesResponse {
  // 行动状态列表
  repeated ActionState action_states = 1;
}

// DeleteActionStateRequest 删除行动状态请求
message DeleteActionStateRequest {
  // ID
  int64 id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// DeleteActionStateResponse 删除行动状态响应
message DeleteActionStateResponse {
}

// CreateTagRequest 创建标签请求
message CreateTagRequest {
  // 公司ID
  int64 company_id = 1;
  // 门店ID
  int64 business_id = 2;
  // 操作员工ID
  int64 staff_id = 3;

  // 名称
  string name = 10;
  // 排序
  int32 sort = 11;
}

// CreateTagResponse 创建标签响应
message CreateTagResponse {
  // Tag ID
  int64 id = 1;
}

// UpdateTagsRequest 批量更新标签请求
message UpdateTagsRequest {
  // 批量更新结构
  message UpdateTag {
    // ID
    int64 id = 1;
    // 名称
    optional string name = 2;
    // 排序
    optional int32 sort = 3;
  }

  // 批量更新数据
  repeated UpdateTag updates = 1;
  // 操作员工ID
  int64 staff_id = 2;
  // 公司ID
  int64 company_id = 3;
}

// UpdateTagsResponse 批量更新标签响应
message UpdateTagsResponse {}

// ListTagsRequest 获取标签列表请求
message ListTagsRequest {
  // 过滤器
  message Filter {
    // 公司ID
    optional int64 company_id = 1;
    // 客户ID
    optional int64 customer_id = 2;
  }

  // 过滤器
  Filter filter = 1;
}

// ListTagsResponse 获取标签列表响应
message ListTagsResponse {
  // 标签列表
  repeated Tag tags = 1;
}

// DeleteTagRequest 删除标签请求
message DeleteTagRequest {
  // ID
  int64 id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// DeleteTagResponse 删除标签响应
message DeleteTagResponse {}

// CoverCustomerTagRequest 覆盖客户标签请求
message CoverCustomerTagRequest {
  // 公司ID
  int64 company_id = 1;
  // 门店ID
  int64 business_id = 2;
  // 客户ID
  int64 customer_id = 3;
  // 操作员工ID
  int64 staff_id = 4;
  // 标签列表
  repeated int64 tag_ids = 5;
}

// CoverCustomerTagResponse 覆盖客户标签响应
message CoverCustomerTagResponse {
}

// CreateNoteRequest 创建备注请求
message CreateNoteRequest {
  // 公司ID
  int64 company_id = 1;
  // 门店ID
  int64 business_id = 2;
  // 客户ID
  int64 customer_id = 4;

  // 备注内容
  string text = 10;
  // 来源
  SystemSource source = 11;
}

// CreateNoteResponse 创建备注响应
message CreateNoteResponse {
  // Note ID
  int64 id = 1;
}

// UpdateNotesRequest 批量更新备注请求
message UpdateNotesRequest {
  // ID
  int64 id = 1;
  // 来源
  SystemSource source = 2;

  // 备注内容
  optional string text = 3;
}

// UpdateNotesResponse 批量更新备注响应
message UpdateNotesResponse {}

// ListNotesRequest 获取备注列表请求
message ListNotesRequest {
  // 过滤器
  message Filter {
    // 客户ID
    optional int64 customer_id = 1;
  }

  // 过滤器
  Filter filter = 1;
}

// ListNotesResponse 获取备注列表响应
message ListNotesResponse {
  // 备注列表
  repeated Note notes = 1;
}

// DeleteNoteRequest 删除备注请求
message DeleteNoteRequest {
  // ID
  int64 id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// DeleteNoteResponse 删除备注响应
message DeleteNoteResponse {}

// CreateSourceRequest 创建来源请求
message CreateSourceRequest {
  // 公司ID
  int64 company_id = 1;
  // 门店ID
  int64 business_id = 2;
  // 操作员工ID
  int64 staff_id = 3;

  // 名称
  string name = 10;
  // 排序
  int32 sort = 11;
}

// CreateSourceResponse 创建来源响应
message CreateSourceResponse {
  // Source ID
  int64 id = 1;
}

// UpdateSourcesRequest 批量更新来源请求
message UpdateSourcesRequest {
  // 批量更新结构
  message UpdateSource {
    // ID
    int64 id = 1;
    // 名称
    optional string name = 2;
    // 排序
    optional int32 sort = 3;
  }

  // 批量更新数据
  repeated UpdateSource updates = 1;
  // 操作员工ID
  int64 staff_id = 2;
  // 公司ID
  int64 company_id = 3;
}

// UpdateSourcesResponse 批量更新来源响应
message UpdateSourcesResponse {}

// ListSourcesRequest 获取来源列表请求
message ListSourcesRequest {
  // 过滤器
  message Filter {
    // 公司ID
    optional int64 company_id = 1;
    // 客户ID
    optional int64 customer_id = 2;
  }

  // 过滤器
  Filter filter = 1;
}

// ListSourcesResponse 获取来源列表响应
message ListSourcesResponse {
  // 来源列表
  repeated Source sources = 1;
}

// DeleteSourceRequest 删除来源请求
message DeleteSourceRequest {
  // ID
  int64 id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// DeleteSourceResponse 删除来源响应
message DeleteSourceResponse {}
