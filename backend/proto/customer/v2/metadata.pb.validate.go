// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/customer/v2/metadata.proto

package customerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Customer with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Customer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Customer with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomerMultiError, or nil
// if none found.
func (m *Customer) ValidateAll() error {
	return m.validate(true)
}

func (m *Customer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetOrganization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrganization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Organization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetGivenName()) > 255 {
		err := CustomerValidationError{
			field:  "GivenName",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFamilyName()) > 255 {
		err := CustomerValidationError{
			field:  "FamilyName",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for State

	// no validation rules for LifecycleId

	// no validation rules for OwnerStaffId

	if all {
		switch v := interface{}(m.GetCustomFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "CustomFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "CustomFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "CustomFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "DeleteTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CustomerMultiError(errors)
	}

	return nil
}

// CustomerMultiError is an error wrapping multiple validation errors returned
// by Customer.ValidateAll() if the designated constraints aren't met.
type CustomerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerMultiError) AllErrors() []error { return m }

// CustomerValidationError is the validation error returned by
// Customer.Validate if the designated constraints aren't met.
type CustomerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerValidationError) ErrorName() string { return "CustomerValidationError" }

// Error satisfies the builtin error interface
func (e CustomerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerValidationError{}

// Validate checks the field values on Lead with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Lead) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lead with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LeadMultiError, or nil if none found.
func (m *Lead) ValidateAll() error {
	return m.validate(true)
}

func (m *Lead) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetOrganization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrganization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LeadValidationError{
				field:  "Organization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetGivenName()) > 255 {
		err := LeadValidationError{
			field:  "GivenName",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFamilyName()) > 255 {
		err := LeadValidationError{
			field:  "FamilyName",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for State

	// no validation rules for LifecycleId

	// no validation rules for OwnerStaffId

	if all {
		switch v := interface{}(m.GetCustomFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "CustomFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "CustomFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LeadValidationError{
				field:  "CustomFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LeadValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LeadValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LeadValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LeadValidationError{
				field:  "DeleteTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LeadMultiError(errors)
	}

	return nil
}

// LeadMultiError is an error wrapping multiple validation errors returned by
// Lead.ValidateAll() if the designated constraints aren't met.
type LeadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LeadMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LeadMultiError) AllErrors() []error { return m }

// LeadValidationError is the validation error returned by Lead.Validate if the
// designated constraints aren't met.
type LeadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LeadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LeadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LeadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LeadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LeadValidationError) ErrorName() string { return "LeadValidationError" }

// Error satisfies the builtin error interface
func (e LeadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLead.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LeadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LeadValidationError{}

// Validate checks the field values on Contact with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Contact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Contact with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ContactMultiError, or nil if none found.
func (m *Contact) ValidateAll() error {
	return m.validate(true)
}

func (m *Contact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CustomerId

	if utf8.RuneCountInString(m.GetGivenName()) > 255 {
		err := ContactValidationError{
			field:  "GivenName",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFamilyName()) > 255 {
		err := ContactValidationError{
			field:  "FamilyName",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetEmail()) > 255 {
		err := ContactValidationError{
			field:  "Email",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if err := m._validateEmail(m.GetEmail()); err != nil {
		err = ContactValidationError{
			field:  "Email",
			reason: "value must be a valid email address",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPhone()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhone()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactValidationError{
				field:  "Phone",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsSelf

	// no validation rules for State

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ContactValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ContactValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ContactValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactValidationError{
				field:  "DeleteTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ContactMultiError(errors)
	}

	return nil
}

func (m *Contact) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *Contact) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// ContactMultiError is an error wrapping multiple validation errors returned
// by Contact.ValidateAll() if the designated constraints aren't met.
type ContactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactMultiError) AllErrors() []error { return m }

// ContactValidationError is the validation error returned by Contact.Validate
// if the designated constraints aren't met.
type ContactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactValidationError) ErrorName() string { return "ContactValidationError" }

// Error satisfies the builtin error interface
func (e ContactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactValidationError{}

// Validate checks the field values on ContactTag with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ContactTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContactTag with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ContactTagMultiError, or
// nil if none found.
func (m *ContactTag) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetOrganization()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactTagValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactTagValidationError{
					field:  "Organization",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrganization()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactTagValidationError{
				field:  "Organization",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetName()) > 100 {
		err := ContactTagValidationError{
			field:  "Name",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDescription()) > 500 {
		err := ContactTagValidationError{
			field:  "Description",
			reason: "value length must be at most 500 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetColor()) > 7 {
		err := ContactTagValidationError{
			field:  "Color",
			reason: "value length must be at most 7 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SortOrder

	// no validation rules for State

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactTagValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactTagValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactTagValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactTagValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactTagValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactTagValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactTagValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactTagValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactTagValidationError{
				field:  "DeleteTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ContactTagMultiError(errors)
	}

	return nil
}

// ContactTagMultiError is an error wrapping multiple validation errors
// returned by ContactTag.ValidateAll() if the designated constraints aren't met.
type ContactTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactTagMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactTagMultiError) AllErrors() []error { return m }

// ContactTagValidationError is the validation error returned by
// ContactTag.Validate if the designated constraints aren't met.
type ContactTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactTagValidationError) ErrorName() string { return "ContactTagValidationError" }

// Error satisfies the builtin error interface
func (e ContactTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactTagValidationError{}

// Validate checks the field values on Address with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Address) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Address with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AddressMultiError, or nil if none found.
func (m *Address) ValidateAll() error {
	return m.validate(true)
}

func (m *Address) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CustomerId

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLatlng()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "Latlng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "Latlng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatlng()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressValidationError{
				field:  "Latlng",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressValidationError{
				field:  "DeleteTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddressMultiError(errors)
	}

	return nil
}

// AddressMultiError is an error wrapping multiple validation errors returned
// by Address.ValidateAll() if the designated constraints aren't met.
type AddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressMultiError) AllErrors() []error { return m }

// AddressValidationError is the validation error returned by Address.Validate
// if the designated constraints aren't met.
type AddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddressValidationError) ErrorName() string { return "AddressValidationError" }

// Error satisfies the builtin error interface
func (e AddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressValidationError{}

// Validate checks the field values on CustomField with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CustomField) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomField with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomFieldMultiError, or
// nil if none found.
func (m *CustomField) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomField) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetOwner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "Owner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "Owner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOwner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomFieldValidationError{
				field:  "Owner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Key

	// no validation rules for Label

	// no validation rules for Type

	// no validation rules for IsRequired

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetDefaultValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "DefaultValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "DefaultValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomFieldValidationError{
				field:  "DefaultValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomFieldValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomFieldValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomFieldValidationError{
					field:  fmt.Sprintf("Options[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetValidationRules()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "ValidationRules",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "ValidationRules",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidationRules()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomFieldValidationError{
				field:  "ValidationRules",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisplayOrder

	// no validation rules for HelpText

	// no validation rules for TenantId

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomFieldValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomFieldValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomFieldValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomFieldValidationError{
				field:  "DeleteTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CustomFieldMultiError(errors)
	}

	return nil
}

// CustomFieldMultiError is an error wrapping multiple validation errors
// returned by CustomField.ValidateAll() if the designated constraints aren't met.
type CustomFieldMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomFieldMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomFieldMultiError) AllErrors() []error { return m }

// CustomFieldValidationError is the validation error returned by
// CustomField.Validate if the designated constraints aren't met.
type CustomFieldValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomFieldValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomFieldValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomFieldValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomFieldValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomFieldValidationError) ErrorName() string { return "CustomFieldValidationError" }

// Error satisfies the builtin error interface
func (e CustomFieldValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomField.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomFieldValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomFieldValidationError{}

// Validate checks the field values on CustomField_Value with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomField_Value) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomField_Value with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomField_ValueMultiError, or nil if none found.
func (m *CustomField_Value) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomField_Value) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Value.(type) {
	case *CustomField_Value_String_:
		if v == nil {
			err := CustomField_ValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for String_
	case *CustomField_Value_DoubleValue:
		if v == nil {
			err := CustomField_ValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DoubleValue
	case *CustomField_Value_Int64:
		if v == nil {
			err := CustomField_ValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Int64
	case *CustomField_Value_Bool:
		if v == nil {
			err := CustomField_ValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Bool
	case *CustomField_Value_Money:
		if v == nil {
			err := CustomField_ValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMoney()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomField_ValueValidationError{
						field:  "Money",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomField_ValueValidationError{
						field:  "Money",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMoney()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomField_ValueValidationError{
					field:  "Money",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomField_Value_TimestampTime:
		if v == nil {
			err := CustomField_ValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTimestampTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomField_ValueValidationError{
						field:  "TimestampTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomField_ValueValidationError{
						field:  "TimestampTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTimestampTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomField_ValueValidationError{
					field:  "TimestampTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomField_Value_Relation_:
		if v == nil {
			err := CustomField_ValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRelation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomField_ValueValidationError{
						field:  "Relation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomField_ValueValidationError{
						field:  "Relation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRelation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomField_ValueValidationError{
					field:  "Relation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CustomField_ValueMultiError(errors)
	}

	return nil
}

// CustomField_ValueMultiError is an error wrapping multiple validation errors
// returned by CustomField_Value.ValidateAll() if the designated constraints
// aren't met.
type CustomField_ValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomField_ValueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomField_ValueMultiError) AllErrors() []error { return m }

// CustomField_ValueValidationError is the validation error returned by
// CustomField_Value.Validate if the designated constraints aren't met.
type CustomField_ValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomField_ValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomField_ValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomField_ValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomField_ValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomField_ValueValidationError) ErrorName() string {
	return "CustomField_ValueValidationError"
}

// Error satisfies the builtin error interface
func (e CustomField_ValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomField_Value.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomField_ValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomField_ValueValidationError{}

// Validate checks the field values on CustomField_Option with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomField_Option) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomField_Option with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomField_OptionMultiError, or nil if none found.
func (m *CustomField_Option) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomField_Option) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomField_OptionValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomField_OptionValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomField_OptionValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Label

	// no validation rules for SortOrder

	// no validation rules for State

	if len(errors) > 0 {
		return CustomField_OptionMultiError(errors)
	}

	return nil
}

// CustomField_OptionMultiError is an error wrapping multiple validation errors
// returned by CustomField_Option.ValidateAll() if the designated constraints
// aren't met.
type CustomField_OptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomField_OptionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomField_OptionMultiError) AllErrors() []error { return m }

// CustomField_OptionValidationError is the validation error returned by
// CustomField_Option.Validate if the designated constraints aren't met.
type CustomField_OptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomField_OptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomField_OptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomField_OptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomField_OptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomField_OptionValidationError) ErrorName() string {
	return "CustomField_OptionValidationError"
}

// Error satisfies the builtin error interface
func (e CustomField_OptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomField_Option.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomField_OptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomField_OptionValidationError{}

// Validate checks the field values on CustomField_Value_Relation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomField_Value_Relation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomField_Value_Relation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomField_Value_RelationMultiError, or nil if none found.
func (m *CustomField_Value_Relation) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomField_Value_Relation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Entity

	// no validation rules for Id

	if len(errors) > 0 {
		return CustomField_Value_RelationMultiError(errors)
	}

	return nil
}

// CustomField_Value_RelationMultiError is an error wrapping multiple
// validation errors returned by CustomField_Value_Relation.ValidateAll() if
// the designated constraints aren't met.
type CustomField_Value_RelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomField_Value_RelationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomField_Value_RelationMultiError) AllErrors() []error { return m }

// CustomField_Value_RelationValidationError is the validation error returned
// by CustomField_Value_Relation.Validate if the designated constraints aren't met.
type CustomField_Value_RelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomField_Value_RelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomField_Value_RelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomField_Value_RelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomField_Value_RelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomField_Value_RelationValidationError) ErrorName() string {
	return "CustomField_Value_RelationValidationError"
}

// Error satisfies the builtin error interface
func (e CustomField_Value_RelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomField_Value_Relation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomField_Value_RelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomField_Value_RelationValidationError{}
