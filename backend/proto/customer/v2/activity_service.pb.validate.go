// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/customer/v2/activity_service.proto

package customerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateCustomerActivityLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateCustomerActivityLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerActivityLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateCustomerActivityLogRequestMultiError, or nil if none found.
func (m *CreateCustomerActivityLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerActivityLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for CustomerName

	// no validation rules for CustomerPhoneNumber

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerActivityLogRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerActivityLogRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerActivityLogRequestValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	if m.Source != nil {

		if all {
			switch v := interface{}(m.GetSource()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateCustomerActivityLogRequestValidationError{
						field:  "Source",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateCustomerActivityLogRequestValidationError{
						field:  "Source",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateCustomerActivityLogRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateCustomerActivityLogRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerActivityLogRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateCustomerActivityLogRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerActivityLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerActivityLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerActivityLogRequestMultiError) AllErrors() []error { return m }

// CreateCustomerActivityLogRequestValidationError is the validation error
// returned by CreateCustomerActivityLogRequest.Validate if the designated
// constraints aren't met.
type CreateCustomerActivityLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerActivityLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerActivityLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerActivityLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerActivityLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerActivityLogRequestValidationError) ErrorName() string {
	return "CreateCustomerActivityLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerActivityLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerActivityLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerActivityLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerActivityLogRequestValidationError{}

// Validate checks the field values on CreateCustomerActivityLogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateCustomerActivityLogResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerActivityLogResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateCustomerActivityLogResponseMultiError, or nil if none found.
func (m *CreateCustomerActivityLogResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerActivityLogResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LogId

	if len(errors) > 0 {
		return CreateCustomerActivityLogResponseMultiError(errors)
	}

	return nil
}

// CreateCustomerActivityLogResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateCustomerActivityLogResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerActivityLogResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerActivityLogResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerActivityLogResponseMultiError) AllErrors() []error { return m }

// CreateCustomerActivityLogResponseValidationError is the validation error
// returned by CreateCustomerActivityLogResponse.Validate if the designated
// constraints aren't met.
type CreateCustomerActivityLogResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerActivityLogResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerActivityLogResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerActivityLogResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerActivityLogResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerActivityLogResponseValidationError) ErrorName() string {
	return "CreateCustomerActivityLogResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerActivityLogResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerActivityLogResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerActivityLogResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerActivityLogResponseValidationError{}

// Validate checks the field values on UpdateCustomerActivityLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCustomerActivityLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerActivityLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateCustomerActivityLogRequestMultiError, or nil if none found.
func (m *UpdateCustomerActivityLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerActivityLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LogId

	// no validation rules for StaffId

	if m.Action != nil {

		if all {
			switch v := interface{}(m.GetAction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCustomerActivityLogRequestValidationError{
						field:  "Action",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCustomerActivityLogRequestValidationError{
						field:  "Action",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCustomerActivityLogRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateCustomerActivityLogRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerActivityLogRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCustomerActivityLogRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerActivityLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerActivityLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerActivityLogRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerActivityLogRequestValidationError is the validation error
// returned by UpdateCustomerActivityLogRequest.Validate if the designated
// constraints aren't met.
type UpdateCustomerActivityLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerActivityLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerActivityLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerActivityLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerActivityLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerActivityLogRequestValidationError) ErrorName() string {
	return "UpdateCustomerActivityLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerActivityLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerActivityLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerActivityLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerActivityLogRequestValidationError{}

// Validate checks the field values on UpdateCustomerActivityLogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCustomerActivityLogResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerActivityLogResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateCustomerActivityLogResponseMultiError, or nil if none found.
func (m *UpdateCustomerActivityLogResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerActivityLogResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateCustomerActivityLogResponseMultiError(errors)
	}

	return nil
}

// UpdateCustomerActivityLogResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCustomerActivityLogResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerActivityLogResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerActivityLogResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerActivityLogResponseMultiError) AllErrors() []error { return m }

// UpdateCustomerActivityLogResponseValidationError is the validation error
// returned by UpdateCustomerActivityLogResponse.Validate if the designated
// constraints aren't met.
type UpdateCustomerActivityLogResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerActivityLogResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerActivityLogResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerActivityLogResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerActivityLogResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerActivityLogResponseValidationError) ErrorName() string {
	return "UpdateCustomerActivityLogResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerActivityLogResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerActivityLogResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerActivityLogResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerActivityLogResponseValidationError{}

// Validate checks the field values on ListCustomerActivityLogsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerActivityLogsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerActivityLogsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCustomerActivityLogsRequestMultiError, or nil if none found.
func (m *ListCustomerActivityLogsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerActivityLogsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Filter != nil {

		if all {
			switch v := interface{}(m.GetFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomerActivityLogsRequestValidationError{
						field:  "Filter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomerActivityLogsRequestValidationError{
						field:  "Filter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomerActivityLogsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.PageSize != nil {
		// no validation rules for PageSize
	}

	if m.PageNum != nil {
		// no validation rules for PageNum
	}

	if len(errors) > 0 {
		return ListCustomerActivityLogsRequestMultiError(errors)
	}

	return nil
}

// ListCustomerActivityLogsRequestMultiError is an error wrapping multiple
// validation errors returned by ListCustomerActivityLogsRequest.ValidateAll()
// if the designated constraints aren't met.
type ListCustomerActivityLogsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerActivityLogsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerActivityLogsRequestMultiError) AllErrors() []error { return m }

// ListCustomerActivityLogsRequestValidationError is the validation error
// returned by ListCustomerActivityLogsRequest.Validate if the designated
// constraints aren't met.
type ListCustomerActivityLogsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerActivityLogsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerActivityLogsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerActivityLogsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerActivityLogsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerActivityLogsRequestValidationError) ErrorName() string {
	return "ListCustomerActivityLogsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerActivityLogsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerActivityLogsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerActivityLogsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerActivityLogsRequestValidationError{}

// Validate checks the field values on ListCustomerActivityLogsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListCustomerActivityLogsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerActivityLogsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCustomerActivityLogsResponseMultiError, or nil if none found.
func (m *ListCustomerActivityLogsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerActivityLogsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetActivityLogs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomerActivityLogsResponseValidationError{
						field:  fmt.Sprintf("ActivityLogs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomerActivityLogsResponseValidationError{
						field:  fmt.Sprintf("ActivityLogs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomerActivityLogsResponseValidationError{
					field:  fmt.Sprintf("ActivityLogs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListCustomerActivityLogsResponseMultiError(errors)
	}

	return nil
}

// ListCustomerActivityLogsResponseMultiError is an error wrapping multiple
// validation errors returned by
// ListCustomerActivityLogsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCustomerActivityLogsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerActivityLogsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerActivityLogsResponseMultiError) AllErrors() []error { return m }

// ListCustomerActivityLogsResponseValidationError is the validation error
// returned by ListCustomerActivityLogsResponse.Validate if the designated
// constraints aren't met.
type ListCustomerActivityLogsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerActivityLogsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerActivityLogsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerActivityLogsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerActivityLogsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerActivityLogsResponseValidationError) ErrorName() string {
	return "ListCustomerActivityLogsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerActivityLogsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerActivityLogsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerActivityLogsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerActivityLogsResponseValidationError{}

// Validate checks the field values on ConvertCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConvertCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConvertCustomerRequestMultiError, or nil if none found.
func (m *ConvertCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	if len(errors) > 0 {
		return ConvertCustomerRequestMultiError(errors)
	}

	return nil
}

// ConvertCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by ConvertCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type ConvertCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertCustomerRequestMultiError) AllErrors() []error { return m }

// ConvertCustomerRequestValidationError is the validation error returned by
// ConvertCustomerRequest.Validate if the designated constraints aren't met.
type ConvertCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertCustomerRequestValidationError) ErrorName() string {
	return "ConvertCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertCustomerRequestValidationError{}

// Validate checks the field values on ConvertCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConvertCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertCustomerResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConvertCustomerResponseMultiError, or nil if none found.
func (m *ConvertCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ConvertCustomerResponseMultiError(errors)
	}

	return nil
}

// ConvertCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by ConvertCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type ConvertCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertCustomerResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertCustomerResponseMultiError) AllErrors() []error { return m }

// ConvertCustomerResponseValidationError is the validation error returned by
// ConvertCustomerResponse.Validate if the designated constraints aren't met.
type ConvertCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertCustomerResponseValidationError) ErrorName() string {
	return "ConvertCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertCustomerResponseValidationError{}

// Validate checks the field values on ConvertCustomersAttributeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConvertCustomersAttributeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertCustomersAttributeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConvertCustomersAttributeRequestMultiError, or nil if none found.
func (m *ConvertCustomersAttributeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertCustomersAttributeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CustomizeLifeCycleId != nil {
		// no validation rules for CustomizeLifeCycleId
	}

	if m.CustomizeActionStateId != nil {
		// no validation rules for CustomizeActionStateId
	}

	if len(errors) > 0 {
		return ConvertCustomersAttributeRequestMultiError(errors)
	}

	return nil
}

// ConvertCustomersAttributeRequestMultiError is an error wrapping multiple
// validation errors returned by
// ConvertCustomersAttributeRequest.ValidateAll() if the designated
// constraints aren't met.
type ConvertCustomersAttributeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertCustomersAttributeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertCustomersAttributeRequestMultiError) AllErrors() []error { return m }

// ConvertCustomersAttributeRequestValidationError is the validation error
// returned by ConvertCustomersAttributeRequest.Validate if the designated
// constraints aren't met.
type ConvertCustomersAttributeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertCustomersAttributeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertCustomersAttributeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertCustomersAttributeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertCustomersAttributeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertCustomersAttributeRequestValidationError) ErrorName() string {
	return "ConvertCustomersAttributeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertCustomersAttributeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertCustomersAttributeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertCustomersAttributeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertCustomersAttributeRequestValidationError{}

// Validate checks the field values on ConvertCustomersAttributeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConvertCustomersAttributeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertCustomersAttributeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConvertCustomersAttributeResponseMultiError, or nil if none found.
func (m *ConvertCustomersAttributeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertCustomersAttributeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ConvertCustomersAttributeResponseMultiError(errors)
	}

	return nil
}

// ConvertCustomersAttributeResponseMultiError is an error wrapping multiple
// validation errors returned by
// ConvertCustomersAttributeResponse.ValidateAll() if the designated
// constraints aren't met.
type ConvertCustomersAttributeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertCustomersAttributeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertCustomersAttributeResponseMultiError) AllErrors() []error { return m }

// ConvertCustomersAttributeResponseValidationError is the validation error
// returned by ConvertCustomersAttributeResponse.Validate if the designated
// constraints aren't met.
type ConvertCustomersAttributeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertCustomersAttributeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertCustomersAttributeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertCustomersAttributeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertCustomersAttributeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertCustomersAttributeResponseValidationError) ErrorName() string {
	return "ConvertCustomersAttributeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertCustomersAttributeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertCustomersAttributeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertCustomersAttributeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertCustomersAttributeResponseValidationError{}

// Validate checks the field values on CreateCustomerTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerTaskRequestMultiError, or nil if none found.
func (m *CreateCustomerTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	// no validation rules for Name

	if m.AllocateStaffId != nil {
		// no validation rules for AllocateStaffId
	}

	if m.CompleteTime != nil {

		if all {
			switch v := interface{}(m.GetCompleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateCustomerTaskRequestValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateCustomerTaskRequestValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateCustomerTaskRequestValidationError{
					field:  "CompleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateCustomerTaskRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerTaskRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCustomerTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateCustomerTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerTaskRequestMultiError) AllErrors() []error { return m }

// CreateCustomerTaskRequestValidationError is the validation error returned by
// CreateCustomerTaskRequest.Validate if the designated constraints aren't met.
type CreateCustomerTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerTaskRequestValidationError) ErrorName() string {
	return "CreateCustomerTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerTaskRequestValidationError{}

// Validate checks the field values on CreateCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerTaskResponseMultiError, or nil if none found.
func (m *CreateCustomerTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return CreateCustomerTaskResponseMultiError(errors)
	}

	return nil
}

// CreateCustomerTaskResponseMultiError is an error wrapping multiple
// validation errors returned by CreateCustomerTaskResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateCustomerTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerTaskResponseMultiError) AllErrors() []error { return m }

// CreateCustomerTaskResponseValidationError is the validation error returned
// by CreateCustomerTaskResponse.Validate if the designated constraints aren't met.
type CreateCustomerTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerTaskResponseValidationError) ErrorName() string {
	return "CreateCustomerTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerTaskResponseValidationError{}

// Validate checks the field values on UpdateCustomerTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerTaskRequestMultiError, or nil if none found.
func (m *UpdateCustomerTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for StaffId

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.AllocateStaffId != nil {
		// no validation rules for AllocateStaffId
	}

	if m.CompleteTime != nil {

		if all {
			switch v := interface{}(m.GetCompleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCustomerTaskRequestValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCustomerTaskRequestValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCustomerTaskRequestValidationError{
					field:  "CompleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.State != nil {
		// no validation rules for State
	}

	if len(errors) > 0 {
		return UpdateCustomerTaskRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerTaskRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomerTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateCustomerTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerTaskRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerTaskRequestValidationError is the validation error returned by
// UpdateCustomerTaskRequest.Validate if the designated constraints aren't met.
type UpdateCustomerTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerTaskRequestValidationError) ErrorName() string {
	return "UpdateCustomerTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerTaskRequestValidationError{}

// Validate checks the field values on UpdateCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerTaskResponseMultiError, or nil if none found.
func (m *UpdateCustomerTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateCustomerTaskResponseMultiError(errors)
	}

	return nil
}

// UpdateCustomerTaskResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateCustomerTaskResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateCustomerTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerTaskResponseMultiError) AllErrors() []error { return m }

// UpdateCustomerTaskResponseValidationError is the validation error returned
// by UpdateCustomerTaskResponse.Validate if the designated constraints aren't met.
type UpdateCustomerTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerTaskResponseValidationError) ErrorName() string {
	return "UpdateCustomerTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerTaskResponseValidationError{}

// Validate checks the field values on ListCustomerTasksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerTasksRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerTasksRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomerTasksRequestMultiError, or nil if none found.
func (m *ListCustomerTasksRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerTasksRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	if len(errors) > 0 {
		return ListCustomerTasksRequestMultiError(errors)
	}

	return nil
}

// ListCustomerTasksRequestMultiError is an error wrapping multiple validation
// errors returned by ListCustomerTasksRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCustomerTasksRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerTasksRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerTasksRequestMultiError) AllErrors() []error { return m }

// ListCustomerTasksRequestValidationError is the validation error returned by
// ListCustomerTasksRequest.Validate if the designated constraints aren't met.
type ListCustomerTasksRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerTasksRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerTasksRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerTasksRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerTasksRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerTasksRequestValidationError) ErrorName() string {
	return "ListCustomerTasksRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerTasksRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerTasksRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerTasksRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerTasksRequestValidationError{}

// Validate checks the field values on ListCustomerTasksResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerTasksResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerTasksResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomerTasksResponseMultiError, or nil if none found.
func (m *ListCustomerTasksResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerTasksResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomerTasksResponseValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomerTasksResponseValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomerTasksResponseValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListCustomerTasksResponseMultiError(errors)
	}

	return nil
}

// ListCustomerTasksResponseMultiError is an error wrapping multiple validation
// errors returned by ListCustomerTasksResponse.ValidateAll() if the
// designated constraints aren't met.
type ListCustomerTasksResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerTasksResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerTasksResponseMultiError) AllErrors() []error { return m }

// ListCustomerTasksResponseValidationError is the validation error returned by
// ListCustomerTasksResponse.Validate if the designated constraints aren't met.
type ListCustomerTasksResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerTasksResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerTasksResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerTasksResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerTasksResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerTasksResponseValidationError) ErrorName() string {
	return "ListCustomerTasksResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerTasksResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerTasksResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerTasksResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerTasksResponseValidationError{}

// Validate checks the field values on DeleteCustomerTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCustomerTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCustomerTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCustomerTaskRequestMultiError, or nil if none found.
func (m *DeleteCustomerTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCustomerTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteCustomerTaskRequestMultiError(errors)
	}

	return nil
}

// DeleteCustomerTaskRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteCustomerTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type DeleteCustomerTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCustomerTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCustomerTaskRequestMultiError) AllErrors() []error { return m }

// DeleteCustomerTaskRequestValidationError is the validation error returned by
// DeleteCustomerTaskRequest.Validate if the designated constraints aren't met.
type DeleteCustomerTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCustomerTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCustomerTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCustomerTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCustomerTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCustomerTaskRequestValidationError) ErrorName() string {
	return "DeleteCustomerTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCustomerTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCustomerTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCustomerTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCustomerTaskRequestValidationError{}

// Validate checks the field values on DeleteCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCustomerTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCustomerTaskResponseMultiError, or nil if none found.
func (m *DeleteCustomerTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCustomerTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteCustomerTaskResponseMultiError(errors)
	}

	return nil
}

// DeleteCustomerTaskResponseMultiError is an error wrapping multiple
// validation errors returned by DeleteCustomerTaskResponse.ValidateAll() if
// the designated constraints aren't met.
type DeleteCustomerTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCustomerTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCustomerTaskResponseMultiError) AllErrors() []error { return m }

// DeleteCustomerTaskResponseValidationError is the validation error returned
// by DeleteCustomerTaskResponse.Validate if the designated constraints aren't met.
type DeleteCustomerTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCustomerTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCustomerTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCustomerTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCustomerTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCustomerTaskResponseValidationError) ErrorName() string {
	return "DeleteCustomerTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCustomerTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCustomerTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCustomerTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCustomerTaskResponseValidationError{}

// Validate checks the field values on CreateLifeCycleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLifeCycleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLifeCycleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLifeCycleRequestMultiError, or nil if none found.
func (m *CreateLifeCycleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLifeCycleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	// no validation rules for Name

	// no validation rules for Sort

	if len(errors) > 0 {
		return CreateLifeCycleRequestMultiError(errors)
	}

	return nil
}

// CreateLifeCycleRequestMultiError is an error wrapping multiple validation
// errors returned by CreateLifeCycleRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateLifeCycleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLifeCycleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLifeCycleRequestMultiError) AllErrors() []error { return m }

// CreateLifeCycleRequestValidationError is the validation error returned by
// CreateLifeCycleRequest.Validate if the designated constraints aren't met.
type CreateLifeCycleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLifeCycleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLifeCycleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLifeCycleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLifeCycleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLifeCycleRequestValidationError) ErrorName() string {
	return "CreateLifeCycleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLifeCycleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLifeCycleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLifeCycleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLifeCycleRequestValidationError{}

// Validate checks the field values on CreateLifeCycleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLifeCycleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLifeCycleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLifeCycleResponseMultiError, or nil if none found.
func (m *CreateLifeCycleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLifeCycleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateLifeCycleResponseMultiError(errors)
	}

	return nil
}

// CreateLifeCycleResponseMultiError is an error wrapping multiple validation
// errors returned by CreateLifeCycleResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateLifeCycleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLifeCycleResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLifeCycleResponseMultiError) AllErrors() []error { return m }

// CreateLifeCycleResponseValidationError is the validation error returned by
// CreateLifeCycleResponse.Validate if the designated constraints aren't met.
type CreateLifeCycleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLifeCycleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLifeCycleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLifeCycleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLifeCycleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLifeCycleResponseValidationError) ErrorName() string {
	return "CreateLifeCycleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLifeCycleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLifeCycleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLifeCycleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLifeCycleResponseValidationError{}

// Validate checks the field values on UpdateLifeCyclesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLifeCyclesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLifeCyclesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLifeCyclesRequestMultiError, or nil if none found.
func (m *UpdateLifeCyclesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLifeCyclesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUpdates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateLifeCyclesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateLifeCyclesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateLifeCyclesRequestValidationError{
					field:  fmt.Sprintf("Updates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for StaffId

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return UpdateLifeCyclesRequestMultiError(errors)
	}

	return nil
}

// UpdateLifeCyclesRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateLifeCyclesRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateLifeCyclesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLifeCyclesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLifeCyclesRequestMultiError) AllErrors() []error { return m }

// UpdateLifeCyclesRequestValidationError is the validation error returned by
// UpdateLifeCyclesRequest.Validate if the designated constraints aren't met.
type UpdateLifeCyclesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLifeCyclesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLifeCyclesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLifeCyclesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLifeCyclesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLifeCyclesRequestValidationError) ErrorName() string {
	return "UpdateLifeCyclesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLifeCyclesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLifeCyclesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLifeCyclesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLifeCyclesRequestValidationError{}

// Validate checks the field values on UpdateLifeCyclesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLifeCyclesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLifeCyclesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLifeCyclesResponseMultiError, or nil if none found.
func (m *UpdateLifeCyclesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLifeCyclesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateLifeCyclesResponseMultiError(errors)
	}

	return nil
}

// UpdateLifeCyclesResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateLifeCyclesResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateLifeCyclesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLifeCyclesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLifeCyclesResponseMultiError) AllErrors() []error { return m }

// UpdateLifeCyclesResponseValidationError is the validation error returned by
// UpdateLifeCyclesResponse.Validate if the designated constraints aren't met.
type UpdateLifeCyclesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLifeCyclesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLifeCyclesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLifeCyclesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLifeCyclesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLifeCyclesResponseValidationError) ErrorName() string {
	return "UpdateLifeCyclesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLifeCyclesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLifeCyclesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLifeCyclesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLifeCyclesResponseValidationError{}

// Validate checks the field values on ListLifeCyclesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLifeCyclesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLifeCyclesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLifeCyclesRequestMultiError, or nil if none found.
func (m *ListLifeCyclesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLifeCyclesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return ListLifeCyclesRequestMultiError(errors)
	}

	return nil
}

// ListLifeCyclesRequestMultiError is an error wrapping multiple validation
// errors returned by ListLifeCyclesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListLifeCyclesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLifeCyclesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLifeCyclesRequestMultiError) AllErrors() []error { return m }

// ListLifeCyclesRequestValidationError is the validation error returned by
// ListLifeCyclesRequest.Validate if the designated constraints aren't met.
type ListLifeCyclesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLifeCyclesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLifeCyclesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLifeCyclesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLifeCyclesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLifeCyclesRequestValidationError) ErrorName() string {
	return "ListLifeCyclesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListLifeCyclesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLifeCyclesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLifeCyclesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLifeCyclesRequestValidationError{}

// Validate checks the field values on ListLifeCyclesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLifeCyclesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLifeCyclesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLifeCyclesResponseMultiError, or nil if none found.
func (m *ListLifeCyclesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLifeCyclesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLifeCycles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLifeCyclesResponseValidationError{
						field:  fmt.Sprintf("LifeCycles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLifeCyclesResponseValidationError{
						field:  fmt.Sprintf("LifeCycles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLifeCyclesResponseValidationError{
					field:  fmt.Sprintf("LifeCycles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListLifeCyclesResponseMultiError(errors)
	}

	return nil
}

// ListLifeCyclesResponseMultiError is an error wrapping multiple validation
// errors returned by ListLifeCyclesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListLifeCyclesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLifeCyclesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLifeCyclesResponseMultiError) AllErrors() []error { return m }

// ListLifeCyclesResponseValidationError is the validation error returned by
// ListLifeCyclesResponse.Validate if the designated constraints aren't met.
type ListLifeCyclesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLifeCyclesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLifeCyclesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLifeCyclesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLifeCyclesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLifeCyclesResponseValidationError) ErrorName() string {
	return "ListLifeCyclesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListLifeCyclesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLifeCyclesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLifeCyclesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLifeCyclesResponseValidationError{}

// Validate checks the field values on DeleteLifeCycleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteLifeCycleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteLifeCycleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteLifeCycleRequestMultiError, or nil if none found.
func (m *DeleteLifeCycleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLifeCycleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteLifeCycleRequestMultiError(errors)
	}

	return nil
}

// DeleteLifeCycleRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteLifeCycleRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteLifeCycleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLifeCycleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLifeCycleRequestMultiError) AllErrors() []error { return m }

// DeleteLifeCycleRequestValidationError is the validation error returned by
// DeleteLifeCycleRequest.Validate if the designated constraints aren't met.
type DeleteLifeCycleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLifeCycleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLifeCycleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLifeCycleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLifeCycleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLifeCycleRequestValidationError) ErrorName() string {
	return "DeleteLifeCycleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteLifeCycleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLifeCycleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLifeCycleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLifeCycleRequestValidationError{}

// Validate checks the field values on DeleteLifeCycleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteLifeCycleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteLifeCycleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteLifeCycleResponseMultiError, or nil if none found.
func (m *DeleteLifeCycleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLifeCycleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteLifeCycleResponseMultiError(errors)
	}

	return nil
}

// DeleteLifeCycleResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteLifeCycleResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteLifeCycleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLifeCycleResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLifeCycleResponseMultiError) AllErrors() []error { return m }

// DeleteLifeCycleResponseValidationError is the validation error returned by
// DeleteLifeCycleResponse.Validate if the designated constraints aren't met.
type DeleteLifeCycleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLifeCycleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLifeCycleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLifeCycleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLifeCycleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLifeCycleResponseValidationError) ErrorName() string {
	return "DeleteLifeCycleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteLifeCycleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLifeCycleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLifeCycleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLifeCycleResponseValidationError{}

// Validate checks the field values on CreateActionStateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateActionStateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateActionStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateActionStateRequestMultiError, or nil if none found.
func (m *CreateActionStateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateActionStateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	// no validation rules for Name

	// no validation rules for Sort

	// no validation rules for Color

	if len(errors) > 0 {
		return CreateActionStateRequestMultiError(errors)
	}

	return nil
}

// CreateActionStateRequestMultiError is an error wrapping multiple validation
// errors returned by CreateActionStateRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateActionStateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateActionStateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateActionStateRequestMultiError) AllErrors() []error { return m }

// CreateActionStateRequestValidationError is the validation error returned by
// CreateActionStateRequest.Validate if the designated constraints aren't met.
type CreateActionStateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateActionStateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateActionStateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateActionStateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateActionStateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateActionStateRequestValidationError) ErrorName() string {
	return "CreateActionStateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateActionStateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateActionStateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateActionStateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateActionStateRequestValidationError{}

// Validate checks the field values on CreateActionStateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateActionStateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateActionStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateActionStateResponseMultiError, or nil if none found.
func (m *CreateActionStateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateActionStateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateActionStateResponseMultiError(errors)
	}

	return nil
}

// CreateActionStateResponseMultiError is an error wrapping multiple validation
// errors returned by CreateActionStateResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateActionStateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateActionStateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateActionStateResponseMultiError) AllErrors() []error { return m }

// CreateActionStateResponseValidationError is the validation error returned by
// CreateActionStateResponse.Validate if the designated constraints aren't met.
type CreateActionStateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateActionStateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateActionStateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateActionStateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateActionStateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateActionStateResponseValidationError) ErrorName() string {
	return "CreateActionStateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateActionStateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateActionStateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateActionStateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateActionStateResponseValidationError{}

// Validate checks the field values on UpdateActionStatesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateActionStatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateActionStatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateActionStatesRequestMultiError, or nil if none found.
func (m *UpdateActionStatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateActionStatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUpdates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateActionStatesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateActionStatesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateActionStatesRequestValidationError{
					field:  fmt.Sprintf("Updates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for StaffId

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return UpdateActionStatesRequestMultiError(errors)
	}

	return nil
}

// UpdateActionStatesRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateActionStatesRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateActionStatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateActionStatesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateActionStatesRequestMultiError) AllErrors() []error { return m }

// UpdateActionStatesRequestValidationError is the validation error returned by
// UpdateActionStatesRequest.Validate if the designated constraints aren't met.
type UpdateActionStatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateActionStatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateActionStatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateActionStatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateActionStatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateActionStatesRequestValidationError) ErrorName() string {
	return "UpdateActionStatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateActionStatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateActionStatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateActionStatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateActionStatesRequestValidationError{}

// Validate checks the field values on UpdateActionsStatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateActionsStatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateActionsStatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateActionsStatesResponseMultiError, or nil if none found.
func (m *UpdateActionsStatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateActionsStatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateActionsStatesResponseMultiError(errors)
	}

	return nil
}

// UpdateActionsStatesResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateActionsStatesResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateActionsStatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateActionsStatesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateActionsStatesResponseMultiError) AllErrors() []error { return m }

// UpdateActionsStatesResponseValidationError is the validation error returned
// by UpdateActionsStatesResponse.Validate if the designated constraints
// aren't met.
type UpdateActionsStatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateActionsStatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateActionsStatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateActionsStatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateActionsStatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateActionsStatesResponseValidationError) ErrorName() string {
	return "UpdateActionsStatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateActionsStatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateActionsStatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateActionsStatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateActionsStatesResponseValidationError{}

// Validate checks the field values on ListActionStatesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListActionStatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListActionStatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListActionStatesRequestMultiError, or nil if none found.
func (m *ListActionStatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListActionStatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return ListActionStatesRequestMultiError(errors)
	}

	return nil
}

// ListActionStatesRequestMultiError is an error wrapping multiple validation
// errors returned by ListActionStatesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListActionStatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListActionStatesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListActionStatesRequestMultiError) AllErrors() []error { return m }

// ListActionStatesRequestValidationError is the validation error returned by
// ListActionStatesRequest.Validate if the designated constraints aren't met.
type ListActionStatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListActionStatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListActionStatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListActionStatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListActionStatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListActionStatesRequestValidationError) ErrorName() string {
	return "ListActionStatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListActionStatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListActionStatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListActionStatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListActionStatesRequestValidationError{}

// Validate checks the field values on ListActionStatesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListActionStatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListActionStatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListActionStatesResponseMultiError, or nil if none found.
func (m *ListActionStatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListActionStatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetActionStates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListActionStatesResponseValidationError{
						field:  fmt.Sprintf("ActionStates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListActionStatesResponseValidationError{
						field:  fmt.Sprintf("ActionStates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListActionStatesResponseValidationError{
					field:  fmt.Sprintf("ActionStates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListActionStatesResponseMultiError(errors)
	}

	return nil
}

// ListActionStatesResponseMultiError is an error wrapping multiple validation
// errors returned by ListActionStatesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListActionStatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListActionStatesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListActionStatesResponseMultiError) AllErrors() []error { return m }

// ListActionStatesResponseValidationError is the validation error returned by
// ListActionStatesResponse.Validate if the designated constraints aren't met.
type ListActionStatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListActionStatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListActionStatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListActionStatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListActionStatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListActionStatesResponseValidationError) ErrorName() string {
	return "ListActionStatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListActionStatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListActionStatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListActionStatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListActionStatesResponseValidationError{}

// Validate checks the field values on DeleteActionStateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteActionStateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteActionStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteActionStateRequestMultiError, or nil if none found.
func (m *DeleteActionStateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteActionStateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteActionStateRequestMultiError(errors)
	}

	return nil
}

// DeleteActionStateRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteActionStateRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteActionStateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteActionStateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteActionStateRequestMultiError) AllErrors() []error { return m }

// DeleteActionStateRequestValidationError is the validation error returned by
// DeleteActionStateRequest.Validate if the designated constraints aren't met.
type DeleteActionStateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteActionStateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteActionStateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteActionStateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteActionStateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteActionStateRequestValidationError) ErrorName() string {
	return "DeleteActionStateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteActionStateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteActionStateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteActionStateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteActionStateRequestValidationError{}

// Validate checks the field values on DeleteActionStateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteActionStateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteActionStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteActionStateResponseMultiError, or nil if none found.
func (m *DeleteActionStateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteActionStateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteActionStateResponseMultiError(errors)
	}

	return nil
}

// DeleteActionStateResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteActionStateResponse.ValidateAll() if the
// designated constraints aren't met.
type DeleteActionStateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteActionStateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteActionStateResponseMultiError) AllErrors() []error { return m }

// DeleteActionStateResponseValidationError is the validation error returned by
// DeleteActionStateResponse.Validate if the designated constraints aren't met.
type DeleteActionStateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteActionStateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteActionStateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteActionStateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteActionStateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteActionStateResponseValidationError) ErrorName() string {
	return "DeleteActionStateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteActionStateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteActionStateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteActionStateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteActionStateResponseValidationError{}

// Validate checks the field values on CreateTagRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTagRequestMultiError, or nil if none found.
func (m *CreateTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	// no validation rules for Name

	// no validation rules for Sort

	if len(errors) > 0 {
		return CreateTagRequestMultiError(errors)
	}

	return nil
}

// CreateTagRequestMultiError is an error wrapping multiple validation errors
// returned by CreateTagRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTagRequestMultiError) AllErrors() []error { return m }

// CreateTagRequestValidationError is the validation error returned by
// CreateTagRequest.Validate if the designated constraints aren't met.
type CreateTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTagRequestValidationError) ErrorName() string { return "CreateTagRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTagRequestValidationError{}

// Validate checks the field values on CreateTagResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateTagResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTagResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTagResponseMultiError, or nil if none found.
func (m *CreateTagResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTagResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateTagResponseMultiError(errors)
	}

	return nil
}

// CreateTagResponseMultiError is an error wrapping multiple validation errors
// returned by CreateTagResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateTagResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTagResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTagResponseMultiError) AllErrors() []error { return m }

// CreateTagResponseValidationError is the validation error returned by
// CreateTagResponse.Validate if the designated constraints aren't met.
type CreateTagResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTagResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTagResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTagResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTagResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTagResponseValidationError) ErrorName() string {
	return "CreateTagResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTagResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTagResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTagResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTagResponseValidationError{}

// Validate checks the field values on UpdateTagsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateTagsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTagsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTagsRequestMultiError, or nil if none found.
func (m *UpdateTagsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTagsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUpdates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateTagsRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateTagsRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateTagsRequestValidationError{
					field:  fmt.Sprintf("Updates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for StaffId

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return UpdateTagsRequestMultiError(errors)
	}

	return nil
}

// UpdateTagsRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateTagsRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateTagsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTagsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTagsRequestMultiError) AllErrors() []error { return m }

// UpdateTagsRequestValidationError is the validation error returned by
// UpdateTagsRequest.Validate if the designated constraints aren't met.
type UpdateTagsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTagsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTagsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTagsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTagsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTagsRequestValidationError) ErrorName() string {
	return "UpdateTagsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTagsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTagsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTagsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTagsRequestValidationError{}

// Validate checks the field values on UpdateTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTagsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTagsResponseMultiError, or nil if none found.
func (m *UpdateTagsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTagsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateTagsResponseMultiError(errors)
	}

	return nil
}

// UpdateTagsResponseMultiError is an error wrapping multiple validation errors
// returned by UpdateTagsResponse.ValidateAll() if the designated constraints
// aren't met.
type UpdateTagsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTagsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTagsResponseMultiError) AllErrors() []error { return m }

// UpdateTagsResponseValidationError is the validation error returned by
// UpdateTagsResponse.Validate if the designated constraints aren't met.
type UpdateTagsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTagsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTagsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTagsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTagsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTagsResponseValidationError) ErrorName() string {
	return "UpdateTagsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTagsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTagsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTagsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTagsResponseValidationError{}

// Validate checks the field values on ListTagsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListTagsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTagsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTagsRequestMultiError, or nil if none found.
func (m *ListTagsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTagsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTagsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTagsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTagsRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListTagsRequestMultiError(errors)
	}

	return nil
}

// ListTagsRequestMultiError is an error wrapping multiple validation errors
// returned by ListTagsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListTagsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTagsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTagsRequestMultiError) AllErrors() []error { return m }

// ListTagsRequestValidationError is the validation error returned by
// ListTagsRequest.Validate if the designated constraints aren't met.
type ListTagsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTagsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTagsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTagsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTagsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTagsRequestValidationError) ErrorName() string { return "ListTagsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListTagsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTagsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTagsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTagsRequestValidationError{}

// Validate checks the field values on ListTagsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListTagsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTagsResponseMultiError, or nil if none found.
func (m *ListTagsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTagsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTagsResponseValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTagsResponseValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTagsResponseValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTagsResponseMultiError(errors)
	}

	return nil
}

// ListTagsResponseMultiError is an error wrapping multiple validation errors
// returned by ListTagsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListTagsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTagsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTagsResponseMultiError) AllErrors() []error { return m }

// ListTagsResponseValidationError is the validation error returned by
// ListTagsResponse.Validate if the designated constraints aren't met.
type ListTagsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTagsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTagsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTagsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTagsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTagsResponseValidationError) ErrorName() string { return "ListTagsResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListTagsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTagsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTagsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTagsResponseValidationError{}

// Validate checks the field values on DeleteTagRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTagRequestMultiError, or nil if none found.
func (m *DeleteTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteTagRequestMultiError(errors)
	}

	return nil
}

// DeleteTagRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteTagRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTagRequestMultiError) AllErrors() []error { return m }

// DeleteTagRequestValidationError is the validation error returned by
// DeleteTagRequest.Validate if the designated constraints aren't met.
type DeleteTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTagRequestValidationError) ErrorName() string { return "DeleteTagRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTagRequestValidationError{}

// Validate checks the field values on DeleteTagResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteTagResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTagResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTagResponseMultiError, or nil if none found.
func (m *DeleteTagResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTagResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteTagResponseMultiError(errors)
	}

	return nil
}

// DeleteTagResponseMultiError is an error wrapping multiple validation errors
// returned by DeleteTagResponse.ValidateAll() if the designated constraints
// aren't met.
type DeleteTagResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTagResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTagResponseMultiError) AllErrors() []error { return m }

// DeleteTagResponseValidationError is the validation error returned by
// DeleteTagResponse.Validate if the designated constraints aren't met.
type DeleteTagResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTagResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTagResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTagResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTagResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTagResponseValidationError) ErrorName() string {
	return "DeleteTagResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTagResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTagResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTagResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTagResponseValidationError{}

// Validate checks the field values on CoverCustomerTagRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CoverCustomerTagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CoverCustomerTagRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CoverCustomerTagRequestMultiError, or nil if none found.
func (m *CoverCustomerTagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CoverCustomerTagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for CustomerId

	// no validation rules for StaffId

	if len(errors) > 0 {
		return CoverCustomerTagRequestMultiError(errors)
	}

	return nil
}

// CoverCustomerTagRequestMultiError is an error wrapping multiple validation
// errors returned by CoverCustomerTagRequest.ValidateAll() if the designated
// constraints aren't met.
type CoverCustomerTagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CoverCustomerTagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CoverCustomerTagRequestMultiError) AllErrors() []error { return m }

// CoverCustomerTagRequestValidationError is the validation error returned by
// CoverCustomerTagRequest.Validate if the designated constraints aren't met.
type CoverCustomerTagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CoverCustomerTagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CoverCustomerTagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CoverCustomerTagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CoverCustomerTagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CoverCustomerTagRequestValidationError) ErrorName() string {
	return "CoverCustomerTagRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CoverCustomerTagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCoverCustomerTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CoverCustomerTagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CoverCustomerTagRequestValidationError{}

// Validate checks the field values on CoverCustomerTagResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CoverCustomerTagResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CoverCustomerTagResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CoverCustomerTagResponseMultiError, or nil if none found.
func (m *CoverCustomerTagResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CoverCustomerTagResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CoverCustomerTagResponseMultiError(errors)
	}

	return nil
}

// CoverCustomerTagResponseMultiError is an error wrapping multiple validation
// errors returned by CoverCustomerTagResponse.ValidateAll() if the designated
// constraints aren't met.
type CoverCustomerTagResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CoverCustomerTagResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CoverCustomerTagResponseMultiError) AllErrors() []error { return m }

// CoverCustomerTagResponseValidationError is the validation error returned by
// CoverCustomerTagResponse.Validate if the designated constraints aren't met.
type CoverCustomerTagResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CoverCustomerTagResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CoverCustomerTagResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CoverCustomerTagResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CoverCustomerTagResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CoverCustomerTagResponseValidationError) ErrorName() string {
	return "CoverCustomerTagResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CoverCustomerTagResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCoverCustomerTagResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CoverCustomerTagResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CoverCustomerTagResponseValidationError{}

// Validate checks the field values on CreateNoteRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateNoteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNoteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNoteRequestMultiError, or nil if none found.
func (m *CreateNoteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNoteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for CustomerId

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNoteRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNoteRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNoteRequestValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNoteRequestMultiError(errors)
	}

	return nil
}

// CreateNoteRequestMultiError is an error wrapping multiple validation errors
// returned by CreateNoteRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateNoteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNoteRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNoteRequestMultiError) AllErrors() []error { return m }

// CreateNoteRequestValidationError is the validation error returned by
// CreateNoteRequest.Validate if the designated constraints aren't met.
type CreateNoteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNoteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNoteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNoteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNoteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNoteRequestValidationError) ErrorName() string {
	return "CreateNoteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNoteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNoteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNoteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNoteRequestValidationError{}

// Validate checks the field values on CreateNoteResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNoteResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNoteResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNoteResponseMultiError, or nil if none found.
func (m *CreateNoteResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNoteResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateNoteResponseMultiError(errors)
	}

	return nil
}

// CreateNoteResponseMultiError is an error wrapping multiple validation errors
// returned by CreateNoteResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateNoteResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNoteResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNoteResponseMultiError) AllErrors() []error { return m }

// CreateNoteResponseValidationError is the validation error returned by
// CreateNoteResponse.Validate if the designated constraints aren't met.
type CreateNoteResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNoteResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNoteResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNoteResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNoteResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNoteResponseValidationError) ErrorName() string {
	return "CreateNoteResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNoteResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNoteResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNoteResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNoteResponseValidationError{}

// Validate checks the field values on UpdateNotesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNotesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNotesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateNotesRequestMultiError, or nil if none found.
func (m *UpdateNotesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNotesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNotesRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNotesRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNotesRequestValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Text != nil {
		// no validation rules for Text
	}

	if len(errors) > 0 {
		return UpdateNotesRequestMultiError(errors)
	}

	return nil
}

// UpdateNotesRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateNotesRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateNotesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNotesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNotesRequestMultiError) AllErrors() []error { return m }

// UpdateNotesRequestValidationError is the validation error returned by
// UpdateNotesRequest.Validate if the designated constraints aren't met.
type UpdateNotesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNotesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNotesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNotesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNotesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNotesRequestValidationError) ErrorName() string {
	return "UpdateNotesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNotesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNotesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNotesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNotesRequestValidationError{}

// Validate checks the field values on UpdateNotesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNotesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNotesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateNotesResponseMultiError, or nil if none found.
func (m *UpdateNotesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNotesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateNotesResponseMultiError(errors)
	}

	return nil
}

// UpdateNotesResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateNotesResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateNotesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNotesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNotesResponseMultiError) AllErrors() []error { return m }

// UpdateNotesResponseValidationError is the validation error returned by
// UpdateNotesResponse.Validate if the designated constraints aren't met.
type UpdateNotesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNotesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNotesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNotesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNotesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNotesResponseValidationError) ErrorName() string {
	return "UpdateNotesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNotesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNotesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNotesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNotesResponseValidationError{}

// Validate checks the field values on ListNotesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListNotesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNotesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNotesRequestMultiError, or nil if none found.
func (m *ListNotesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNotesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListNotesRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListNotesRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListNotesRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListNotesRequestMultiError(errors)
	}

	return nil
}

// ListNotesRequestMultiError is an error wrapping multiple validation errors
// returned by ListNotesRequest.ValidateAll() if the designated constraints
// aren't met.
type ListNotesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNotesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNotesRequestMultiError) AllErrors() []error { return m }

// ListNotesRequestValidationError is the validation error returned by
// ListNotesRequest.Validate if the designated constraints aren't met.
type ListNotesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNotesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNotesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNotesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNotesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNotesRequestValidationError) ErrorName() string { return "ListNotesRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListNotesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNotesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNotesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNotesRequestValidationError{}

// Validate checks the field values on ListNotesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListNotesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNotesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNotesResponseMultiError, or nil if none found.
func (m *ListNotesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNotesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNotes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNotesResponseValidationError{
						field:  fmt.Sprintf("Notes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNotesResponseValidationError{
						field:  fmt.Sprintf("Notes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNotesResponseValidationError{
					field:  fmt.Sprintf("Notes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListNotesResponseMultiError(errors)
	}

	return nil
}

// ListNotesResponseMultiError is an error wrapping multiple validation errors
// returned by ListNotesResponse.ValidateAll() if the designated constraints
// aren't met.
type ListNotesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNotesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNotesResponseMultiError) AllErrors() []error { return m }

// ListNotesResponseValidationError is the validation error returned by
// ListNotesResponse.Validate if the designated constraints aren't met.
type ListNotesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNotesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNotesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNotesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNotesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNotesResponseValidationError) ErrorName() string {
	return "ListNotesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListNotesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNotesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNotesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNotesResponseValidationError{}

// Validate checks the field values on DeleteNoteRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteNoteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteNoteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteNoteRequestMultiError, or nil if none found.
func (m *DeleteNoteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteNoteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteNoteRequestMultiError(errors)
	}

	return nil
}

// DeleteNoteRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteNoteRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteNoteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteNoteRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteNoteRequestMultiError) AllErrors() []error { return m }

// DeleteNoteRequestValidationError is the validation error returned by
// DeleteNoteRequest.Validate if the designated constraints aren't met.
type DeleteNoteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteNoteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteNoteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteNoteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteNoteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteNoteRequestValidationError) ErrorName() string {
	return "DeleteNoteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteNoteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteNoteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteNoteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteNoteRequestValidationError{}

// Validate checks the field values on DeleteNoteResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteNoteResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteNoteResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteNoteResponseMultiError, or nil if none found.
func (m *DeleteNoteResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteNoteResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteNoteResponseMultiError(errors)
	}

	return nil
}

// DeleteNoteResponseMultiError is an error wrapping multiple validation errors
// returned by DeleteNoteResponse.ValidateAll() if the designated constraints
// aren't met.
type DeleteNoteResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteNoteResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteNoteResponseMultiError) AllErrors() []error { return m }

// DeleteNoteResponseValidationError is the validation error returned by
// DeleteNoteResponse.Validate if the designated constraints aren't met.
type DeleteNoteResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteNoteResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteNoteResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteNoteResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteNoteResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteNoteResponseValidationError) ErrorName() string {
	return "DeleteNoteResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteNoteResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteNoteResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteNoteResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteNoteResponseValidationError{}

// Validate checks the field values on CreateSourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSourceRequestMultiError, or nil if none found.
func (m *CreateSourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	// no validation rules for Name

	// no validation rules for Sort

	if len(errors) > 0 {
		return CreateSourceRequestMultiError(errors)
	}

	return nil
}

// CreateSourceRequestMultiError is an error wrapping multiple validation
// errors returned by CreateSourceRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateSourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSourceRequestMultiError) AllErrors() []error { return m }

// CreateSourceRequestValidationError is the validation error returned by
// CreateSourceRequest.Validate if the designated constraints aren't met.
type CreateSourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSourceRequestValidationError) ErrorName() string {
	return "CreateSourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSourceRequestValidationError{}

// Validate checks the field values on CreateSourceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSourceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSourceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSourceResponseMultiError, or nil if none found.
func (m *CreateSourceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSourceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateSourceResponseMultiError(errors)
	}

	return nil
}

// CreateSourceResponseMultiError is an error wrapping multiple validation
// errors returned by CreateSourceResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateSourceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSourceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSourceResponseMultiError) AllErrors() []error { return m }

// CreateSourceResponseValidationError is the validation error returned by
// CreateSourceResponse.Validate if the designated constraints aren't met.
type CreateSourceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSourceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSourceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSourceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSourceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSourceResponseValidationError) ErrorName() string {
	return "CreateSourceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSourceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSourceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSourceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSourceResponseValidationError{}

// Validate checks the field values on UpdateSourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSourcesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSourcesRequestMultiError, or nil if none found.
func (m *UpdateSourcesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSourcesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUpdates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateSourcesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateSourcesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateSourcesRequestValidationError{
					field:  fmt.Sprintf("Updates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for StaffId

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return UpdateSourcesRequestMultiError(errors)
	}

	return nil
}

// UpdateSourcesRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateSourcesRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateSourcesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSourcesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSourcesRequestMultiError) AllErrors() []error { return m }

// UpdateSourcesRequestValidationError is the validation error returned by
// UpdateSourcesRequest.Validate if the designated constraints aren't met.
type UpdateSourcesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSourcesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSourcesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSourcesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSourcesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSourcesRequestValidationError) ErrorName() string {
	return "UpdateSourcesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSourcesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSourcesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSourcesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSourcesRequestValidationError{}

// Validate checks the field values on UpdateSourcesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSourcesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSourcesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSourcesResponseMultiError, or nil if none found.
func (m *UpdateSourcesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSourcesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateSourcesResponseMultiError(errors)
	}

	return nil
}

// UpdateSourcesResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateSourcesResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateSourcesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSourcesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSourcesResponseMultiError) AllErrors() []error { return m }

// UpdateSourcesResponseValidationError is the validation error returned by
// UpdateSourcesResponse.Validate if the designated constraints aren't met.
type UpdateSourcesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSourcesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSourcesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSourcesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSourcesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSourcesResponseValidationError) ErrorName() string {
	return "UpdateSourcesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSourcesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSourcesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSourcesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSourcesResponseValidationError{}

// Validate checks the field values on ListSourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSourcesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSourcesRequestMultiError, or nil if none found.
func (m *ListSourcesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSourcesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListSourcesRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListSourcesRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListSourcesRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListSourcesRequestMultiError(errors)
	}

	return nil
}

// ListSourcesRequestMultiError is an error wrapping multiple validation errors
// returned by ListSourcesRequest.ValidateAll() if the designated constraints
// aren't met.
type ListSourcesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSourcesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSourcesRequestMultiError) AllErrors() []error { return m }

// ListSourcesRequestValidationError is the validation error returned by
// ListSourcesRequest.Validate if the designated constraints aren't met.
type ListSourcesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSourcesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSourcesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSourcesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSourcesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSourcesRequestValidationError) ErrorName() string {
	return "ListSourcesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSourcesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSourcesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSourcesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSourcesRequestValidationError{}

// Validate checks the field values on ListSourcesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSourcesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSourcesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSourcesResponseMultiError, or nil if none found.
func (m *ListSourcesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSourcesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSourcesResponseValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSourcesResponseValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSourcesResponseValidationError{
					field:  fmt.Sprintf("Sources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListSourcesResponseMultiError(errors)
	}

	return nil
}

// ListSourcesResponseMultiError is an error wrapping multiple validation
// errors returned by ListSourcesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListSourcesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSourcesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSourcesResponseMultiError) AllErrors() []error { return m }

// ListSourcesResponseValidationError is the validation error returned by
// ListSourcesResponse.Validate if the designated constraints aren't met.
type ListSourcesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSourcesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSourcesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSourcesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSourcesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSourcesResponseValidationError) ErrorName() string {
	return "ListSourcesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListSourcesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSourcesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSourcesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSourcesResponseValidationError{}

// Validate checks the field values on DeleteSourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSourceRequestMultiError, or nil if none found.
func (m *DeleteSourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteSourceRequestMultiError(errors)
	}

	return nil
}

// DeleteSourceRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteSourceRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteSourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSourceRequestMultiError) AllErrors() []error { return m }

// DeleteSourceRequestValidationError is the validation error returned by
// DeleteSourceRequest.Validate if the designated constraints aren't met.
type DeleteSourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSourceRequestValidationError) ErrorName() string {
	return "DeleteSourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSourceRequestValidationError{}

// Validate checks the field values on DeleteSourceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSourceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSourceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSourceResponseMultiError, or nil if none found.
func (m *DeleteSourceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSourceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteSourceResponseMultiError(errors)
	}

	return nil
}

// DeleteSourceResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteSourceResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteSourceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSourceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSourceResponseMultiError) AllErrors() []error { return m }

// DeleteSourceResponseValidationError is the validation error returned by
// DeleteSourceResponse.Validate if the designated constraints aren't met.
type DeleteSourceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSourceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSourceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSourceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSourceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSourceResponseValidationError) ErrorName() string {
	return "DeleteSourceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSourceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSourceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSourceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSourceResponseValidationError{}

// Validate checks the field values on ListCustomerActivityLogsRequest_Filter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListCustomerActivityLogsRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListCustomerActivityLogsRequest_Filter with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ListCustomerActivityLogsRequest_FilterMultiError, or nil if none found.
func (m *ListCustomerActivityLogsRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerActivityLogsRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CustomerId != nil {
		// no validation rules for CustomerId
	}

	if m.Type != nil {
		// no validation rules for Type
	}

	if m.CompanyId != nil {
		// no validation rules for CompanyId
	}

	if len(errors) > 0 {
		return ListCustomerActivityLogsRequest_FilterMultiError(errors)
	}

	return nil
}

// ListCustomerActivityLogsRequest_FilterMultiError is an error wrapping
// multiple validation errors returned by
// ListCustomerActivityLogsRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type ListCustomerActivityLogsRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerActivityLogsRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerActivityLogsRequest_FilterMultiError) AllErrors() []error { return m }

// ListCustomerActivityLogsRequest_FilterValidationError is the validation
// error returned by ListCustomerActivityLogsRequest_Filter.Validate if the
// designated constraints aren't met.
type ListCustomerActivityLogsRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerActivityLogsRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerActivityLogsRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerActivityLogsRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerActivityLogsRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerActivityLogsRequest_FilterValidationError) ErrorName() string {
	return "ListCustomerActivityLogsRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerActivityLogsRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerActivityLogsRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerActivityLogsRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerActivityLogsRequest_FilterValidationError{}

// Validate checks the field values on UpdateLifeCyclesRequest_UpdateLifeCycle
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateLifeCyclesRequest_UpdateLifeCycle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateLifeCyclesRequest_UpdateLifeCycle with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateLifeCyclesRequest_UpdateLifeCycleMultiError, or nil if none found.
func (m *UpdateLifeCyclesRequest_UpdateLifeCycle) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLifeCyclesRequest_UpdateLifeCycle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Sort != nil {
		// no validation rules for Sort
	}

	if len(errors) > 0 {
		return UpdateLifeCyclesRequest_UpdateLifeCycleMultiError(errors)
	}

	return nil
}

// UpdateLifeCyclesRequest_UpdateLifeCycleMultiError is an error wrapping
// multiple validation errors returned by
// UpdateLifeCyclesRequest_UpdateLifeCycle.ValidateAll() if the designated
// constraints aren't met.
type UpdateLifeCyclesRequest_UpdateLifeCycleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLifeCyclesRequest_UpdateLifeCycleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLifeCyclesRequest_UpdateLifeCycleMultiError) AllErrors() []error { return m }

// UpdateLifeCyclesRequest_UpdateLifeCycleValidationError is the validation
// error returned by UpdateLifeCyclesRequest_UpdateLifeCycle.Validate if the
// designated constraints aren't met.
type UpdateLifeCyclesRequest_UpdateLifeCycleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) ErrorName() string {
	return "UpdateLifeCyclesRequest_UpdateLifeCycleValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLifeCyclesRequest_UpdateLifeCycle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLifeCyclesRequest_UpdateLifeCycleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLifeCyclesRequest_UpdateLifeCycleValidationError{}

// Validate checks the field values on
// UpdateActionStatesRequest_UpdateActionState with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateActionStatesRequest_UpdateActionState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateActionStatesRequest_UpdateActionState with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateActionStatesRequest_UpdateActionStateMultiError, or nil if none found.
func (m *UpdateActionStatesRequest_UpdateActionState) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateActionStatesRequest_UpdateActionState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Sort != nil {
		// no validation rules for Sort
	}

	if m.Color != nil {
		// no validation rules for Color
	}

	if len(errors) > 0 {
		return UpdateActionStatesRequest_UpdateActionStateMultiError(errors)
	}

	return nil
}

// UpdateActionStatesRequest_UpdateActionStateMultiError is an error wrapping
// multiple validation errors returned by
// UpdateActionStatesRequest_UpdateActionState.ValidateAll() if the designated
// constraints aren't met.
type UpdateActionStatesRequest_UpdateActionStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateActionStatesRequest_UpdateActionStateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateActionStatesRequest_UpdateActionStateMultiError) AllErrors() []error { return m }

// UpdateActionStatesRequest_UpdateActionStateValidationError is the validation
// error returned by UpdateActionStatesRequest_UpdateActionState.Validate if
// the designated constraints aren't met.
type UpdateActionStatesRequest_UpdateActionStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) ErrorName() string {
	return "UpdateActionStatesRequest_UpdateActionStateValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateActionStatesRequest_UpdateActionState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateActionStatesRequest_UpdateActionStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateActionStatesRequest_UpdateActionStateValidationError{}

// Validate checks the field values on UpdateTagsRequest_UpdateTag with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTagsRequest_UpdateTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTagsRequest_UpdateTag with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTagsRequest_UpdateTagMultiError, or nil if none found.
func (m *UpdateTagsRequest_UpdateTag) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTagsRequest_UpdateTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Sort != nil {
		// no validation rules for Sort
	}

	if len(errors) > 0 {
		return UpdateTagsRequest_UpdateTagMultiError(errors)
	}

	return nil
}

// UpdateTagsRequest_UpdateTagMultiError is an error wrapping multiple
// validation errors returned by UpdateTagsRequest_UpdateTag.ValidateAll() if
// the designated constraints aren't met.
type UpdateTagsRequest_UpdateTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTagsRequest_UpdateTagMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTagsRequest_UpdateTagMultiError) AllErrors() []error { return m }

// UpdateTagsRequest_UpdateTagValidationError is the validation error returned
// by UpdateTagsRequest_UpdateTag.Validate if the designated constraints
// aren't met.
type UpdateTagsRequest_UpdateTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTagsRequest_UpdateTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTagsRequest_UpdateTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTagsRequest_UpdateTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTagsRequest_UpdateTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTagsRequest_UpdateTagValidationError) ErrorName() string {
	return "UpdateTagsRequest_UpdateTagValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTagsRequest_UpdateTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTagsRequest_UpdateTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTagsRequest_UpdateTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTagsRequest_UpdateTagValidationError{}

// Validate checks the field values on ListTagsRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTagsRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTagsRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTagsRequest_FilterMultiError, or nil if none found.
func (m *ListTagsRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTagsRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CompanyId != nil {
		// no validation rules for CompanyId
	}

	if m.CustomerId != nil {
		// no validation rules for CustomerId
	}

	if len(errors) > 0 {
		return ListTagsRequest_FilterMultiError(errors)
	}

	return nil
}

// ListTagsRequest_FilterMultiError is an error wrapping multiple validation
// errors returned by ListTagsRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type ListTagsRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTagsRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTagsRequest_FilterMultiError) AllErrors() []error { return m }

// ListTagsRequest_FilterValidationError is the validation error returned by
// ListTagsRequest_Filter.Validate if the designated constraints aren't met.
type ListTagsRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTagsRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTagsRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTagsRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTagsRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTagsRequest_FilterValidationError) ErrorName() string {
	return "ListTagsRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListTagsRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTagsRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTagsRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTagsRequest_FilterValidationError{}

// Validate checks the field values on ListNotesRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListNotesRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNotesRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNotesRequest_FilterMultiError, or nil if none found.
func (m *ListNotesRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNotesRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CustomerId != nil {
		// no validation rules for CustomerId
	}

	if len(errors) > 0 {
		return ListNotesRequest_FilterMultiError(errors)
	}

	return nil
}

// ListNotesRequest_FilterMultiError is an error wrapping multiple validation
// errors returned by ListNotesRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type ListNotesRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNotesRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNotesRequest_FilterMultiError) AllErrors() []error { return m }

// ListNotesRequest_FilterValidationError is the validation error returned by
// ListNotesRequest_Filter.Validate if the designated constraints aren't met.
type ListNotesRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNotesRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNotesRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNotesRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNotesRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNotesRequest_FilterValidationError) ErrorName() string {
	return "ListNotesRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListNotesRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNotesRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNotesRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNotesRequest_FilterValidationError{}

// Validate checks the field values on UpdateSourcesRequest_UpdateSource with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateSourcesRequest_UpdateSource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSourcesRequest_UpdateSource
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateSourcesRequest_UpdateSourceMultiError, or nil if none found.
func (m *UpdateSourcesRequest_UpdateSource) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSourcesRequest_UpdateSource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Sort != nil {
		// no validation rules for Sort
	}

	if len(errors) > 0 {
		return UpdateSourcesRequest_UpdateSourceMultiError(errors)
	}

	return nil
}

// UpdateSourcesRequest_UpdateSourceMultiError is an error wrapping multiple
// validation errors returned by
// UpdateSourcesRequest_UpdateSource.ValidateAll() if the designated
// constraints aren't met.
type UpdateSourcesRequest_UpdateSourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSourcesRequest_UpdateSourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSourcesRequest_UpdateSourceMultiError) AllErrors() []error { return m }

// UpdateSourcesRequest_UpdateSourceValidationError is the validation error
// returned by UpdateSourcesRequest_UpdateSource.Validate if the designated
// constraints aren't met.
type UpdateSourcesRequest_UpdateSourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSourcesRequest_UpdateSourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSourcesRequest_UpdateSourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSourcesRequest_UpdateSourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSourcesRequest_UpdateSourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSourcesRequest_UpdateSourceValidationError) ErrorName() string {
	return "UpdateSourcesRequest_UpdateSourceValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSourcesRequest_UpdateSourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSourcesRequest_UpdateSource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSourcesRequest_UpdateSourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSourcesRequest_UpdateSourceValidationError{}

// Validate checks the field values on ListSourcesRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSourcesRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSourcesRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSourcesRequest_FilterMultiError, or nil if none found.
func (m *ListSourcesRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSourcesRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CompanyId != nil {
		// no validation rules for CompanyId
	}

	if m.CustomerId != nil {
		// no validation rules for CustomerId
	}

	if len(errors) > 0 {
		return ListSourcesRequest_FilterMultiError(errors)
	}

	return nil
}

// ListSourcesRequest_FilterMultiError is an error wrapping multiple validation
// errors returned by ListSourcesRequest_Filter.ValidateAll() if the
// designated constraints aren't met.
type ListSourcesRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSourcesRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSourcesRequest_FilterMultiError) AllErrors() []error { return m }

// ListSourcesRequest_FilterValidationError is the validation error returned by
// ListSourcesRequest_Filter.Validate if the designated constraints aren't met.
type ListSourcesRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSourcesRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSourcesRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSourcesRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSourcesRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSourcesRequest_FilterValidationError) ErrorName() string {
	return "ListSourcesRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListSourcesRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSourcesRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSourcesRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSourcesRequest_FilterValidationError{}
