syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 分页信息
message PaginationRef {
  // 偏移量，默认0
  int32 offset = 1;
  // 每页数量，默认200
  int32 limit = 2;
}
  
// 排序方式
enum SortType {
  // SORT_TYPE_UNSPECIFIED 默认排序类型
  SORT_TYPE_UNSPECIFIED = 0;
  // SORT_TYPE_START_TIME 按服务开始时间
  SORT_TYPE_START_TIME = 1;
}

// 日期类型
enum DateType {
  // 未指定日期类型
  DATE_TYPE_UNSPECIFIED = 0;
  // 每日日期
  DATE_TYPE_DATE_EVERYDAY = 1;
  // 特定日期
  DATE_TYPE_SPECIFIC_DATE = 2;
  // 日期点
  DATE_TYPE_DATE_POINT = 3;
  // 每日包含退房日
  DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY = 4;
  // 每日排除入住日
  DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY = 5;
  // 最后一天
  DATE_TYPE_LAST_DAY = 6;
  // 第一天
  DATE_TYPE_FIRST_DAY = 7;
}

// 服务类型
enum CareType {
    // 未知护理类型
    CARE_TYPE_UNSPECIFIED = 0;
    // GROOMING类型
    CARE_TYPE_GROOMING = 1;
    // BOARDING类型
    CARE_TYPE_BOARDING = 2;
    // DAYCARE类型
    CARE_TYPE_DAYCARE = 3;
    // EVALUATION类型
    CARE_TYPE_EVALUATION = 4;
    // DOG_WALKING类型
    CARE_TYPE_DOG_WALKING = 5;
    // GROUP_CLASS类型
    CARE_TYPE_GROUP_CLASS = 6;
}

// 服务类型枚举
enum ServiceType {
  // 未指定的服务类型
  SERVICE_TYPE_UNSPECIFIED = 0;
  // 服务类型
  SERVICE_TYPE_SERVICE = 1;
  // 选项类型
  SERVICE_TYPE_OPTION = 2;
}

