// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/fulfillment/v1/appointment_service.proto

package fulfillmentpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetAppointmentByIDsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAppointmentByIDsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAppointmentByIDsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAppointmentByIDsRequestMultiError, or nil if none found.
func (m *GetAppointmentByIDsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAppointmentByIDsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAppointmentByIDsRequestMultiError(errors)
	}

	return nil
}

// GetAppointmentByIDsRequestMultiError is an error wrapping multiple
// validation errors returned by GetAppointmentByIDsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAppointmentByIDsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAppointmentByIDsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAppointmentByIDsRequestMultiError) AllErrors() []error { return m }

// GetAppointmentByIDsRequestValidationError is the validation error returned
// by GetAppointmentByIDsRequest.Validate if the designated constraints aren't met.
type GetAppointmentByIDsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAppointmentByIDsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAppointmentByIDsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAppointmentByIDsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAppointmentByIDsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAppointmentByIDsRequestValidationError) ErrorName() string {
	return "GetAppointmentByIDsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAppointmentByIDsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAppointmentByIDsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAppointmentByIDsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAppointmentByIDsRequestValidationError{}

// Validate checks the field values on GetAppointmentByIDsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAppointmentByIDsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAppointmentByIDsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAppointmentByIDsResponseMultiError, or nil if none found.
func (m *GetAppointmentByIDsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAppointmentByIDsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAppointments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAppointmentByIDsResponseValidationError{
						field:  fmt.Sprintf("Appointments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAppointmentByIDsResponseValidationError{
						field:  fmt.Sprintf("Appointments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAppointmentByIDsResponseValidationError{
					field:  fmt.Sprintf("Appointments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAppointmentByIDsResponseMultiError(errors)
	}

	return nil
}

// GetAppointmentByIDsResponseMultiError is an error wrapping multiple
// validation errors returned by GetAppointmentByIDsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAppointmentByIDsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAppointmentByIDsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAppointmentByIDsResponseMultiError) AllErrors() []error { return m }

// GetAppointmentByIDsResponseValidationError is the validation error returned
// by GetAppointmentByIDsResponse.Validate if the designated constraints
// aren't met.
type GetAppointmentByIDsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAppointmentByIDsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAppointmentByIDsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAppointmentByIDsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAppointmentByIDsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAppointmentByIDsResponseValidationError) ErrorName() string {
	return "GetAppointmentByIDsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAppointmentByIDsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAppointmentByIDsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAppointmentByIDsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAppointmentByIDsResponseValidationError{}

// Validate checks the field values on ListAppointmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAppointmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAppointmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAppointmentRequestMultiError, or nil if none found.
func (m *ListAppointmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAppointmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := ListAppointmentRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := ListAppointmentRequestValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAppointmentRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAppointmentRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAppointmentRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAppointmentRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAppointmentRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAppointmentRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAppointmentRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAppointmentRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAppointmentRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortType

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAppointmentRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAppointmentRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAppointmentRequestValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAppointmentRequestMultiError(errors)
	}

	return nil
}

// ListAppointmentRequestMultiError is an error wrapping multiple validation
// errors returned by ListAppointmentRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAppointmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAppointmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAppointmentRequestMultiError) AllErrors() []error { return m }

// ListAppointmentRequestValidationError is the validation error returned by
// ListAppointmentRequest.Validate if the designated constraints aren't met.
type ListAppointmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAppointmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAppointmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAppointmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAppointmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAppointmentRequestValidationError) ErrorName() string {
	return "ListAppointmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAppointmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAppointmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAppointmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAppointmentRequestValidationError{}

// Validate checks the field values on ListAppointmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAppointmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAppointmentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAppointmentResponseMultiError, or nil if none found.
func (m *ListAppointmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAppointmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAppointments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAppointmentResponseValidationError{
						field:  fmt.Sprintf("Appointments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAppointmentResponseValidationError{
						field:  fmt.Sprintf("Appointments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAppointmentResponseValidationError{
					field:  fmt.Sprintf("Appointments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAppointmentResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAppointmentResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAppointmentResponseValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEnd

	// no validation rules for Total

	if len(errors) > 0 {
		return ListAppointmentResponseMultiError(errors)
	}

	return nil
}

// ListAppointmentResponseMultiError is an error wrapping multiple validation
// errors returned by ListAppointmentResponse.ValidateAll() if the designated
// constraints aren't met.
type ListAppointmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAppointmentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAppointmentResponseMultiError) AllErrors() []error { return m }

// ListAppointmentResponseValidationError is the validation error returned by
// ListAppointmentResponse.Validate if the designated constraints aren't met.
type ListAppointmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAppointmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAppointmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAppointmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAppointmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAppointmentResponseValidationError) ErrorName() string {
	return "ListAppointmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAppointmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAppointmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAppointmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAppointmentResponseValidationError{}

// Validate checks the field values on CreateAppointmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAppointmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAppointmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAppointmentRequestMultiError, or nil if none found.
func (m *CreateAppointmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAppointmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BusinessId

	// no validation rules for CompanyId

	// no validation rules for CustomerId

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAppointmentRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAppointmentRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAppointmentRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAppointmentRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAppointmentRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAppointmentRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateAppointmentRequestValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateAppointmentRequestValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateAppointmentRequestValidationError{
					field:  fmt.Sprintf("Pets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ColorCode

	// no validation rules for Source

	if len(errors) > 0 {
		return CreateAppointmentRequestMultiError(errors)
	}

	return nil
}

// CreateAppointmentRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAppointmentRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAppointmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAppointmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAppointmentRequestMultiError) AllErrors() []error { return m }

// CreateAppointmentRequestValidationError is the validation error returned by
// CreateAppointmentRequest.Validate if the designated constraints aren't met.
type CreateAppointmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAppointmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAppointmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAppointmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAppointmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAppointmentRequestValidationError) ErrorName() string {
	return "CreateAppointmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAppointmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAppointmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAppointmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAppointmentRequestValidationError{}

// Validate checks the field values on CreateAppointmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAppointmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAppointmentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAppointmentResponseMultiError, or nil if none found.
func (m *CreateAppointmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAppointmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetAppointment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAppointmentResponseValidationError{
					field:  "Appointment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAppointmentResponseValidationError{
					field:  "Appointment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAppointment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAppointmentResponseValidationError{
				field:  "Appointment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAppointmentResponseMultiError(errors)
	}

	return nil
}

// CreateAppointmentResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAppointmentResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateAppointmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAppointmentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAppointmentResponseMultiError) AllErrors() []error { return m }

// CreateAppointmentResponseValidationError is the validation error returned by
// CreateAppointmentResponse.Validate if the designated constraints aren't met.
type CreateAppointmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAppointmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAppointmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAppointmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAppointmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAppointmentResponseValidationError) ErrorName() string {
	return "CreateAppointmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAppointmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAppointmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAppointmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAppointmentResponseValidationError{}

// Validate checks the field values on UpdateAppointmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAppointmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAppointmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAppointmentRequestMultiError, or nil if none found.
func (m *UpdateAppointmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAppointmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppointmentId

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	if all {
		switch v := interface{}(m.GetAppointmentOperations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAppointmentRequestValidationError{
					field:  "AppointmentOperations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAppointmentRequestValidationError{
					field:  "AppointmentOperations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAppointmentOperations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAppointmentRequestValidationError{
				field:  "AppointmentOperations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetServiceOperations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAppointmentRequestValidationError{
						field:  fmt.Sprintf("ServiceOperations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAppointmentRequestValidationError{
						field:  fmt.Sprintf("ServiceOperations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAppointmentRequestValidationError{
					field:  fmt.Sprintf("ServiceOperations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOptionOperations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAppointmentRequestValidationError{
						field:  fmt.Sprintf("OptionOperations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAppointmentRequestValidationError{
						field:  fmt.Sprintf("OptionOperations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAppointmentRequestValidationError{
					field:  fmt.Sprintf("OptionOperations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateAppointmentRequestMultiError(errors)
	}

	return nil
}

// UpdateAppointmentRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAppointmentRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAppointmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAppointmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAppointmentRequestMultiError) AllErrors() []error { return m }

// UpdateAppointmentRequestValidationError is the validation error returned by
// UpdateAppointmentRequest.Validate if the designated constraints aren't met.
type UpdateAppointmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAppointmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAppointmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAppointmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAppointmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAppointmentRequestValidationError) ErrorName() string {
	return "UpdateAppointmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAppointmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAppointmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAppointmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAppointmentRequestValidationError{}

// Validate checks the field values on UpdateAppointmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAppointmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAppointmentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAppointmentResponseMultiError, or nil if none found.
func (m *UpdateAppointmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAppointmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	// no validation rules for Message

	if len(errors) > 0 {
		return UpdateAppointmentResponseMultiError(errors)
	}

	return nil
}

// UpdateAppointmentResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateAppointmentResponse.ValidateAll() if the
// designated constraints aren't met.
type UpdateAppointmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAppointmentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAppointmentResponseMultiError) AllErrors() []error { return m }

// UpdateAppointmentResponseValidationError is the validation error returned by
// UpdateAppointmentResponse.Validate if the designated constraints aren't met.
type UpdateAppointmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAppointmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAppointmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAppointmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAppointmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAppointmentResponseValidationError) ErrorName() string {
	return "UpdateAppointmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAppointmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAppointmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAppointmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAppointmentResponseValidationError{}

// Validate checks the field values on
// UpdateAppointmentRequest_AppointmentOperation with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateAppointmentRequest_AppointmentOperation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateAppointmentRequest_AppointmentOperation with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateAppointmentRequest_AppointmentOperationMultiError, or nil if none found.
func (m *UpdateAppointmentRequest_AppointmentOperation) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAppointmentRequest_AppointmentOperation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.StartTime != nil {

		if all {
			switch v := interface{}(m.GetStartTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAppointmentRequest_AppointmentOperationValidationError{
						field:  "StartTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAppointmentRequest_AppointmentOperationValidationError{
						field:  "StartTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAppointmentRequest_AppointmentOperationValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.EndTime != nil {

		if all {
			switch v := interface{}(m.GetEndTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAppointmentRequest_AppointmentOperationValidationError{
						field:  "EndTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAppointmentRequest_AppointmentOperationValidationError{
						field:  "EndTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAppointmentRequest_AppointmentOperationValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ColorCode != nil {
		// no validation rules for ColorCode
	}

	if m.NewStatus != nil {
		// no validation rules for NewStatus
	}

	if len(errors) > 0 {
		return UpdateAppointmentRequest_AppointmentOperationMultiError(errors)
	}

	return nil
}

// UpdateAppointmentRequest_AppointmentOperationMultiError is an error wrapping
// multiple validation errors returned by
// UpdateAppointmentRequest_AppointmentOperation.ValidateAll() if the
// designated constraints aren't met.
type UpdateAppointmentRequest_AppointmentOperationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAppointmentRequest_AppointmentOperationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAppointmentRequest_AppointmentOperationMultiError) AllErrors() []error { return m }

// UpdateAppointmentRequest_AppointmentOperationValidationError is the
// validation error returned by
// UpdateAppointmentRequest_AppointmentOperation.Validate if the designated
// constraints aren't met.
type UpdateAppointmentRequest_AppointmentOperationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAppointmentRequest_AppointmentOperationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAppointmentRequest_AppointmentOperationValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpdateAppointmentRequest_AppointmentOperationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAppointmentRequest_AppointmentOperationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAppointmentRequest_AppointmentOperationValidationError) ErrorName() string {
	return "UpdateAppointmentRequest_AppointmentOperationValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAppointmentRequest_AppointmentOperationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAppointmentRequest_AppointmentOperation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAppointmentRequest_AppointmentOperationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAppointmentRequest_AppointmentOperationValidationError{}

// Validate checks the field values on
// UpdateAppointmentRequest_ServiceOperation with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateAppointmentRequest_ServiceOperation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateAppointmentRequest_ServiceOperation with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateAppointmentRequest_ServiceOperationMultiError, or nil if none found.
func (m *UpdateAppointmentRequest_ServiceOperation) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAppointmentRequest_ServiceOperation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OperationMode

	// no validation rules for ServiceInstanceId

	// no validation rules for ServiceTemplateId

	// no validation rules for ParentServiceInstanceId

	// no validation rules for DateType

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAppointmentRequest_ServiceOperationValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAppointmentRequest_ServiceOperationValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAppointmentRequest_ServiceOperationValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAppointmentRequest_ServiceOperationValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAppointmentRequest_ServiceOperationValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAppointmentRequest_ServiceOperationValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSubServiceInstance() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAppointmentRequest_ServiceOperationValidationError{
						field:  fmt.Sprintf("SubServiceInstance[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAppointmentRequest_ServiceOperationValidationError{
						field:  fmt.Sprintf("SubServiceInstance[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAppointmentRequest_ServiceOperationValidationError{
					field:  fmt.Sprintf("SubServiceInstance[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAppointmentRequest_ServiceOperationValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAppointmentRequest_ServiceOperationValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAppointmentRequest_ServiceOperationValidationError{
					field:  fmt.Sprintf("Options[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateAppointmentRequest_ServiceOperationMultiError(errors)
	}

	return nil
}

// UpdateAppointmentRequest_ServiceOperationMultiError is an error wrapping
// multiple validation errors returned by
// UpdateAppointmentRequest_ServiceOperation.ValidateAll() if the designated
// constraints aren't met.
type UpdateAppointmentRequest_ServiceOperationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAppointmentRequest_ServiceOperationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAppointmentRequest_ServiceOperationMultiError) AllErrors() []error { return m }

// UpdateAppointmentRequest_ServiceOperationValidationError is the validation
// error returned by UpdateAppointmentRequest_ServiceOperation.Validate if the
// designated constraints aren't met.
type UpdateAppointmentRequest_ServiceOperationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAppointmentRequest_ServiceOperationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAppointmentRequest_ServiceOperationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAppointmentRequest_ServiceOperationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAppointmentRequest_ServiceOperationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAppointmentRequest_ServiceOperationValidationError) ErrorName() string {
	return "UpdateAppointmentRequest_ServiceOperationValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAppointmentRequest_ServiceOperationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAppointmentRequest_ServiceOperation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAppointmentRequest_ServiceOperationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAppointmentRequest_ServiceOperationValidationError{}

// Validate checks the field values on UpdateAppointmentRequest_OptionOperation
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateAppointmentRequest_OptionOperation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateAppointmentRequest_OptionOperation with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateAppointmentRequest_OptionOperationMultiError, or nil if none found.
func (m *UpdateAppointmentRequest_OptionOperation) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAppointmentRequest_OptionOperation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OperationMode

	// no validation rules for ServiceOptionId

	// no validation rules for ServiceOptionTemplateId

	// no validation rules for ServiceInstanceId

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAppointmentRequest_OptionOperationValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAppointmentRequest_OptionOperationValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAppointmentRequest_OptionOperationValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAppointmentRequest_OptionOperationValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAppointmentRequest_OptionOperationValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAppointmentRequest_OptionOperationValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for Quantity

	// no validation rules for Price

	// no validation rules for Tax

	if len(errors) > 0 {
		return UpdateAppointmentRequest_OptionOperationMultiError(errors)
	}

	return nil
}

// UpdateAppointmentRequest_OptionOperationMultiError is an error wrapping
// multiple validation errors returned by
// UpdateAppointmentRequest_OptionOperation.ValidateAll() if the designated
// constraints aren't met.
type UpdateAppointmentRequest_OptionOperationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAppointmentRequest_OptionOperationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAppointmentRequest_OptionOperationMultiError) AllErrors() []error { return m }

// UpdateAppointmentRequest_OptionOperationValidationError is the validation
// error returned by UpdateAppointmentRequest_OptionOperation.Validate if the
// designated constraints aren't met.
type UpdateAppointmentRequest_OptionOperationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAppointmentRequest_OptionOperationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAppointmentRequest_OptionOperationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAppointmentRequest_OptionOperationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAppointmentRequest_OptionOperationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAppointmentRequest_OptionOperationValidationError) ErrorName() string {
	return "UpdateAppointmentRequest_OptionOperationValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAppointmentRequest_OptionOperationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAppointmentRequest_OptionOperation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAppointmentRequest_OptionOperationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAppointmentRequest_OptionOperationValidationError{}
