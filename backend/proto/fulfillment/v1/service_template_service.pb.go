// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/service_template_service.proto

package fulfillmentpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 列出服务模板请求
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: company_id is used as parent in this context --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要page_token --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 不需要page_size，使用现有的分页机制 --)
type ListServiceTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessId []int64 `protobuf:"varint,2,rep,packed,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 是否包含非活跃状态
	Inactive bool `protobuf:"varint,3,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// 服务类型
	ServiceType ServiceType `protobuf:"varint,4,opt,name=service_type,json=serviceType,proto3,enum=backend.proto.fulfillment.v1.ServiceType" json:"service_type,omitempty"`
	// 护理类型
	CareType CareType `protobuf:"varint,5,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// 服务模板ID列表
	ServiceTemplateIds []int64 `protobuf:"varint,6,rep,packed,name=service_template_ids,json=serviceTemplateIds,proto3" json:"service_template_ids,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListServiceTemplateRequest) Reset() {
	*x = ListServiceTemplateRequest{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceTemplateRequest) ProtoMessage() {}

func (x *ListServiceTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceTemplateRequest.ProtoReflect.Descriptor instead.
func (*ListServiceTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListServiceTemplateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListServiceTemplateRequest) GetBusinessId() []int64 {
	if x != nil {
		return x.BusinessId
	}
	return nil
}

func (x *ListServiceTemplateRequest) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ListServiceTemplateRequest) GetServiceType() ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ListServiceTemplateRequest) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *ListServiceTemplateRequest) GetServiceTemplateIds() []int64 {
	if x != nil {
		return x.ServiceTemplateIds
	}
	return nil
}

// 列出服务模板响应
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要next_page_token，使用现有的分页机制 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: service_templates字段是必要的响应内容 --)
type ListServiceTemplateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务工厂列表
	ServiceTemplates []*ServiceTemplate `protobuf:"bytes,1,rep,name=service_templates,json=serviceTemplates,proto3" json:"service_templates,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ListServiceTemplateResponse) Reset() {
	*x = ListServiceTemplateResponse{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceTemplateResponse) ProtoMessage() {}

func (x *ListServiceTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceTemplateResponse.ProtoReflect.Descriptor instead.
func (*ListServiceTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListServiceTemplateResponse) GetServiceTemplates() []*ServiceTemplate {
	if x != nil {
		return x.ServiceTemplates
	}
	return nil
}

// 设置服务属性值请求
type SetServiceAttributeValuesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	ServiceTemplateId int64 `protobuf:"varint,1,opt,name=service_template_id,json=serviceTemplateId,proto3" json:"service_template_id,omitempty"`
	// 属性值列表
	Values        []*ServiceAttributeValue `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetServiceAttributeValuesRequest) Reset() {
	*x = SetServiceAttributeValuesRequest{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetServiceAttributeValuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServiceAttributeValuesRequest) ProtoMessage() {}

func (x *SetServiceAttributeValuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServiceAttributeValuesRequest.ProtoReflect.Descriptor instead.
func (*SetServiceAttributeValuesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{2}
}

func (x *SetServiceAttributeValuesRequest) GetServiceTemplateId() int64 {
	if x != nil {
		return x.ServiceTemplateId
	}
	return 0
}

func (x *SetServiceAttributeValuesRequest) GetValues() []*ServiceAttributeValue {
	if x != nil {
		return x.Values
	}
	return nil
}

// 设置服务属性值响应
type SetServiceAttributeValuesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetServiceAttributeValuesResponse) Reset() {
	*x = SetServiceAttributeValuesResponse{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetServiceAttributeValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServiceAttributeValuesResponse) ProtoMessage() {}

func (x *SetServiceAttributeValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServiceAttributeValuesResponse.ProtoReflect.Descriptor instead.
func (*SetServiceAttributeValuesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{3}
}

// 创建服务类型请求
// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: 不需要ServiceType字段，直接使用各个字段 --)
type CreateServiceTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 类型
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// 描述
	Description   string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceTypeRequest) Reset() {
	*x = CreateServiceTypeRequest{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTypeRequest) ProtoMessage() {}

func (x *CreateServiceTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTypeRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceTypeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateServiceTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateServiceTypeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateServiceTypeRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateServiceTypeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 创建服务类型响应
type CreateServiceTypeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务类型ID
	ServiceTypeId int64 `protobuf:"varint,1,opt,name=service_type_id,json=serviceTypeId,proto3" json:"service_type_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceTypeResponse) Reset() {
	*x = CreateServiceTypeResponse{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTypeResponse) ProtoMessage() {}

func (x *CreateServiceTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTypeResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceTypeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateServiceTypeResponse) GetServiceTypeId() int64 {
	if x != nil {
		return x.ServiceTypeId
	}
	return 0
}

// 设置服务类型属性请求
type SetServiceTypeAttributeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务类型ID
	ServiceTypeId int64 `protobuf:"varint,1,opt,name=service_type_id,json=serviceTypeId,proto3" json:"service_type_id,omitempty"`
	// 服务属性值列表
	ServiceAttributeValues []*ServiceAttributeValue `protobuf:"bytes,2,rep,name=service_attribute_values,json=serviceAttributeValues,proto3" json:"service_attribute_values,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *SetServiceTypeAttributeRequest) Reset() {
	*x = SetServiceTypeAttributeRequest{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetServiceTypeAttributeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServiceTypeAttributeRequest) ProtoMessage() {}

func (x *SetServiceTypeAttributeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServiceTypeAttributeRequest.ProtoReflect.Descriptor instead.
func (*SetServiceTypeAttributeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{6}
}

func (x *SetServiceTypeAttributeRequest) GetServiceTypeId() int64 {
	if x != nil {
		return x.ServiceTypeId
	}
	return 0
}

func (x *SetServiceTypeAttributeRequest) GetServiceAttributeValues() []*ServiceAttributeValue {
	if x != nil {
		return x.ServiceAttributeValues
	}
	return nil
}

// 设置服务类型属性响应
type SetServiceTypeAttributeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetServiceTypeAttributeResponse) Reset() {
	*x = SetServiceTypeAttributeResponse{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetServiceTypeAttributeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServiceTypeAttributeResponse) ProtoMessage() {}

func (x *SetServiceTypeAttributeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServiceTypeAttributeResponse.ProtoReflect.Descriptor instead.
func (*SetServiceTypeAttributeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{7}
}

// 创建服务工厂请求
type CreateServiceTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务工厂
	ServiceTemplate *ServiceTemplate `protobuf:"bytes,1,opt,name=service_template,json=serviceTemplate,proto3" json:"service_template,omitempty"`
	// 属性值列表
	Values []*ServiceAttributeValue `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
	// 服务可用性
	ServiceAvailability *ServiceAvailability `protobuf:"bytes,3,opt,name=service_availability,json=serviceAvailability,proto3" json:"service_availability,omitempty"`
	// 定价信息
	Pricing       *Pricing `protobuf:"bytes,4,opt,name=pricing,proto3" json:"pricing,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceTemplateRequest) Reset() {
	*x = CreateServiceTemplateRequest{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTemplateRequest) ProtoMessage() {}

func (x *CreateServiceTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateServiceTemplateRequest) GetServiceTemplate() *ServiceTemplate {
	if x != nil {
		return x.ServiceTemplate
	}
	return nil
}

func (x *CreateServiceTemplateRequest) GetValues() []*ServiceAttributeValue {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *CreateServiceTemplateRequest) GetServiceAvailability() *ServiceAvailability {
	if x != nil {
		return x.ServiceAvailability
	}
	return nil
}

func (x *CreateServiceTemplateRequest) GetPricing() *Pricing {
	if x != nil {
		return x.Pricing
	}
	return nil
}

// 创建服务工厂响应
type CreateServiceTemplateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	// (-- api-linter: core::0141::forbidden-types=disabled
	//
	//	aip.dev/not-precedent: uint64 is appropriate for ID field --)
	ServiceTemplateId uint64 `protobuf:"varint,1,opt,name=service_template_id,json=serviceTemplateId,proto3" json:"service_template_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateServiceTemplateResponse) Reset() {
	*x = CreateServiceTemplateResponse{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTemplateResponse) ProtoMessage() {}

func (x *CreateServiceTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTemplateResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{9}
}

func (x *CreateServiceTemplateResponse) GetServiceTemplateId() uint64 {
	if x != nil {
		return x.ServiceTemplateId
	}
	return 0
}

// 获取服务工厂请求
type GetServiceTemplateByIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID列表
	ServiceTemplateIds []int64 `protobuf:"varint,3,rep,packed,name=service_template_ids,json=serviceTemplateIds,proto3" json:"service_template_ids,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetServiceTemplateByIdsRequest) Reset() {
	*x = GetServiceTemplateByIdsRequest{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceTemplateByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceTemplateByIdsRequest) ProtoMessage() {}

func (x *GetServiceTemplateByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceTemplateByIdsRequest.ProtoReflect.Descriptor instead.
func (*GetServiceTemplateByIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetServiceTemplateByIdsRequest) GetServiceTemplateIds() []int64 {
	if x != nil {
		return x.ServiceTemplateIds
	}
	return nil
}

// 获取服务工厂响应
type GetServiceTemplateByIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务工厂列表
	ServiceTemplates []*ServiceTemplate `protobuf:"bytes,1,rep,name=service_templates,json=serviceTemplates,proto3" json:"service_templates,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetServiceTemplateByIdsResponse) Reset() {
	*x = GetServiceTemplateByIdsResponse{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceTemplateByIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceTemplateByIdsResponse) ProtoMessage() {}

func (x *GetServiceTemplateByIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceTemplateByIdsResponse.ProtoReflect.Descriptor instead.
func (*GetServiceTemplateByIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetServiceTemplateByIdsResponse) GetServiceTemplates() []*ServiceTemplate {
	if x != nil {
		return x.ServiceTemplates
	}
	return nil
}

// 获取服务类型属性请求
type GetServiceTypeAttributesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务类型ID列表
	ServiceTypeIds []int64 `protobuf:"varint,1,rep,packed,name=service_type_ids,json=serviceTypeIds,proto3" json:"service_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetServiceTypeAttributesRequest) Reset() {
	*x = GetServiceTypeAttributesRequest{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceTypeAttributesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceTypeAttributesRequest) ProtoMessage() {}

func (x *GetServiceTypeAttributesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceTypeAttributesRequest.ProtoReflect.Descriptor instead.
func (*GetServiceTypeAttributesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetServiceTypeAttributesRequest) GetServiceTypeIds() []int64 {
	if x != nil {
		return x.ServiceTypeIds
	}
	return nil
}

// 获取服务类型属性响应
type GetServiceTypeAttributesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// key: service_type_id, value: 该类型下的属性值列表
	ServiceTypeAttributes map[int64]*ServiceTypeAttributes `protobuf:"bytes,1,rep,name=service_type_attributes,json=serviceTypeAttributes,proto3" json:"service_type_attributes,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetServiceTypeAttributesResponse) Reset() {
	*x = GetServiceTypeAttributesResponse{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceTypeAttributesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceTypeAttributesResponse) ProtoMessage() {}

func (x *GetServiceTypeAttributesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceTypeAttributesResponse.ProtoReflect.Descriptor instead.
func (*GetServiceTypeAttributesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetServiceTypeAttributesResponse) GetServiceTypeAttributes() map[int64]*ServiceTypeAttributes {
	if x != nil {
		return x.ServiceTypeAttributes
	}
	return nil
}

// 用于包装属性值列表
type ServiceTypeAttributes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务属性值列表
	ServiceAttributeValues []*ServiceAttributeValue `protobuf:"bytes,1,rep,name=service_attribute_values,json=serviceAttributeValues,proto3" json:"service_attribute_values,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ServiceTypeAttributes) Reset() {
	*x = ServiceTypeAttributes{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceTypeAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTypeAttributes) ProtoMessage() {}

func (x *ServiceTypeAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTypeAttributes.ProtoReflect.Descriptor instead.
func (*ServiceTypeAttributes) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{14}
}

func (x *ServiceTypeAttributes) GetServiceAttributeValues() []*ServiceAttributeValue {
	if x != nil {
		return x.ServiceAttributeValues
	}
	return nil
}

// 获取服务属性值请求
type GetServiceAttributeValuesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID列表
	ServiceTemplateIds []int64 `protobuf:"varint,1,rep,packed,name=service_template_ids,json=serviceTemplateIds,proto3" json:"service_template_ids,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetServiceAttributeValuesRequest) Reset() {
	*x = GetServiceAttributeValuesRequest{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceAttributeValuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceAttributeValuesRequest) ProtoMessage() {}

func (x *GetServiceAttributeValuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceAttributeValuesRequest.ProtoReflect.Descriptor instead.
func (*GetServiceAttributeValuesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetServiceAttributeValuesRequest) GetServiceTemplateIds() []int64 {
	if x != nil {
		return x.ServiceTemplateIds
	}
	return nil
}

// 获取服务属性值响应
type GetServiceAttributeValuesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// key: service_template_id, value: 该工厂下的属性值列表
	ServiceAttributeValues map[int64]*ServiceAttributeValues `protobuf:"bytes,1,rep,name=service_attribute_values,json=serviceAttributeValues,proto3" json:"service_attribute_values,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *GetServiceAttributeValuesResponse) Reset() {
	*x = GetServiceAttributeValuesResponse{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceAttributeValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceAttributeValuesResponse) ProtoMessage() {}

func (x *GetServiceAttributeValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceAttributeValuesResponse.ProtoReflect.Descriptor instead.
func (*GetServiceAttributeValuesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetServiceAttributeValuesResponse) GetServiceAttributeValues() map[int64]*ServiceAttributeValues {
	if x != nil {
		return x.ServiceAttributeValues
	}
	return nil
}

// 用于包装属性值列表
type ServiceAttributeValues struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 属性值列表
	Values        []*ServiceAttributeValue `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceAttributeValues) Reset() {
	*x = ServiceAttributeValues{}
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAttributeValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAttributeValues) ProtoMessage() {}

func (x *ServiceAttributeValues) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAttributeValues.ProtoReflect.Descriptor instead.
func (*ServiceAttributeValues) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP(), []int{17}
}

func (x *ServiceAttributeValues) GetValues() []*ServiceAttributeValue {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_backend_proto_fulfillment_v1_service_template_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_service_template_service_proto_rawDesc = "" +
	"\n" +
	";backend/proto/fulfillment/v1/service_template_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17validate/validate.proto\x1a3backend/proto/fulfillment/v1/service_template.proto\x1a)backend/proto/fulfillment/v1/common.proto\"\xd0\x02\n" +
	"\x1aListServiceTemplateRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12)\n" +
	"\vbusiness_id\x18\x02 \x03(\x03B\b\xfaB\x05\x92\x01\x02\b\x01R\n" +
	"businessId\x12\x1a\n" +
	"\binactive\x18\x03 \x01(\bR\binactive\x12L\n" +
	"\fservice_type\x18\x04 \x01(\x0e2).backend.proto.fulfillment.v1.ServiceTypeR\vserviceType\x12C\n" +
	"\tcare_type\x18\x05 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeR\bcareType\x120\n" +
	"\x14service_template_ids\x18\x06 \x03(\x03R\x12serviceTemplateIds\"y\n" +
	"\x1bListServiceTemplateResponse\x12Z\n" +
	"\x11service_templates\x18\x01 \x03(\v2-.backend.proto.fulfillment.v1.ServiceTemplateR\x10serviceTemplates\"\x9f\x01\n" +
	" SetServiceAttributeValuesRequest\x12.\n" +
	"\x13service_template_id\x18\x01 \x01(\x03R\x11serviceTemplateId\x12K\n" +
	"\x06values\x18\x02 \x03(\v23.backend.proto.fulfillment.v1.ServiceAttributeValueR\x06values\"#\n" +
	"!SetServiceAttributeValuesResponse\"\x90\x01\n" +
	"\x18CreateServiceTypeRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"C\n" +
	"\x19CreateServiceTypeResponse\x12&\n" +
	"\x0fservice_type_id\x18\x01 \x01(\x03R\rserviceTypeId\"\xb7\x01\n" +
	"\x1eSetServiceTypeAttributeRequest\x12&\n" +
	"\x0fservice_type_id\x18\x01 \x01(\x03R\rserviceTypeId\x12m\n" +
	"\x18service_attribute_values\x18\x02 \x03(\v23.backend.proto.fulfillment.v1.ServiceAttributeValueR\x16serviceAttributeValues\"!\n" +
	"\x1fSetServiceTypeAttributeResponse\"\xec\x02\n" +
	"\x1cCreateServiceTemplateRequest\x12X\n" +
	"\x10service_template\x18\x01 \x01(\v2-.backend.proto.fulfillment.v1.ServiceTemplateR\x0fserviceTemplate\x12K\n" +
	"\x06values\x18\x02 \x03(\v23.backend.proto.fulfillment.v1.ServiceAttributeValueR\x06values\x12d\n" +
	"\x14service_availability\x18\x03 \x01(\v21.backend.proto.fulfillment.v1.ServiceAvailabilityR\x13serviceAvailability\x12?\n" +
	"\apricing\x18\x04 \x01(\v2%.backend.proto.fulfillment.v1.PricingR\apricing\"O\n" +
	"\x1dCreateServiceTemplateResponse\x12.\n" +
	"\x13service_template_id\x18\x01 \x01(\x04R\x11serviceTemplateId\"R\n" +
	"\x1eGetServiceTemplateByIdsRequest\x120\n" +
	"\x14service_template_ids\x18\x03 \x03(\x03R\x12serviceTemplateIds\"}\n" +
	"\x1fGetServiceTemplateByIdsResponse\x12Z\n" +
	"\x11service_templates\x18\x01 \x03(\v2-.backend.proto.fulfillment.v1.ServiceTemplateR\x10serviceTemplates\"K\n" +
	"\x1fGetServiceTypeAttributesRequest\x12(\n" +
	"\x10service_type_ids\x18\x01 \x03(\x03R\x0eserviceTypeIds\"\xb5\x02\n" +
	" GetServiceTypeAttributesResponse\x12\x91\x01\n" +
	"\x17service_type_attributes\x18\x01 \x03(\v2Y.backend.proto.fulfillment.v1.GetServiceTypeAttributesResponse.ServiceTypeAttributesEntryR\x15serviceTypeAttributes\x1a}\n" +
	"\x1aServiceTypeAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12I\n" +
	"\x05value\x18\x02 \x01(\v23.backend.proto.fulfillment.v1.ServiceTypeAttributesR\x05value:\x028\x01\"\x86\x01\n" +
	"\x15ServiceTypeAttributes\x12m\n" +
	"\x18service_attribute_values\x18\x01 \x03(\v23.backend.proto.fulfillment.v1.ServiceAttributeValueR\x16serviceAttributeValues\"T\n" +
	" GetServiceAttributeValuesRequest\x120\n" +
	"\x14service_template_ids\x18\x01 \x03(\x03R\x12serviceTemplateIds\"\xbc\x02\n" +
	"!GetServiceAttributeValuesResponse\x12\x95\x01\n" +
	"\x18service_attribute_values\x18\x01 \x03(\v2[.backend.proto.fulfillment.v1.GetServiceAttributeValuesResponse.ServiceAttributeValuesEntryR\x16serviceAttributeValues\x1a\x7f\n" +
	"\x1bServiceAttributeValuesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12J\n" +
	"\x05value\x18\x02 \x01(\v24.backend.proto.fulfillment.v1.ServiceAttributeValuesR\x05value:\x028\x01\"e\n" +
	"\x16ServiceAttributeValues\x12K\n" +
	"\x06values\x18\x01 \x03(\v23.backend.proto.fulfillment.v1.ServiceAttributeValueR\x06values2\xcb\t\n" +
	"\x16ServiceTemplateService\x12\x84\x01\n" +
	"\x11CreateServiceType\x126.backend.proto.fulfillment.v1.CreateServiceTypeRequest\x1a7.backend.proto.fulfillment.v1.CreateServiceTypeResponse\x12\x90\x01\n" +
	"\x15CreateServiceTemplate\x12:.backend.proto.fulfillment.v1.CreateServiceTemplateRequest\x1a;.backend.proto.fulfillment.v1.CreateServiceTemplateResponse\x12\x9c\x01\n" +
	"\x19SetServiceAttributeValues\x12>.backend.proto.fulfillment.v1.SetServiceAttributeValuesRequest\x1a?.backend.proto.fulfillment.v1.SetServiceAttributeValuesResponse\x12\x96\x01\n" +
	"\x17SetServiceTypeAttribute\x12<.backend.proto.fulfillment.v1.SetServiceTypeAttributeRequest\x1a=.backend.proto.fulfillment.v1.SetServiceTypeAttributeResponse\x12\x96\x01\n" +
	"\x17GetServiceTemplateByIds\x12<.backend.proto.fulfillment.v1.GetServiceTemplateByIdsRequest\x1a=.backend.proto.fulfillment.v1.GetServiceTemplateByIdsResponse\x12\x99\x01\n" +
	"\x18GetServiceTypeAttributes\x12=.backend.proto.fulfillment.v1.GetServiceTypeAttributesRequest\x1a>.backend.proto.fulfillment.v1.GetServiceTypeAttributesResponse\x12\x9c\x01\n" +
	"\x19GetServiceAttributeValues\x12>.backend.proto.fulfillment.v1.GetServiceAttributeValuesRequest\x1a?.backend.proto.fulfillment.v1.GetServiceAttributeValuesResponse\x12\x8a\x01\n" +
	"\x13ListServiceTemplate\x128.backend.proto.fulfillment.v1.ListServiceTemplateRequest\x1a9.backend.proto.fulfillment.v1.ListServiceTemplateResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_service_template_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_service_template_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_service_template_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_backend_proto_fulfillment_v1_service_template_service_proto_goTypes = []any{
	(*ListServiceTemplateRequest)(nil),        // 0: backend.proto.fulfillment.v1.ListServiceTemplateRequest
	(*ListServiceTemplateResponse)(nil),       // 1: backend.proto.fulfillment.v1.ListServiceTemplateResponse
	(*SetServiceAttributeValuesRequest)(nil),  // 2: backend.proto.fulfillment.v1.SetServiceAttributeValuesRequest
	(*SetServiceAttributeValuesResponse)(nil), // 3: backend.proto.fulfillment.v1.SetServiceAttributeValuesResponse
	(*CreateServiceTypeRequest)(nil),          // 4: backend.proto.fulfillment.v1.CreateServiceTypeRequest
	(*CreateServiceTypeResponse)(nil),         // 5: backend.proto.fulfillment.v1.CreateServiceTypeResponse
	(*SetServiceTypeAttributeRequest)(nil),    // 6: backend.proto.fulfillment.v1.SetServiceTypeAttributeRequest
	(*SetServiceTypeAttributeResponse)(nil),   // 7: backend.proto.fulfillment.v1.SetServiceTypeAttributeResponse
	(*CreateServiceTemplateRequest)(nil),      // 8: backend.proto.fulfillment.v1.CreateServiceTemplateRequest
	(*CreateServiceTemplateResponse)(nil),     // 9: backend.proto.fulfillment.v1.CreateServiceTemplateResponse
	(*GetServiceTemplateByIdsRequest)(nil),    // 10: backend.proto.fulfillment.v1.GetServiceTemplateByIdsRequest
	(*GetServiceTemplateByIdsResponse)(nil),   // 11: backend.proto.fulfillment.v1.GetServiceTemplateByIdsResponse
	(*GetServiceTypeAttributesRequest)(nil),   // 12: backend.proto.fulfillment.v1.GetServiceTypeAttributesRequest
	(*GetServiceTypeAttributesResponse)(nil),  // 13: backend.proto.fulfillment.v1.GetServiceTypeAttributesResponse
	(*ServiceTypeAttributes)(nil),             // 14: backend.proto.fulfillment.v1.ServiceTypeAttributes
	(*GetServiceAttributeValuesRequest)(nil),  // 15: backend.proto.fulfillment.v1.GetServiceAttributeValuesRequest
	(*GetServiceAttributeValuesResponse)(nil), // 16: backend.proto.fulfillment.v1.GetServiceAttributeValuesResponse
	(*ServiceAttributeValues)(nil),            // 17: backend.proto.fulfillment.v1.ServiceAttributeValues
	nil,                                       // 18: backend.proto.fulfillment.v1.GetServiceTypeAttributesResponse.ServiceTypeAttributesEntry
	nil,                                       // 19: backend.proto.fulfillment.v1.GetServiceAttributeValuesResponse.ServiceAttributeValuesEntry
	(ServiceType)(0),                          // 20: backend.proto.fulfillment.v1.ServiceType
	(CareType)(0),                             // 21: backend.proto.fulfillment.v1.CareType
	(*ServiceTemplate)(nil),                   // 22: backend.proto.fulfillment.v1.ServiceTemplate
	(*ServiceAttributeValue)(nil),             // 23: backend.proto.fulfillment.v1.ServiceAttributeValue
	(*ServiceAvailability)(nil),               // 24: backend.proto.fulfillment.v1.ServiceAvailability
	(*Pricing)(nil),                           // 25: backend.proto.fulfillment.v1.Pricing
}
var file_backend_proto_fulfillment_v1_service_template_service_proto_depIdxs = []int32{
	20, // 0: backend.proto.fulfillment.v1.ListServiceTemplateRequest.service_type:type_name -> backend.proto.fulfillment.v1.ServiceType
	21, // 1: backend.proto.fulfillment.v1.ListServiceTemplateRequest.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	22, // 2: backend.proto.fulfillment.v1.ListServiceTemplateResponse.service_templates:type_name -> backend.proto.fulfillment.v1.ServiceTemplate
	23, // 3: backend.proto.fulfillment.v1.SetServiceAttributeValuesRequest.values:type_name -> backend.proto.fulfillment.v1.ServiceAttributeValue
	23, // 4: backend.proto.fulfillment.v1.SetServiceTypeAttributeRequest.service_attribute_values:type_name -> backend.proto.fulfillment.v1.ServiceAttributeValue
	22, // 5: backend.proto.fulfillment.v1.CreateServiceTemplateRequest.service_template:type_name -> backend.proto.fulfillment.v1.ServiceTemplate
	23, // 6: backend.proto.fulfillment.v1.CreateServiceTemplateRequest.values:type_name -> backend.proto.fulfillment.v1.ServiceAttributeValue
	24, // 7: backend.proto.fulfillment.v1.CreateServiceTemplateRequest.service_availability:type_name -> backend.proto.fulfillment.v1.ServiceAvailability
	25, // 8: backend.proto.fulfillment.v1.CreateServiceTemplateRequest.pricing:type_name -> backend.proto.fulfillment.v1.Pricing
	22, // 9: backend.proto.fulfillment.v1.GetServiceTemplateByIdsResponse.service_templates:type_name -> backend.proto.fulfillment.v1.ServiceTemplate
	18, // 10: backend.proto.fulfillment.v1.GetServiceTypeAttributesResponse.service_type_attributes:type_name -> backend.proto.fulfillment.v1.GetServiceTypeAttributesResponse.ServiceTypeAttributesEntry
	23, // 11: backend.proto.fulfillment.v1.ServiceTypeAttributes.service_attribute_values:type_name -> backend.proto.fulfillment.v1.ServiceAttributeValue
	19, // 12: backend.proto.fulfillment.v1.GetServiceAttributeValuesResponse.service_attribute_values:type_name -> backend.proto.fulfillment.v1.GetServiceAttributeValuesResponse.ServiceAttributeValuesEntry
	23, // 13: backend.proto.fulfillment.v1.ServiceAttributeValues.values:type_name -> backend.proto.fulfillment.v1.ServiceAttributeValue
	14, // 14: backend.proto.fulfillment.v1.GetServiceTypeAttributesResponse.ServiceTypeAttributesEntry.value:type_name -> backend.proto.fulfillment.v1.ServiceTypeAttributes
	17, // 15: backend.proto.fulfillment.v1.GetServiceAttributeValuesResponse.ServiceAttributeValuesEntry.value:type_name -> backend.proto.fulfillment.v1.ServiceAttributeValues
	4,  // 16: backend.proto.fulfillment.v1.ServiceTemplateService.CreateServiceType:input_type -> backend.proto.fulfillment.v1.CreateServiceTypeRequest
	8,  // 17: backend.proto.fulfillment.v1.ServiceTemplateService.CreateServiceTemplate:input_type -> backend.proto.fulfillment.v1.CreateServiceTemplateRequest
	2,  // 18: backend.proto.fulfillment.v1.ServiceTemplateService.SetServiceAttributeValues:input_type -> backend.proto.fulfillment.v1.SetServiceAttributeValuesRequest
	6,  // 19: backend.proto.fulfillment.v1.ServiceTemplateService.SetServiceTypeAttribute:input_type -> backend.proto.fulfillment.v1.SetServiceTypeAttributeRequest
	10, // 20: backend.proto.fulfillment.v1.ServiceTemplateService.GetServiceTemplateByIds:input_type -> backend.proto.fulfillment.v1.GetServiceTemplateByIdsRequest
	12, // 21: backend.proto.fulfillment.v1.ServiceTemplateService.GetServiceTypeAttributes:input_type -> backend.proto.fulfillment.v1.GetServiceTypeAttributesRequest
	15, // 22: backend.proto.fulfillment.v1.ServiceTemplateService.GetServiceAttributeValues:input_type -> backend.proto.fulfillment.v1.GetServiceAttributeValuesRequest
	0,  // 23: backend.proto.fulfillment.v1.ServiceTemplateService.ListServiceTemplate:input_type -> backend.proto.fulfillment.v1.ListServiceTemplateRequest
	5,  // 24: backend.proto.fulfillment.v1.ServiceTemplateService.CreateServiceType:output_type -> backend.proto.fulfillment.v1.CreateServiceTypeResponse
	9,  // 25: backend.proto.fulfillment.v1.ServiceTemplateService.CreateServiceTemplate:output_type -> backend.proto.fulfillment.v1.CreateServiceTemplateResponse
	3,  // 26: backend.proto.fulfillment.v1.ServiceTemplateService.SetServiceAttributeValues:output_type -> backend.proto.fulfillment.v1.SetServiceAttributeValuesResponse
	7,  // 27: backend.proto.fulfillment.v1.ServiceTemplateService.SetServiceTypeAttribute:output_type -> backend.proto.fulfillment.v1.SetServiceTypeAttributeResponse
	11, // 28: backend.proto.fulfillment.v1.ServiceTemplateService.GetServiceTemplateByIds:output_type -> backend.proto.fulfillment.v1.GetServiceTemplateByIdsResponse
	13, // 29: backend.proto.fulfillment.v1.ServiceTemplateService.GetServiceTypeAttributes:output_type -> backend.proto.fulfillment.v1.GetServiceTypeAttributesResponse
	16, // 30: backend.proto.fulfillment.v1.ServiceTemplateService.GetServiceAttributeValues:output_type -> backend.proto.fulfillment.v1.GetServiceAttributeValuesResponse
	1,  // 31: backend.proto.fulfillment.v1.ServiceTemplateService.ListServiceTemplate:output_type -> backend.proto.fulfillment.v1.ListServiceTemplateResponse
	24, // [24:32] is the sub-list for method output_type
	16, // [16:24] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_service_template_service_proto_init() }
func file_backend_proto_fulfillment_v1_service_template_service_proto_init() {
	if File_backend_proto_fulfillment_v1_service_template_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_service_template_proto_init()
	file_backend_proto_fulfillment_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_service_template_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_service_template_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_service_template_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_service_template_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_service_template_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_service_template_service_proto = out.File
	file_backend_proto_fulfillment_v1_service_template_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_service_template_service_proto_depIdxs = nil
}
