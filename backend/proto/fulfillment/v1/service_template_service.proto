syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "backend/proto/fulfillment/v1/service_template.proto";
import "backend/proto/fulfillment/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 服务模板服务
service ServiceTemplateService {
    // 创建服务类型
    // (-- api-linter: core::0133::response-message-name=disabled
    //     aip.dev/not-precedent: CreateServiceTypeResponse is appropriate for this use case --)
    rpc CreateServiceType(CreateServiceTypeRequest) returns (CreateServiceTypeResponse);
    // 创建一个服务
    // (-- api-linter: core::0133::response-message-name=disabled
    //     aip.dev/not-precedent: CreateServiceTemplateResponse is appropriate for this use case --)
    rpc CreateServiceTemplate(CreateServiceTemplateRequest) returns (CreateServiceTemplateResponse);
    // 设置service的attribute值
    // (-- api-linter: core::0134::synonyms=disabled
    //     aip.dev/not-precedent: Set is more appropriate for this use case --)
    rpc SetServiceAttributeValues(SetServiceAttributeValuesRequest) returns (SetServiceAttributeValuesResponse);
    // 设置service_type的attribute
    // (-- api-linter: core::0134::synonyms=disabled
    //     aip.dev/not-precedent: Set is more appropriate for this use case --)
    rpc SetServiceTypeAttribute(SetServiceTypeAttributeRequest) returns (SetServiceTypeAttributeResponse);
    // 通过 repeated service_template_id 获取 ServiceTemplate 列表
    // (-- api-linter: core::0131::response-message-name=disabled
    //     aip.dev/not-precedent: GetServiceTemplateByIdsResponse is appropriate for this use case --)
    // (-- api-linter: core::0136::prepositions=disabled
    //     aip.dev/not-precedent: GetServiceTemplateByIds is appropriate for this use case --)
    rpc GetServiceTemplateByIds(GetServiceTemplateByIdsRequest) returns (GetServiceTemplateByIdsResponse);
    // 通过 repeated service_type_id 获取 ServiceTypeAttribute（属性值）列表
    // (-- api-linter: core::0131::response-message-name=disabled
    //     aip.dev/not-precedent: GetServiceTypeAttributesResponse is appropriate for this use case --)
    rpc GetServiceTypeAttributes(GetServiceTypeAttributesRequest) returns (GetServiceTypeAttributesResponse);
    // 通过 repeated service_template_id 获取 ServiceAttributeValues 列表
    // (-- api-linter: core::0131::response-message-name=disabled
    //     aip.dev/not-precedent: GetServiceAttributeValuesResponse is appropriate for this use case --)
    rpc GetServiceAttributeValues(GetServiceAttributeValuesRequest) returns (GetServiceAttributeValuesResponse);

    // 列出服务模板
    // (-- api-linter: core::0158::request-page_token-field=disabled
    // aip.dev/not-precedent: 不需要page_token --)
    rpc ListServiceTemplate(ListServiceTemplateRequest) returns (ListServiceTemplateResponse);
}

// 列出服务模板请求
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: company_id is used as parent in this context --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 不需要page_token --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 不需要page_size，使用现有的分页机制 --)
message ListServiceTemplateRequest{
  // 公司ID
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // 商家ID
  repeated int64 business_id = 2 [(validate.rules).repeated = {min_items: 1}];  
  // 是否包含非活跃状态
  bool inactive = 3;
  // 服务类型
  ServiceType service_type = 4;
  // 护理类型
  CareType care_type = 5;
  // 服务模板ID列表
  repeated int64 service_template_ids = 6;
}

// 列出服务模板响应
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 不需要next_page_token，使用现有的分页机制 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: service_templates字段是必要的响应内容 --)
message ListServiceTemplateResponse{
  // 服务工厂列表
  repeated ServiceTemplate service_templates = 1;
}
  
// 设置服务属性值请求
message SetServiceAttributeValuesRequest {
  // 服务模板ID
  int64 service_template_id = 1;
  // 属性值列表
  repeated ServiceAttributeValue values = 2;
}
  
// 设置服务属性值响应
message SetServiceAttributeValuesResponse{}
  
// 创建服务类型请求
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 不需要ServiceType字段，直接使用各个字段 --)
message CreateServiceTypeRequest {
  // 公司ID
  int64 company_id = 1;
  // 商家ID
  int64 business_id = 2;
  // 类型
  string type = 3;
  // 描述
  string description = 4;
}

// 创建服务类型响应
message CreateServiceTypeResponse {
  // 服务类型ID
  int64 service_type_id = 1;
}

// 设置服务类型属性请求
message SetServiceTypeAttributeRequest{
  // 服务类型ID
  int64 service_type_id = 1;
  // 服务属性值列表
  repeated ServiceAttributeValue service_attribute_values = 2;
}

// 设置服务类型属性响应
message SetServiceTypeAttributeResponse{}

// 创建服务工厂请求
message CreateServiceTemplateRequest{
  // 服务工厂
  ServiceTemplate service_template = 1;
  // 属性值列表
  repeated ServiceAttributeValue values = 2;
  // 服务可用性
  ServiceAvailability service_availability = 3;
  // 定价信息
  Pricing pricing = 4;
}

// 创建服务工厂响应
message CreateServiceTemplateResponse{
  // 服务模板ID
  // (-- api-linter: core::0141::forbidden-types=disabled
  //     aip.dev/not-precedent: uint64 is appropriate for ID field --)
  uint64 service_template_id = 1;
}

// 获取服务工厂请求
message GetServiceTemplateByIdsRequest {
  // 服务模板ID列表
  repeated int64 service_template_ids = 3;
}

// 获取服务工厂响应
message GetServiceTemplateByIdsResponse {
  // 服务工厂列表
  repeated ServiceTemplate service_templates = 1;
}

// 获取服务类型属性请求
message GetServiceTypeAttributesRequest {
  // 服务类型ID列表
  repeated int64 service_type_ids = 1;
}

// 获取服务类型属性响应
message GetServiceTypeAttributesResponse {
  // key: service_type_id, value: 该类型下的属性值列表
  map<int64, ServiceTypeAttributes> service_type_attributes = 1;
}

// 用于包装属性值列表
message ServiceTypeAttributes {
  // 服务属性值列表
  repeated ServiceAttributeValue service_attribute_values = 1;
}

// 获取服务属性值请求
message GetServiceAttributeValuesRequest {
  // 服务模板ID列表
  repeated int64 service_template_ids = 1;
}

// 获取服务属性值响应
message GetServiceAttributeValuesResponse {
  // key: service_template_id, value: 该工厂下的属性值列表
  map<int64, ServiceAttributeValues> service_attribute_values = 1;
}

// 用于包装属性值列表
message ServiceAttributeValues {
  // 属性值列表
  repeated ServiceAttributeValue values = 1;
}
  