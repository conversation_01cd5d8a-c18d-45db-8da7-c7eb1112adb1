// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/fulfillment.proto

package fulfillmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// State信息
type State int32

const (
	// STATE_UNSPECIFIED 默认state
	State_STATE_UNSPECIFIED State = 0
	// STATE_NOT_START 未开始的state
	State_STATE_NOT_START State = 1
)

// Enum value maps for State.
var (
	State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "STATE_NOT_START",
	}
	State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"STATE_NOT_START":   1,
	}
)

func (x State) Enum() *State {
	p := new(State)
	*p = x
	return p
}

func (x State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_proto_enumTypes[0].Descriptor()
}

func (State) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_proto_enumTypes[0]
}

func (x State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use State.Descriptor instead.
func (State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescGZIP(), []int{0}
}

// 过滤条件
type FulfillmentFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务类型（可选，不填就是所有类型）
	CareTypes []CareType `protobuf:"varint,1,rep,packed,name=care_types,json=careTypes,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_types,omitempty"`
	// 宠物ID（可选，不填就是所有pet）
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// 员工ID列表(可选)
	StaffIds []int64 `protobuf:"varint,3,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// 状态(可选)
	States []int32 `protobuf:"varint,4,rep,packed,name=states,proto3" json:"states,omitempty"`
	// 客户ID（可选）
	CustomerIds   []int64 `protobuf:"varint,5,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FulfillmentFilter) Reset() {
	*x = FulfillmentFilter{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FulfillmentFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FulfillmentFilter) ProtoMessage() {}

func (x *FulfillmentFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FulfillmentFilter.ProtoReflect.Descriptor instead.
func (*FulfillmentFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescGZIP(), []int{0}
}

func (x *FulfillmentFilter) GetCareTypes() []CareType {
	if x != nil {
		return x.CareTypes
	}
	return nil
}

func (x *FulfillmentFilter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *FulfillmentFilter) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *FulfillmentFilter) GetStates() []int32 {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *FulfillmentFilter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 履约信息
type Fulfillment struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 履约ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 公司ID
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 宠物ID
	PetId int64 `protobuf:"varint,6,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 服务实例ID
	ServiceInstanceId int64 `protobuf:"varint,7,opt,name=service_instance_id,json=serviceInstanceId,proto3" json:"service_instance_id,omitempty"`
	// 服务factory_id
	ServiceFactoryId int64 `protobuf:"varint,8,opt,name=service_factory_id,json=serviceFactoryId,proto3" json:"service_factory_id,omitempty"`
	// 护理类型
	CareType CareType `protobuf:"varint,9,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// 住宿ID
	LodgingId int64 `protobuf:"varint,10,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// 场地ID
	PlaygroupId int64 `protobuf:"varint,11,opt,name=playgroup_id,json=playgroupId,proto3" json:"playgroup_id,omitempty"`
	// 状态
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: don't need --)
	State State `protobuf:"varint,12,opt,name=state,proto3,enum=backend.proto.fulfillment.v1.State" json:"state,omitempty"`
	// 员工ID类型
	StaffIds []int64 `protobuf:"varint,13,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// 订单ID
	OrderId int64 `protobuf:"varint,14,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// 服务类型
	ServiceType   ServiceType `protobuf:"varint,15,opt,name=service_type,json=serviceType,proto3,enum=backend.proto.fulfillment.v1.ServiceType" json:"service_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Fulfillment) Reset() {
	*x = Fulfillment{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Fulfillment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fulfillment) ProtoMessage() {}

func (x *Fulfillment) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fulfillment.ProtoReflect.Descriptor instead.
func (*Fulfillment) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescGZIP(), []int{1}
}

func (x *Fulfillment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Fulfillment) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Fulfillment) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *Fulfillment) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *Fulfillment) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Fulfillment) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *Fulfillment) GetServiceInstanceId() int64 {
	if x != nil {
		return x.ServiceInstanceId
	}
	return 0
}

func (x *Fulfillment) GetServiceFactoryId() int64 {
	if x != nil {
		return x.ServiceFactoryId
	}
	return 0
}

func (x *Fulfillment) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *Fulfillment) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *Fulfillment) GetPlaygroupId() int64 {
	if x != nil {
		return x.PlaygroupId
	}
	return 0
}

func (x *Fulfillment) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *Fulfillment) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *Fulfillment) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *Fulfillment) GetServiceType() ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

var File_backend_proto_fulfillment_v1_fulfillment_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_fulfillment_proto_rawDesc = "" +
	"\n" +
	".backend/proto/fulfillment/v1/fulfillment.proto\x12\x1cbackend.proto.fulfillment.v1\x1a)backend/proto/fulfillment/v1/common.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xcb\x01\n" +
	"\x11FulfillmentFilter\x12E\n" +
	"\n" +
	"care_types\x18\x01 \x03(\x0e2&.backend.proto.fulfillment.v1.CareTypeR\tcareTypes\x12\x17\n" +
	"\apet_ids\x18\x02 \x03(\x03R\x06petIds\x12\x1b\n" +
	"\tstaff_ids\x18\x03 \x03(\x03R\bstaffIds\x12\x16\n" +
	"\x06states\x18\x04 \x03(\x05R\x06states\x12!\n" +
	"\fcustomer_ids\x18\x05 \x03(\x03R\vcustomerIds\"\x8e\x05\n" +
	"\vFulfillment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x129\n" +
	"\n" +
	"start_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x1f\n" +
	"\vbusiness_id\x18\x04 \x01(\x03R\n" +
	"businessId\x12\x1f\n" +
	"\vcustomer_id\x18\x05 \x01(\x03R\n" +
	"customerId\x12\x15\n" +
	"\x06pet_id\x18\x06 \x01(\x03R\x05petId\x12.\n" +
	"\x13service_instance_id\x18\a \x01(\x03R\x11serviceInstanceId\x12,\n" +
	"\x12service_factory_id\x18\b \x01(\x03R\x10serviceFactoryId\x12C\n" +
	"\tcare_type\x18\t \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeR\bcareType\x12\x1d\n" +
	"\n" +
	"lodging_id\x18\n" +
	" \x01(\x03R\tlodgingId\x12!\n" +
	"\fplaygroup_id\x18\v \x01(\x03R\vplaygroupId\x129\n" +
	"\x05state\x18\f \x01(\x0e2#.backend.proto.fulfillment.v1.StateR\x05state\x12\x1b\n" +
	"\tstaff_ids\x18\r \x03(\x03R\bstaffIds\x12\x19\n" +
	"\border_id\x18\x0e \x01(\x03R\aorderId\x12L\n" +
	"\fservice_type\x18\x0f \x01(\x0e2).backend.proto.fulfillment.v1.ServiceTypeR\vserviceType*3\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fSTATE_NOT_START\x10\x01Bt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_fulfillment_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_fulfillment_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_fulfillment_v1_fulfillment_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_fulfillment_v1_fulfillment_proto_goTypes = []any{
	(State)(0),                    // 0: backend.proto.fulfillment.v1.State
	(*FulfillmentFilter)(nil),     // 1: backend.proto.fulfillment.v1.FulfillmentFilter
	(*Fulfillment)(nil),           // 2: backend.proto.fulfillment.v1.Fulfillment
	(CareType)(0),                 // 3: backend.proto.fulfillment.v1.CareType
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
	(ServiceType)(0),              // 5: backend.proto.fulfillment.v1.ServiceType
}
var file_backend_proto_fulfillment_v1_fulfillment_proto_depIdxs = []int32{
	3, // 0: backend.proto.fulfillment.v1.FulfillmentFilter.care_types:type_name -> backend.proto.fulfillment.v1.CareType
	4, // 1: backend.proto.fulfillment.v1.Fulfillment.start_time:type_name -> google.protobuf.Timestamp
	4, // 2: backend.proto.fulfillment.v1.Fulfillment.end_time:type_name -> google.protobuf.Timestamp
	3, // 3: backend.proto.fulfillment.v1.Fulfillment.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	0, // 4: backend.proto.fulfillment.v1.Fulfillment.state:type_name -> backend.proto.fulfillment.v1.State
	5, // 5: backend.proto.fulfillment.v1.Fulfillment.service_type:type_name -> backend.proto.fulfillment.v1.ServiceType
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_fulfillment_proto_init() }
func file_backend_proto_fulfillment_v1_fulfillment_proto_init() {
	if File_backend_proto_fulfillment_v1_fulfillment_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_fulfillment_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_fulfillment_proto_depIdxs,
		EnumInfos:         file_backend_proto_fulfillment_v1_fulfillment_proto_enumTypes,
		MessageInfos:      file_backend_proto_fulfillment_v1_fulfillment_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_fulfillment_proto = out.File
	file_backend_proto_fulfillment_v1_fulfillment_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_fulfillment_proto_depIdxs = nil
}
