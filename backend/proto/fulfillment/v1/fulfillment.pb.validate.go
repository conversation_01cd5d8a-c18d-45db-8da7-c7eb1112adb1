// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/fulfillment/v1/fulfillment.proto

package fulfillmentpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FulfillmentFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentFilterMultiError, or nil if none found.
func (m *FulfillmentFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FulfillmentFilterMultiError(errors)
	}

	return nil
}

// FulfillmentFilterMultiError is an error wrapping multiple validation errors
// returned by FulfillmentFilter.ValidateAll() if the designated constraints
// aren't met.
type FulfillmentFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentFilterMultiError) AllErrors() []error { return m }

// FulfillmentFilterValidationError is the validation error returned by
// FulfillmentFilter.Validate if the designated constraints aren't met.
type FulfillmentFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentFilterValidationError) ErrorName() string {
	return "FulfillmentFilterValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentFilterValidationError{}

// Validate checks the field values on Fulfillment with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Fulfillment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Fulfillment with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FulfillmentMultiError, or
// nil if none found.
func (m *Fulfillment) ValidateAll() error {
	return m.validate(true)
}

func (m *Fulfillment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BusinessId

	// no validation rules for CustomerId

	// no validation rules for PetId

	// no validation rules for ServiceInstanceId

	// no validation rules for ServiceFactoryId

	// no validation rules for CareType

	// no validation rules for LodgingId

	// no validation rules for PlaygroupId

	// no validation rules for State

	// no validation rules for OrderId

	// no validation rules for ServiceType

	if len(errors) > 0 {
		return FulfillmentMultiError(errors)
	}

	return nil
}

// FulfillmentMultiError is an error wrapping multiple validation errors
// returned by Fulfillment.ValidateAll() if the designated constraints aren't met.
type FulfillmentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentMultiError) AllErrors() []error { return m }

// FulfillmentValidationError is the validation error returned by
// Fulfillment.Validate if the designated constraints aren't met.
type FulfillmentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentValidationError) ErrorName() string { return "FulfillmentValidationError" }

// Error satisfies the builtin error interface
func (e FulfillmentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentValidationError{}
