// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/appointment.proto

package fulfillmentpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 预约单状态枚举
// (-- api-linter: core::0216::nesting=disabled
//
//	aip.dev/not-precedent: AppointmentState is appropriate as top-level enum --)
type AppointmentState int32

const (
	// 未指定状态
	AppointmentState_APPOINTMENT_STATE_UNSPECIFIED AppointmentState = 0
	// 未确认
	AppointmentState_APPOINTMENT_STATE_UNCONFIRMED AppointmentState = 1
	// 已确认
	AppointmentState_APPOINTMENT_STATE_CONFIRMED AppointmentState = 2
	// 已完成(check out)
	AppointmentState_APPOINTMENT_STATE_FINISHED AppointmentState = 3
	// 已取消
	AppointmentState_APPOINTMENT_STATE_CANCELED AppointmentState = 4
	// 准备就绪
	AppointmentState_APPOINTMENT_STATE_READY AppointmentState = 5
	// 已入住
	AppointmentState_APPOINTMENT_STATE_CHECKED_IN AppointmentState = 6
)

// Enum value maps for AppointmentState.
var (
	AppointmentState_name = map[int32]string{
		0: "APPOINTMENT_STATE_UNSPECIFIED",
		1: "APPOINTMENT_STATE_UNCONFIRMED",
		2: "APPOINTMENT_STATE_CONFIRMED",
		3: "APPOINTMENT_STATE_FINISHED",
		4: "APPOINTMENT_STATE_CANCELED",
		5: "APPOINTMENT_STATE_READY",
		6: "APPOINTMENT_STATE_CHECKED_IN",
	}
	AppointmentState_value = map[string]int32{
		"APPOINTMENT_STATE_UNSPECIFIED": 0,
		"APPOINTMENT_STATE_UNCONFIRMED": 1,
		"APPOINTMENT_STATE_CONFIRMED":   2,
		"APPOINTMENT_STATE_FINISHED":    3,
		"APPOINTMENT_STATE_CANCELED":    4,
		"APPOINTMENT_STATE_READY":       5,
		"APPOINTMENT_STATE_CHECKED_IN":  6,
	}
)

func (x AppointmentState) Enum() *AppointmentState {
	p := new(AppointmentState)
	*p = x
	return p
}

func (x AppointmentState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[0].Descriptor()
}

func (AppointmentState) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[0]
}

func (x AppointmentState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentState.Descriptor instead.
func (AppointmentState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{0}
}

// 操作模式枚举
type OperationMode int32

const (
	// 未指定操作模式
	OperationMode_OPERATION_MODE_UNSPECIFIED OperationMode = 0
	// 创建
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	OperationMode_OPERATION_MODE_CREATE OperationMode = 1
	// 更新
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	OperationMode_OPERATION_MODE_UPDATE OperationMode = 2
	// 删除
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	OperationMode_OPERATION_MODE_DELETE OperationMode = 3
)

// Enum value maps for OperationMode.
var (
	OperationMode_name = map[int32]string{
		0: "OPERATION_MODE_UNSPECIFIED",
		1: "OPERATION_MODE_CREATE",
		2: "OPERATION_MODE_UPDATE",
		3: "OPERATION_MODE_DELETE",
	}
	OperationMode_value = map[string]int32{
		"OPERATION_MODE_UNSPECIFIED": 0,
		"OPERATION_MODE_CREATE":      1,
		"OPERATION_MODE_UPDATE":      2,
		"OPERATION_MODE_DELETE":      3,
	}
)

func (x OperationMode) Enum() *OperationMode {
	p := new(OperationMode)
	*p = x
	return p
}

func (x OperationMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperationMode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[1].Descriptor()
}

func (OperationMode) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[1]
}

func (x OperationMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperationMode.Descriptor instead.
func (OperationMode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{1}
}

// 预约过滤器
type AppointmentFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务类型（可选，不填就是所有类型）
	CareTypes []CareType `protobuf:"varint,1,rep,packed,name=care_types,json=careTypes,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_types,omitempty"`
	// 宠物ID（可选，不填就是所有pet）
	PetIds        []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppointmentFilter) Reset() {
	*x = AppointmentFilter{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppointmentFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentFilter) ProtoMessage() {}

func (x *AppointmentFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentFilter.ProtoReflect.Descriptor instead.
func (*AppointmentFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{0}
}

func (x *AppointmentFilter) GetCareTypes() []CareType {
	if x != nil {
		return x.CareTypes
	}
	return nil
}

func (x *AppointmentFilter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// 预约单核心数据
type Appointment struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预约ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 预约状态
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: 状态字段需要可写 --)
	Status AppointmentState `protobuf:"varint,5,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.AppointmentState" json:"status,omitempty"`
	// 服务位图
	ServiceItemType int32 `protobuf:"varint,6,opt,name=service_item_type,json=serviceItemType,proto3" json:"service_item_type,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 宠物详情列表
	Pets          []*PetDetail `protobuf:"bytes,10,rep,name=pets,proto3" json:"pets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Appointment) Reset() {
	*x = Appointment{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Appointment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Appointment) ProtoMessage() {}

func (x *Appointment) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Appointment.ProtoReflect.Descriptor instead.
func (*Appointment) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{1}
}

func (x *Appointment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Appointment) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *Appointment) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *Appointment) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Appointment) GetStatus() AppointmentState {
	if x != nil {
		return x.Status
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

func (x *Appointment) GetServiceItemType() int32 {
	if x != nil {
		return x.ServiceItemType
	}
	return 0
}

func (x *Appointment) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *Appointment) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Appointment) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *Appointment) GetPets() []*PetDetail {
	if x != nil {
		return x.Pets
	}
	return nil
}

// 宠物信息
type PetDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 服务实例列表
	Services      []*ServiceInstance `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetDetail) Reset() {
	*x = PetDetail{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetail) ProtoMessage() {}

func (x *PetDetail) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetail.ProtoReflect.Descriptor instead.
func (*PetDetail) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{2}
}

func (x *PetDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetDetail) GetServices() []*ServiceInstance {
	if x != nil {
		return x.Services
	}
	return nil
}

// 服务实例
type ServiceInstance struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	ServiceTemplateId int64 `protobuf:"varint,1,opt,name=service_template_id,json=serviceTemplateId,proto3" json:"service_template_id,omitempty"`
	// 服务开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 服务结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 日期类型
	DateType DateType `protobuf:"varint,4,opt,name=date_type,json=dateType,proto3,enum=backend.proto.fulfillment.v1.DateType" json:"date_type,omitempty"`
	// 子服务实例列表
	SubServiceInstance []*ServiceInstance `protobuf:"bytes,5,rep,name=sub_service_instance,json=subServiceInstance,proto3" json:"sub_service_instance,omitempty"`
	// 服务选项列表
	Options []*ServiceOption `protobuf:"bytes,6,rep,name=options,proto3" json:"options,omitempty"`
	// 服务费用列表
	Charge []*ServiceCharge `protobuf:"bytes,7,rep,name=charge,proto3" json:"charge,omitempty"`
	// 备注列表
	Note []*Note `protobuf:"bytes,8,rep,name=note,proto3" json:"note,omitempty"`
	// 喂养用药列表
	FeedingMedication []*FeedingMedication `protobuf:"bytes,9,rep,name=feeding_medication,json=feedingMedication,proto3" json:"feeding_medication,omitempty"`
	// 服务细则（包含所有执行细节）
	ServiceDetails []*ServiceDetail `protobuf:"bytes,10,rep,name=service_details,json=serviceDetails,proto3" json:"service_details,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ServiceInstance) Reset() {
	*x = ServiceInstance{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInstance) ProtoMessage() {}

func (x *ServiceInstance) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInstance.ProtoReflect.Descriptor instead.
func (*ServiceInstance) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceInstance) GetServiceTemplateId() int64 {
	if x != nil {
		return x.ServiceTemplateId
	}
	return 0
}

func (x *ServiceInstance) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ServiceInstance) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ServiceInstance) GetDateType() DateType {
	if x != nil {
		return x.DateType
	}
	return DateType_DATE_TYPE_UNSPECIFIED
}

func (x *ServiceInstance) GetSubServiceInstance() []*ServiceInstance {
	if x != nil {
		return x.SubServiceInstance
	}
	return nil
}

func (x *ServiceInstance) GetOptions() []*ServiceOption {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *ServiceInstance) GetCharge() []*ServiceCharge {
	if x != nil {
		return x.Charge
	}
	return nil
}

func (x *ServiceInstance) GetNote() []*Note {
	if x != nil {
		return x.Note
	}
	return nil
}

func (x *ServiceInstance) GetFeedingMedication() []*FeedingMedication {
	if x != nil {
		return x.FeedingMedication
	}
	return nil
}

func (x *ServiceInstance) GetServiceDetails() []*ServiceDetail {
	if x != nil {
		return x.ServiceDetails
	}
	return nil
}

// 服务细则
type ServiceDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 员工细则列表
	StaffDetails  []*StaffDetail `protobuf:"bytes,1,rep,name=staff_details,json=staffDetails,proto3" json:"staff_details,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceDetail) Reset() {
	*x = ServiceDetail{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetail) ProtoMessage() {}

func (x *ServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetail.ProtoReflect.Descriptor instead.
func (*ServiceDetail) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{4}
}

func (x *ServiceDetail) GetStaffDetails() []*StaffDetail {
	if x != nil {
		return x.StaffDetails
	}
	return nil
}

// 员工细则
type StaffDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 员工ID
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 员工工作开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 员工工作结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 分配原因
	AssignmentReason *string `protobuf:"bytes,4,opt,name=assignment_reason,json=assignmentReason,proto3,oneof" json:"assignment_reason,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *StaffDetail) Reset() {
	*x = StaffDetail{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaffDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffDetail) ProtoMessage() {}

func (x *StaffDetail) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffDetail.ProtoReflect.Descriptor instead.
func (*StaffDetail) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{5}
}

func (x *StaffDetail) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffDetail) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *StaffDetail) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *StaffDetail) GetAssignmentReason() string {
	if x != nil && x.AssignmentReason != nil {
		return *x.AssignmentReason
	}
	return ""
}

// 服务费用
type ServiceCharge struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 费用名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 费用金额
	Amount float64 `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// 费用描述
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// 费用类型
	ChargeType    int32 `protobuf:"varint,4,opt,name=charge_type,json=chargeType,proto3" json:"charge_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceCharge) Reset() {
	*x = ServiceCharge{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCharge) ProtoMessage() {}

func (x *ServiceCharge) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCharge.ProtoReflect.Descriptor instead.
func (*ServiceCharge) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{6}
}

func (x *ServiceCharge) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCharge) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ServiceCharge) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceCharge) GetChargeType() int32 {
	if x != nil {
		return x.ChargeType
	}
	return 0
}

// 备注
type Note struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 备注类型
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` // 1-alert note,2-ticket comment,3-additional note
	// 备注内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// 员工ID
	StaffId       int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Note) Reset() {
	*x = Note{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Note) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Note) ProtoMessage() {}

func (x *Note) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Note.ProtoReflect.Descriptor instead.
func (*Note) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{7}
}

func (x *Note) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Note) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Note) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// 喂养用药
type FeedingMedication struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 喂养规则
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	FeedingRules string `protobuf:"bytes,1,opt,name=feeding_rules,json=feedingRules,proto3" json:"feeding_rules,omitempty"`
	// 用药规则
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	MedicationRules string `protobuf:"bytes,2,opt,name=medication_rules,json=medicationRules,proto3" json:"medication_rules,omitempty"`
	// 用药计划列表
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Medications   []*MedicationSchedule `protobuf:"bytes,3,rep,name=medications,proto3" json:"medications,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeedingMedication) Reset() {
	*x = FeedingMedication{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeedingMedication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedingMedication) ProtoMessage() {}

func (x *FeedingMedication) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedingMedication.ProtoReflect.Descriptor instead.
func (*FeedingMedication) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{8}
}

func (x *FeedingMedication) GetFeedingRules() string {
	if x != nil {
		return x.FeedingRules
	}
	return ""
}

func (x *FeedingMedication) GetMedicationRules() string {
	if x != nil {
		return x.MedicationRules
	}
	return ""
}

func (x *FeedingMedication) GetMedications() []*MedicationSchedule {
	if x != nil {
		return x.Medications
	}
	return nil
}

// 用药计划
type MedicationSchedule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 药品名称
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: medication is clear and appropriate --)
	Medication string `protobuf:"bytes,1,opt,name=medication,proto3" json:"medication,omitempty"`
	// 剂量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Dosage string `protobuf:"bytes,2,opt,name=dosage,proto3" json:"dosage,omitempty"`
	// 频率(如"每日两次")
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Frequency string `protobuf:"bytes,3,opt,name=frequency,proto3" json:"frequency,omitempty"`
	// 给药方式(如"口服")
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	AdministrationMethod string `protobuf:"bytes,4,opt,name=administration_method,json=administrationMethod,proto3" json:"administration_method,omitempty"`
	// 注意事项
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Notes         string `protobuf:"bytes,5,opt,name=notes,proto3" json:"notes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MedicationSchedule) Reset() {
	*x = MedicationSchedule{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MedicationSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MedicationSchedule) ProtoMessage() {}

func (x *MedicationSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MedicationSchedule.ProtoReflect.Descriptor instead.
func (*MedicationSchedule) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{9}
}

func (x *MedicationSchedule) GetMedication() string {
	if x != nil {
		return x.Medication
	}
	return ""
}

func (x *MedicationSchedule) GetDosage() string {
	if x != nil {
		return x.Dosage
	}
	return ""
}

func (x *MedicationSchedule) GetFrequency() string {
	if x != nil {
		return x.Frequency
	}
	return ""
}

func (x *MedicationSchedule) GetAdministrationMethod() string {
	if x != nil {
		return x.AdministrationMethod
	}
	return ""
}

func (x *MedicationSchedule) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

// 服务选项
type ServiceOption struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务选项模板ID
	ServiceOptionTemplateId int64 `protobuf:"varint,1,opt,name=service_option_template_id,json=serviceOptionTemplateId,proto3" json:"service_option_template_id,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 选项名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 数量
	Quantity int32 `protobuf:"varint,5,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// 价格
	Price float64 `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	// 税费
	Tax float64 `protobuf:"fixed64,7,opt,name=tax,proto3" json:"tax,omitempty"`
	// 每天执行数量
	QuantityPerDay int64 `protobuf:"varint,8,opt,name=quantity_per_day,json=quantityPerDay,proto3" json:"quantity_per_day,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ServiceOption) Reset() {
	*x = ServiceOption{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOption) ProtoMessage() {}

func (x *ServiceOption) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOption.ProtoReflect.Descriptor instead.
func (*ServiceOption) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{10}
}

func (x *ServiceOption) GetServiceOptionTemplateId() int64 {
	if x != nil {
		return x.ServiceOptionTemplateId
	}
	return 0
}

func (x *ServiceOption) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ServiceOption) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ServiceOption) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceOption) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *ServiceOption) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceOption) GetTax() float64 {
	if x != nil {
		return x.Tax
	}
	return 0
}

func (x *ServiceOption) GetQuantityPerDay() int64 {
	if x != nil {
		return x.QuantityPerDay
	}
	return 0
}

// 元数据
type Metadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签映射
	Tags          map[string]string `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{11}
}

func (x *Metadata) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

// 预约单操作结果
type AppointmentOperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作类型
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	OperationType string `protobuf:"bytes,1,opt,name=operation_type,json=operationType,proto3" json:"operation_type,omitempty"` // "start_time", "end_time", "color_code", "status"
	// 操作是否成功
	Success bool `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 操作前的值（如果更新失败，用于回滚）
	OldStartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=old_start_time,json=oldStartTime,proto3,oneof" json:"old_start_time,omitempty"`
	// 操作前的结束时间
	OldEndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=old_end_time,json=oldEndTime,proto3,oneof" json:"old_end_time,omitempty"`
	// 操作前的颜色代码
	OldColorCode *string `protobuf:"bytes,6,opt,name=old_color_code,json=oldColorCode,proto3,oneof" json:"old_color_code,omitempty"`
	// 操作前的状态
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: 状态字段需要可写 --)
	OldStatus *AppointmentState `protobuf:"varint,7,opt,name=old_status,json=oldStatus,proto3,enum=backend.proto.fulfillment.v1.AppointmentState,oneof" json:"old_status,omitempty"`
	// 操作后的值
	NewStartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=new_start_time,json=newStartTime,proto3,oneof" json:"new_start_time,omitempty"`
	// 操作后的结束时间
	NewEndTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=new_end_time,json=newEndTime,proto3,oneof" json:"new_end_time,omitempty"`
	// 操作后的颜色代码
	NewColorCode *string `protobuf:"bytes,10,opt,name=new_color_code,json=newColorCode,proto3,oneof" json:"new_color_code,omitempty"`
	// 操作后的状态
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: 状态字段需要可写 --)
	NewStatus     *AppointmentState `protobuf:"varint,11,opt,name=new_status,json=newStatus,proto3,enum=backend.proto.fulfillment.v1.AppointmentState,oneof" json:"new_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppointmentOperationResult) Reset() {
	*x = AppointmentOperationResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppointmentOperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentOperationResult) ProtoMessage() {}

func (x *AppointmentOperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentOperationResult.ProtoReflect.Descriptor instead.
func (*AppointmentOperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{12}
}

func (x *AppointmentOperationResult) GetOperationType() string {
	if x != nil {
		return x.OperationType
	}
	return ""
}

func (x *AppointmentOperationResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AppointmentOperationResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *AppointmentOperationResult) GetOldStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.OldStartTime
	}
	return nil
}

func (x *AppointmentOperationResult) GetOldEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.OldEndTime
	}
	return nil
}

func (x *AppointmentOperationResult) GetOldColorCode() string {
	if x != nil && x.OldColorCode != nil {
		return *x.OldColorCode
	}
	return ""
}

func (x *AppointmentOperationResult) GetOldStatus() AppointmentState {
	if x != nil && x.OldStatus != nil {
		return *x.OldStatus
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

func (x *AppointmentOperationResult) GetNewStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.NewStartTime
	}
	return nil
}

func (x *AppointmentOperationResult) GetNewEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.NewEndTime
	}
	return nil
}

func (x *AppointmentOperationResult) GetNewColorCode() string {
	if x != nil && x.NewColorCode != nil {
		return *x.NewColorCode
	}
	return ""
}

func (x *AppointmentOperationResult) GetNewStatus() AppointmentState {
	if x != nil && x.NewStatus != nil {
		return *x.NewStatus
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

// 宠物操作结果
type PetOperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 操作模式
	OperationMode OperationMode `protobuf:"varint,2,opt,name=operation_mode,json=operationMode,proto3,enum=backend.proto.fulfillment.v1.OperationMode" json:"operation_mode,omitempty"`
	// 操作是否成功
	Success bool `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 操作详情
	//
	// Types that are valid to be assigned to OperationDetail:
	//
	//	*PetOperationResult_DeleteResult
	//	*PetOperationResult_CreateResult
	//	*PetOperationResult_UpdateResult
	OperationDetail isPetOperationResult_OperationDetail `protobuf_oneof:"operation_detail"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PetOperationResult) Reset() {
	*x = PetOperationResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetOperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetOperationResult) ProtoMessage() {}

func (x *PetOperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetOperationResult.ProtoReflect.Descriptor instead.
func (*PetOperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{13}
}

func (x *PetOperationResult) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetOperationResult) GetOperationMode() OperationMode {
	if x != nil {
		return x.OperationMode
	}
	return OperationMode_OPERATION_MODE_UNSPECIFIED
}

func (x *PetOperationResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PetOperationResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *PetOperationResult) GetOperationDetail() isPetOperationResult_OperationDetail {
	if x != nil {
		return x.OperationDetail
	}
	return nil
}

func (x *PetOperationResult) GetDeleteResult() *PetDeleteResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*PetOperationResult_DeleteResult); ok {
			return x.DeleteResult
		}
	}
	return nil
}

func (x *PetOperationResult) GetCreateResult() *PetCreateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*PetOperationResult_CreateResult); ok {
			return x.CreateResult
		}
	}
	return nil
}

func (x *PetOperationResult) GetUpdateResult() *PetUpdateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*PetOperationResult_UpdateResult); ok {
			return x.UpdateResult
		}
	}
	return nil
}

type isPetOperationResult_OperationDetail interface {
	isPetOperationResult_OperationDetail()
}

type PetOperationResult_DeleteResult struct {
	// 删除结果
	DeleteResult *PetDeleteResult `protobuf:"bytes,5,opt,name=delete_result,json=deleteResult,proto3,oneof"`
}

type PetOperationResult_CreateResult struct {
	// 创建结果
	CreateResult *PetCreateResult `protobuf:"bytes,6,opt,name=create_result,json=createResult,proto3,oneof"`
}

type PetOperationResult_UpdateResult struct {
	// 更新结果
	UpdateResult *PetUpdateResult `protobuf:"bytes,7,opt,name=update_result,json=updateResult,proto3,oneof"`
}

func (*PetOperationResult_DeleteResult) isPetOperationResult_OperationDetail() {}

func (*PetOperationResult_CreateResult) isPetOperationResult_OperationDetail() {}

func (*PetOperationResult_UpdateResult) isPetOperationResult_OperationDetail() {}

// 宠物删除结果
type PetDeleteResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否删除成功
	Deleted bool `protobuf:"varint,1,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// 删除的服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	DeletedServiceCount int32 `protobuf:"varint,2,opt,name=deleted_service_count,json=deletedServiceCount,proto3" json:"deleted_service_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PetDeleteResult) Reset() {
	*x = PetDeleteResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetDeleteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDeleteResult) ProtoMessage() {}

func (x *PetDeleteResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDeleteResult.ProtoReflect.Descriptor instead.
func (*PetDeleteResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{14}
}

func (x *PetDeleteResult) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *PetDeleteResult) GetDeletedServiceCount() int32 {
	if x != nil {
		return x.DeletedServiceCount
	}
	return 0
}

// 宠物创建结果
type PetCreateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 新宠物ID
	NewPetId int64 `protobuf:"varint,1,opt,name=new_pet_id,json=newPetId,proto3" json:"new_pet_id,omitempty"`
	// 创建的服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	CreatedServiceCount int32 `protobuf:"varint,2,opt,name=created_service_count,json=createdServiceCount,proto3" json:"created_service_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PetCreateResult) Reset() {
	*x = PetCreateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetCreateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCreateResult) ProtoMessage() {}

func (x *PetCreateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCreateResult.ProtoReflect.Descriptor instead.
func (*PetCreateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{15}
}

func (x *PetCreateResult) GetNewPetId() int64 {
	if x != nil {
		return x.NewPetId
	}
	return 0
}

func (x *PetCreateResult) GetCreatedServiceCount() int32 {
	if x != nil {
		return x.CreatedServiceCount
	}
	return 0
}

// 宠物更新结果
type PetUpdateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 更新后的宠物
	UpdatedPet *PetDetail `protobuf:"bytes,1,opt,name=updated_pet,json=updatedPet,proto3" json:"updated_pet,omitempty"`
	// 更新的服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	UpdatedServiceCount int32 `protobuf:"varint,2,opt,name=updated_service_count,json=updatedServiceCount,proto3" json:"updated_service_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PetUpdateResult) Reset() {
	*x = PetUpdateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetUpdateResult) ProtoMessage() {}

func (x *PetUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetUpdateResult.ProtoReflect.Descriptor instead.
func (*PetUpdateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{16}
}

func (x *PetUpdateResult) GetUpdatedPet() *PetDetail {
	if x != nil {
		return x.UpdatedPet
	}
	return nil
}

func (x *PetUpdateResult) GetUpdatedServiceCount() int32 {
	if x != nil {
		return x.UpdatedServiceCount
	}
	return 0
}

// 服务操作结果
type ServiceOperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务实例ID
	ServiceInstanceId int64 `protobuf:"varint,1,opt,name=service_instance_id,json=serviceInstanceId,proto3" json:"service_instance_id,omitempty"`
	// 宠物ID
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 操作模式
	OperationMode OperationMode `protobuf:"varint,3,opt,name=operation_mode,json=operationMode,proto3,enum=backend.proto.fulfillment.v1.OperationMode" json:"operation_mode,omitempty"`
	// 操作是否成功
	Success bool `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 操作详情
	//
	// Types that are valid to be assigned to OperationDetail:
	//
	//	*ServiceOperationResult_DeleteResult
	//	*ServiceOperationResult_CreateResult
	//	*ServiceOperationResult_UpdateResult
	OperationDetail isServiceOperationResult_OperationDetail `protobuf_oneof:"operation_detail"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServiceOperationResult) Reset() {
	*x = ServiceOperationResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceOperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOperationResult) ProtoMessage() {}

func (x *ServiceOperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOperationResult.ProtoReflect.Descriptor instead.
func (*ServiceOperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{17}
}

func (x *ServiceOperationResult) GetServiceInstanceId() int64 {
	if x != nil {
		return x.ServiceInstanceId
	}
	return 0
}

func (x *ServiceOperationResult) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ServiceOperationResult) GetOperationMode() OperationMode {
	if x != nil {
		return x.OperationMode
	}
	return OperationMode_OPERATION_MODE_UNSPECIFIED
}

func (x *ServiceOperationResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ServiceOperationResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ServiceOperationResult) GetOperationDetail() isServiceOperationResult_OperationDetail {
	if x != nil {
		return x.OperationDetail
	}
	return nil
}

func (x *ServiceOperationResult) GetDeleteResult() *ServiceDeleteResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*ServiceOperationResult_DeleteResult); ok {
			return x.DeleteResult
		}
	}
	return nil
}

func (x *ServiceOperationResult) GetCreateResult() *ServiceCreateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*ServiceOperationResult_CreateResult); ok {
			return x.CreateResult
		}
	}
	return nil
}

func (x *ServiceOperationResult) GetUpdateResult() *ServiceUpdateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*ServiceOperationResult_UpdateResult); ok {
			return x.UpdateResult
		}
	}
	return nil
}

type isServiceOperationResult_OperationDetail interface {
	isServiceOperationResult_OperationDetail()
}

type ServiceOperationResult_DeleteResult struct {
	// 删除结果
	DeleteResult *ServiceDeleteResult `protobuf:"bytes,6,opt,name=delete_result,json=deleteResult,proto3,oneof"`
}

type ServiceOperationResult_CreateResult struct {
	// 创建结果
	CreateResult *ServiceCreateResult `protobuf:"bytes,7,opt,name=create_result,json=createResult,proto3,oneof"`
}

type ServiceOperationResult_UpdateResult struct {
	// 更新结果
	UpdateResult *ServiceUpdateResult `protobuf:"bytes,8,opt,name=update_result,json=updateResult,proto3,oneof"`
}

func (*ServiceOperationResult_DeleteResult) isServiceOperationResult_OperationDetail() {}

func (*ServiceOperationResult_CreateResult) isServiceOperationResult_OperationDetail() {}

func (*ServiceOperationResult_UpdateResult) isServiceOperationResult_OperationDetail() {}

// 服务删除结果
type ServiceDeleteResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否删除成功
	Deleted bool `protobuf:"varint,1,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// 删除的附加服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	DeletedOptionCount int32 `protobuf:"varint,2,opt,name=deleted_option_count,json=deletedOptionCount,proto3" json:"deleted_option_count,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ServiceDeleteResult) Reset() {
	*x = ServiceDeleteResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceDeleteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDeleteResult) ProtoMessage() {}

func (x *ServiceDeleteResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDeleteResult.ProtoReflect.Descriptor instead.
func (*ServiceDeleteResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{18}
}

func (x *ServiceDeleteResult) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *ServiceDeleteResult) GetDeletedOptionCount() int32 {
	if x != nil {
		return x.DeletedOptionCount
	}
	return 0
}

// 服务创建结果
type ServiceCreateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 新服务实例ID
	NewServiceInstanceId int64 `protobuf:"varint,1,opt,name=new_service_instance_id,json=newServiceInstanceId,proto3" json:"new_service_instance_id,omitempty"`
	// 创建的附加服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	CreatedOptionCount int32 `protobuf:"varint,2,opt,name=created_option_count,json=createdOptionCount,proto3" json:"created_option_count,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ServiceCreateResult) Reset() {
	*x = ServiceCreateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCreateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCreateResult) ProtoMessage() {}

func (x *ServiceCreateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCreateResult.ProtoReflect.Descriptor instead.
func (*ServiceCreateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{19}
}

func (x *ServiceCreateResult) GetNewServiceInstanceId() int64 {
	if x != nil {
		return x.NewServiceInstanceId
	}
	return 0
}

func (x *ServiceCreateResult) GetCreatedOptionCount() int32 {
	if x != nil {
		return x.CreatedOptionCount
	}
	return 0
}

// 服务更新结果
type ServiceUpdateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 更新后的服务
	UpdatedService *ServiceInstance `protobuf:"bytes,1,opt,name=updated_service,json=updatedService,proto3" json:"updated_service,omitempty"`
	// 更新的附加服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	UpdatedOptionCount int32 `protobuf:"varint,2,opt,name=updated_option_count,json=updatedOptionCount,proto3" json:"updated_option_count,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ServiceUpdateResult) Reset() {
	*x = ServiceUpdateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceUpdateResult) ProtoMessage() {}

func (x *ServiceUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceUpdateResult.ProtoReflect.Descriptor instead.
func (*ServiceUpdateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{20}
}

func (x *ServiceUpdateResult) GetUpdatedService() *ServiceInstance {
	if x != nil {
		return x.UpdatedService
	}
	return nil
}

func (x *ServiceUpdateResult) GetUpdatedOptionCount() int32 {
	if x != nil {
		return x.UpdatedOptionCount
	}
	return 0
}

// 附加服务操作结果
type OptionOperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务选项ID
	ServiceOptionId int64 `protobuf:"varint,1,opt,name=service_option_id,json=serviceOptionId,proto3" json:"service_option_id,omitempty"`
	// 服务实例ID
	ServiceInstanceId int64 `protobuf:"varint,2,opt,name=service_instance_id,json=serviceInstanceId,proto3" json:"service_instance_id,omitempty"`
	// 操作模式
	OperationMode OperationMode `protobuf:"varint,3,opt,name=operation_mode,json=operationMode,proto3,enum=backend.proto.fulfillment.v1.OperationMode" json:"operation_mode,omitempty"`
	// 操作是否成功
	Success bool `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 操作详情
	//
	// Types that are valid to be assigned to OperationDetail:
	//
	//	*OptionOperationResult_DeleteResult
	//	*OptionOperationResult_CreateResult
	//	*OptionOperationResult_UpdateResult
	OperationDetail isOptionOperationResult_OperationDetail `protobuf_oneof:"operation_detail"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *OptionOperationResult) Reset() {
	*x = OptionOperationResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptionOperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionOperationResult) ProtoMessage() {}

func (x *OptionOperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionOperationResult.ProtoReflect.Descriptor instead.
func (*OptionOperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{21}
}

func (x *OptionOperationResult) GetServiceOptionId() int64 {
	if x != nil {
		return x.ServiceOptionId
	}
	return 0
}

func (x *OptionOperationResult) GetServiceInstanceId() int64 {
	if x != nil {
		return x.ServiceInstanceId
	}
	return 0
}

func (x *OptionOperationResult) GetOperationMode() OperationMode {
	if x != nil {
		return x.OperationMode
	}
	return OperationMode_OPERATION_MODE_UNSPECIFIED
}

func (x *OptionOperationResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *OptionOperationResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *OptionOperationResult) GetOperationDetail() isOptionOperationResult_OperationDetail {
	if x != nil {
		return x.OperationDetail
	}
	return nil
}

func (x *OptionOperationResult) GetDeleteResult() *OptionDeleteResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*OptionOperationResult_DeleteResult); ok {
			return x.DeleteResult
		}
	}
	return nil
}

func (x *OptionOperationResult) GetCreateResult() *OptionCreateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*OptionOperationResult_CreateResult); ok {
			return x.CreateResult
		}
	}
	return nil
}

func (x *OptionOperationResult) GetUpdateResult() *OptionUpdateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*OptionOperationResult_UpdateResult); ok {
			return x.UpdateResult
		}
	}
	return nil
}

type isOptionOperationResult_OperationDetail interface {
	isOptionOperationResult_OperationDetail()
}

type OptionOperationResult_DeleteResult struct {
	// 删除结果
	DeleteResult *OptionDeleteResult `protobuf:"bytes,6,opt,name=delete_result,json=deleteResult,proto3,oneof"`
}

type OptionOperationResult_CreateResult struct {
	// 创建结果
	CreateResult *OptionCreateResult `protobuf:"bytes,7,opt,name=create_result,json=createResult,proto3,oneof"`
}

type OptionOperationResult_UpdateResult struct {
	// 更新结果
	UpdateResult *OptionUpdateResult `protobuf:"bytes,8,opt,name=update_result,json=updateResult,proto3,oneof"`
}

func (*OptionOperationResult_DeleteResult) isOptionOperationResult_OperationDetail() {}

func (*OptionOperationResult_CreateResult) isOptionOperationResult_OperationDetail() {}

func (*OptionOperationResult_UpdateResult) isOptionOperationResult_OperationDetail() {}

// 选项删除结果
type OptionDeleteResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否删除成功
	Deleted       bool `protobuf:"varint,1,opt,name=deleted,proto3" json:"deleted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OptionDeleteResult) Reset() {
	*x = OptionDeleteResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptionDeleteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionDeleteResult) ProtoMessage() {}

func (x *OptionDeleteResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionDeleteResult.ProtoReflect.Descriptor instead.
func (*OptionDeleteResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{22}
}

func (x *OptionDeleteResult) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

// 选项创建结果
type OptionCreateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 新服务选项ID
	NewServiceOptionId int64 `protobuf:"varint,1,opt,name=new_service_option_id,json=newServiceOptionId,proto3" json:"new_service_option_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *OptionCreateResult) Reset() {
	*x = OptionCreateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptionCreateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionCreateResult) ProtoMessage() {}

func (x *OptionCreateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionCreateResult.ProtoReflect.Descriptor instead.
func (*OptionCreateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{23}
}

func (x *OptionCreateResult) GetNewServiceOptionId() int64 {
	if x != nil {
		return x.NewServiceOptionId
	}
	return 0
}

// 选项更新结果
type OptionUpdateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 更新后的选项
	UpdatedOption *ServiceOption `protobuf:"bytes,1,opt,name=updated_option,json=updatedOption,proto3" json:"updated_option,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OptionUpdateResult) Reset() {
	*x = OptionUpdateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptionUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionUpdateResult) ProtoMessage() {}

func (x *OptionUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionUpdateResult.ProtoReflect.Descriptor instead.
func (*OptionUpdateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{24}
}

func (x *OptionUpdateResult) GetUpdatedOption() *ServiceOption {
	if x != nil {
		return x.UpdatedOption
	}
	return nil
}

var File_backend_proto_fulfillment_v1_appointment_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_appointment_proto_rawDesc = "" +
	"\n" +
	".backend/proto/fulfillment/v1/appointment.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17validate/validate.proto\x1a)backend/proto/fulfillment/v1/common.proto\"s\n" +
	"\x11AppointmentFilter\x12E\n" +
	"\n" +
	"care_types\x18\x01 \x03(\x0e2&.backend.proto.fulfillment.v1.CareTypeR\tcareTypes\x12\x17\n" +
	"\apet_ids\x18\x02 \x03(\x03R\x06petIds\"\xd2\x03\n" +
	"\vAppointment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x12&\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12\x1f\n" +
	"\vcustomer_id\x18\x04 \x01(\x03R\n" +
	"customerId\x12F\n" +
	"\x06status\x18\x05 \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateR\x06status\x12*\n" +
	"\x11service_item_type\x18\x06 \x01(\x05R\x0fserviceItemType\x12\x1d\n" +
	"\n" +
	"color_code\x18\a \x01(\tR\tcolorCode\x129\n" +
	"\n" +
	"start_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12;\n" +
	"\x04pets\x18\n" +
	" \x03(\v2'.backend.proto.fulfillment.v1.PetDetailR\x04pets\"m\n" +
	"\tPetDetail\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12I\n" +
	"\bservices\x18\x02 \x03(\v2-.backend.proto.fulfillment.v1.ServiceInstanceR\bservices\"\xd3\x05\n" +
	"\x0fServiceInstance\x12.\n" +
	"\x13service_template_id\x18\x01 \x01(\x03R\x11serviceTemplateId\x129\n" +
	"\n" +
	"start_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12C\n" +
	"\tdate_type\x18\x04 \x01(\x0e2&.backend.proto.fulfillment.v1.DateTypeR\bdateType\x12_\n" +
	"\x14sub_service_instance\x18\x05 \x03(\v2-.backend.proto.fulfillment.v1.ServiceInstanceR\x12subServiceInstance\x12E\n" +
	"\aoptions\x18\x06 \x03(\v2+.backend.proto.fulfillment.v1.ServiceOptionR\aoptions\x12C\n" +
	"\x06charge\x18\a \x03(\v2+.backend.proto.fulfillment.v1.ServiceChargeR\x06charge\x126\n" +
	"\x04note\x18\b \x03(\v2\".backend.proto.fulfillment.v1.NoteR\x04note\x12^\n" +
	"\x12feeding_medication\x18\t \x03(\v2/.backend.proto.fulfillment.v1.FeedingMedicationR\x11feedingMedication\x12T\n" +
	"\x0fservice_details\x18\n" +
	" \x03(\v2+.backend.proto.fulfillment.v1.ServiceDetailR\x0eserviceDetails\"_\n" +
	"\rServiceDetail\x12N\n" +
	"\rstaff_details\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.StaffDetailR\fstaffDetails\"\xe2\x01\n" +
	"\vStaffDetail\x12\x19\n" +
	"\bstaff_id\x18\x01 \x01(\x03R\astaffId\x129\n" +
	"\n" +
	"start_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x120\n" +
	"\x11assignment_reason\x18\x04 \x01(\tH\x00R\x10assignmentReason\x88\x01\x01B\x14\n" +
	"\x12_assignment_reason\"~\n" +
	"\rServiceCharge\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1f\n" +
	"\vcharge_type\x18\x04 \x01(\x05R\n" +
	"chargeType\"O\n" +
	"\x04Note\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\"\xb7\x01\n" +
	"\x11FeedingMedication\x12#\n" +
	"\rfeeding_rules\x18\x01 \x01(\tR\ffeedingRules\x12)\n" +
	"\x10medication_rules\x18\x02 \x01(\tR\x0fmedicationRules\x12R\n" +
	"\vmedications\x18\x03 \x03(\v20.backend.proto.fulfillment.v1.MedicationScheduleR\vmedications\"\xb5\x01\n" +
	"\x12MedicationSchedule\x12\x1e\n" +
	"\n" +
	"medication\x18\x01 \x01(\tR\n" +
	"medication\x12\x16\n" +
	"\x06dosage\x18\x02 \x01(\tR\x06dosage\x12\x1c\n" +
	"\tfrequency\x18\x03 \x01(\tR\tfrequency\x123\n" +
	"\x15administration_method\x18\x04 \x01(\tR\x14administrationMethod\x12\x14\n" +
	"\x05notes\x18\x05 \x01(\tR\x05notes\"\xc0\x02\n" +
	"\rServiceOption\x12;\n" +
	"\x1aservice_option_template_id\x18\x01 \x01(\x03R\x17serviceOptionTemplateId\x129\n" +
	"\n" +
	"start_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1a\n" +
	"\bquantity\x18\x05 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05price\x18\x06 \x01(\x01R\x05price\x12\x10\n" +
	"\x03tax\x18\a \x01(\x01R\x03tax\x12(\n" +
	"\x10quantity_per_day\x18\b \x01(\x03R\x0equantityPerDay\"\x89\x01\n" +
	"\bMetadata\x12D\n" +
	"\x04tags\x18\x01 \x03(\v20.backend.proto.fulfillment.v1.Metadata.TagsEntryR\x04tags\x1a7\n" +
	"\tTagsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xa0\x06\n" +
	"\x1aAppointmentOperationResult\x12%\n" +
	"\x0eoperation_type\x18\x01 \x01(\tR\roperationType\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x12E\n" +
	"\x0eold_start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\foldStartTime\x88\x01\x01\x12A\n" +
	"\fold_end_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\n" +
	"oldEndTime\x88\x01\x01\x12)\n" +
	"\x0eold_color_code\x18\x06 \x01(\tH\x02R\foldColorCode\x88\x01\x01\x12R\n" +
	"\n" +
	"old_status\x18\a \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateH\x03R\toldStatus\x88\x01\x01\x12E\n" +
	"\x0enew_start_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x04R\fnewStartTime\x88\x01\x01\x12A\n" +
	"\fnew_end_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x05R\n" +
	"newEndTime\x88\x01\x01\x12)\n" +
	"\x0enew_color_code\x18\n" +
	" \x01(\tH\x06R\fnewColorCode\x88\x01\x01\x12R\n" +
	"\n" +
	"new_status\x18\v \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateH\aR\tnewStatus\x88\x01\x01B\x11\n" +
	"\x0f_old_start_timeB\x0f\n" +
	"\r_old_end_timeB\x11\n" +
	"\x0f_old_color_codeB\r\n" +
	"\v_old_statusB\x11\n" +
	"\x0f_new_start_timeB\x0f\n" +
	"\r_new_end_timeB\x11\n" +
	"\x0f_new_color_codeB\r\n" +
	"\v_new_status\"\xd4\x03\n" +
	"\x12PetOperationResult\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12R\n" +
	"\x0eoperation_mode\x18\x02 \x01(\x0e2+.backend.proto.fulfillment.v1.OperationModeR\roperationMode\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\x12T\n" +
	"\rdelete_result\x18\x05 \x01(\v2-.backend.proto.fulfillment.v1.PetDeleteResultH\x00R\fdeleteResult\x12T\n" +
	"\rcreate_result\x18\x06 \x01(\v2-.backend.proto.fulfillment.v1.PetCreateResultH\x00R\fcreateResult\x12T\n" +
	"\rupdate_result\x18\a \x01(\v2-.backend.proto.fulfillment.v1.PetUpdateResultH\x00R\fupdateResultB\x12\n" +
	"\x10operation_detail\"_\n" +
	"\x0fPetDeleteResult\x12\x18\n" +
	"\adeleted\x18\x01 \x01(\bR\adeleted\x122\n" +
	"\x15deleted_service_count\x18\x02 \x01(\x05R\x13deletedServiceCount\"c\n" +
	"\x0fPetCreateResult\x12\x1c\n" +
	"\n" +
	"new_pet_id\x18\x01 \x01(\x03R\bnewPetId\x122\n" +
	"\x15created_service_count\x18\x02 \x01(\x05R\x13createdServiceCount\"\x8f\x01\n" +
	"\x0fPetUpdateResult\x12H\n" +
	"\vupdated_pet\x18\x01 \x01(\v2'.backend.proto.fulfillment.v1.PetDetailR\n" +
	"updatedPet\x122\n" +
	"\x15updated_service_count\x18\x02 \x01(\x05R\x13updatedServiceCount\"\x94\x04\n" +
	"\x16ServiceOperationResult\x12.\n" +
	"\x13service_instance_id\x18\x01 \x01(\x03R\x11serviceInstanceId\x12\x15\n" +
	"\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12R\n" +
	"\x0eoperation_mode\x18\x03 \x01(\x0e2+.backend.proto.fulfillment.v1.OperationModeR\roperationMode\x12\x18\n" +
	"\asuccess\x18\x04 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x05 \x01(\tR\ferrorMessage\x12X\n" +
	"\rdelete_result\x18\x06 \x01(\v21.backend.proto.fulfillment.v1.ServiceDeleteResultH\x00R\fdeleteResult\x12X\n" +
	"\rcreate_result\x18\a \x01(\v21.backend.proto.fulfillment.v1.ServiceCreateResultH\x00R\fcreateResult\x12X\n" +
	"\rupdate_result\x18\b \x01(\v21.backend.proto.fulfillment.v1.ServiceUpdateResultH\x00R\fupdateResultB\x12\n" +
	"\x10operation_detail\"a\n" +
	"\x13ServiceDeleteResult\x12\x18\n" +
	"\adeleted\x18\x01 \x01(\bR\adeleted\x120\n" +
	"\x14deleted_option_count\x18\x02 \x01(\x05R\x12deletedOptionCount\"~\n" +
	"\x13ServiceCreateResult\x125\n" +
	"\x17new_service_instance_id\x18\x01 \x01(\x03R\x14newServiceInstanceId\x120\n" +
	"\x14created_option_count\x18\x02 \x01(\x05R\x12createdOptionCount\"\x9f\x01\n" +
	"\x13ServiceUpdateResult\x12V\n" +
	"\x0fupdated_service\x18\x01 \x01(\v2-.backend.proto.fulfillment.v1.ServiceInstanceR\x0eupdatedService\x120\n" +
	"\x14updated_option_count\x18\x02 \x01(\x05R\x12updatedOptionCount\"\xa5\x04\n" +
	"\x15OptionOperationResult\x12*\n" +
	"\x11service_option_id\x18\x01 \x01(\x03R\x0fserviceOptionId\x12.\n" +
	"\x13service_instance_id\x18\x02 \x01(\x03R\x11serviceInstanceId\x12R\n" +
	"\x0eoperation_mode\x18\x03 \x01(\x0e2+.backend.proto.fulfillment.v1.OperationModeR\roperationMode\x12\x18\n" +
	"\asuccess\x18\x04 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x05 \x01(\tR\ferrorMessage\x12W\n" +
	"\rdelete_result\x18\x06 \x01(\v20.backend.proto.fulfillment.v1.OptionDeleteResultH\x00R\fdeleteResult\x12W\n" +
	"\rcreate_result\x18\a \x01(\v20.backend.proto.fulfillment.v1.OptionCreateResultH\x00R\fcreateResult\x12W\n" +
	"\rupdate_result\x18\b \x01(\v20.backend.proto.fulfillment.v1.OptionUpdateResultH\x00R\fupdateResultB\x12\n" +
	"\x10operation_detail\".\n" +
	"\x12OptionDeleteResult\x12\x18\n" +
	"\adeleted\x18\x01 \x01(\bR\adeleted\"G\n" +
	"\x12OptionCreateResult\x121\n" +
	"\x15new_service_option_id\x18\x01 \x01(\x03R\x12newServiceOptionId\"h\n" +
	"\x12OptionUpdateResult\x12R\n" +
	"\x0eupdated_option\x18\x01 \x01(\v2+.backend.proto.fulfillment.v1.ServiceOptionR\rupdatedOption*\xf8\x01\n" +
	"\x10AppointmentState\x12!\n" +
	"\x1dAPPOINTMENT_STATE_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dAPPOINTMENT_STATE_UNCONFIRMED\x10\x01\x12\x1f\n" +
	"\x1bAPPOINTMENT_STATE_CONFIRMED\x10\x02\x12\x1e\n" +
	"\x1aAPPOINTMENT_STATE_FINISHED\x10\x03\x12\x1e\n" +
	"\x1aAPPOINTMENT_STATE_CANCELED\x10\x04\x12\x1b\n" +
	"\x17APPOINTMENT_STATE_READY\x10\x05\x12 \n" +
	"\x1cAPPOINTMENT_STATE_CHECKED_IN\x10\x06*\x80\x01\n" +
	"\rOperationMode\x12\x1e\n" +
	"\x1aOPERATION_MODE_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15OPERATION_MODE_CREATE\x10\x01\x12\x19\n" +
	"\x15OPERATION_MODE_UPDATE\x10\x02\x12\x19\n" +
	"\x15OPERATION_MODE_DELETE\x10\x03Bt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_appointment_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_appointment_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_appointment_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_appointment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_appointment_proto_rawDesc), len(file_backend_proto_fulfillment_v1_appointment_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_appointment_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_fulfillment_v1_appointment_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_backend_proto_fulfillment_v1_appointment_proto_goTypes = []any{
	(AppointmentState)(0),              // 0: backend.proto.fulfillment.v1.AppointmentState
	(OperationMode)(0),                 // 1: backend.proto.fulfillment.v1.OperationMode
	(*AppointmentFilter)(nil),          // 2: backend.proto.fulfillment.v1.AppointmentFilter
	(*Appointment)(nil),                // 3: backend.proto.fulfillment.v1.Appointment
	(*PetDetail)(nil),                  // 4: backend.proto.fulfillment.v1.PetDetail
	(*ServiceInstance)(nil),            // 5: backend.proto.fulfillment.v1.ServiceInstance
	(*ServiceDetail)(nil),              // 6: backend.proto.fulfillment.v1.ServiceDetail
	(*StaffDetail)(nil),                // 7: backend.proto.fulfillment.v1.StaffDetail
	(*ServiceCharge)(nil),              // 8: backend.proto.fulfillment.v1.ServiceCharge
	(*Note)(nil),                       // 9: backend.proto.fulfillment.v1.Note
	(*FeedingMedication)(nil),          // 10: backend.proto.fulfillment.v1.FeedingMedication
	(*MedicationSchedule)(nil),         // 11: backend.proto.fulfillment.v1.MedicationSchedule
	(*ServiceOption)(nil),              // 12: backend.proto.fulfillment.v1.ServiceOption
	(*Metadata)(nil),                   // 13: backend.proto.fulfillment.v1.Metadata
	(*AppointmentOperationResult)(nil), // 14: backend.proto.fulfillment.v1.AppointmentOperationResult
	(*PetOperationResult)(nil),         // 15: backend.proto.fulfillment.v1.PetOperationResult
	(*PetDeleteResult)(nil),            // 16: backend.proto.fulfillment.v1.PetDeleteResult
	(*PetCreateResult)(nil),            // 17: backend.proto.fulfillment.v1.PetCreateResult
	(*PetUpdateResult)(nil),            // 18: backend.proto.fulfillment.v1.PetUpdateResult
	(*ServiceOperationResult)(nil),     // 19: backend.proto.fulfillment.v1.ServiceOperationResult
	(*ServiceDeleteResult)(nil),        // 20: backend.proto.fulfillment.v1.ServiceDeleteResult
	(*ServiceCreateResult)(nil),        // 21: backend.proto.fulfillment.v1.ServiceCreateResult
	(*ServiceUpdateResult)(nil),        // 22: backend.proto.fulfillment.v1.ServiceUpdateResult
	(*OptionOperationResult)(nil),      // 23: backend.proto.fulfillment.v1.OptionOperationResult
	(*OptionDeleteResult)(nil),         // 24: backend.proto.fulfillment.v1.OptionDeleteResult
	(*OptionCreateResult)(nil),         // 25: backend.proto.fulfillment.v1.OptionCreateResult
	(*OptionUpdateResult)(nil),         // 26: backend.proto.fulfillment.v1.OptionUpdateResult
	nil,                                // 27: backend.proto.fulfillment.v1.Metadata.TagsEntry
	(CareType)(0),                      // 28: backend.proto.fulfillment.v1.CareType
	(*timestamppb.Timestamp)(nil),      // 29: google.protobuf.Timestamp
	(DateType)(0),                      // 30: backend.proto.fulfillment.v1.DateType
}
var file_backend_proto_fulfillment_v1_appointment_proto_depIdxs = []int32{
	28, // 0: backend.proto.fulfillment.v1.AppointmentFilter.care_types:type_name -> backend.proto.fulfillment.v1.CareType
	0,  // 1: backend.proto.fulfillment.v1.Appointment.status:type_name -> backend.proto.fulfillment.v1.AppointmentState
	29, // 2: backend.proto.fulfillment.v1.Appointment.start_time:type_name -> google.protobuf.Timestamp
	29, // 3: backend.proto.fulfillment.v1.Appointment.end_time:type_name -> google.protobuf.Timestamp
	4,  // 4: backend.proto.fulfillment.v1.Appointment.pets:type_name -> backend.proto.fulfillment.v1.PetDetail
	5,  // 5: backend.proto.fulfillment.v1.PetDetail.services:type_name -> backend.proto.fulfillment.v1.ServiceInstance
	29, // 6: backend.proto.fulfillment.v1.ServiceInstance.start_time:type_name -> google.protobuf.Timestamp
	29, // 7: backend.proto.fulfillment.v1.ServiceInstance.end_time:type_name -> google.protobuf.Timestamp
	30, // 8: backend.proto.fulfillment.v1.ServiceInstance.date_type:type_name -> backend.proto.fulfillment.v1.DateType
	5,  // 9: backend.proto.fulfillment.v1.ServiceInstance.sub_service_instance:type_name -> backend.proto.fulfillment.v1.ServiceInstance
	12, // 10: backend.proto.fulfillment.v1.ServiceInstance.options:type_name -> backend.proto.fulfillment.v1.ServiceOption
	8,  // 11: backend.proto.fulfillment.v1.ServiceInstance.charge:type_name -> backend.proto.fulfillment.v1.ServiceCharge
	9,  // 12: backend.proto.fulfillment.v1.ServiceInstance.note:type_name -> backend.proto.fulfillment.v1.Note
	10, // 13: backend.proto.fulfillment.v1.ServiceInstance.feeding_medication:type_name -> backend.proto.fulfillment.v1.FeedingMedication
	6,  // 14: backend.proto.fulfillment.v1.ServiceInstance.service_details:type_name -> backend.proto.fulfillment.v1.ServiceDetail
	7,  // 15: backend.proto.fulfillment.v1.ServiceDetail.staff_details:type_name -> backend.proto.fulfillment.v1.StaffDetail
	29, // 16: backend.proto.fulfillment.v1.StaffDetail.start_time:type_name -> google.protobuf.Timestamp
	29, // 17: backend.proto.fulfillment.v1.StaffDetail.end_time:type_name -> google.protobuf.Timestamp
	11, // 18: backend.proto.fulfillment.v1.FeedingMedication.medications:type_name -> backend.proto.fulfillment.v1.MedicationSchedule
	29, // 19: backend.proto.fulfillment.v1.ServiceOption.start_time:type_name -> google.protobuf.Timestamp
	29, // 20: backend.proto.fulfillment.v1.ServiceOption.end_time:type_name -> google.protobuf.Timestamp
	27, // 21: backend.proto.fulfillment.v1.Metadata.tags:type_name -> backend.proto.fulfillment.v1.Metadata.TagsEntry
	29, // 22: backend.proto.fulfillment.v1.AppointmentOperationResult.old_start_time:type_name -> google.protobuf.Timestamp
	29, // 23: backend.proto.fulfillment.v1.AppointmentOperationResult.old_end_time:type_name -> google.protobuf.Timestamp
	0,  // 24: backend.proto.fulfillment.v1.AppointmentOperationResult.old_status:type_name -> backend.proto.fulfillment.v1.AppointmentState
	29, // 25: backend.proto.fulfillment.v1.AppointmentOperationResult.new_start_time:type_name -> google.protobuf.Timestamp
	29, // 26: backend.proto.fulfillment.v1.AppointmentOperationResult.new_end_time:type_name -> google.protobuf.Timestamp
	0,  // 27: backend.proto.fulfillment.v1.AppointmentOperationResult.new_status:type_name -> backend.proto.fulfillment.v1.AppointmentState
	1,  // 28: backend.proto.fulfillment.v1.PetOperationResult.operation_mode:type_name -> backend.proto.fulfillment.v1.OperationMode
	16, // 29: backend.proto.fulfillment.v1.PetOperationResult.delete_result:type_name -> backend.proto.fulfillment.v1.PetDeleteResult
	17, // 30: backend.proto.fulfillment.v1.PetOperationResult.create_result:type_name -> backend.proto.fulfillment.v1.PetCreateResult
	18, // 31: backend.proto.fulfillment.v1.PetOperationResult.update_result:type_name -> backend.proto.fulfillment.v1.PetUpdateResult
	4,  // 32: backend.proto.fulfillment.v1.PetUpdateResult.updated_pet:type_name -> backend.proto.fulfillment.v1.PetDetail
	1,  // 33: backend.proto.fulfillment.v1.ServiceOperationResult.operation_mode:type_name -> backend.proto.fulfillment.v1.OperationMode
	20, // 34: backend.proto.fulfillment.v1.ServiceOperationResult.delete_result:type_name -> backend.proto.fulfillment.v1.ServiceDeleteResult
	21, // 35: backend.proto.fulfillment.v1.ServiceOperationResult.create_result:type_name -> backend.proto.fulfillment.v1.ServiceCreateResult
	22, // 36: backend.proto.fulfillment.v1.ServiceOperationResult.update_result:type_name -> backend.proto.fulfillment.v1.ServiceUpdateResult
	5,  // 37: backend.proto.fulfillment.v1.ServiceUpdateResult.updated_service:type_name -> backend.proto.fulfillment.v1.ServiceInstance
	1,  // 38: backend.proto.fulfillment.v1.OptionOperationResult.operation_mode:type_name -> backend.proto.fulfillment.v1.OperationMode
	24, // 39: backend.proto.fulfillment.v1.OptionOperationResult.delete_result:type_name -> backend.proto.fulfillment.v1.OptionDeleteResult
	25, // 40: backend.proto.fulfillment.v1.OptionOperationResult.create_result:type_name -> backend.proto.fulfillment.v1.OptionCreateResult
	26, // 41: backend.proto.fulfillment.v1.OptionOperationResult.update_result:type_name -> backend.proto.fulfillment.v1.OptionUpdateResult
	12, // 42: backend.proto.fulfillment.v1.OptionUpdateResult.updated_option:type_name -> backend.proto.fulfillment.v1.ServiceOption
	43, // [43:43] is the sub-list for method output_type
	43, // [43:43] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_appointment_proto_init() }
func file_backend_proto_fulfillment_v1_appointment_proto_init() {
	if File_backend_proto_fulfillment_v1_appointment_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_common_proto_init()
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[5].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[13].OneofWrappers = []any{
		(*PetOperationResult_DeleteResult)(nil),
		(*PetOperationResult_CreateResult)(nil),
		(*PetOperationResult_UpdateResult)(nil),
	}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[17].OneofWrappers = []any{
		(*ServiceOperationResult_DeleteResult)(nil),
		(*ServiceOperationResult_CreateResult)(nil),
		(*ServiceOperationResult_UpdateResult)(nil),
	}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[21].OneofWrappers = []any{
		(*OptionOperationResult_DeleteResult)(nil),
		(*OptionOperationResult_CreateResult)(nil),
		(*OptionOperationResult_UpdateResult)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_appointment_proto_rawDesc), len(file_backend_proto_fulfillment_v1_appointment_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_appointment_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_appointment_proto_depIdxs,
		EnumInfos:         file_backend_proto_fulfillment_v1_appointment_proto_enumTypes,
		MessageInfos:      file_backend_proto_fulfillment_v1_appointment_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_appointment_proto = out.File
	file_backend_proto_fulfillment_v1_appointment_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_appointment_proto_depIdxs = nil
}
