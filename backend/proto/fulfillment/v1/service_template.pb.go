// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/service_template.proto

package fulfillmentpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务属性值
type ServiceAttributeValue struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 属性值ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 工厂ID
	TemplateId int64 `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// 属性名称
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: attribute is clear and appropriate --)
	Attribute string `protobuf:"bytes,3,opt,name=attribute,proto3" json:"attribute,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*ServiceAttributeValue_TextValue
	//	*ServiceAttributeValue_IntValue
	//	*ServiceAttributeValue_DecimalValue
	//	*ServiceAttributeValue_BoolValue
	//	*ServiceAttributeValue_JsonValue
	Value         isServiceAttributeValue_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceAttributeValue) Reset() {
	*x = ServiceAttributeValue{}
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAttributeValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAttributeValue) ProtoMessage() {}

func (x *ServiceAttributeValue) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAttributeValue.ProtoReflect.Descriptor instead.
func (*ServiceAttributeValue) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceAttributeValue) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceAttributeValue) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *ServiceAttributeValue) GetAttribute() string {
	if x != nil {
		return x.Attribute
	}
	return ""
}

func (x *ServiceAttributeValue) GetValue() isServiceAttributeValue_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ServiceAttributeValue) GetTextValue() string {
	if x != nil {
		if x, ok := x.Value.(*ServiceAttributeValue_TextValue); ok {
			return x.TextValue
		}
	}
	return ""
}

func (x *ServiceAttributeValue) GetIntValue() int64 {
	if x != nil {
		if x, ok := x.Value.(*ServiceAttributeValue_IntValue); ok {
			return x.IntValue
		}
	}
	return 0
}

func (x *ServiceAttributeValue) GetDecimalValue() float64 {
	if x != nil {
		if x, ok := x.Value.(*ServiceAttributeValue_DecimalValue); ok {
			return x.DecimalValue
		}
	}
	return 0
}

func (x *ServiceAttributeValue) GetBoolValue() bool {
	if x != nil {
		if x, ok := x.Value.(*ServiceAttributeValue_BoolValue); ok {
			return x.BoolValue
		}
	}
	return false
}

func (x *ServiceAttributeValue) GetJsonValue() string {
	if x != nil {
		if x, ok := x.Value.(*ServiceAttributeValue_JsonValue); ok {
			return x.JsonValue
		}
	}
	return ""
}

type isServiceAttributeValue_Value interface {
	isServiceAttributeValue_Value()
}

type ServiceAttributeValue_TextValue struct {
	// 文本值
	TextValue string `protobuf:"bytes,4,opt,name=text_value,json=textValue,proto3,oneof"`
}

type ServiceAttributeValue_IntValue struct {
	// 整数值
	IntValue int64 `protobuf:"varint,5,opt,name=int_value,json=intValue,proto3,oneof"`
}

type ServiceAttributeValue_DecimalValue struct {
	// 小数值
	DecimalValue float64 `protobuf:"fixed64,6,opt,name=decimal_value,json=decimalValue,proto3,oneof"`
}

type ServiceAttributeValue_BoolValue struct {
	// 布尔值
	BoolValue bool `protobuf:"varint,7,opt,name=bool_value,json=boolValue,proto3,oneof"`
}

type ServiceAttributeValue_JsonValue struct {
	// JSON字符串值
	JsonValue string `protobuf:"bytes,8,opt,name=json_value,json=jsonValue,proto3,oneof"`
}

func (*ServiceAttributeValue_TextValue) isServiceAttributeValue_Value() {}

func (*ServiceAttributeValue_IntValue) isServiceAttributeValue_Value() {}

func (*ServiceAttributeValue_DecimalValue) isServiceAttributeValue_Value() {}

func (*ServiceAttributeValue_BoolValue) isServiceAttributeValue_Value() {}

func (*ServiceAttributeValue_JsonValue) isServiceAttributeValue_Value() {}

// 服务可用性配置
type ServiceAvailability struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否停用
	Inactive bool `protobuf:"varint,1,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// 是否支持所有品种
	IsAllBreed bool `protobuf:"varint,2,opt,name=is_all_breed,json=isAllBreed,proto3" json:"is_all_breed,omitempty"`
	// 是否支持所有体重范围
	IsAllWeightRange bool `protobuf:"varint,3,opt,name=is_all_weight_range,json=isAllWeightRange,proto3" json:"is_all_weight_range,omitempty"`
	// 体重下限
	WeightDownLimit float64 `protobuf:"fixed64,4,opt,name=weight_down_limit,json=weightDownLimit,proto3" json:"weight_down_limit,omitempty"`
	// 体重上限
	WeightUpLimit float64 `protobuf:"fixed64,5,opt,name=weight_up_limit,json=weightUpLimit,proto3" json:"weight_up_limit,omitempty"`
	// 是否支持所有毛发过滤
	IsAllCoatFilter bool `protobuf:"varint,6,opt,name=is_all_coat_filter,json=isAllCoatFilter,proto3" json:"is_all_coat_filter,omitempty"`
	// 服务过滤
	ServiceFilter bool `protobuf:"varint,7,opt,name=service_filter,json=serviceFilter,proto3" json:"service_filter,omitempty"`
	// 是否支持所有宠物尺寸
	IsAllPetSize bool `protobuf:"varint,8,opt,name=is_all_pet_size,json=isAllPetSize,proto3" json:"is_all_pet_size,omitempty"`
	// 允许的宠物尺寸列表
	AllowedPetSizeList string `protobuf:"bytes,9,opt,name=allowed_pet_size_list,json=allowedPetSizeList,proto3" json:"allowed_pet_size_list,omitempty"`
	// 是否支持所有住宿
	IsAllLodging  bool `protobuf:"varint,10,opt,name=is_all_lodging,json=isAllLodging,proto3" json:"is_all_lodging,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceAvailability) Reset() {
	*x = ServiceAvailability{}
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAvailability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAvailability) ProtoMessage() {}

func (x *ServiceAvailability) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAvailability.ProtoReflect.Descriptor instead.
func (*ServiceAvailability) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceAvailability) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ServiceAvailability) GetIsAllBreed() bool {
	if x != nil {
		return x.IsAllBreed
	}
	return false
}

func (x *ServiceAvailability) GetIsAllWeightRange() bool {
	if x != nil {
		return x.IsAllWeightRange
	}
	return false
}

func (x *ServiceAvailability) GetWeightDownLimit() float64 {
	if x != nil {
		return x.WeightDownLimit
	}
	return 0
}

func (x *ServiceAvailability) GetWeightUpLimit() float64 {
	if x != nil {
		return x.WeightUpLimit
	}
	return 0
}

func (x *ServiceAvailability) GetIsAllCoatFilter() bool {
	if x != nil {
		return x.IsAllCoatFilter
	}
	return false
}

func (x *ServiceAvailability) GetServiceFilter() bool {
	if x != nil {
		return x.ServiceFilter
	}
	return false
}

func (x *ServiceAvailability) GetIsAllPetSize() bool {
	if x != nil {
		return x.IsAllPetSize
	}
	return false
}

func (x *ServiceAvailability) GetAllowedPetSizeList() string {
	if x != nil {
		return x.AllowedPetSizeList
	}
	return ""
}

func (x *ServiceAvailability) GetIsAllLodging() bool {
	if x != nil {
		return x.IsAllLodging
	}
	return false
}

// 定价信息
type Pricing struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 税费ID
	TaxId int64 `protobuf:"varint,1,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// 价格
	Price float64 `protobuf:"fixed64,2,opt,name=price,proto3" json:"price,omitempty"`
	// 价格单位
	PriceUnit int32 `protobuf:"varint,3,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit,omitempty"`
	// 是否添加到佣金
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: add_commission is clear and concise --)
	AddCommission bool `protobuf:"varint,4,opt,name=add_commission,json=addCommission,proto3" json:"add_commission,omitempty"`
	// 是否允许小费
	CanTip        bool `protobuf:"varint,5,opt,name=can_tip,json=canTip,proto3" json:"can_tip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pricing) Reset() {
	*x = Pricing{}
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pricing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pricing) ProtoMessage() {}

func (x *Pricing) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pricing.ProtoReflect.Descriptor instead.
func (*Pricing) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_proto_rawDescGZIP(), []int{2}
}

func (x *Pricing) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *Pricing) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Pricing) GetPriceUnit() int32 {
	if x != nil {
		return x.PriceUnit
	}
	return 0
}

func (x *Pricing) GetAddCommission() bool {
	if x != nil {
		return x.AddCommission
	}
	return false
}

func (x *Pricing) GetCanTip() bool {
	if x != nil {
		return x.CanTip
	}
	return false
}

// 服务工厂
type ServiceTemplate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务工厂ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 分类ID
	CategoryId int64 `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 类型ID
	CareType CareType `protobuf:"varint,5,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// 服务名称
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// 服务描述
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// 服务时长(分钟)
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Duration int32 `protobuf:"varint,8,opt,name=duration,proto3" json:"duration,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,9,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 是否活跃
	IsActive bool `protobuf:"varint,10,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// 价格
	Price float64 `protobuf:"fixed64,11,opt,name=price,proto3" json:"price,omitempty"`
	// 货币代码
	// (-- api-linter: core::0143::standardized-codes=disabled
	//
	//	aip.dev/not-precedent: currency_code is clear and appropriate --)
	CurrencyCode string `protobuf:"bytes,12,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,13,opt,name=sort,proto3" json:"sort,omitempty"`
	// 图片URL列表
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Images []string `protobuf:"bytes,14,rep,name=images,proto3" json:"images,omitempty"`
	// 来源:1-MoeGo平台;2-企业中心
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Source int32 `protobuf:"varint,15,opt,name=source,proto3" json:"source,omitempty"`
	// 属性值列表
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	AttributeValues []*ServiceAttributeValue `protobuf:"bytes,18,rep,name=attribute_values,json=attributeValues,proto3" json:"attribute_values,omitempty"`
	// 附加选项列表
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Options       []*ServiceOptionTemplate `protobuf:"bytes,19,rep,name=options,proto3" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceTemplate) Reset() {
	*x = ServiceTemplate{}
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTemplate) ProtoMessage() {}

func (x *ServiceTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTemplate.ProtoReflect.Descriptor instead.
func (*ServiceTemplate) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceTemplate) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceTemplate) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ServiceTemplate) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ServiceTemplate) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceTemplate) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *ServiceTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceTemplate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceTemplate) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceTemplate) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ServiceTemplate) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *ServiceTemplate) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceTemplate) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *ServiceTemplate) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceTemplate) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceTemplate) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *ServiceTemplate) GetAttributeValues() []*ServiceAttributeValue {
	if x != nil {
		return x.AttributeValues
	}
	return nil
}

func (x *ServiceTemplate) GetOptions() []*ServiceOptionTemplate {
	if x != nil {
		return x.Options
	}
	return nil
}

// 服务选项工厂
type ServiceOptionTemplate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 选项名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 选项描述
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// 选项价格
	Price float64 `protobuf:"fixed64,3,opt,name=price,proto3" json:"price,omitempty"`
	// 选项时长
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Duration int32 `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	// 选项排序
	Sort int32 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
	// 分类ID
	CategoryId int64 `protobuf:"varint,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 是否激活
	IsActive bool `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// 是否必需
	IsRequired bool `protobuf:"varint,8,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// 最大可选数量
	MaxSelectable int32 `protobuf:"varint,9,opt,name=max_selectable,json=maxSelectable,proto3" json:"max_selectable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceOptionTemplate) Reset() {
	*x = ServiceOptionTemplate{}
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceOptionTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOptionTemplate) ProtoMessage() {}

func (x *ServiceOptionTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOptionTemplate.ProtoReflect.Descriptor instead.
func (*ServiceOptionTemplate) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_service_template_proto_rawDescGZIP(), []int{4}
}

func (x *ServiceOptionTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceOptionTemplate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceOptionTemplate) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceOptionTemplate) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceOptionTemplate) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceOptionTemplate) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceOptionTemplate) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *ServiceOptionTemplate) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *ServiceOptionTemplate) GetMaxSelectable() int32 {
	if x != nil {
		return x.MaxSelectable
	}
	return 0
}

var File_backend_proto_fulfillment_v1_service_template_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_service_template_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/fulfillment/v1/service_template.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17validate/validate.proto\x1a)backend/proto/fulfillment/v1/common.proto\"\x98\x02\n" +
	"\x15ServiceAttributeValue\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vtemplate_id\x18\x02 \x01(\x03R\n" +
	"templateId\x12\x1c\n" +
	"\tattribute\x18\x03 \x01(\tR\tattribute\x12\x1f\n" +
	"\n" +
	"text_value\x18\x04 \x01(\tH\x00R\ttextValue\x12\x1d\n" +
	"\tint_value\x18\x05 \x01(\x03H\x00R\bintValue\x12%\n" +
	"\rdecimal_value\x18\x06 \x01(\x01H\x00R\fdecimalValue\x12\x1f\n" +
	"\n" +
	"bool_value\x18\a \x01(\bH\x00R\tboolValue\x12\x1f\n" +
	"\n" +
	"json_value\x18\b \x01(\tH\x00R\tjsonValueB\a\n" +
	"\x05value\"\xaa\x03\n" +
	"\x13ServiceAvailability\x12\x1a\n" +
	"\binactive\x18\x01 \x01(\bR\binactive\x12 \n" +
	"\fis_all_breed\x18\x02 \x01(\bR\n" +
	"isAllBreed\x12-\n" +
	"\x13is_all_weight_range\x18\x03 \x01(\bR\x10isAllWeightRange\x12*\n" +
	"\x11weight_down_limit\x18\x04 \x01(\x01R\x0fweightDownLimit\x12&\n" +
	"\x0fweight_up_limit\x18\x05 \x01(\x01R\rweightUpLimit\x12+\n" +
	"\x12is_all_coat_filter\x18\x06 \x01(\bR\x0fisAllCoatFilter\x12%\n" +
	"\x0eservice_filter\x18\a \x01(\bR\rserviceFilter\x12%\n" +
	"\x0fis_all_pet_size\x18\b \x01(\bR\fisAllPetSize\x121\n" +
	"\x15allowed_pet_size_list\x18\t \x01(\tR\x12allowedPetSizeList\x12$\n" +
	"\x0eis_all_lodging\x18\n" +
	" \x01(\bR\fisAllLodging\"\x95\x01\n" +
	"\aPricing\x12\x15\n" +
	"\x06tax_id\x18\x01 \x01(\x03R\x05taxId\x12\x14\n" +
	"\x05price\x18\x02 \x01(\x01R\x05price\x12\x1d\n" +
	"\n" +
	"price_unit\x18\x03 \x01(\x05R\tpriceUnit\x12%\n" +
	"\x0eadd_commission\x18\x04 \x01(\bR\raddCommission\x12\x17\n" +
	"\acan_tip\x18\x05 \x01(\bR\x06canTip\"\x83\x05\n" +
	"\x0fServiceTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x12\x1f\n" +
	"\vcategory_id\x18\x04 \x01(\x03R\n" +
	"categoryId\x12C\n" +
	"\tcare_type\x18\x05 \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeR\bcareType\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\a \x01(\tR\vdescription\x12\x1a\n" +
	"\bduration\x18\b \x01(\x05R\bduration\x12\x1d\n" +
	"\n" +
	"color_code\x18\t \x01(\tR\tcolorCode\x12\x1b\n" +
	"\tis_active\x18\n" +
	" \x01(\bR\bisActive\x12\x14\n" +
	"\x05price\x18\v \x01(\x01R\x05price\x12#\n" +
	"\rcurrency_code\x18\f \x01(\tR\fcurrencyCode\x12\x12\n" +
	"\x04sort\x18\r \x01(\x05R\x04sort\x12\x16\n" +
	"\x06images\x18\x0e \x03(\tR\x06images\x12\x16\n" +
	"\x06source\x18\x0f \x01(\x05R\x06source\x12^\n" +
	"\x10attribute_values\x18\x12 \x03(\v23.backend.proto.fulfillment.v1.ServiceAttributeValueR\x0fattributeValues\x12M\n" +
	"\aoptions\x18\x13 \x03(\v23.backend.proto.fulfillment.v1.ServiceOptionTemplateR\aoptions\"\x99\x02\n" +
	"\x15ServiceOptionTemplate\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x01R\x05price\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x05R\bduration\x12\x12\n" +
	"\x04sort\x18\x05 \x01(\x05R\x04sort\x12\x1f\n" +
	"\vcategory_id\x18\x06 \x01(\x03R\n" +
	"categoryId\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\x12\x1f\n" +
	"\vis_required\x18\b \x01(\bR\n" +
	"isRequired\x12%\n" +
	"\x0emax_selectable\x18\t \x01(\x05R\rmaxSelectableBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_service_template_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_service_template_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_service_template_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_service_template_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_service_template_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_service_template_proto_rawDesc), len(file_backend_proto_fulfillment_v1_service_template_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_service_template_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_service_template_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_backend_proto_fulfillment_v1_service_template_proto_goTypes = []any{
	(*ServiceAttributeValue)(nil), // 0: backend.proto.fulfillment.v1.ServiceAttributeValue
	(*ServiceAvailability)(nil),   // 1: backend.proto.fulfillment.v1.ServiceAvailability
	(*Pricing)(nil),               // 2: backend.proto.fulfillment.v1.Pricing
	(*ServiceTemplate)(nil),       // 3: backend.proto.fulfillment.v1.ServiceTemplate
	(*ServiceOptionTemplate)(nil), // 4: backend.proto.fulfillment.v1.ServiceOptionTemplate
	(CareType)(0),                 // 5: backend.proto.fulfillment.v1.CareType
}
var file_backend_proto_fulfillment_v1_service_template_proto_depIdxs = []int32{
	5, // 0: backend.proto.fulfillment.v1.ServiceTemplate.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	0, // 1: backend.proto.fulfillment.v1.ServiceTemplate.attribute_values:type_name -> backend.proto.fulfillment.v1.ServiceAttributeValue
	4, // 2: backend.proto.fulfillment.v1.ServiceTemplate.options:type_name -> backend.proto.fulfillment.v1.ServiceOptionTemplate
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_service_template_proto_init() }
func file_backend_proto_fulfillment_v1_service_template_proto_init() {
	if File_backend_proto_fulfillment_v1_service_template_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_common_proto_init()
	file_backend_proto_fulfillment_v1_service_template_proto_msgTypes[0].OneofWrappers = []any{
		(*ServiceAttributeValue_TextValue)(nil),
		(*ServiceAttributeValue_IntValue)(nil),
		(*ServiceAttributeValue_DecimalValue)(nil),
		(*ServiceAttributeValue_BoolValue)(nil),
		(*ServiceAttributeValue_JsonValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_service_template_proto_rawDesc), len(file_backend_proto_fulfillment_v1_service_template_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_service_template_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_service_template_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_service_template_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_service_template_proto = out.File
	file_backend_proto_fulfillment_v1_service_template_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_service_template_proto_depIdxs = nil
}
