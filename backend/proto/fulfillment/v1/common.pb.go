// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/common.proto

package fulfillmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 排序方式
type SortType int32

const (
	// SORT_TYPE_UNSPECIFIED 默认排序类型
	SortType_SORT_TYPE_UNSPECIFIED SortType = 0
	// SORT_TYPE_START_TIME 按服务开始时间
	SortType_SORT_TYPE_START_TIME SortType = 1
)

// Enum value maps for SortType.
var (
	SortType_name = map[int32]string{
		0: "SORT_TYPE_UNSPECIFIED",
		1: "SORT_TYPE_START_TIME",
	}
	SortType_value = map[string]int32{
		"SORT_TYPE_UNSPECIFIED": 0,
		"SORT_TYPE_START_TIME":  1,
	}
)

func (x SortType) Enum() *SortType {
	p := new(SortType)
	*p = x
	return p
}

func (x SortType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[0].Descriptor()
}

func (SortType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[0]
}

func (x SortType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortType.Descriptor instead.
func (SortType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{0}
}

// 日期类型
type DateType int32

const (
	// 未指定日期类型
	DateType_DATE_TYPE_UNSPECIFIED DateType = 0
	// 每日日期
	DateType_DATE_TYPE_DATE_EVERYDAY DateType = 1
	// 特定日期
	DateType_DATE_TYPE_SPECIFIC_DATE DateType = 2
	// 日期点
	DateType_DATE_TYPE_DATE_POINT DateType = 3
	// 每日包含退房日
	DateType_DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY DateType = 4
	// 每日排除入住日
	DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY DateType = 5
	// 最后一天
	DateType_DATE_TYPE_LAST_DAY DateType = 6
	// 第一天
	DateType_DATE_TYPE_FIRST_DAY DateType = 7
)

// Enum value maps for DateType.
var (
	DateType_name = map[int32]string{
		0: "DATE_TYPE_UNSPECIFIED",
		1: "DATE_TYPE_DATE_EVERYDAY",
		2: "DATE_TYPE_SPECIFIC_DATE",
		3: "DATE_TYPE_DATE_POINT",
		4: "DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY",
		5: "DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY",
		6: "DATE_TYPE_LAST_DAY",
		7: "DATE_TYPE_FIRST_DAY",
	}
	DateType_value = map[string]int32{
		"DATE_TYPE_UNSPECIFIED":                   0,
		"DATE_TYPE_DATE_EVERYDAY":                 1,
		"DATE_TYPE_SPECIFIC_DATE":                 2,
		"DATE_TYPE_DATE_POINT":                    3,
		"DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY": 4,
		"DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY":   5,
		"DATE_TYPE_LAST_DAY":                      6,
		"DATE_TYPE_FIRST_DAY":                     7,
	}
)

func (x DateType) Enum() *DateType {
	p := new(DateType)
	*p = x
	return p
}

func (x DateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DateType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[1].Descriptor()
}

func (DateType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[1]
}

func (x DateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DateType.Descriptor instead.
func (DateType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{1}
}

// 服务类型
type CareType int32

const (
	// 未知护理类型
	CareType_CARE_TYPE_UNSPECIFIED CareType = 0
	// GROOMING类型
	CareType_CARE_TYPE_GROOMING CareType = 1
	// BOARDING类型
	CareType_CARE_TYPE_BOARDING CareType = 2
	// DAYCARE类型
	CareType_CARE_TYPE_DAYCARE CareType = 3
	// EVALUATION类型
	CareType_CARE_TYPE_EVALUATION CareType = 4
	// DOG_WALKING类型
	CareType_CARE_TYPE_DOG_WALKING CareType = 5
	// GROUP_CLASS类型
	CareType_CARE_TYPE_GROUP_CLASS CareType = 6
)

// Enum value maps for CareType.
var (
	CareType_name = map[int32]string{
		0: "CARE_TYPE_UNSPECIFIED",
		1: "CARE_TYPE_GROOMING",
		2: "CARE_TYPE_BOARDING",
		3: "CARE_TYPE_DAYCARE",
		4: "CARE_TYPE_EVALUATION",
		5: "CARE_TYPE_DOG_WALKING",
		6: "CARE_TYPE_GROUP_CLASS",
	}
	CareType_value = map[string]int32{
		"CARE_TYPE_UNSPECIFIED": 0,
		"CARE_TYPE_GROOMING":    1,
		"CARE_TYPE_BOARDING":    2,
		"CARE_TYPE_DAYCARE":     3,
		"CARE_TYPE_EVALUATION":  4,
		"CARE_TYPE_DOG_WALKING": 5,
		"CARE_TYPE_GROUP_CLASS": 6,
	}
)

func (x CareType) Enum() *CareType {
	p := new(CareType)
	*p = x
	return p
}

func (x CareType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CareType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[2].Descriptor()
}

func (CareType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[2]
}

func (x CareType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CareType.Descriptor instead.
func (CareType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{2}
}

// 服务类型枚举
type ServiceType int32

const (
	// 未指定的服务类型
	ServiceType_SERVICE_TYPE_UNSPECIFIED ServiceType = 0
	// 服务类型
	ServiceType_SERVICE_TYPE_SERVICE ServiceType = 1
	// 选项类型
	ServiceType_SERVICE_TYPE_OPTION ServiceType = 2
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNSPECIFIED",
		1: "SERVICE_TYPE_SERVICE",
		2: "SERVICE_TYPE_OPTION",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNSPECIFIED": 0,
		"SERVICE_TYPE_SERVICE":     1,
		"SERVICE_TYPE_OPTION":      2,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[3].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[3]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{3}
}

// 分页信息
type PaginationRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 偏移量，默认0
	Offset int32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// 每页数量，默认200
	Limit         int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRef) Reset() {
	*x = PaginationRef{}
	mi := &file_backend_proto_fulfillment_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRef) ProtoMessage() {}

func (x *PaginationRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRef.ProtoReflect.Descriptor instead.
func (*PaginationRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *PaginationRef) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *PaginationRef) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

var File_backend_proto_fulfillment_v1_common_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_common_proto_rawDesc = "" +
	"\n" +
	")backend/proto/fulfillment/v1/common.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"=\n" +
	"\rPaginationRef\x12\x16\n" +
	"\x06offset\x18\x01 \x01(\x05R\x06offset\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit*?\n" +
	"\bSortType\x12\x19\n" +
	"\x15SORT_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14SORT_TYPE_START_TIME\x10\x01*\x82\x02\n" +
	"\bDateType\x12\x19\n" +
	"\x15DATE_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17DATE_TYPE_DATE_EVERYDAY\x10\x01\x12\x1b\n" +
	"\x17DATE_TYPE_SPECIFIC_DATE\x10\x02\x12\x18\n" +
	"\x14DATE_TYPE_DATE_POINT\x10\x03\x12+\n" +
	"'DATE_TYPE_EVERYDAY_INCLUDE_CHECKOUT_DAY\x10\x04\x12)\n" +
	"%DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY\x10\x05\x12\x16\n" +
	"\x12DATE_TYPE_LAST_DAY\x10\x06\x12\x17\n" +
	"\x13DATE_TYPE_FIRST_DAY\x10\a*\xbc\x01\n" +
	"\bCareType\x12\x19\n" +
	"\x15CARE_TYPE_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12CARE_TYPE_GROOMING\x10\x01\x12\x16\n" +
	"\x12CARE_TYPE_BOARDING\x10\x02\x12\x15\n" +
	"\x11CARE_TYPE_DAYCARE\x10\x03\x12\x18\n" +
	"\x14CARE_TYPE_EVALUATION\x10\x04\x12\x19\n" +
	"\x15CARE_TYPE_DOG_WALKING\x10\x05\x12\x19\n" +
	"\x15CARE_TYPE_GROUP_CLASS\x10\x06*^\n" +
	"\vServiceType\x12\x1c\n" +
	"\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14SERVICE_TYPE_SERVICE\x10\x01\x12\x17\n" +
	"\x13SERVICE_TYPE_OPTION\x10\x02Bt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_common_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_common_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_common_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_common_proto_rawDesc), len(file_backend_proto_fulfillment_v1_common_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_common_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_backend_proto_fulfillment_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_fulfillment_v1_common_proto_goTypes = []any{
	(SortType)(0),         // 0: backend.proto.fulfillment.v1.SortType
	(DateType)(0),         // 1: backend.proto.fulfillment.v1.DateType
	(CareType)(0),         // 2: backend.proto.fulfillment.v1.CareType
	(ServiceType)(0),      // 3: backend.proto.fulfillment.v1.ServiceType
	(*PaginationRef)(nil), // 4: backend.proto.fulfillment.v1.PaginationRef
}
var file_backend_proto_fulfillment_v1_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_common_proto_init() }
func file_backend_proto_fulfillment_v1_common_proto_init() {
	if File_backend_proto_fulfillment_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_common_proto_rawDesc), len(file_backend_proto_fulfillment_v1_common_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_common_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_common_proto_depIdxs,
		EnumInfos:         file_backend_proto_fulfillment_v1_common_proto_enumTypes,
		MessageInfos:      file_backend_proto_fulfillment_v1_common_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_common_proto = out.File
	file_backend_proto_fulfillment_v1_common_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_common_proto_depIdxs = nil
}
