// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/fulfillment/v1/service_template_service.proto

package fulfillmentpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ServiceTemplateService_CreateServiceType_FullMethodName         = "/backend.proto.fulfillment.v1.ServiceTemplateService/CreateServiceType"
	ServiceTemplateService_CreateServiceTemplate_FullMethodName     = "/backend.proto.fulfillment.v1.ServiceTemplateService/CreateServiceTemplate"
	ServiceTemplateService_SetServiceAttributeValues_FullMethodName = "/backend.proto.fulfillment.v1.ServiceTemplateService/SetServiceAttributeValues"
	ServiceTemplateService_SetServiceTypeAttribute_FullMethodName   = "/backend.proto.fulfillment.v1.ServiceTemplateService/SetServiceTypeAttribute"
	ServiceTemplateService_GetServiceTemplateByIds_FullMethodName   = "/backend.proto.fulfillment.v1.ServiceTemplateService/GetServiceTemplateByIds"
	ServiceTemplateService_GetServiceTypeAttributes_FullMethodName  = "/backend.proto.fulfillment.v1.ServiceTemplateService/GetServiceTypeAttributes"
	ServiceTemplateService_GetServiceAttributeValues_FullMethodName = "/backend.proto.fulfillment.v1.ServiceTemplateService/GetServiceAttributeValues"
	ServiceTemplateService_ListServiceTemplate_FullMethodName       = "/backend.proto.fulfillment.v1.ServiceTemplateService/ListServiceTemplate"
)

// ServiceTemplateServiceClient is the client API for ServiceTemplateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 服务模板服务
type ServiceTemplateServiceClient interface {
	// 创建服务类型
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateServiceTypeResponse is appropriate for this use case --)
	CreateServiceType(ctx context.Context, in *CreateServiceTypeRequest, opts ...grpc.CallOption) (*CreateServiceTypeResponse, error)
	// 创建一个服务
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateServiceTemplateResponse is appropriate for this use case --)
	CreateServiceTemplate(ctx context.Context, in *CreateServiceTemplateRequest, opts ...grpc.CallOption) (*CreateServiceTemplateResponse, error)
	// 设置service的attribute值
	// (-- api-linter: core::0134::synonyms=disabled
	//
	//	aip.dev/not-precedent: Set is more appropriate for this use case --)
	SetServiceAttributeValues(ctx context.Context, in *SetServiceAttributeValuesRequest, opts ...grpc.CallOption) (*SetServiceAttributeValuesResponse, error)
	// 设置service_type的attribute
	// (-- api-linter: core::0134::synonyms=disabled
	//
	//	aip.dev/not-precedent: Set is more appropriate for this use case --)
	SetServiceTypeAttribute(ctx context.Context, in *SetServiceTypeAttributeRequest, opts ...grpc.CallOption) (*SetServiceTypeAttributeResponse, error)
	// 通过 repeated service_template_id 获取 ServiceTemplate 列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceTemplateByIdsResponse is appropriate for this use case --)
	//
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetServiceTemplateByIds is appropriate for this use case --)
	GetServiceTemplateByIds(ctx context.Context, in *GetServiceTemplateByIdsRequest, opts ...grpc.CallOption) (*GetServiceTemplateByIdsResponse, error)
	// 通过 repeated service_type_id 获取 ServiceTypeAttribute（属性值）列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceTypeAttributesResponse is appropriate for this use case --)
	GetServiceTypeAttributes(ctx context.Context, in *GetServiceTypeAttributesRequest, opts ...grpc.CallOption) (*GetServiceTypeAttributesResponse, error)
	// 通过 repeated service_template_id 获取 ServiceAttributeValues 列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceAttributeValuesResponse is appropriate for this use case --)
	GetServiceAttributeValues(ctx context.Context, in *GetServiceAttributeValuesRequest, opts ...grpc.CallOption) (*GetServiceAttributeValuesResponse, error)
	// 列出服务模板
	// (-- api-linter: core::0158::request-page_token-field=disabled
	// aip.dev/not-precedent: 不需要page_token --)
	ListServiceTemplate(ctx context.Context, in *ListServiceTemplateRequest, opts ...grpc.CallOption) (*ListServiceTemplateResponse, error)
}

type serviceTemplateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceTemplateServiceClient(cc grpc.ClientConnInterface) ServiceTemplateServiceClient {
	return &serviceTemplateServiceClient{cc}
}

func (c *serviceTemplateServiceClient) CreateServiceType(ctx context.Context, in *CreateServiceTypeRequest, opts ...grpc.CallOption) (*CreateServiceTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceTypeResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_CreateServiceType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) CreateServiceTemplate(ctx context.Context, in *CreateServiceTemplateRequest, opts ...grpc.CallOption) (*CreateServiceTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceTemplateResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_CreateServiceTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) SetServiceAttributeValues(ctx context.Context, in *SetServiceAttributeValuesRequest, opts ...grpc.CallOption) (*SetServiceAttributeValuesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetServiceAttributeValuesResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_SetServiceAttributeValues_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) SetServiceTypeAttribute(ctx context.Context, in *SetServiceTypeAttributeRequest, opts ...grpc.CallOption) (*SetServiceTypeAttributeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetServiceTypeAttributeResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_SetServiceTypeAttribute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) GetServiceTemplateByIds(ctx context.Context, in *GetServiceTemplateByIdsRequest, opts ...grpc.CallOption) (*GetServiceTemplateByIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceTemplateByIdsResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_GetServiceTemplateByIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) GetServiceTypeAttributes(ctx context.Context, in *GetServiceTypeAttributesRequest, opts ...grpc.CallOption) (*GetServiceTypeAttributesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceTypeAttributesResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_GetServiceTypeAttributes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) GetServiceAttributeValues(ctx context.Context, in *GetServiceAttributeValuesRequest, opts ...grpc.CallOption) (*GetServiceAttributeValuesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceAttributeValuesResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_GetServiceAttributeValues_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) ListServiceTemplate(ctx context.Context, in *ListServiceTemplateRequest, opts ...grpc.CallOption) (*ListServiceTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServiceTemplateResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_ListServiceTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceTemplateServiceServer is the server API for ServiceTemplateService service.
// All implementations must embed UnimplementedServiceTemplateServiceServer
// for forward compatibility.
//
// 服务模板服务
type ServiceTemplateServiceServer interface {
	// 创建服务类型
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateServiceTypeResponse is appropriate for this use case --)
	CreateServiceType(context.Context, *CreateServiceTypeRequest) (*CreateServiceTypeResponse, error)
	// 创建一个服务
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateServiceTemplateResponse is appropriate for this use case --)
	CreateServiceTemplate(context.Context, *CreateServiceTemplateRequest) (*CreateServiceTemplateResponse, error)
	// 设置service的attribute值
	// (-- api-linter: core::0134::synonyms=disabled
	//
	//	aip.dev/not-precedent: Set is more appropriate for this use case --)
	SetServiceAttributeValues(context.Context, *SetServiceAttributeValuesRequest) (*SetServiceAttributeValuesResponse, error)
	// 设置service_type的attribute
	// (-- api-linter: core::0134::synonyms=disabled
	//
	//	aip.dev/not-precedent: Set is more appropriate for this use case --)
	SetServiceTypeAttribute(context.Context, *SetServiceTypeAttributeRequest) (*SetServiceTypeAttributeResponse, error)
	// 通过 repeated service_template_id 获取 ServiceTemplate 列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceTemplateByIdsResponse is appropriate for this use case --)
	//
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetServiceTemplateByIds is appropriate for this use case --)
	GetServiceTemplateByIds(context.Context, *GetServiceTemplateByIdsRequest) (*GetServiceTemplateByIdsResponse, error)
	// 通过 repeated service_type_id 获取 ServiceTypeAttribute（属性值）列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceTypeAttributesResponse is appropriate for this use case --)
	GetServiceTypeAttributes(context.Context, *GetServiceTypeAttributesRequest) (*GetServiceTypeAttributesResponse, error)
	// 通过 repeated service_template_id 获取 ServiceAttributeValues 列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceAttributeValuesResponse is appropriate for this use case --)
	GetServiceAttributeValues(context.Context, *GetServiceAttributeValuesRequest) (*GetServiceAttributeValuesResponse, error)
	// 列出服务模板
	// (-- api-linter: core::0158::request-page_token-field=disabled
	// aip.dev/not-precedent: 不需要page_token --)
	ListServiceTemplate(context.Context, *ListServiceTemplateRequest) (*ListServiceTemplateResponse, error)
	mustEmbedUnimplementedServiceTemplateServiceServer()
}

// UnimplementedServiceTemplateServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedServiceTemplateServiceServer struct{}

func (UnimplementedServiceTemplateServiceServer) CreateServiceType(context.Context, *CreateServiceTypeRequest) (*CreateServiceTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceType not implemented")
}
func (UnimplementedServiceTemplateServiceServer) CreateServiceTemplate(context.Context, *CreateServiceTemplateRequest) (*CreateServiceTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceTemplate not implemented")
}
func (UnimplementedServiceTemplateServiceServer) SetServiceAttributeValues(context.Context, *SetServiceAttributeValuesRequest) (*SetServiceAttributeValuesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetServiceAttributeValues not implemented")
}
func (UnimplementedServiceTemplateServiceServer) SetServiceTypeAttribute(context.Context, *SetServiceTypeAttributeRequest) (*SetServiceTypeAttributeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetServiceTypeAttribute not implemented")
}
func (UnimplementedServiceTemplateServiceServer) GetServiceTemplateByIds(context.Context, *GetServiceTemplateByIdsRequest) (*GetServiceTemplateByIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceTemplateByIds not implemented")
}
func (UnimplementedServiceTemplateServiceServer) GetServiceTypeAttributes(context.Context, *GetServiceTypeAttributesRequest) (*GetServiceTypeAttributesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceTypeAttributes not implemented")
}
func (UnimplementedServiceTemplateServiceServer) GetServiceAttributeValues(context.Context, *GetServiceAttributeValuesRequest) (*GetServiceAttributeValuesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceAttributeValues not implemented")
}
func (UnimplementedServiceTemplateServiceServer) ListServiceTemplate(context.Context, *ListServiceTemplateRequest) (*ListServiceTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceTemplate not implemented")
}
func (UnimplementedServiceTemplateServiceServer) mustEmbedUnimplementedServiceTemplateServiceServer() {
}
func (UnimplementedServiceTemplateServiceServer) testEmbeddedByValue() {}

// UnsafeServiceTemplateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceTemplateServiceServer will
// result in compilation errors.
type UnsafeServiceTemplateServiceServer interface {
	mustEmbedUnimplementedServiceTemplateServiceServer()
}

func RegisterServiceTemplateServiceServer(s grpc.ServiceRegistrar, srv ServiceTemplateServiceServer) {
	// If the following call pancis, it indicates UnimplementedServiceTemplateServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ServiceTemplateService_ServiceDesc, srv)
}

func _ServiceTemplateService_CreateServiceType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).CreateServiceType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_CreateServiceType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).CreateServiceType(ctx, req.(*CreateServiceTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_CreateServiceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).CreateServiceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_CreateServiceTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).CreateServiceTemplate(ctx, req.(*CreateServiceTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_SetServiceAttributeValues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetServiceAttributeValuesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).SetServiceAttributeValues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_SetServiceAttributeValues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).SetServiceAttributeValues(ctx, req.(*SetServiceAttributeValuesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_SetServiceTypeAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetServiceTypeAttributeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).SetServiceTypeAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_SetServiceTypeAttribute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).SetServiceTypeAttribute(ctx, req.(*SetServiceTypeAttributeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_GetServiceTemplateByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceTemplateByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).GetServiceTemplateByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_GetServiceTemplateByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).GetServiceTemplateByIds(ctx, req.(*GetServiceTemplateByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_GetServiceTypeAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceTypeAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).GetServiceTypeAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_GetServiceTypeAttributes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).GetServiceTypeAttributes(ctx, req.(*GetServiceTypeAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_GetServiceAttributeValues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceAttributeValuesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).GetServiceAttributeValues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_GetServiceAttributeValues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).GetServiceAttributeValues(ctx, req.(*GetServiceAttributeValuesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_ListServiceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).ListServiceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_ListServiceTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).ListServiceTemplate(ctx, req.(*ListServiceTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceTemplateService_ServiceDesc is the grpc.ServiceDesc for ServiceTemplateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceTemplateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.fulfillment.v1.ServiceTemplateService",
	HandlerType: (*ServiceTemplateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateServiceType",
			Handler:    _ServiceTemplateService_CreateServiceType_Handler,
		},
		{
			MethodName: "CreateServiceTemplate",
			Handler:    _ServiceTemplateService_CreateServiceTemplate_Handler,
		},
		{
			MethodName: "SetServiceAttributeValues",
			Handler:    _ServiceTemplateService_SetServiceAttributeValues_Handler,
		},
		{
			MethodName: "SetServiceTypeAttribute",
			Handler:    _ServiceTemplateService_SetServiceTypeAttribute_Handler,
		},
		{
			MethodName: "GetServiceTemplateByIds",
			Handler:    _ServiceTemplateService_GetServiceTemplateByIds_Handler,
		},
		{
			MethodName: "GetServiceTypeAttributes",
			Handler:    _ServiceTemplateService_GetServiceTypeAttributes_Handler,
		},
		{
			MethodName: "GetServiceAttributeValues",
			Handler:    _ServiceTemplateService_GetServiceAttributeValues_Handler,
		},
		{
			MethodName: "ListServiceTemplate",
			Handler:    _ServiceTemplateService_ListServiceTemplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/fulfillment/v1/service_template_service.proto",
}
