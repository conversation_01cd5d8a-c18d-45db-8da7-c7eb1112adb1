// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/fulfillment/v1/fulfillment_report_service.proto

package fulfillmentpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = petpb.Pet_PetType(0)
)

// Validate checks the field values on GetFulfillmentReportTemplateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFulfillmentReportTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportTemplateRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportTemplateRequestMultiError, or nil if none found.
func (m *GetFulfillmentReportTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := GetFulfillmentReportTemplateRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := GetFulfillmentReportTemplateRequestValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.BusinessId != nil {

		if m.GetBusinessId() <= 0 {
			err := GetFulfillmentReportTemplateRequestValidationError{
				field:  "BusinessId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return GetFulfillmentReportTemplateRequestMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFulfillmentReportTemplateRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportTemplateRequestMultiError) AllErrors() []error { return m }

// GetFulfillmentReportTemplateRequestValidationError is the validation error
// returned by GetFulfillmentReportTemplateRequest.Validate if the designated
// constraints aren't met.
type GetFulfillmentReportTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportTemplateRequestValidationError) ErrorName() string {
	return "GetFulfillmentReportTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportTemplateRequestValidationError{}

// Validate checks the field values on GetFulfillmentReportTemplateResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFulfillmentReportTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportTemplateResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportTemplateResponseMultiError, or nil if none found.
func (m *GetFulfillmentReportTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFulfillmentReportTemplateResponseValidationError{
					field:  "Template",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFulfillmentReportTemplateResponseValidationError{
					field:  "Template",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFulfillmentReportTemplateResponseValidationError{
				field:  "Template",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFulfillmentReportTemplateResponseMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportTemplateResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetFulfillmentReportTemplateResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportTemplateResponseMultiError) AllErrors() []error { return m }

// GetFulfillmentReportTemplateResponseValidationError is the validation error
// returned by GetFulfillmentReportTemplateResponse.Validate if the designated
// constraints aren't met.
type GetFulfillmentReportTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportTemplateResponseValidationError) ErrorName() string {
	return "GetFulfillmentReportTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportTemplateResponseValidationError{}

// Validate checks the field values on UpdateFulfillmentReportTemplateRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateFulfillmentReportTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateFulfillmentReportTemplateRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateFulfillmentReportTemplateRequestMultiError, or nil if none found.
func (m *UpdateFulfillmentReportTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFulfillmentReportTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := UpdateFulfillmentReportTemplateRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := UpdateFulfillmentReportTemplateRequestValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFulfillmentReportTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFulfillmentReportTemplateRequestValidationError{
					field:  "FulfillmentReportTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFulfillmentReportTemplateRequestValidationError{
					field:  "FulfillmentReportTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFulfillmentReportTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFulfillmentReportTemplateRequestValidationError{
				field:  "FulfillmentReportTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.BusinessId != nil {

		if m.GetBusinessId() <= 0 {
			err := UpdateFulfillmentReportTemplateRequestValidationError{
				field:  "BusinessId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return UpdateFulfillmentReportTemplateRequestMultiError(errors)
	}

	return nil
}

// UpdateFulfillmentReportTemplateRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateFulfillmentReportTemplateRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateFulfillmentReportTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFulfillmentReportTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFulfillmentReportTemplateRequestMultiError) AllErrors() []error { return m }

// UpdateFulfillmentReportTemplateRequestValidationError is the validation
// error returned by UpdateFulfillmentReportTemplateRequest.Validate if the
// designated constraints aren't met.
type UpdateFulfillmentReportTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFulfillmentReportTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFulfillmentReportTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFulfillmentReportTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFulfillmentReportTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFulfillmentReportTemplateRequestValidationError) ErrorName() string {
	return "UpdateFulfillmentReportTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFulfillmentReportTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFulfillmentReportTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFulfillmentReportTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFulfillmentReportTemplateRequestValidationError{}

// Validate checks the field values on UpdateFulfillmentReportTemplateResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateFulfillmentReportTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateFulfillmentReportTemplateResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateFulfillmentReportTemplateResponseMultiError, or nil if none found.
func (m *UpdateFulfillmentReportTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFulfillmentReportTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateFulfillmentReportTemplateResponseMultiError(errors)
	}

	return nil
}

// UpdateFulfillmentReportTemplateResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateFulfillmentReportTemplateResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateFulfillmentReportTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFulfillmentReportTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFulfillmentReportTemplateResponseMultiError) AllErrors() []error { return m }

// UpdateFulfillmentReportTemplateResponseValidationError is the validation
// error returned by UpdateFulfillmentReportTemplateResponse.Validate if the
// designated constraints aren't met.
type UpdateFulfillmentReportTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFulfillmentReportTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFulfillmentReportTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFulfillmentReportTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFulfillmentReportTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFulfillmentReportTemplateResponseValidationError) ErrorName() string {
	return "UpdateFulfillmentReportTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFulfillmentReportTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFulfillmentReportTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFulfillmentReportTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFulfillmentReportTemplateResponseValidationError{}

// Validate checks the field values on GetFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFulfillmentReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportRequestMultiError, or nil if none found.
func (m *GetFulfillmentReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAppointmentId() <= 0 {
		err := GetFulfillmentReportRequestValidationError{
			field:  "AppointmentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPetId() <= 0 {
		err := GetFulfillmentReportRequestValidationError{
			field:  "PetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := GetFulfillmentReportRequestValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.ServiceDate != nil {
		// no validation rules for ServiceDate
	}

	if len(errors) > 0 {
		return GetFulfillmentReportRequestMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportRequestMultiError is an error wrapping multiple
// validation errors returned by GetFulfillmentReportRequest.ValidateAll() if
// the designated constraints aren't met.
type GetFulfillmentReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportRequestMultiError) AllErrors() []error { return m }

// GetFulfillmentReportRequestValidationError is the validation error returned
// by GetFulfillmentReportRequest.Validate if the designated constraints
// aren't met.
type GetFulfillmentReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportRequestValidationError) ErrorName() string {
	return "GetFulfillmentReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportRequestValidationError{}

// Validate checks the field values on GetFulfillmentReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFulfillmentReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportResponseMultiError, or nil if none found.
func (m *GetFulfillmentReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFulfillmentReport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFulfillmentReportResponseValidationError{
					field:  "FulfillmentReport",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFulfillmentReportResponseValidationError{
					field:  "FulfillmentReport",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFulfillmentReport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFulfillmentReportResponseValidationError{
				field:  "FulfillmentReport",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsNeedRefresh

	if len(errors) > 0 {
		return GetFulfillmentReportResponseMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportResponseMultiError is an error wrapping multiple
// validation errors returned by GetFulfillmentReportResponse.ValidateAll() if
// the designated constraints aren't met.
type GetFulfillmentReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportResponseMultiError) AllErrors() []error { return m }

// GetFulfillmentReportResponseValidationError is the validation error returned
// by GetFulfillmentReportResponse.Validate if the designated constraints
// aren't met.
type GetFulfillmentReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportResponseValidationError) ErrorName() string {
	return "GetFulfillmentReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportResponseValidationError{}

// Validate checks the field values on UpdateFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFulfillmentReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFulfillmentReportRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateFulfillmentReportRequestMultiError, or nil if none found.
func (m *UpdateFulfillmentReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFulfillmentReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFulfillmentReport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFulfillmentReportRequestValidationError{
					field:  "FulfillmentReport",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFulfillmentReportRequestValidationError{
					field:  "FulfillmentReport",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFulfillmentReport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFulfillmentReportRequestValidationError{
				field:  "FulfillmentReport",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFulfillmentReportRequestMultiError(errors)
	}

	return nil
}

// UpdateFulfillmentReportRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateFulfillmentReportRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateFulfillmentReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFulfillmentReportRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFulfillmentReportRequestMultiError) AllErrors() []error { return m }

// UpdateFulfillmentReportRequestValidationError is the validation error
// returned by UpdateFulfillmentReportRequest.Validate if the designated
// constraints aren't met.
type UpdateFulfillmentReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFulfillmentReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFulfillmentReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFulfillmentReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFulfillmentReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFulfillmentReportRequestValidationError) ErrorName() string {
	return "UpdateFulfillmentReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFulfillmentReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFulfillmentReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFulfillmentReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFulfillmentReportRequestValidationError{}

// Validate checks the field values on UpdateFulfillmentReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFulfillmentReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFulfillmentReportResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateFulfillmentReportResponseMultiError, or nil if none found.
func (m *UpdateFulfillmentReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFulfillmentReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateFulfillmentReportResponseMultiError(errors)
	}

	return nil
}

// UpdateFulfillmentReportResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateFulfillmentReportResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateFulfillmentReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFulfillmentReportResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFulfillmentReportResponseMultiError) AllErrors() []error { return m }

// UpdateFulfillmentReportResponseValidationError is the validation error
// returned by UpdateFulfillmentReportResponse.Validate if the designated
// constraints aren't met.
type UpdateFulfillmentReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFulfillmentReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFulfillmentReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFulfillmentReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFulfillmentReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFulfillmentReportResponseValidationError) ErrorName() string {
	return "UpdateFulfillmentReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFulfillmentReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFulfillmentReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFulfillmentReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFulfillmentReportResponseValidationError{}

// Validate checks the field values on GetFulfillmentReportSummaryInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFulfillmentReportSummaryInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFulfillmentReportSummaryInfoRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetFulfillmentReportSummaryInfoRequestMultiError, or nil if none found.
func (m *GetFulfillmentReportSummaryInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportSummaryInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.FulfillmentReportId != nil {

		if m.GetFulfillmentReportId() <= 0 {
			err := GetFulfillmentReportSummaryInfoRequestValidationError{
				field:  "FulfillmentReportId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Uuid != nil {
		// no validation rules for Uuid
	}

	if len(errors) > 0 {
		return GetFulfillmentReportSummaryInfoRequestMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportSummaryInfoRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetFulfillmentReportSummaryInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportSummaryInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportSummaryInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportSummaryInfoRequestMultiError) AllErrors() []error { return m }

// GetFulfillmentReportSummaryInfoRequestValidationError is the validation
// error returned by GetFulfillmentReportSummaryInfoRequest.Validate if the
// designated constraints aren't met.
type GetFulfillmentReportSummaryInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportSummaryInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportSummaryInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportSummaryInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportSummaryInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportSummaryInfoRequestValidationError) ErrorName() string {
	return "GetFulfillmentReportSummaryInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportSummaryInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportSummaryInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportSummaryInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportSummaryInfoRequestValidationError{}

// Validate checks the field values on GetFulfillmentReportSummaryInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFulfillmentReportSummaryInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFulfillmentReportSummaryInfoResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetFulfillmentReportSummaryInfoResponseMultiError, or nil if none found.
func (m *GetFulfillmentReportSummaryInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportSummaryInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSummaryInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFulfillmentReportSummaryInfoResponseValidationError{
					field:  "SummaryInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFulfillmentReportSummaryInfoResponseValidationError{
					field:  "SummaryInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummaryInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFulfillmentReportSummaryInfoResponseValidationError{
				field:  "SummaryInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFulfillmentReportSummaryInfoResponseMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportSummaryInfoResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetFulfillmentReportSummaryInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportSummaryInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportSummaryInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportSummaryInfoResponseMultiError) AllErrors() []error { return m }

// GetFulfillmentReportSummaryInfoResponseValidationError is the validation
// error returned by GetFulfillmentReportSummaryInfoResponse.Validate if the
// designated constraints aren't met.
type GetFulfillmentReportSummaryInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportSummaryInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportSummaryInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportSummaryInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportSummaryInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportSummaryInfoResponseValidationError) ErrorName() string {
	return "GetFulfillmentReportSummaryInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportSummaryInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportSummaryInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportSummaryInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportSummaryInfoResponseValidationError{}

// Validate checks the field values on GetFulfillmentReportRecordsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFulfillmentReportRecordsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportRecordsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportRecordsRequestMultiError, or nil if none found.
func (m *GetFulfillmentReportRecordsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportRecordsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := GetFulfillmentReportRecordsRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppointmentId() <= 0 {
		err := GetFulfillmentReportRecordsRequestValidationError{
			field:  "AppointmentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.BusinessId != nil {

		if m.GetBusinessId() <= 0 {
			err := GetFulfillmentReportRecordsRequestValidationError{
				field:  "BusinessId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return GetFulfillmentReportRecordsRequestMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportRecordsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFulfillmentReportRecordsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportRecordsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportRecordsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportRecordsRequestMultiError) AllErrors() []error { return m }

// GetFulfillmentReportRecordsRequestValidationError is the validation error
// returned by GetFulfillmentReportRecordsRequest.Validate if the designated
// constraints aren't met.
type GetFulfillmentReportRecordsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportRecordsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportRecordsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportRecordsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportRecordsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportRecordsRequestValidationError) ErrorName() string {
	return "GetFulfillmentReportRecordsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportRecordsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportRecordsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportRecordsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportRecordsRequestValidationError{}

// Validate checks the field values on GetFulfillmentReportRecordsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFulfillmentReportRecordsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportRecordsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportRecordsResponseMultiError, or nil if none found.
func (m *GetFulfillmentReportRecordsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportRecordsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFulfillmentReportRecordsResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFulfillmentReportRecordsResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFulfillmentReportRecordsResponseValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFulfillmentReportRecordsResponseMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportRecordsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetFulfillmentReportRecordsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportRecordsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportRecordsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportRecordsResponseMultiError) AllErrors() []error { return m }

// GetFulfillmentReportRecordsResponseValidationError is the validation error
// returned by GetFulfillmentReportRecordsResponse.Validate if the designated
// constraints aren't met.
type GetFulfillmentReportRecordsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportRecordsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportRecordsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportRecordsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportRecordsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportRecordsResponseValidationError) ErrorName() string {
	return "GetFulfillmentReportRecordsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportRecordsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportRecordsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportRecordsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportRecordsResponseValidationError{}

// Validate checks the field values on GetFulfillmentReportPreviewRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFulfillmentReportPreviewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportPreviewRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportPreviewRequestMultiError, or nil if none found.
func (m *GetFulfillmentReportPreviewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportPreviewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.ReportId != nil {

		if m.GetReportId() <= 0 {
			err := GetFulfillmentReportPreviewRequestValidationError{
				field:  "ReportId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.ThemeCode != nil {
		// no validation rules for ThemeCode
	}

	if len(errors) > 0 {
		return GetFulfillmentReportPreviewRequestMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportPreviewRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFulfillmentReportPreviewRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportPreviewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportPreviewRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportPreviewRequestMultiError) AllErrors() []error { return m }

// GetFulfillmentReportPreviewRequestValidationError is the validation error
// returned by GetFulfillmentReportPreviewRequest.Validate if the designated
// constraints aren't met.
type GetFulfillmentReportPreviewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportPreviewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportPreviewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportPreviewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportPreviewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportPreviewRequestValidationError) ErrorName() string {
	return "GetFulfillmentReportPreviewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportPreviewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportPreviewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportPreviewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportPreviewRequestValidationError{}

// Validate checks the field values on GetFulfillmentReportPreviewResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFulfillmentReportPreviewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportPreviewResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportPreviewResponseMultiError, or nil if none found.
func (m *GetFulfillmentReportPreviewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportPreviewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSummaryInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFulfillmentReportPreviewResponseValidationError{
					field:  "SummaryInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFulfillmentReportPreviewResponseValidationError{
					field:  "SummaryInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummaryInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFulfillmentReportPreviewResponseValidationError{
				field:  "SummaryInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSampleValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFulfillmentReportPreviewResponseValidationError{
					field:  "SampleValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFulfillmentReportPreviewResponseValidationError{
					field:  "SampleValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSampleValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFulfillmentReportPreviewResponseValidationError{
				field:  "SampleValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFulfillmentReportPreviewResponseMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportPreviewResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetFulfillmentReportPreviewResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportPreviewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportPreviewResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportPreviewResponseMultiError) AllErrors() []error { return m }

// GetFulfillmentReportPreviewResponseValidationError is the validation error
// returned by GetFulfillmentReportPreviewResponse.Validate if the designated
// constraints aren't met.
type GetFulfillmentReportPreviewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportPreviewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportPreviewResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportPreviewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportPreviewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportPreviewResponseValidationError) ErrorName() string {
	return "GetFulfillmentReportPreviewResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportPreviewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportPreviewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportPreviewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportPreviewResponseValidationError{}

// Validate checks the field values on ListFulfullmentThemeConfigRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListFulfullmentThemeConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFulfullmentThemeConfigRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListFulfullmentThemeConfigRequestMultiError, or nil if none found.
func (m *ListFulfullmentThemeConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFulfullmentThemeConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := ListFulfullmentThemeConfigRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListFulfullmentThemeConfigRequestMultiError(errors)
	}

	return nil
}

// ListFulfullmentThemeConfigRequestMultiError is an error wrapping multiple
// validation errors returned by
// ListFulfullmentThemeConfigRequest.ValidateAll() if the designated
// constraints aren't met.
type ListFulfullmentThemeConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFulfullmentThemeConfigRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFulfullmentThemeConfigRequestMultiError) AllErrors() []error { return m }

// ListFulfullmentThemeConfigRequestValidationError is the validation error
// returned by ListFulfullmentThemeConfigRequest.Validate if the designated
// constraints aren't met.
type ListFulfullmentThemeConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFulfullmentThemeConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFulfullmentThemeConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFulfullmentThemeConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFulfullmentThemeConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFulfullmentThemeConfigRequestValidationError) ErrorName() string {
	return "ListFulfullmentThemeConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListFulfullmentThemeConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFulfullmentThemeConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFulfullmentThemeConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFulfullmentThemeConfigRequestValidationError{}

// Validate checks the field values on ListFulfullmentThemeConfigResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListFulfullmentThemeConfigResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFulfullmentThemeConfigResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListFulfullmentThemeConfigResponseMultiError, or nil if none found.
func (m *ListFulfullmentThemeConfigResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFulfullmentThemeConfigResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetThemeConfigs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFulfullmentThemeConfigResponseValidationError{
						field:  fmt.Sprintf("ThemeConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFulfullmentThemeConfigResponseValidationError{
						field:  fmt.Sprintf("ThemeConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFulfullmentThemeConfigResponseValidationError{
					field:  fmt.Sprintf("ThemeConfigs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListFulfullmentThemeConfigResponseMultiError(errors)
	}

	return nil
}

// ListFulfullmentThemeConfigResponseMultiError is an error wrapping multiple
// validation errors returned by
// ListFulfullmentThemeConfigResponse.ValidateAll() if the designated
// constraints aren't met.
type ListFulfullmentThemeConfigResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFulfullmentThemeConfigResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFulfullmentThemeConfigResponseMultiError) AllErrors() []error { return m }

// ListFulfullmentThemeConfigResponseValidationError is the validation error
// returned by ListFulfullmentThemeConfigResponse.Validate if the designated
// constraints aren't met.
type ListFulfullmentThemeConfigResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFulfullmentThemeConfigResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFulfullmentThemeConfigResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFulfullmentThemeConfigResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFulfullmentThemeConfigResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFulfullmentThemeConfigResponseValidationError) ErrorName() string {
	return "ListFulfullmentThemeConfigResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListFulfullmentThemeConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFulfullmentThemeConfigResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFulfullmentThemeConfigResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFulfullmentThemeConfigResponseValidationError{}

// Validate checks the field values on GenerateMessageContentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateMessageContentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateMessageContentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GenerateMessageContentRequestMultiError, or nil if none found.
func (m *GenerateMessageContentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateMessageContentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := GenerateMessageContentRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppointmentId() <= 0 {
		err := GenerateMessageContentRequestValidationError{
			field:  "AppointmentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPetId() <= 0 {
		err := GenerateMessageContentRequestValidationError{
			field:  "PetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := GenerateMessageContentRequestValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.ServiceDate != nil {
		// no validation rules for ServiceDate
	}

	if len(errors) > 0 {
		return GenerateMessageContentRequestMultiError(errors)
	}

	return nil
}

// GenerateMessageContentRequestMultiError is an error wrapping multiple
// validation errors returned by GenerateMessageContentRequest.ValidateAll()
// if the designated constraints aren't met.
type GenerateMessageContentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateMessageContentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateMessageContentRequestMultiError) AllErrors() []error { return m }

// GenerateMessageContentRequestValidationError is the validation error
// returned by GenerateMessageContentRequest.Validate if the designated
// constraints aren't met.
type GenerateMessageContentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateMessageContentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateMessageContentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateMessageContentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateMessageContentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateMessageContentRequestValidationError) ErrorName() string {
	return "GenerateMessageContentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateMessageContentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateMessageContentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateMessageContentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateMessageContentRequestValidationError{}

// Validate checks the field values on GenerateMessageContentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateMessageContentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateMessageContentResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GenerateMessageContentResponseMultiError, or nil if none found.
func (m *GenerateMessageContentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateMessageContentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MessageContent

	if len(errors) > 0 {
		return GenerateMessageContentResponseMultiError(errors)
	}

	return nil
}

// GenerateMessageContentResponseMultiError is an error wrapping multiple
// validation errors returned by GenerateMessageContentResponse.ValidateAll()
// if the designated constraints aren't met.
type GenerateMessageContentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateMessageContentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateMessageContentResponseMultiError) AllErrors() []error { return m }

// GenerateMessageContentResponseValidationError is the validation error
// returned by GenerateMessageContentResponse.Validate if the designated
// constraints aren't met.
type GenerateMessageContentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateMessageContentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateMessageContentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateMessageContentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateMessageContentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateMessageContentResponseValidationError) ErrorName() string {
	return "GenerateMessageContentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateMessageContentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateMessageContentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateMessageContentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateMessageContentResponseValidationError{}

// Validate checks the field values on SendFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendFulfillmentReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendFulfillmentReportRequestMultiError, or nil if none found.
func (m *SendFulfillmentReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendFulfillmentReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFulfillmentReportId() <= 0 {
		err := SendFulfillmentReportRequestValidationError{
			field:  "FulfillmentReportId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := SendMethod_name[int32(m.GetSendMethod())]; !ok {
		err := SendFulfillmentReportRequestValidationError{
			field:  "SendMethod",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	_SendFulfillmentReportRequest_RecipientEmails_Unique := make(map[string]struct{}, len(m.GetRecipientEmails()))

	for idx, item := range m.GetRecipientEmails() {
		_, _ = idx, item

		if _, exists := _SendFulfillmentReportRequest_RecipientEmails_Unique[item]; exists {
			err := SendFulfillmentReportRequestValidationError{
				field:  fmt.Sprintf("RecipientEmails[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_SendFulfillmentReportRequest_RecipientEmails_Unique[item] = struct{}{}
		}

		if utf8.RuneCountInString(item) < 1 {
			err := SendFulfillmentReportRequestValidationError{
				field:  fmt.Sprintf("RecipientEmails[%v]", idx),
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if err := m._validateEmail(item); err != nil {
			err = SendFulfillmentReportRequestValidationError{
				field:  fmt.Sprintf("RecipientEmails[%v]", idx),
				reason: "value must be a valid email address",
				cause:  err,
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return SendFulfillmentReportRequestMultiError(errors)
	}

	return nil
}

func (m *SendFulfillmentReportRequest) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *SendFulfillmentReportRequest) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// SendFulfillmentReportRequestMultiError is an error wrapping multiple
// validation errors returned by SendFulfillmentReportRequest.ValidateAll() if
// the designated constraints aren't met.
type SendFulfillmentReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendFulfillmentReportRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendFulfillmentReportRequestMultiError) AllErrors() []error { return m }

// SendFulfillmentReportRequestValidationError is the validation error returned
// by SendFulfillmentReportRequest.Validate if the designated constraints
// aren't met.
type SendFulfillmentReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendFulfillmentReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendFulfillmentReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendFulfillmentReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendFulfillmentReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendFulfillmentReportRequestValidationError) ErrorName() string {
	return "SendFulfillmentReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendFulfillmentReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendFulfillmentReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendFulfillmentReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendFulfillmentReportRequestValidationError{}

// Validate checks the field values on SendFulfillmentReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendFulfillmentReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendFulfillmentReportResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SendFulfillmentReportResponseMultiError, or nil if none found.
func (m *SendFulfillmentReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendFulfillmentReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSendResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendFulfillmentReportResponseValidationError{
					field:  "SendResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendFulfillmentReportResponseValidationError{
					field:  "SendResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSendResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendFulfillmentReportResponseValidationError{
				field:  "SendResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendFulfillmentReportResponseMultiError(errors)
	}

	return nil
}

// SendFulfillmentReportResponseMultiError is an error wrapping multiple
// validation errors returned by SendFulfillmentReportResponse.ValidateAll()
// if the designated constraints aren't met.
type SendFulfillmentReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendFulfillmentReportResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendFulfillmentReportResponseMultiError) AllErrors() []error { return m }

// SendFulfillmentReportResponseValidationError is the validation error
// returned by SendFulfillmentReportResponse.Validate if the designated
// constraints aren't met.
type SendFulfillmentReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendFulfillmentReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendFulfillmentReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendFulfillmentReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendFulfillmentReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendFulfillmentReportResponseValidationError) ErrorName() string {
	return "SendFulfillmentReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendFulfillmentReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendFulfillmentReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendFulfillmentReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendFulfillmentReportResponseValidationError{}

// Validate checks the field values on GetFulfillmentReportSendHistoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFulfillmentReportSendHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFulfillmentReportSendHistoryRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetFulfillmentReportSendHistoryRequestMultiError, or nil if none found.
func (m *GetFulfillmentReportSendHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportSendHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := GetFulfillmentReportSendHistoryRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppointmentId() <= 0 {
		err := GetFulfillmentReportSendHistoryRequestValidationError{
			field:  "AppointmentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPetId() <= 0 {
		err := GetFulfillmentReportSendHistoryRequestValidationError{
			field:  "PetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := GetFulfillmentReportSendHistoryRequestValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetFulfillmentReportSendHistoryRequestMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportSendHistoryRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetFulfillmentReportSendHistoryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportSendHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportSendHistoryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportSendHistoryRequestMultiError) AllErrors() []error { return m }

// GetFulfillmentReportSendHistoryRequestValidationError is the validation
// error returned by GetFulfillmentReportSendHistoryRequest.Validate if the
// designated constraints aren't met.
type GetFulfillmentReportSendHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportSendHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportSendHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportSendHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportSendHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportSendHistoryRequestValidationError) ErrorName() string {
	return "GetFulfillmentReportSendHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportSendHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportSendHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportSendHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportSendHistoryRequestValidationError{}

// Validate checks the field values on GetFulfillmentReportSendHistoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFulfillmentReportSendHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFulfillmentReportSendHistoryResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetFulfillmentReportSendHistoryResponseMultiError, or nil if none found.
func (m *GetFulfillmentReportSendHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportSendHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSendRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFulfillmentReportSendHistoryResponseValidationError{
						field:  fmt.Sprintf("SendRecords[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFulfillmentReportSendHistoryResponseValidationError{
						field:  fmt.Sprintf("SendRecords[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFulfillmentReportSendHistoryResponseValidationError{
					field:  fmt.Sprintf("SendRecords[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFulfillmentReportSendHistoryResponseMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportSendHistoryResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetFulfillmentReportSendHistoryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportSendHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportSendHistoryResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportSendHistoryResponseMultiError) AllErrors() []error { return m }

// GetFulfillmentReportSendHistoryResponseValidationError is the validation
// error returned by GetFulfillmentReportSendHistoryResponse.Validate if the
// designated constraints aren't met.
type GetFulfillmentReportSendHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportSendHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportSendHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportSendHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportSendHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportSendHistoryResponseValidationError) ErrorName() string {
	return "GetFulfillmentReportSendHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportSendHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportSendHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportSendHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportSendHistoryResponseValidationError{}

// Validate checks the field values on GetFulfillmentReportSendResultRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFulfillmentReportSendResultRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFulfillmentReportSendResultRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFulfillmentReportSendResultRequestMultiError, or nil if none found.
func (m *GetFulfillmentReportSendResultRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportSendResultRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.FulfillmentReportId != nil {

		if m.GetFulfillmentReportId() <= 0 {
			err := GetFulfillmentReportSendResultRequestValidationError{
				field:  "FulfillmentReportId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.AppointmentId != nil {

		if m.GetAppointmentId() <= 0 {
			err := GetFulfillmentReportSendResultRequestValidationError{
				field:  "AppointmentId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.PetId != nil {

		if m.GetPetId() <= 0 {
			err := GetFulfillmentReportSendResultRequestValidationError{
				field:  "PetId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.CareType != nil {

		if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
			err := GetFulfillmentReportSendResultRequestValidationError{
				field:  "CareType",
				reason: "value must be one of the defined enum values",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.ServiceDate != nil {
		// no validation rules for ServiceDate
	}

	if len(errors) > 0 {
		return GetFulfillmentReportSendResultRequestMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportSendResultRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetFulfillmentReportSendResultRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportSendResultRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportSendResultRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportSendResultRequestMultiError) AllErrors() []error { return m }

// GetFulfillmentReportSendResultRequestValidationError is the validation error
// returned by GetFulfillmentReportSendResultRequest.Validate if the
// designated constraints aren't met.
type GetFulfillmentReportSendResultRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportSendResultRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportSendResultRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportSendResultRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportSendResultRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportSendResultRequestValidationError) ErrorName() string {
	return "GetFulfillmentReportSendResultRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportSendResultRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportSendResultRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportSendResultRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportSendResultRequestValidationError{}

// Validate checks the field values on GetFulfillmentReportSendResultResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFulfillmentReportSendResultResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFulfillmentReportSendResultResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetFulfillmentReportSendResultResponseMultiError, or nil if none found.
func (m *GetFulfillmentReportSendResultResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFulfillmentReportSendResultResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSendResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFulfillmentReportSendResultResponseValidationError{
					field:  "SendResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFulfillmentReportSendResultResponseValidationError{
					field:  "SendResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSendResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFulfillmentReportSendResultResponseValidationError{
				field:  "SendResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFulfillmentReportSendResultResponseMultiError(errors)
	}

	return nil
}

// GetFulfillmentReportSendResultResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetFulfillmentReportSendResultResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFulfillmentReportSendResultResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFulfillmentReportSendResultResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFulfillmentReportSendResultResponseMultiError) AllErrors() []error { return m }

// GetFulfillmentReportSendResultResponseValidationError is the validation
// error returned by GetFulfillmentReportSendResultResponse.Validate if the
// designated constraints aren't met.
type GetFulfillmentReportSendResultResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFulfillmentReportSendResultResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFulfillmentReportSendResultResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFulfillmentReportSendResultResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFulfillmentReportSendResultResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFulfillmentReportSendResultResponseValidationError) ErrorName() string {
	return "GetFulfillmentReportSendResultResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFulfillmentReportSendResultResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFulfillmentReportSendResultResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFulfillmentReportSendResultResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFulfillmentReportSendResultResponseValidationError{}

// Validate checks the field values on ListFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListFulfillmentReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListFulfillmentReportRequestMultiError, or nil if none found.
func (m *ListFulfillmentReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFulfillmentReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := ListFulfillmentReportRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := ListFulfillmentReportRequestValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFulfillmentReportRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFulfillmentReportRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFulfillmentReportRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPagination() == nil {
		err := ListFulfillmentReportRequestValidationError{
			field:  "Pagination",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFulfillmentReportRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFulfillmentReportRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFulfillmentReportRequestValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListFulfillmentReportRequestMultiError(errors)
	}

	return nil
}

// ListFulfillmentReportRequestMultiError is an error wrapping multiple
// validation errors returned by ListFulfillmentReportRequest.ValidateAll() if
// the designated constraints aren't met.
type ListFulfillmentReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFulfillmentReportRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFulfillmentReportRequestMultiError) AllErrors() []error { return m }

// ListFulfillmentReportRequestValidationError is the validation error returned
// by ListFulfillmentReportRequest.Validate if the designated constraints
// aren't met.
type ListFulfillmentReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFulfillmentReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFulfillmentReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFulfillmentReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFulfillmentReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFulfillmentReportRequestValidationError) ErrorName() string {
	return "ListFulfillmentReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListFulfillmentReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFulfillmentReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFulfillmentReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFulfillmentReportRequestValidationError{}

// Validate checks the field values on ListFulfillmentReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListFulfillmentReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFulfillmentReportResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListFulfillmentReportResponseMultiError, or nil if none found.
func (m *ListFulfillmentReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFulfillmentReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFulfillmentReportCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFulfillmentReportResponseValidationError{
						field:  fmt.Sprintf("FulfillmentReportCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFulfillmentReportResponseValidationError{
						field:  fmt.Sprintf("FulfillmentReportCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFulfillmentReportResponseValidationError{
					field:  fmt.Sprintf("FulfillmentReportCards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFulfillmentReportResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFulfillmentReportResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFulfillmentReportResponseValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListFulfillmentReportResponseMultiError(errors)
	}

	return nil
}

// ListFulfillmentReportResponseMultiError is an error wrapping multiple
// validation errors returned by ListFulfillmentReportResponse.ValidateAll()
// if the designated constraints aren't met.
type ListFulfillmentReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFulfillmentReportResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFulfillmentReportResponseMultiError) AllErrors() []error { return m }

// ListFulfillmentReportResponseValidationError is the validation error
// returned by ListFulfillmentReportResponse.Validate if the designated
// constraints aren't met.
type ListFulfillmentReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFulfillmentReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFulfillmentReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFulfillmentReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFulfillmentReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFulfillmentReportResponseValidationError) ErrorName() string {
	return "ListFulfillmentReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListFulfillmentReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFulfillmentReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFulfillmentReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFulfillmentReportResponseValidationError{}

// Validate checks the field values on ListFulfillmentReportConfigFilter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListFulfillmentReportConfigFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFulfillmentReportConfigFilter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListFulfillmentReportConfigFilterMultiError, or nil if none found.
func (m *ListFulfillmentReportConfigFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFulfillmentReportConfigFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	_ListFulfillmentReportConfigFilter_CareTypes_Unique := make(map[CareType]struct{}, len(m.GetCareTypes()))

	for idx, item := range m.GetCareTypes() {
		_, _ = idx, item

		if _, exists := _ListFulfillmentReportConfigFilter_CareTypes_Unique[item]; exists {
			err := ListFulfillmentReportConfigFilterValidationError{
				field:  fmt.Sprintf("CareTypes[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_ListFulfillmentReportConfigFilter_CareTypes_Unique[item] = struct{}{}
		}

		if _, ok := _ListFulfillmentReportConfigFilter_CareTypes_NotInLookup[item]; ok {
			err := ListFulfillmentReportConfigFilterValidationError{
				field:  fmt.Sprintf("CareTypes[%v]", idx),
				reason: "value must not be in list [0]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if _, ok := CareType_name[int32(item)]; !ok {
			err := ListFulfillmentReportConfigFilterValidationError{
				field:  fmt.Sprintf("CareTypes[%v]", idx),
				reason: "value must be one of the defined enum values",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Status != nil {

		if _, ok := _ListFulfillmentReportConfigFilter_Status_NotInLookup[m.GetStatus()]; ok {
			err := ListFulfillmentReportConfigFilterValidationError{
				field:  "Status",
				reason: "value must not be in list [REPORT_STATUS_UNSPECIFIED]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if _, ok := ReportStatus_name[int32(m.GetStatus())]; !ok {
			err := ListFulfillmentReportConfigFilterValidationError{
				field:  "Status",
				reason: "value must be one of the defined enum values",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.StartDate != nil {
		// no validation rules for StartDate
	}

	if m.EndDate != nil {
		// no validation rules for EndDate
	}

	if m.PetId != nil {

		if m.GetPetId() <= 0 {
			err := ListFulfillmentReportConfigFilterValidationError{
				field:  "PetId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ListFulfillmentReportConfigFilterMultiError(errors)
	}

	return nil
}

// ListFulfillmentReportConfigFilterMultiError is an error wrapping multiple
// validation errors returned by
// ListFulfillmentReportConfigFilter.ValidateAll() if the designated
// constraints aren't met.
type ListFulfillmentReportConfigFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFulfillmentReportConfigFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFulfillmentReportConfigFilterMultiError) AllErrors() []error { return m }

// ListFulfillmentReportConfigFilterValidationError is the validation error
// returned by ListFulfillmentReportConfigFilter.Validate if the designated
// constraints aren't met.
type ListFulfillmentReportConfigFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFulfillmentReportConfigFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFulfillmentReportConfigFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFulfillmentReportConfigFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFulfillmentReportConfigFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFulfillmentReportConfigFilterValidationError) ErrorName() string {
	return "ListFulfillmentReportConfigFilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListFulfillmentReportConfigFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFulfillmentReportConfigFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFulfillmentReportConfigFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFulfillmentReportConfigFilterValidationError{}

var _ListFulfillmentReportConfigFilter_Status_NotInLookup = map[ReportStatus]struct{}{
	0: {},
}

var _ListFulfillmentReportConfigFilter_CareTypes_NotInLookup = map[CareType]struct{}{
	0: {},
}

// Validate checks the field values on BatchDeleteFulfillmentReportRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BatchDeleteFulfillmentReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteFulfillmentReportRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchDeleteFulfillmentReportRequestMultiError, or nil if none found.
func (m *BatchDeleteFulfillmentReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteFulfillmentReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := BatchDeleteFulfillmentReportRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := BatchDeleteFulfillmentReportRequestValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	_BatchDeleteFulfillmentReportRequest_ReportCardIds_Unique := make(map[int64]struct{}, len(m.GetReportCardIds()))

	for idx, item := range m.GetReportCardIds() {
		_, _ = idx, item

		if _, exists := _BatchDeleteFulfillmentReportRequest_ReportCardIds_Unique[item]; exists {
			err := BatchDeleteFulfillmentReportRequestValidationError{
				field:  fmt.Sprintf("ReportCardIds[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_BatchDeleteFulfillmentReportRequest_ReportCardIds_Unique[item] = struct{}{}
		}

		if item <= 0 {
			err := BatchDeleteFulfillmentReportRequestValidationError{
				field:  fmt.Sprintf("ReportCardIds[%v]", idx),
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return BatchDeleteFulfillmentReportRequestMultiError(errors)
	}

	return nil
}

// BatchDeleteFulfillmentReportRequestMultiError is an error wrapping multiple
// validation errors returned by
// BatchDeleteFulfillmentReportRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchDeleteFulfillmentReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteFulfillmentReportRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteFulfillmentReportRequestMultiError) AllErrors() []error { return m }

// BatchDeleteFulfillmentReportRequestValidationError is the validation error
// returned by BatchDeleteFulfillmentReportRequest.Validate if the designated
// constraints aren't met.
type BatchDeleteFulfillmentReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteFulfillmentReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteFulfillmentReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteFulfillmentReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteFulfillmentReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteFulfillmentReportRequestValidationError) ErrorName() string {
	return "BatchDeleteFulfillmentReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteFulfillmentReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteFulfillmentReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteFulfillmentReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteFulfillmentReportRequestValidationError{}

// Validate checks the field values on BatchDeleteFulfillmentReportResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchDeleteFulfillmentReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteFulfillmentReportResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchDeleteFulfillmentReportResponseMultiError, or nil if none found.
func (m *BatchDeleteFulfillmentReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteFulfillmentReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchDeleteFulfillmentReportResponseMultiError(errors)
	}

	return nil
}

// BatchDeleteFulfillmentReportResponseMultiError is an error wrapping multiple
// validation errors returned by
// BatchDeleteFulfillmentReportResponse.ValidateAll() if the designated
// constraints aren't met.
type BatchDeleteFulfillmentReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteFulfillmentReportResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteFulfillmentReportResponseMultiError) AllErrors() []error { return m }

// BatchDeleteFulfillmentReportResponseValidationError is the validation error
// returned by BatchDeleteFulfillmentReportResponse.Validate if the designated
// constraints aren't met.
type BatchDeleteFulfillmentReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteFulfillmentReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteFulfillmentReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteFulfillmentReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteFulfillmentReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteFulfillmentReportResponseValidationError) ErrorName() string {
	return "BatchDeleteFulfillmentReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteFulfillmentReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteFulfillmentReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteFulfillmentReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteFulfillmentReportResponseValidationError{}

// Validate checks the field values on BatchSendFulfillmentReportRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BatchSendFulfillmentReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchSendFulfillmentReportRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchSendFulfillmentReportRequestMultiError, or nil if none found.
func (m *BatchSendFulfillmentReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchSendFulfillmentReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := BatchSendFulfillmentReportRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := BatchSendFulfillmentReportRequestValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _BatchSendFulfillmentReportRequest_SendMethod_NotInLookup[m.GetSendMethod()]; ok {
		err := BatchSendFulfillmentReportRequestValidationError{
			field:  "SendMethod",
			reason: "value must not be in list [SEND_METHOD_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := SendMethod_name[int32(m.GetSendMethod())]; !ok {
		err := BatchSendFulfillmentReportRequestValidationError{
			field:  "SendMethod",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetStaffId() <= 0 {
		err := BatchSendFulfillmentReportRequestValidationError{
			field:  "StaffId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BatchSendFulfillmentReportRequestMultiError(errors)
	}

	return nil
}

// BatchSendFulfillmentReportRequestMultiError is an error wrapping multiple
// validation errors returned by
// BatchSendFulfillmentReportRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchSendFulfillmentReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchSendFulfillmentReportRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchSendFulfillmentReportRequestMultiError) AllErrors() []error { return m }

// BatchSendFulfillmentReportRequestValidationError is the validation error
// returned by BatchSendFulfillmentReportRequest.Validate if the designated
// constraints aren't met.
type BatchSendFulfillmentReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchSendFulfillmentReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchSendFulfillmentReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchSendFulfillmentReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchSendFulfillmentReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchSendFulfillmentReportRequestValidationError) ErrorName() string {
	return "BatchSendFulfillmentReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchSendFulfillmentReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchSendFulfillmentReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchSendFulfillmentReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchSendFulfillmentReportRequestValidationError{}

var _BatchSendFulfillmentReportRequest_SendMethod_NotInLookup = map[SendMethod]struct{}{
	0: {},
}

// Validate checks the field values on BatchSendFulfillmentReportResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BatchSendFulfillmentReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchSendFulfillmentReportResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchSendFulfillmentReportResponseMultiError, or nil if none found.
func (m *BatchSendFulfillmentReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchSendFulfillmentReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSendResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchSendFulfillmentReportResponseValidationError{
						field:  fmt.Sprintf("SendResults[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchSendFulfillmentReportResponseValidationError{
						field:  fmt.Sprintf("SendResults[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchSendFulfillmentReportResponseValidationError{
					field:  fmt.Sprintf("SendResults[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchSendFulfillmentReportResponseMultiError(errors)
	}

	return nil
}

// BatchSendFulfillmentReportResponseMultiError is an error wrapping multiple
// validation errors returned by
// BatchSendFulfillmentReportResponse.ValidateAll() if the designated
// constraints aren't met.
type BatchSendFulfillmentReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchSendFulfillmentReportResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchSendFulfillmentReportResponseMultiError) AllErrors() []error { return m }

// BatchSendFulfillmentReportResponseValidationError is the validation error
// returned by BatchSendFulfillmentReportResponse.Validate if the designated
// constraints aren't met.
type BatchSendFulfillmentReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchSendFulfillmentReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchSendFulfillmentReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchSendFulfillmentReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchSendFulfillmentReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchSendFulfillmentReportResponseValidationError) ErrorName() string {
	return "BatchSendFulfillmentReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchSendFulfillmentReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchSendFulfillmentReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchSendFulfillmentReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchSendFulfillmentReportResponseValidationError{}

// Validate checks the field values on IncreaseFulfillmentOpenedCountRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *IncreaseFulfillmentOpenedCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IncreaseFulfillmentOpenedCountRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// IncreaseFulfillmentOpenedCountRequestMultiError, or nil if none found.
func (m *IncreaseFulfillmentOpenedCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IncreaseFulfillmentOpenedCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uuid

	if len(errors) > 0 {
		return IncreaseFulfillmentOpenedCountRequestMultiError(errors)
	}

	return nil
}

// IncreaseFulfillmentOpenedCountRequestMultiError is an error wrapping
// multiple validation errors returned by
// IncreaseFulfillmentOpenedCountRequest.ValidateAll() if the designated
// constraints aren't met.
type IncreaseFulfillmentOpenedCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IncreaseFulfillmentOpenedCountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IncreaseFulfillmentOpenedCountRequestMultiError) AllErrors() []error { return m }

// IncreaseFulfillmentOpenedCountRequestValidationError is the validation error
// returned by IncreaseFulfillmentOpenedCountRequest.Validate if the
// designated constraints aren't met.
type IncreaseFulfillmentOpenedCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IncreaseFulfillmentOpenedCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IncreaseFulfillmentOpenedCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IncreaseFulfillmentOpenedCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IncreaseFulfillmentOpenedCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IncreaseFulfillmentOpenedCountRequestValidationError) ErrorName() string {
	return "IncreaseFulfillmentOpenedCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IncreaseFulfillmentOpenedCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIncreaseFulfillmentOpenedCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IncreaseFulfillmentOpenedCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IncreaseFulfillmentOpenedCountRequestValidationError{}

// Validate checks the field values on IncreaseFulfillmentOpenedCountResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *IncreaseFulfillmentOpenedCountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// IncreaseFulfillmentOpenedCountResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// IncreaseFulfillmentOpenedCountResponseMultiError, or nil if none found.
func (m *IncreaseFulfillmentOpenedCountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IncreaseFulfillmentOpenedCountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return IncreaseFulfillmentOpenedCountResponseMultiError(errors)
	}

	return nil
}

// IncreaseFulfillmentOpenedCountResponseMultiError is an error wrapping
// multiple validation errors returned by
// IncreaseFulfillmentOpenedCountResponse.ValidateAll() if the designated
// constraints aren't met.
type IncreaseFulfillmentOpenedCountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IncreaseFulfillmentOpenedCountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IncreaseFulfillmentOpenedCountResponseMultiError) AllErrors() []error { return m }

// IncreaseFulfillmentOpenedCountResponseValidationError is the validation
// error returned by IncreaseFulfillmentOpenedCountResponse.Validate if the
// designated constraints aren't met.
type IncreaseFulfillmentOpenedCountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IncreaseFulfillmentOpenedCountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IncreaseFulfillmentOpenedCountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IncreaseFulfillmentOpenedCountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IncreaseFulfillmentOpenedCountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IncreaseFulfillmentOpenedCountResponseValidationError) ErrorName() string {
	return "IncreaseFulfillmentOpenedCountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IncreaseFulfillmentOpenedCountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIncreaseFulfillmentOpenedCountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IncreaseFulfillmentOpenedCountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IncreaseFulfillmentOpenedCountResponseValidationError{}

// Validate checks the field values on FulfillmentReportTemplateSync with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportTemplateSync) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportTemplateSync with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FulfillmentReportTemplateSyncMultiError, or nil if none found.
func (m *FulfillmentReportTemplateSync) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportTemplateSync) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := FulfillmentReportTemplateSyncValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := FulfillmentReportTemplateSyncValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := FulfillmentReportTemplateSyncValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetThankYouMessage()) > 255 {
		err := FulfillmentReportTemplateSyncValidationError{
			field:  "ThankYouMessage",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetThemeColor()) > 30 {
		err := FulfillmentReportTemplateSyncValidationError{
			field:  "ThemeColor",
			reason: "value length must be at most 30 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetLightThemeColor()) > 30 {
		err := FulfillmentReportTemplateSyncValidationError{
			field:  "LightThemeColor",
			reason: "value length must be at most 30 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ShowShowcase

	// no validation rules for ShowOverallFeedback

	// no validation rules for RequireBeforePhoto

	// no validation rules for RequireAfterPhoto

	// no validation rules for ShowPetCondition

	// no validation rules for ShowServiceStaffName

	// no validation rules for ShowNextAppointment

	// no validation rules for NextAppointmentDateFormatType

	// no validation rules for ShowReviewBooster

	// no validation rules for ShowYelpReview

	// no validation rules for ShowGoogleReview

	// no validation rules for ShowFacebookReview

	if all {
		switch v := interface{}(m.GetLastPublishTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateSyncValidationError{
					field:  "LastPublishTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateSyncValidationError{
					field:  "LastPublishTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastPublishTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateSyncValidationError{
				field:  "LastPublishTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetTitle()) > 50 {
		err := FulfillmentReportTemplateSyncValidationError{
			field:  "Title",
			reason: "value length must be at most 50 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UpdateBy

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateSyncValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateSyncValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateSyncValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateSyncValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateSyncValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateSyncValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Id != nil {

		if m.GetId() <= 0 {
			err := FulfillmentReportTemplateSyncValidationError{
				field:  "Id",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return FulfillmentReportTemplateSyncMultiError(errors)
	}

	return nil
}

// FulfillmentReportTemplateSyncMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportTemplateSync.ValidateAll()
// if the designated constraints aren't met.
type FulfillmentReportTemplateSyncMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportTemplateSyncMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportTemplateSyncMultiError) AllErrors() []error { return m }

// FulfillmentReportTemplateSyncValidationError is the validation error
// returned by FulfillmentReportTemplateSync.Validate if the designated
// constraints aren't met.
type FulfillmentReportTemplateSyncValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportTemplateSyncValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportTemplateSyncValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportTemplateSyncValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportTemplateSyncValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportTemplateSyncValidationError) ErrorName() string {
	return "FulfillmentReportTemplateSyncValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportTemplateSyncValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportTemplateSync.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportTemplateSyncValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportTemplateSyncValidationError{}

// Validate checks the field values on FulfillmentReportTemplateUniqueKey with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FulfillmentReportTemplateUniqueKey) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportTemplateUniqueKey
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FulfillmentReportTemplateUniqueKeyMultiError, or nil if none found.
func (m *FulfillmentReportTemplateUniqueKey) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportTemplateUniqueKey) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := FulfillmentReportTemplateUniqueKeyValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := FulfillmentReportTemplateUniqueKeyValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := FulfillmentReportTemplateUniqueKeyValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FulfillmentReportTemplateUniqueKeyMultiError(errors)
	}

	return nil
}

// FulfillmentReportTemplateUniqueKeyMultiError is an error wrapping multiple
// validation errors returned by
// FulfillmentReportTemplateUniqueKey.ValidateAll() if the designated
// constraints aren't met.
type FulfillmentReportTemplateUniqueKeyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportTemplateUniqueKeyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportTemplateUniqueKeyMultiError) AllErrors() []error { return m }

// FulfillmentReportTemplateUniqueKeyValidationError is the validation error
// returned by FulfillmentReportTemplateUniqueKey.Validate if the designated
// constraints aren't met.
type FulfillmentReportTemplateUniqueKeyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportTemplateUniqueKeyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportTemplateUniqueKeyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportTemplateUniqueKeyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportTemplateUniqueKeyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportTemplateUniqueKeyValidationError) ErrorName() string {
	return "FulfillmentReportTemplateUniqueKeyValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportTemplateUniqueKeyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportTemplateUniqueKey.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportTemplateUniqueKeyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportTemplateUniqueKeyValidationError{}

// Validate checks the field values on SyncFulfillmentReportTemplateRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SyncFulfillmentReportTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncFulfillmentReportTemplateRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SyncFulfillmentReportTemplateRequestMultiError, or nil if none found.
func (m *SyncFulfillmentReportTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncFulfillmentReportTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := SyncOperation_name[int32(m.GetOperation())]; !ok {
		err := SyncFulfillmentReportTemplateRequestValidationError{
			field:  "Operation",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *SyncFulfillmentReportTemplateRequest_TemplateId:
		if v == nil {
			err := SyncFulfillmentReportTemplateRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetTemplateId() <= 0 {
			err := SyncFulfillmentReportTemplateRequestValidationError{
				field:  "TemplateId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *SyncFulfillmentReportTemplateRequest_UniqueKey:
		if v == nil {
			err := SyncFulfillmentReportTemplateRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUniqueKey()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncFulfillmentReportTemplateRequestValidationError{
						field:  "UniqueKey",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncFulfillmentReportTemplateRequestValidationError{
						field:  "UniqueKey",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUniqueKey()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncFulfillmentReportTemplateRequestValidationError{
					field:  "UniqueKey",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if m.Template != nil {

		if all {
			switch v := interface{}(m.GetTemplate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncFulfillmentReportTemplateRequestValidationError{
						field:  "Template",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncFulfillmentReportTemplateRequestValidationError{
						field:  "Template",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTemplate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncFulfillmentReportTemplateRequestValidationError{
					field:  "Template",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SyncFulfillmentReportTemplateRequestMultiError(errors)
	}

	return nil
}

// SyncFulfillmentReportTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by
// SyncFulfillmentReportTemplateRequest.ValidateAll() if the designated
// constraints aren't met.
type SyncFulfillmentReportTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncFulfillmentReportTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncFulfillmentReportTemplateRequestMultiError) AllErrors() []error { return m }

// SyncFulfillmentReportTemplateRequestValidationError is the validation error
// returned by SyncFulfillmentReportTemplateRequest.Validate if the designated
// constraints aren't met.
type SyncFulfillmentReportTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncFulfillmentReportTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncFulfillmentReportTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncFulfillmentReportTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncFulfillmentReportTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncFulfillmentReportTemplateRequestValidationError) ErrorName() string {
	return "SyncFulfillmentReportTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncFulfillmentReportTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncFulfillmentReportTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncFulfillmentReportTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncFulfillmentReportTemplateRequestValidationError{}

// Validate checks the field values on SyncFulfillmentReportTemplateResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SyncFulfillmentReportTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncFulfillmentReportTemplateResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SyncFulfillmentReportTemplateResponseMultiError, or nil if none found.
func (m *SyncFulfillmentReportTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncFulfillmentReportTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TemplateId

	// no validation rules for Status

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return SyncFulfillmentReportTemplateResponseMultiError(errors)
	}

	return nil
}

// SyncFulfillmentReportTemplateResponseMultiError is an error wrapping
// multiple validation errors returned by
// SyncFulfillmentReportTemplateResponse.ValidateAll() if the designated
// constraints aren't met.
type SyncFulfillmentReportTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncFulfillmentReportTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncFulfillmentReportTemplateResponseMultiError) AllErrors() []error { return m }

// SyncFulfillmentReportTemplateResponseValidationError is the validation error
// returned by SyncFulfillmentReportTemplateResponse.Validate if the
// designated constraints aren't met.
type SyncFulfillmentReportTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncFulfillmentReportTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncFulfillmentReportTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncFulfillmentReportTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncFulfillmentReportTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncFulfillmentReportTemplateResponseValidationError) ErrorName() string {
	return "SyncFulfillmentReportTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncFulfillmentReportTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncFulfillmentReportTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncFulfillmentReportTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncFulfillmentReportTemplateResponseValidationError{}

// Validate checks the field values on FulfillmentReportQuestionSync with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportQuestionSync) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportQuestionSync with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FulfillmentReportQuestionSyncMultiError, or nil if none found.
func (m *FulfillmentReportQuestionSync) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportQuestionSync) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := FulfillmentReportQuestionSyncValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := FulfillmentReportQuestionSyncValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := QuestionCategory_name[int32(m.GetCategory())]; !ok {
		err := FulfillmentReportQuestionSyncValidationError{
			field:  "Category",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := QuestionType_name[int32(m.GetType())]; !ok {
		err := FulfillmentReportQuestionSyncValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetKey()) > 50 {
		err := FulfillmentReportQuestionSyncValidationError{
			field:  "Key",
			reason: "value length must be at most 50 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTitle()) > 50 {
		err := FulfillmentReportQuestionSyncValidationError{
			field:  "Title",
			reason: "value length must be at most 50 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ExtraJson

	// no validation rules for IsDefault

	// no validation rules for IsRequired

	// no validation rules for IsTypeEditable

	// no validation rules for IsTitleEditable

	// no validation rules for IsOptionsEditable

	// no validation rules for Sort

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportQuestionSyncValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportQuestionSyncValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportQuestionSyncValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportQuestionSyncValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportQuestionSyncValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportQuestionSyncValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Id != nil {

		if m.GetId() <= 0 {
			err := FulfillmentReportQuestionSyncValidationError{
				field:  "Id",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.TemplateId != nil {

		if m.GetTemplateId() <= 0 {
			err := FulfillmentReportQuestionSyncValidationError{
				field:  "TemplateId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return FulfillmentReportQuestionSyncMultiError(errors)
	}

	return nil
}

// FulfillmentReportQuestionSyncMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportQuestionSync.ValidateAll()
// if the designated constraints aren't met.
type FulfillmentReportQuestionSyncMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportQuestionSyncMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportQuestionSyncMultiError) AllErrors() []error { return m }

// FulfillmentReportQuestionSyncValidationError is the validation error
// returned by FulfillmentReportQuestionSync.Validate if the designated
// constraints aren't met.
type FulfillmentReportQuestionSyncValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportQuestionSyncValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportQuestionSyncValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportQuestionSyncValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportQuestionSyncValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportQuestionSyncValidationError) ErrorName() string {
	return "FulfillmentReportQuestionSyncValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportQuestionSyncValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportQuestionSync.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportQuestionSyncValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportQuestionSyncValidationError{}

// Validate checks the field values on QuestionTemplateIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionTemplateIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionTemplateIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionTemplateIdentifierMultiError, or nil if none found.
func (m *QuestionTemplateIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionTemplateIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *QuestionTemplateIdentifier_TemplateId:
		if v == nil {
			err := QuestionTemplateIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetTemplateId() <= 0 {
			err := QuestionTemplateIdentifierValidationError{
				field:  "TemplateId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *QuestionTemplateIdentifier_TemplateUniqueKey:
		if v == nil {
			err := QuestionTemplateIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTemplateUniqueKey()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionTemplateIdentifierValidationError{
						field:  "TemplateUniqueKey",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionTemplateIdentifierValidationError{
						field:  "TemplateUniqueKey",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTemplateUniqueKey()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionTemplateIdentifierValidationError{
					field:  "TemplateUniqueKey",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return QuestionTemplateIdentifierMultiError(errors)
	}

	return nil
}

// QuestionTemplateIdentifierMultiError is an error wrapping multiple
// validation errors returned by QuestionTemplateIdentifier.ValidateAll() if
// the designated constraints aren't met.
type QuestionTemplateIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionTemplateIdentifierMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionTemplateIdentifierMultiError) AllErrors() []error { return m }

// QuestionTemplateIdentifierValidationError is the validation error returned
// by QuestionTemplateIdentifier.Validate if the designated constraints aren't met.
type QuestionTemplateIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionTemplateIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionTemplateIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionTemplateIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionTemplateIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionTemplateIdentifierValidationError) ErrorName() string {
	return "QuestionTemplateIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionTemplateIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionTemplateIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionTemplateIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionTemplateIdentifierValidationError{}

// Validate checks the field values on
// BatchSyncFulfillmentReportQuestionsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BatchSyncFulfillmentReportQuestionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BatchSyncFulfillmentReportQuestionsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// BatchSyncFulfillmentReportQuestionsRequestMultiError, or nil if none found.
func (m *BatchSyncFulfillmentReportQuestionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchSyncFulfillmentReportQuestionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := SyncOperation_name[int32(m.GetOperation())]; !ok {
		err := BatchSyncFulfillmentReportQuestionsRequestValidationError{
			field:  "Operation",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTemplateIdentifier() == nil {
		err := BatchSyncFulfillmentReportQuestionsRequestValidationError{
			field:  "TemplateIdentifier",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTemplateIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchSyncFulfillmentReportQuestionsRequestValidationError{
					field:  "TemplateIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchSyncFulfillmentReportQuestionsRequestValidationError{
					field:  "TemplateIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTemplateIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchSyncFulfillmentReportQuestionsRequestValidationError{
				field:  "TemplateIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchSyncFulfillmentReportQuestionsRequestValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchSyncFulfillmentReportQuestionsRequestValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchSyncFulfillmentReportQuestionsRequestValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchSyncFulfillmentReportQuestionsRequestMultiError(errors)
	}

	return nil
}

// BatchSyncFulfillmentReportQuestionsRequestMultiError is an error wrapping
// multiple validation errors returned by
// BatchSyncFulfillmentReportQuestionsRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchSyncFulfillmentReportQuestionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchSyncFulfillmentReportQuestionsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchSyncFulfillmentReportQuestionsRequestMultiError) AllErrors() []error { return m }

// BatchSyncFulfillmentReportQuestionsRequestValidationError is the validation
// error returned by BatchSyncFulfillmentReportQuestionsRequest.Validate if
// the designated constraints aren't met.
type BatchSyncFulfillmentReportQuestionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchSyncFulfillmentReportQuestionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchSyncFulfillmentReportQuestionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchSyncFulfillmentReportQuestionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchSyncFulfillmentReportQuestionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchSyncFulfillmentReportQuestionsRequestValidationError) ErrorName() string {
	return "BatchSyncFulfillmentReportQuestionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchSyncFulfillmentReportQuestionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchSyncFulfillmentReportQuestionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchSyncFulfillmentReportQuestionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchSyncFulfillmentReportQuestionsRequestValidationError{}

// Validate checks the field values on
// BatchSyncFulfillmentReportQuestionsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BatchSyncFulfillmentReportQuestionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BatchSyncFulfillmentReportQuestionsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// BatchSyncFulfillmentReportQuestionsResponseMultiError, or nil if none found.
func (m *BatchSyncFulfillmentReportQuestionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchSyncFulfillmentReportQuestionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for ErrorMessage

	// no validation rules for TemplateId

	if len(errors) > 0 {
		return BatchSyncFulfillmentReportQuestionsResponseMultiError(errors)
	}

	return nil
}

// BatchSyncFulfillmentReportQuestionsResponseMultiError is an error wrapping
// multiple validation errors returned by
// BatchSyncFulfillmentReportQuestionsResponse.ValidateAll() if the designated
// constraints aren't met.
type BatchSyncFulfillmentReportQuestionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchSyncFulfillmentReportQuestionsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchSyncFulfillmentReportQuestionsResponseMultiError) AllErrors() []error { return m }

// BatchSyncFulfillmentReportQuestionsResponseValidationError is the validation
// error returned by BatchSyncFulfillmentReportQuestionsResponse.Validate if
// the designated constraints aren't met.
type BatchSyncFulfillmentReportQuestionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchSyncFulfillmentReportQuestionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchSyncFulfillmentReportQuestionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchSyncFulfillmentReportQuestionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchSyncFulfillmentReportQuestionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchSyncFulfillmentReportQuestionsResponseValidationError) ErrorName() string {
	return "BatchSyncFulfillmentReportQuestionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchSyncFulfillmentReportQuestionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchSyncFulfillmentReportQuestionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchSyncFulfillmentReportQuestionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchSyncFulfillmentReportQuestionsResponseValidationError{}

// Validate checks the field values on FulfillmentReportSync with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportSync) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportSync with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportSyncMultiError, or nil if none found.
func (m *FulfillmentReportSync) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportSync) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := FulfillmentReportSyncValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := FulfillmentReportSyncValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCustomerId() <= 0 {
		err := FulfillmentReportSyncValidationError{
			field:  "CustomerId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppointmentId() <= 0 {
		err := FulfillmentReportSyncValidationError{
			field:  "AppointmentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := FulfillmentReportSyncValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPetId() <= 0 {
		err := FulfillmentReportSyncValidationError{
			field:  "PetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUuid()) > 50 {
		err := FulfillmentReportSyncValidationError{
			field:  "Uuid",
			reason: "value length must be at most 50 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTemplateVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSyncValidationError{
					field:  "TemplateVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSyncValidationError{
					field:  "TemplateVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTemplateVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSyncValidationError{
				field:  "TemplateVersion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TemplateJson

	// no validation rules for ContentJson

	if utf8.RuneCountInString(m.GetStatus()) > 20 {
		err := FulfillmentReportSyncValidationError{
			field:  "Status",
			reason: "value length must be at most 20 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for LinkOpenedCount

	// no validation rules for ServiceDate

	// no validation rules for UpdateBy

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSyncValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSyncValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSyncValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSyncValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSyncValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSyncValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Id != nil {

		if m.GetId() <= 0 {
			err := FulfillmentReportSyncValidationError{
				field:  "Id",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return FulfillmentReportSyncMultiError(errors)
	}

	return nil
}

// FulfillmentReportSyncMultiError is an error wrapping multiple validation
// errors returned by FulfillmentReportSync.ValidateAll() if the designated
// constraints aren't met.
type FulfillmentReportSyncMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportSyncMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportSyncMultiError) AllErrors() []error { return m }

// FulfillmentReportSyncValidationError is the validation error returned by
// FulfillmentReportSync.Validate if the designated constraints aren't met.
type FulfillmentReportSyncValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportSyncValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportSyncValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportSyncValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportSyncValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportSyncValidationError) ErrorName() string {
	return "FulfillmentReportSyncValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportSyncValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportSync.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportSyncValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportSyncValidationError{}

// Validate checks the field values on FulfillmentReportUniqueKey with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportUniqueKey) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportUniqueKey with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportUniqueKeyMultiError, or nil if none found.
func (m *FulfillmentReportUniqueKey) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportUniqueKey) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetBusinessId() <= 0 {
		err := FulfillmentReportUniqueKeyValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppointmentId() <= 0 {
		err := FulfillmentReportUniqueKeyValidationError{
			field:  "AppointmentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPetId() <= 0 {
		err := FulfillmentReportUniqueKeyValidationError{
			field:  "PetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := FulfillmentReportUniqueKeyValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ServiceDate

	if len(errors) > 0 {
		return FulfillmentReportUniqueKeyMultiError(errors)
	}

	return nil
}

// FulfillmentReportUniqueKeyMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportUniqueKey.ValidateAll() if
// the designated constraints aren't met.
type FulfillmentReportUniqueKeyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportUniqueKeyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportUniqueKeyMultiError) AllErrors() []error { return m }

// FulfillmentReportUniqueKeyValidationError is the validation error returned
// by FulfillmentReportUniqueKey.Validate if the designated constraints aren't met.
type FulfillmentReportUniqueKeyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportUniqueKeyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportUniqueKeyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportUniqueKeyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportUniqueKeyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportUniqueKeyValidationError) ErrorName() string {
	return "FulfillmentReportUniqueKeyValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportUniqueKeyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportUniqueKey.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportUniqueKeyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportUniqueKeyValidationError{}

// Validate checks the field values on SyncFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncFulfillmentReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncFulfillmentReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncFulfillmentReportRequestMultiError, or nil if none found.
func (m *SyncFulfillmentReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncFulfillmentReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := SyncOperation_name[int32(m.GetOperation())]; !ok {
		err := SyncFulfillmentReportRequestValidationError{
			field:  "Operation",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *SyncFulfillmentReportRequest_ReportId:
		if v == nil {
			err := SyncFulfillmentReportRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetReportId() <= 0 {
			err := SyncFulfillmentReportRequestValidationError{
				field:  "ReportId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *SyncFulfillmentReportRequest_UniqueKey:
		if v == nil {
			err := SyncFulfillmentReportRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUniqueKey()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncFulfillmentReportRequestValidationError{
						field:  "UniqueKey",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncFulfillmentReportRequestValidationError{
						field:  "UniqueKey",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUniqueKey()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncFulfillmentReportRequestValidationError{
					field:  "UniqueKey",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if m.Report != nil {

		if all {
			switch v := interface{}(m.GetReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncFulfillmentReportRequestValidationError{
						field:  "Report",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncFulfillmentReportRequestValidationError{
						field:  "Report",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncFulfillmentReportRequestValidationError{
					field:  "Report",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SyncFulfillmentReportRequestMultiError(errors)
	}

	return nil
}

// SyncFulfillmentReportRequestMultiError is an error wrapping multiple
// validation errors returned by SyncFulfillmentReportRequest.ValidateAll() if
// the designated constraints aren't met.
type SyncFulfillmentReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncFulfillmentReportRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncFulfillmentReportRequestMultiError) AllErrors() []error { return m }

// SyncFulfillmentReportRequestValidationError is the validation error returned
// by SyncFulfillmentReportRequest.Validate if the designated constraints
// aren't met.
type SyncFulfillmentReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncFulfillmentReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncFulfillmentReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncFulfillmentReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncFulfillmentReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncFulfillmentReportRequestValidationError) ErrorName() string {
	return "SyncFulfillmentReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncFulfillmentReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncFulfillmentReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncFulfillmentReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncFulfillmentReportRequestValidationError{}

// Validate checks the field values on SyncFulfillmentReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncFulfillmentReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncFulfillmentReportResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SyncFulfillmentReportResponseMultiError, or nil if none found.
func (m *SyncFulfillmentReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncFulfillmentReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReportId

	// no validation rules for Status

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return SyncFulfillmentReportResponseMultiError(errors)
	}

	return nil
}

// SyncFulfillmentReportResponseMultiError is an error wrapping multiple
// validation errors returned by SyncFulfillmentReportResponse.ValidateAll()
// if the designated constraints aren't met.
type SyncFulfillmentReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncFulfillmentReportResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncFulfillmentReportResponseMultiError) AllErrors() []error { return m }

// SyncFulfillmentReportResponseValidationError is the validation error
// returned by SyncFulfillmentReportResponse.Validate if the designated
// constraints aren't met.
type SyncFulfillmentReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncFulfillmentReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncFulfillmentReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncFulfillmentReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncFulfillmentReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncFulfillmentReportResponseValidationError) ErrorName() string {
	return "SyncFulfillmentReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncFulfillmentReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncFulfillmentReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncFulfillmentReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncFulfillmentReportResponseValidationError{}

// Validate checks the field values on FulfillmentReportSendRecordSync with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportSendRecordSync) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportSendRecordSync with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FulfillmentReportSendRecordSyncMultiError, or nil if none found.
func (m *FulfillmentReportSendRecordSync) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportSendRecordSync) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReportId() <= 0 {
		err := FulfillmentReportSendRecordSyncValidationError{
			field:  "ReportId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCompanyId() <= 0 {
		err := FulfillmentReportSendRecordSyncValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := FulfillmentReportSendRecordSyncValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppointmentId() <= 0 {
		err := FulfillmentReportSendRecordSyncValidationError{
			field:  "AppointmentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ContentJson

	if m.GetPetId() <= 0 {
		err := FulfillmentReportSendRecordSyncValidationError{
			field:  "PetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := SendMethod_name[int32(m.GetSendMethod())]; !ok {
		err := FulfillmentReportSendRecordSyncValidationError{
			field:  "SendMethod",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSentTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordSyncValidationError{
					field:  "SentTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordSyncValidationError{
					field:  "SentTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSentTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSendRecordSyncValidationError{
				field:  "SentTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SentBy

	if utf8.RuneCountInString(m.GetErrorMessage()) > 500 {
		err := FulfillmentReportSendRecordSyncValidationError{
			field:  "ErrorMessage",
			reason: "value length must be at most 500 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsSentSuccess

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordSyncValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordSyncValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSendRecordSyncValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordSyncValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordSyncValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSendRecordSyncValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Id != nil {

		if m.GetId() <= 0 {
			err := FulfillmentReportSendRecordSyncValidationError{
				field:  "Id",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return FulfillmentReportSendRecordSyncMultiError(errors)
	}

	return nil
}

// FulfillmentReportSendRecordSyncMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportSendRecordSync.ValidateAll()
// if the designated constraints aren't met.
type FulfillmentReportSendRecordSyncMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportSendRecordSyncMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportSendRecordSyncMultiError) AllErrors() []error { return m }

// FulfillmentReportSendRecordSyncValidationError is the validation error
// returned by FulfillmentReportSendRecordSync.Validate if the designated
// constraints aren't met.
type FulfillmentReportSendRecordSyncValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportSendRecordSyncValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportSendRecordSyncValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportSendRecordSyncValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportSendRecordSyncValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportSendRecordSyncValidationError) ErrorName() string {
	return "FulfillmentReportSendRecordSyncValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportSendRecordSyncValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportSendRecordSync.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportSendRecordSyncValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportSendRecordSyncValidationError{}

// Validate checks the field values on SendRecordIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendRecordIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendRecordIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendRecordIdentifierMultiError, or nil if none found.
func (m *SendRecordIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *SendRecordIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := SendMethod_name[int32(m.GetSendMethod())]; !ok {
		err := SendRecordIdentifierValidationError{
			field:  "SendMethod",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetReportUniqueKey() == nil {
		err := SendRecordIdentifierValidationError{
			field:  "ReportUniqueKey",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReportUniqueKey()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendRecordIdentifierValidationError{
					field:  "ReportUniqueKey",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendRecordIdentifierValidationError{
					field:  "ReportUniqueKey",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportUniqueKey()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendRecordIdentifierValidationError{
				field:  "ReportUniqueKey",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendRecordIdentifierMultiError(errors)
	}

	return nil
}

// SendRecordIdentifierMultiError is an error wrapping multiple validation
// errors returned by SendRecordIdentifier.ValidateAll() if the designated
// constraints aren't met.
type SendRecordIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendRecordIdentifierMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendRecordIdentifierMultiError) AllErrors() []error { return m }

// SendRecordIdentifierValidationError is the validation error returned by
// SendRecordIdentifier.Validate if the designated constraints aren't met.
type SendRecordIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendRecordIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendRecordIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendRecordIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendRecordIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendRecordIdentifierValidationError) ErrorName() string {
	return "SendRecordIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e SendRecordIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendRecordIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendRecordIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendRecordIdentifierValidationError{}

// Validate checks the field values on SyncFulfillmentReportSendRecordRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SyncFulfillmentReportSendRecordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SyncFulfillmentReportSendRecordRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// SyncFulfillmentReportSendRecordRequestMultiError, or nil if none found.
func (m *SyncFulfillmentReportSendRecordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncFulfillmentReportSendRecordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := SyncOperation_name[int32(m.GetOperation())]; !ok {
		err := SyncFulfillmentReportSendRecordRequestValidationError{
			field:  "Operation",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *SyncFulfillmentReportSendRecordRequest_RecordId:
		if v == nil {
			err := SyncFulfillmentReportSendRecordRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetRecordId() <= 0 {
			err := SyncFulfillmentReportSendRecordRequestValidationError{
				field:  "RecordId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *SyncFulfillmentReportSendRecordRequest_BusinessIdentifier:
		if v == nil {
			err := SyncFulfillmentReportSendRecordRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBusinessIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncFulfillmentReportSendRecordRequestValidationError{
						field:  "BusinessIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncFulfillmentReportSendRecordRequestValidationError{
						field:  "BusinessIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBusinessIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncFulfillmentReportSendRecordRequestValidationError{
					field:  "BusinessIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if m.SendRecord != nil {

		if all {
			switch v := interface{}(m.GetSendRecord()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncFulfillmentReportSendRecordRequestValidationError{
						field:  "SendRecord",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncFulfillmentReportSendRecordRequestValidationError{
						field:  "SendRecord",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSendRecord()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncFulfillmentReportSendRecordRequestValidationError{
					field:  "SendRecord",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SyncFulfillmentReportSendRecordRequestMultiError(errors)
	}

	return nil
}

// SyncFulfillmentReportSendRecordRequestMultiError is an error wrapping
// multiple validation errors returned by
// SyncFulfillmentReportSendRecordRequest.ValidateAll() if the designated
// constraints aren't met.
type SyncFulfillmentReportSendRecordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncFulfillmentReportSendRecordRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncFulfillmentReportSendRecordRequestMultiError) AllErrors() []error { return m }

// SyncFulfillmentReportSendRecordRequestValidationError is the validation
// error returned by SyncFulfillmentReportSendRecordRequest.Validate if the
// designated constraints aren't met.
type SyncFulfillmentReportSendRecordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncFulfillmentReportSendRecordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncFulfillmentReportSendRecordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncFulfillmentReportSendRecordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncFulfillmentReportSendRecordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncFulfillmentReportSendRecordRequestValidationError) ErrorName() string {
	return "SyncFulfillmentReportSendRecordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncFulfillmentReportSendRecordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncFulfillmentReportSendRecordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncFulfillmentReportSendRecordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncFulfillmentReportSendRecordRequestValidationError{}

// Validate checks the field values on SyncFulfillmentReportSendRecordResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SyncFulfillmentReportSendRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SyncFulfillmentReportSendRecordResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// SyncFulfillmentReportSendRecordResponseMultiError, or nil if none found.
func (m *SyncFulfillmentReportSendRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncFulfillmentReportSendRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecordId

	// no validation rules for ReportId

	// no validation rules for Status

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return SyncFulfillmentReportSendRecordResponseMultiError(errors)
	}

	return nil
}

// SyncFulfillmentReportSendRecordResponseMultiError is an error wrapping
// multiple validation errors returned by
// SyncFulfillmentReportSendRecordResponse.ValidateAll() if the designated
// constraints aren't met.
type SyncFulfillmentReportSendRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncFulfillmentReportSendRecordResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncFulfillmentReportSendRecordResponseMultiError) AllErrors() []error { return m }

// SyncFulfillmentReportSendRecordResponseValidationError is the validation
// error returned by SyncFulfillmentReportSendRecordResponse.Validate if the
// designated constraints aren't met.
type SyncFulfillmentReportSendRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncFulfillmentReportSendRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncFulfillmentReportSendRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncFulfillmentReportSendRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncFulfillmentReportSendRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncFulfillmentReportSendRecordResponseValidationError) ErrorName() string {
	return "SyncFulfillmentReportSendRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncFulfillmentReportSendRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncFulfillmentReportSendRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncFulfillmentReportSendRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncFulfillmentReportSendRecordResponseValidationError{}

// Validate checks the field values on BatchMigrateTemplatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchMigrateTemplatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchMigrateTemplatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchMigrateTemplatesRequestMultiError, or nil if none found.
func (m *BatchMigrateTemplatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchMigrateTemplatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTemplates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchMigrateTemplatesRequestValidationError{
						field:  fmt.Sprintf("Templates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchMigrateTemplatesRequestValidationError{
						field:  fmt.Sprintf("Templates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchMigrateTemplatesRequestValidationError{
					field:  fmt.Sprintf("Templates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchMigrateTemplatesRequestMultiError(errors)
	}

	return nil
}

// BatchMigrateTemplatesRequestMultiError is an error wrapping multiple
// validation errors returned by BatchMigrateTemplatesRequest.ValidateAll() if
// the designated constraints aren't met.
type BatchMigrateTemplatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMigrateTemplatesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMigrateTemplatesRequestMultiError) AllErrors() []error { return m }

// BatchMigrateTemplatesRequestValidationError is the validation error returned
// by BatchMigrateTemplatesRequest.Validate if the designated constraints
// aren't met.
type BatchMigrateTemplatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchMigrateTemplatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchMigrateTemplatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchMigrateTemplatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchMigrateTemplatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchMigrateTemplatesRequestValidationError) ErrorName() string {
	return "BatchMigrateTemplatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchMigrateTemplatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchMigrateTemplatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchMigrateTemplatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchMigrateTemplatesRequestValidationError{}

// Validate checks the field values on BatchMigrateTemplatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchMigrateTemplatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchMigrateTemplatesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchMigrateTemplatesResponseMultiError, or nil if none found.
func (m *BatchMigrateTemplatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchMigrateTemplatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessCount

	// no validation rules for FailedCount

	// no validation rules for SkippedCount

	if len(errors) > 0 {
		return BatchMigrateTemplatesResponseMultiError(errors)
	}

	return nil
}

// BatchMigrateTemplatesResponseMultiError is an error wrapping multiple
// validation errors returned by BatchMigrateTemplatesResponse.ValidateAll()
// if the designated constraints aren't met.
type BatchMigrateTemplatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMigrateTemplatesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMigrateTemplatesResponseMultiError) AllErrors() []error { return m }

// BatchMigrateTemplatesResponseValidationError is the validation error
// returned by BatchMigrateTemplatesResponse.Validate if the designated
// constraints aren't met.
type BatchMigrateTemplatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchMigrateTemplatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchMigrateTemplatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchMigrateTemplatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchMigrateTemplatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchMigrateTemplatesResponseValidationError) ErrorName() string {
	return "BatchMigrateTemplatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchMigrateTemplatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchMigrateTemplatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchMigrateTemplatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchMigrateTemplatesResponseValidationError{}

// Validate checks the field values on BatchMigrateQuestionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchMigrateQuestionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchMigrateQuestionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchMigrateQuestionsRequestMultiError, or nil if none found.
func (m *BatchMigrateQuestionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchMigrateQuestionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetQuestions()); l < 1 || l > 500 {
		err := BatchMigrateQuestionsRequestValidationError{
			field:  "Questions",
			reason: "value must contain between 1 and 500 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchMigrateQuestionsRequestValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchMigrateQuestionsRequestValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchMigrateQuestionsRequestValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchMigrateQuestionsRequestMultiError(errors)
	}

	return nil
}

// BatchMigrateQuestionsRequestMultiError is an error wrapping multiple
// validation errors returned by BatchMigrateQuestionsRequest.ValidateAll() if
// the designated constraints aren't met.
type BatchMigrateQuestionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMigrateQuestionsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMigrateQuestionsRequestMultiError) AllErrors() []error { return m }

// BatchMigrateQuestionsRequestValidationError is the validation error returned
// by BatchMigrateQuestionsRequest.Validate if the designated constraints
// aren't met.
type BatchMigrateQuestionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchMigrateQuestionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchMigrateQuestionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchMigrateQuestionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchMigrateQuestionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchMigrateQuestionsRequestValidationError) ErrorName() string {
	return "BatchMigrateQuestionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchMigrateQuestionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchMigrateQuestionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchMigrateQuestionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchMigrateQuestionsRequestValidationError{}

// Validate checks the field values on BatchMigrateQuestionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchMigrateQuestionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchMigrateQuestionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchMigrateQuestionsResponseMultiError, or nil if none found.
func (m *BatchMigrateQuestionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchMigrateQuestionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessCount

	// no validation rules for FailedCount

	// no validation rules for SkippedCount

	if len(errors) > 0 {
		return BatchMigrateQuestionsResponseMultiError(errors)
	}

	return nil
}

// BatchMigrateQuestionsResponseMultiError is an error wrapping multiple
// validation errors returned by BatchMigrateQuestionsResponse.ValidateAll()
// if the designated constraints aren't met.
type BatchMigrateQuestionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMigrateQuestionsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMigrateQuestionsResponseMultiError) AllErrors() []error { return m }

// BatchMigrateQuestionsResponseValidationError is the validation error
// returned by BatchMigrateQuestionsResponse.Validate if the designated
// constraints aren't met.
type BatchMigrateQuestionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchMigrateQuestionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchMigrateQuestionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchMigrateQuestionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchMigrateQuestionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchMigrateQuestionsResponseValidationError) ErrorName() string {
	return "BatchMigrateQuestionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchMigrateQuestionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchMigrateQuestionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchMigrateQuestionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchMigrateQuestionsResponseValidationError{}

// Validate checks the field values on BatchMigrateReportsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchMigrateReportsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchMigrateReportsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchMigrateReportsRequestMultiError, or nil if none found.
func (m *BatchMigrateReportsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchMigrateReportsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetReports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchMigrateReportsRequestValidationError{
						field:  fmt.Sprintf("Reports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchMigrateReportsRequestValidationError{
						field:  fmt.Sprintf("Reports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchMigrateReportsRequestValidationError{
					field:  fmt.Sprintf("Reports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchMigrateReportsRequestMultiError(errors)
	}

	return nil
}

// BatchMigrateReportsRequestMultiError is an error wrapping multiple
// validation errors returned by BatchMigrateReportsRequest.ValidateAll() if
// the designated constraints aren't met.
type BatchMigrateReportsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMigrateReportsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMigrateReportsRequestMultiError) AllErrors() []error { return m }

// BatchMigrateReportsRequestValidationError is the validation error returned
// by BatchMigrateReportsRequest.Validate if the designated constraints aren't met.
type BatchMigrateReportsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchMigrateReportsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchMigrateReportsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchMigrateReportsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchMigrateReportsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchMigrateReportsRequestValidationError) ErrorName() string {
	return "BatchMigrateReportsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchMigrateReportsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchMigrateReportsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchMigrateReportsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchMigrateReportsRequestValidationError{}

// Validate checks the field values on BatchMigrateReportsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchMigrateReportsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchMigrateReportsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchMigrateReportsResponseMultiError, or nil if none found.
func (m *BatchMigrateReportsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchMigrateReportsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessCount

	// no validation rules for FailedCount

	// no validation rules for SkippedCount

	if len(errors) > 0 {
		return BatchMigrateReportsResponseMultiError(errors)
	}

	return nil
}

// BatchMigrateReportsResponseMultiError is an error wrapping multiple
// validation errors returned by BatchMigrateReportsResponse.ValidateAll() if
// the designated constraints aren't met.
type BatchMigrateReportsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMigrateReportsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMigrateReportsResponseMultiError) AllErrors() []error { return m }

// BatchMigrateReportsResponseValidationError is the validation error returned
// by BatchMigrateReportsResponse.Validate if the designated constraints
// aren't met.
type BatchMigrateReportsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchMigrateReportsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchMigrateReportsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchMigrateReportsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchMigrateReportsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchMigrateReportsResponseValidationError) ErrorName() string {
	return "BatchMigrateReportsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchMigrateReportsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchMigrateReportsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchMigrateReportsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchMigrateReportsResponseValidationError{}

// Validate checks the field values on BatchMigrateRecordsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchMigrateRecordsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchMigrateRecordsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchMigrateRecordsRequestMultiError, or nil if none found.
func (m *BatchMigrateRecordsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchMigrateRecordsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchMigrateRecordsRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchMigrateRecordsRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchMigrateRecordsRequestValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchMigrateRecordsRequestMultiError(errors)
	}

	return nil
}

// BatchMigrateRecordsRequestMultiError is an error wrapping multiple
// validation errors returned by BatchMigrateRecordsRequest.ValidateAll() if
// the designated constraints aren't met.
type BatchMigrateRecordsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMigrateRecordsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMigrateRecordsRequestMultiError) AllErrors() []error { return m }

// BatchMigrateRecordsRequestValidationError is the validation error returned
// by BatchMigrateRecordsRequest.Validate if the designated constraints aren't met.
type BatchMigrateRecordsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchMigrateRecordsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchMigrateRecordsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchMigrateRecordsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchMigrateRecordsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchMigrateRecordsRequestValidationError) ErrorName() string {
	return "BatchMigrateRecordsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchMigrateRecordsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchMigrateRecordsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchMigrateRecordsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchMigrateRecordsRequestValidationError{}

// Validate checks the field values on BatchMigrateRecordsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchMigrateRecordsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchMigrateRecordsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchMigrateRecordsResponseMultiError, or nil if none found.
func (m *BatchMigrateRecordsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchMigrateRecordsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessCount

	// no validation rules for FailedCount

	// no validation rules for SkippedCount

	if len(errors) > 0 {
		return BatchMigrateRecordsResponseMultiError(errors)
	}

	return nil
}

// BatchMigrateRecordsResponseMultiError is an error wrapping multiple
// validation errors returned by BatchMigrateRecordsResponse.ValidateAll() if
// the designated constraints aren't met.
type BatchMigrateRecordsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMigrateRecordsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMigrateRecordsResponseMultiError) AllErrors() []error { return m }

// BatchMigrateRecordsResponseValidationError is the validation error returned
// by BatchMigrateRecordsResponse.Validate if the designated constraints
// aren't met.
type BatchMigrateRecordsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchMigrateRecordsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchMigrateRecordsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchMigrateRecordsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchMigrateRecordsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchMigrateRecordsResponseValidationError) ErrorName() string {
	return "BatchMigrateRecordsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchMigrateRecordsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchMigrateRecordsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchMigrateRecordsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchMigrateRecordsResponseValidationError{}

// Validate checks the field values on GetTemplatesByUniqueKeysRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTemplatesByUniqueKeysRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTemplatesByUniqueKeysRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTemplatesByUniqueKeysRequestMultiError, or nil if none found.
func (m *GetTemplatesByUniqueKeysRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTemplatesByUniqueKeysRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetUniqueKeys()); l < 1 || l > 100 {
		err := GetTemplatesByUniqueKeysRequestValidationError{
			field:  "UniqueKeys",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetUniqueKeys() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTemplatesByUniqueKeysRequestValidationError{
						field:  fmt.Sprintf("UniqueKeys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTemplatesByUniqueKeysRequestValidationError{
						field:  fmt.Sprintf("UniqueKeys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTemplatesByUniqueKeysRequestValidationError{
					field:  fmt.Sprintf("UniqueKeys[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTemplatesByUniqueKeysRequestMultiError(errors)
	}

	return nil
}

// GetTemplatesByUniqueKeysRequestMultiError is an error wrapping multiple
// validation errors returned by GetTemplatesByUniqueKeysRequest.ValidateAll()
// if the designated constraints aren't met.
type GetTemplatesByUniqueKeysRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTemplatesByUniqueKeysRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTemplatesByUniqueKeysRequestMultiError) AllErrors() []error { return m }

// GetTemplatesByUniqueKeysRequestValidationError is the validation error
// returned by GetTemplatesByUniqueKeysRequest.Validate if the designated
// constraints aren't met.
type GetTemplatesByUniqueKeysRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTemplatesByUniqueKeysRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTemplatesByUniqueKeysRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTemplatesByUniqueKeysRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTemplatesByUniqueKeysRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTemplatesByUniqueKeysRequestValidationError) ErrorName() string {
	return "GetTemplatesByUniqueKeysRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTemplatesByUniqueKeysRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTemplatesByUniqueKeysRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTemplatesByUniqueKeysRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTemplatesByUniqueKeysRequestValidationError{}

// Validate checks the field values on GetTemplatesByUniqueKeysResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTemplatesByUniqueKeysResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTemplatesByUniqueKeysResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTemplatesByUniqueKeysResponseMultiError, or nil if none found.
func (m *GetTemplatesByUniqueKeysResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTemplatesByUniqueKeysResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTemplates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTemplatesByUniqueKeysResponseValidationError{
						field:  fmt.Sprintf("Templates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTemplatesByUniqueKeysResponseValidationError{
						field:  fmt.Sprintf("Templates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTemplatesByUniqueKeysResponseValidationError{
					field:  fmt.Sprintf("Templates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTemplatesByUniqueKeysResponseMultiError(errors)
	}

	return nil
}

// GetTemplatesByUniqueKeysResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTemplatesByUniqueKeysResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTemplatesByUniqueKeysResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTemplatesByUniqueKeysResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTemplatesByUniqueKeysResponseMultiError) AllErrors() []error { return m }

// GetTemplatesByUniqueKeysResponseValidationError is the validation error
// returned by GetTemplatesByUniqueKeysResponse.Validate if the designated
// constraints aren't met.
type GetTemplatesByUniqueKeysResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTemplatesByUniqueKeysResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTemplatesByUniqueKeysResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTemplatesByUniqueKeysResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTemplatesByUniqueKeysResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTemplatesByUniqueKeysResponseValidationError) ErrorName() string {
	return "GetTemplatesByUniqueKeysResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTemplatesByUniqueKeysResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTemplatesByUniqueKeysResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTemplatesByUniqueKeysResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTemplatesByUniqueKeysResponseValidationError{}

// Validate checks the field values on GetQuestionsByTemplateKeysRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetQuestionsByTemplateKeysRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionsByTemplateKeysRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetQuestionsByTemplateKeysRequestMultiError, or nil if none found.
func (m *GetQuestionsByTemplateKeysRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionsByTemplateKeysRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetTemplateKeys()); l < 1 || l > 100 {
		err := GetQuestionsByTemplateKeysRequestValidationError{
			field:  "TemplateKeys",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetTemplateKeys() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQuestionsByTemplateKeysRequestValidationError{
						field:  fmt.Sprintf("TemplateKeys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQuestionsByTemplateKeysRequestValidationError{
						field:  fmt.Sprintf("TemplateKeys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQuestionsByTemplateKeysRequestValidationError{
					field:  fmt.Sprintf("TemplateKeys[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetQuestionsByTemplateKeysRequestMultiError(errors)
	}

	return nil
}

// GetQuestionsByTemplateKeysRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetQuestionsByTemplateKeysRequest.ValidateAll() if the designated
// constraints aren't met.
type GetQuestionsByTemplateKeysRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionsByTemplateKeysRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionsByTemplateKeysRequestMultiError) AllErrors() []error { return m }

// GetQuestionsByTemplateKeysRequestValidationError is the validation error
// returned by GetQuestionsByTemplateKeysRequest.Validate if the designated
// constraints aren't met.
type GetQuestionsByTemplateKeysRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionsByTemplateKeysRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionsByTemplateKeysRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionsByTemplateKeysRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionsByTemplateKeysRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionsByTemplateKeysRequestValidationError) ErrorName() string {
	return "GetQuestionsByTemplateKeysRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionsByTemplateKeysRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionsByTemplateKeysRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionsByTemplateKeysRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionsByTemplateKeysRequestValidationError{}

// Validate checks the field values on GetQuestionsByTemplateKeysResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetQuestionsByTemplateKeysResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionsByTemplateKeysResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetQuestionsByTemplateKeysResponseMultiError, or nil if none found.
func (m *GetQuestionsByTemplateKeysResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionsByTemplateKeysResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQuestionsByTemplateKeysResponseValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQuestionsByTemplateKeysResponseValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQuestionsByTemplateKeysResponseValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetQuestionsByTemplateKeysResponseMultiError(errors)
	}

	return nil
}

// GetQuestionsByTemplateKeysResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetQuestionsByTemplateKeysResponse.ValidateAll() if the designated
// constraints aren't met.
type GetQuestionsByTemplateKeysResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionsByTemplateKeysResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionsByTemplateKeysResponseMultiError) AllErrors() []error { return m }

// GetQuestionsByTemplateKeysResponseValidationError is the validation error
// returned by GetQuestionsByTemplateKeysResponse.Validate if the designated
// constraints aren't met.
type GetQuestionsByTemplateKeysResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionsByTemplateKeysResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionsByTemplateKeysResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionsByTemplateKeysResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionsByTemplateKeysResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionsByTemplateKeysResponseValidationError) ErrorName() string {
	return "GetQuestionsByTemplateKeysResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionsByTemplateKeysResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionsByTemplateKeysResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionsByTemplateKeysResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionsByTemplateKeysResponseValidationError{}

// Validate checks the field values on GetReportsByUniqueKeysRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReportsByUniqueKeysRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReportsByUniqueKeysRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetReportsByUniqueKeysRequestMultiError, or nil if none found.
func (m *GetReportsByUniqueKeysRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReportsByUniqueKeysRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetUniqueKeys()); l < 1 || l > 100 {
		err := GetReportsByUniqueKeysRequestValidationError{
			field:  "UniqueKeys",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetUniqueKeys() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetReportsByUniqueKeysRequestValidationError{
						field:  fmt.Sprintf("UniqueKeys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetReportsByUniqueKeysRequestValidationError{
						field:  fmt.Sprintf("UniqueKeys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetReportsByUniqueKeysRequestValidationError{
					field:  fmt.Sprintf("UniqueKeys[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetReportsByUniqueKeysRequestMultiError(errors)
	}

	return nil
}

// GetReportsByUniqueKeysRequestMultiError is an error wrapping multiple
// validation errors returned by GetReportsByUniqueKeysRequest.ValidateAll()
// if the designated constraints aren't met.
type GetReportsByUniqueKeysRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReportsByUniqueKeysRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReportsByUniqueKeysRequestMultiError) AllErrors() []error { return m }

// GetReportsByUniqueKeysRequestValidationError is the validation error
// returned by GetReportsByUniqueKeysRequest.Validate if the designated
// constraints aren't met.
type GetReportsByUniqueKeysRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReportsByUniqueKeysRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReportsByUniqueKeysRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReportsByUniqueKeysRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReportsByUniqueKeysRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReportsByUniqueKeysRequestValidationError) ErrorName() string {
	return "GetReportsByUniqueKeysRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetReportsByUniqueKeysRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReportsByUniqueKeysRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReportsByUniqueKeysRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReportsByUniqueKeysRequestValidationError{}

// Validate checks the field values on GetReportsByUniqueKeysResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReportsByUniqueKeysResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReportsByUniqueKeysResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetReportsByUniqueKeysResponseMultiError, or nil if none found.
func (m *GetReportsByUniqueKeysResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReportsByUniqueKeysResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetReports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetReportsByUniqueKeysResponseValidationError{
						field:  fmt.Sprintf("Reports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetReportsByUniqueKeysResponseValidationError{
						field:  fmt.Sprintf("Reports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetReportsByUniqueKeysResponseValidationError{
					field:  fmt.Sprintf("Reports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetReportsByUniqueKeysResponseMultiError(errors)
	}

	return nil
}

// GetReportsByUniqueKeysResponseMultiError is an error wrapping multiple
// validation errors returned by GetReportsByUniqueKeysResponse.ValidateAll()
// if the designated constraints aren't met.
type GetReportsByUniqueKeysResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReportsByUniqueKeysResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReportsByUniqueKeysResponseMultiError) AllErrors() []error { return m }

// GetReportsByUniqueKeysResponseValidationError is the validation error
// returned by GetReportsByUniqueKeysResponse.Validate if the designated
// constraints aren't met.
type GetReportsByUniqueKeysResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReportsByUniqueKeysResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReportsByUniqueKeysResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReportsByUniqueKeysResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReportsByUniqueKeysResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReportsByUniqueKeysResponseValidationError) ErrorName() string {
	return "GetReportsByUniqueKeysResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetReportsByUniqueKeysResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReportsByUniqueKeysResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReportsByUniqueKeysResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReportsByUniqueKeysResponseValidationError{}

// Validate checks the field values on GetRecordsByReportKeysRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecordsByReportKeysRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecordsByReportKeysRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRecordsByReportKeysRequestMultiError, or nil if none found.
func (m *GetRecordsByReportKeysRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecordsByReportKeysRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetReportKeys()); l < 1 || l > 100 {
		err := GetRecordsByReportKeysRequestValidationError{
			field:  "ReportKeys",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetReportKeys() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecordsByReportKeysRequestValidationError{
						field:  fmt.Sprintf("ReportKeys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecordsByReportKeysRequestValidationError{
						field:  fmt.Sprintf("ReportKeys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecordsByReportKeysRequestValidationError{
					field:  fmt.Sprintf("ReportKeys[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.SendMethod != nil {
		// no validation rules for SendMethod
	}

	if len(errors) > 0 {
		return GetRecordsByReportKeysRequestMultiError(errors)
	}

	return nil
}

// GetRecordsByReportKeysRequestMultiError is an error wrapping multiple
// validation errors returned by GetRecordsByReportKeysRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRecordsByReportKeysRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecordsByReportKeysRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecordsByReportKeysRequestMultiError) AllErrors() []error { return m }

// GetRecordsByReportKeysRequestValidationError is the validation error
// returned by GetRecordsByReportKeysRequest.Validate if the designated
// constraints aren't met.
type GetRecordsByReportKeysRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecordsByReportKeysRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecordsByReportKeysRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecordsByReportKeysRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecordsByReportKeysRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecordsByReportKeysRequestValidationError) ErrorName() string {
	return "GetRecordsByReportKeysRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecordsByReportKeysRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecordsByReportKeysRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecordsByReportKeysRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecordsByReportKeysRequestValidationError{}

// Validate checks the field values on GetRecordsByReportKeysResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecordsByReportKeysResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecordsByReportKeysResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRecordsByReportKeysResponseMultiError, or nil if none found.
func (m *GetRecordsByReportKeysResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecordsByReportKeysResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecordsByReportKeysResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecordsByReportKeysResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecordsByReportKeysResponseValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRecordsByReportKeysResponseMultiError(errors)
	}

	return nil
}

// GetRecordsByReportKeysResponseMultiError is an error wrapping multiple
// validation errors returned by GetRecordsByReportKeysResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRecordsByReportKeysResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecordsByReportKeysResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecordsByReportKeysResponseMultiError) AllErrors() []error { return m }

// GetRecordsByReportKeysResponseValidationError is the validation error
// returned by GetRecordsByReportKeysResponse.Validate if the designated
// constraints aren't met.
type GetRecordsByReportKeysResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecordsByReportKeysResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecordsByReportKeysResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecordsByReportKeysResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecordsByReportKeysResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecordsByReportKeysResponseValidationError) ErrorName() string {
	return "GetRecordsByReportKeysResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecordsByReportKeysResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecordsByReportKeysResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecordsByReportKeysResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecordsByReportKeysResponseValidationError{}

// Validate checks the field values on
// ListFulfillmentReportResponse_FulfillmentReportCard with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListFulfillmentReportResponse_FulfillmentReportCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListFulfillmentReportResponse_FulfillmentReportCard with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ListFulfillmentReportResponse_FulfillmentReportCardMultiError, or nil if
// none found.
func (m *ListFulfillmentReportResponse_FulfillmentReportCard) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFulfillmentReportResponse_FulfillmentReportCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReportCardId

	if all {
		switch v := interface{}(m.GetPet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
					field:  "Pet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
					field:  "Pet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
				field:  "Pet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSendTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
					field:  "SendTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
					field:  "SendTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSendTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFulfillmentReportResponse_FulfillmentReportCardValidationError{
				field:  "SendTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AppointmentId

	// no validation rules for SendMethod

	// no validation rules for MediaCount

	// no validation rules for ServiceDate

	// no validation rules for Uuid

	if len(errors) > 0 {
		return ListFulfillmentReportResponse_FulfillmentReportCardMultiError(errors)
	}

	return nil
}

// ListFulfillmentReportResponse_FulfillmentReportCardMultiError is an error
// wrapping multiple validation errors returned by
// ListFulfillmentReportResponse_FulfillmentReportCard.ValidateAll() if the
// designated constraints aren't met.
type ListFulfillmentReportResponse_FulfillmentReportCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFulfillmentReportResponse_FulfillmentReportCardMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFulfillmentReportResponse_FulfillmentReportCardMultiError) AllErrors() []error { return m }

// ListFulfillmentReportResponse_FulfillmentReportCardValidationError is the
// validation error returned by
// ListFulfillmentReportResponse_FulfillmentReportCard.Validate if the
// designated constraints aren't met.
type ListFulfillmentReportResponse_FulfillmentReportCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFulfillmentReportResponse_FulfillmentReportCardValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ListFulfillmentReportResponse_FulfillmentReportCardValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListFulfillmentReportResponse_FulfillmentReportCardValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ListFulfillmentReportResponse_FulfillmentReportCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFulfillmentReportResponse_FulfillmentReportCardValidationError) ErrorName() string {
	return "ListFulfillmentReportResponse_FulfillmentReportCardValidationError"
}

// Error satisfies the builtin error interface
func (e ListFulfillmentReportResponse_FulfillmentReportCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFulfillmentReportResponse_FulfillmentReportCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFulfillmentReportResponse_FulfillmentReportCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFulfillmentReportResponse_FulfillmentReportCardValidationError{}

// Validate checks the field values on
// ListFulfillmentReportResponse_FulfillmentReportCard_Pet with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListFulfillmentReportResponse_FulfillmentReportCard_Pet with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListFulfillmentReportResponse_FulfillmentReportCard_PetMultiError, or nil
// if none found.
func (m *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFulfillmentReportResponse_FulfillmentReportCard_Pet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PetId

	// no validation rules for PetName

	// no validation rules for AvatarPath

	// no validation rules for PetType

	if len(errors) > 0 {
		return ListFulfillmentReportResponse_FulfillmentReportCard_PetMultiError(errors)
	}

	return nil
}

// ListFulfillmentReportResponse_FulfillmentReportCard_PetMultiError is an
// error wrapping multiple validation errors returned by
// ListFulfillmentReportResponse_FulfillmentReportCard_Pet.ValidateAll() if
// the designated constraints aren't met.
type ListFulfillmentReportResponse_FulfillmentReportCard_PetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFulfillmentReportResponse_FulfillmentReportCard_PetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFulfillmentReportResponse_FulfillmentReportCard_PetMultiError) AllErrors() []error {
	return m
}

// ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError is
// the validation error returned by
// ListFulfillmentReportResponse_FulfillmentReportCard_Pet.Validate if the
// designated constraints aren't met.
type ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError) ErrorName() string {
	return "ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError"
}

// Error satisfies the builtin error interface
func (e ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFulfillmentReportResponse_FulfillmentReportCard_Pet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFulfillmentReportResponse_FulfillmentReportCard_PetValidationError{}
