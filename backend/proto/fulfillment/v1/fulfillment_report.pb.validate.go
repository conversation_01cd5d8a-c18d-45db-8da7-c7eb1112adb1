// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/fulfillment/v1/fulfillment_report.proto

package fulfillmentpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FulfillmentReportTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportTemplate with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportTemplateMultiError, or nil if none found.
func (m *FulfillmentReportTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	if _, ok := CareType_name[int32(m.GetCareType())]; !ok {
		err := FulfillmentReportTemplateValidationError{
			field:  "CareType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Title

	// no validation rules for ThemeColor

	// no validation rules for LightThemeColor

	// no validation rules for ThemeCode

	// no validation rules for ShowShowcase

	// no validation rules for RequireBeforePhoto

	// no validation rules for RequireAfterPhoto

	// no validation rules for ShowOverallFeedback

	// no validation rules for ShowPetCondition

	// no validation rules for ShowStaff

	// no validation rules for ShowCustomizeFeedback

	// no validation rules for ShowNextAppointment

	// no validation rules for NextAppointmentDateFormatType

	// no validation rules for ShowReviewBooster

	// no validation rules for ShowYelpReview

	// no validation rules for YelpReviewLink

	// no validation rules for ShowGoogleReview

	// no validation rules for GoogleReviewLink

	// no validation rules for ShowFacebookReview

	// no validation rules for FacebookReviewLink

	if all {
		switch v := interface{}(m.GetLastPublishTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateValidationError{
					field:  "LastPublishTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateValidationError{
					field:  "LastPublishTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastPublishTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateValidationError{
				field:  "LastPublishTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdateBy

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FulfillmentReportTemplateValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FulfillmentReportTemplateValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FulfillmentReportTemplateValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FulfillmentReportTemplateMultiError(errors)
	}

	return nil
}

// FulfillmentReportTemplateMultiError is an error wrapping multiple validation
// errors returned by FulfillmentReportTemplate.ValidateAll() if the
// designated constraints aren't met.
type FulfillmentReportTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportTemplateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportTemplateMultiError) AllErrors() []error { return m }

// FulfillmentReportTemplateValidationError is the validation error returned by
// FulfillmentReportTemplate.Validate if the designated constraints aren't met.
type FulfillmentReportTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportTemplateValidationError) ErrorName() string {
	return "FulfillmentReportTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportTemplateValidationError{}

// Validate checks the field values on FulfillmentReportTemplateQuestion with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FulfillmentReportTemplateQuestion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportTemplateQuestion
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FulfillmentReportTemplateQuestionMultiError, or nil if none found.
func (m *FulfillmentReportTemplateQuestion) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportTemplateQuestion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for FulfillmentReportTemplateId

	// no validation rules for Category

	// no validation rules for Type

	// no validation rules for Key

	// no validation rules for Title

	// no validation rules for IsDefault

	// no validation rules for IsRequired

	// no validation rules for IsTypeEditable

	// no validation rules for IsTitleEditable

	// no validation rules for IsOptionsEditable

	// no validation rules for Sort

	// no validation rules for IsDeleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateQuestionValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateQuestionValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateQuestionValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateQuestionValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateQuestionValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateQuestionValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportTemplateQuestionValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportTemplateQuestionValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportTemplateQuestionValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FulfillmentReportTemplateQuestionMultiError(errors)
	}

	return nil
}

// FulfillmentReportTemplateQuestionMultiError is an error wrapping multiple
// validation errors returned by
// FulfillmentReportTemplateQuestion.ValidateAll() if the designated
// constraints aren't met.
type FulfillmentReportTemplateQuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportTemplateQuestionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportTemplateQuestionMultiError) AllErrors() []error { return m }

// FulfillmentReportTemplateQuestionValidationError is the validation error
// returned by FulfillmentReportTemplateQuestion.Validate if the designated
// constraints aren't met.
type FulfillmentReportTemplateQuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportTemplateQuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportTemplateQuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportTemplateQuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportTemplateQuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportTemplateQuestionValidationError) ErrorName() string {
	return "FulfillmentReportTemplateQuestionValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportTemplateQuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportTemplateQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportTemplateQuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportTemplateQuestionValidationError{}

// Validate checks the field values on FulfillmentReport with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportMultiError, or nil if none found.
func (m *FulfillmentReport) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := FulfillmentReportValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BusinessId

	// no validation rules for CustomerId

	if m.GetAppointmentId() <= 0 {
		err := FulfillmentReportValidationError{
			field:  "AppointmentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPetId() <= 0 {
		err := FulfillmentReportValidationError{
			field:  "PetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PetTypeId

	// no validation rules for CareType

	// no validation rules for ServiceDate

	// no validation rules for Status

	// no validation rules for Uuid

	// no validation rules for LinkOpenedCount

	// no validation rules for ThemeCode

	if all {
		switch v := interface{}(m.GetTemplateVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "TemplateVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "TemplateVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTemplateVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportValidationError{
				field:  "TemplateVersion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "Template",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "Template",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportValidationError{
				field:  "Template",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Id != nil {

		if m.GetId() <= 0 {
			err := FulfillmentReportValidationError{
				field:  "Id",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return FulfillmentReportMultiError(errors)
	}

	return nil
}

// FulfillmentReportMultiError is an error wrapping multiple validation errors
// returned by FulfillmentReport.ValidateAll() if the designated constraints
// aren't met.
type FulfillmentReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportMultiError) AllErrors() []error { return m }

// FulfillmentReportValidationError is the validation error returned by
// FulfillmentReport.Validate if the designated constraints aren't met.
type FulfillmentReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportValidationError) ErrorName() string {
	return "FulfillmentReportValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportValidationError{}

// Validate checks the field values on FulfillmentReportContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportContent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportContentMultiError, or nil if none found.
func (m *FulfillmentReportContent) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFeedbacks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FulfillmentReportContentValidationError{
						field:  fmt.Sprintf("Feedbacks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FulfillmentReportContentValidationError{
						field:  fmt.Sprintf("Feedbacks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FulfillmentReportContentValidationError{
					field:  fmt.Sprintf("Feedbacks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPetConditions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FulfillmentReportContentValidationError{
						field:  fmt.Sprintf("PetConditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FulfillmentReportContentValidationError{
						field:  fmt.Sprintf("PetConditions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FulfillmentReportContentValidationError{
					field:  fmt.Sprintf("PetConditions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if !_FulfillmentReportContent_ThemeColor_Pattern.MatchString(m.GetThemeColor()) {
		err := FulfillmentReportContentValidationError{
			field:  "ThemeColor",
			reason: "value does not match regex pattern \"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.Recommendation != nil {

		if all {
			switch v := interface{}(m.GetRecommendation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FulfillmentReportContentValidationError{
						field:  "Recommendation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FulfillmentReportContentValidationError{
						field:  "Recommendation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRecommendation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FulfillmentReportContentValidationError{
					field:  "Recommendation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.LightThemeColor != nil {

		if !_FulfillmentReportContent_LightThemeColor_Pattern.MatchString(m.GetLightThemeColor()) {
			err := FulfillmentReportContentValidationError{
				field:  "LightThemeColor",
				reason: "value does not match regex pattern \"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return FulfillmentReportContentMultiError(errors)
	}

	return nil
}

// FulfillmentReportContentMultiError is an error wrapping multiple validation
// errors returned by FulfillmentReportContent.ValidateAll() if the designated
// constraints aren't met.
type FulfillmentReportContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportContentMultiError) AllErrors() []error { return m }

// FulfillmentReportContentValidationError is the validation error returned by
// FulfillmentReportContent.Validate if the designated constraints aren't met.
type FulfillmentReportContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportContentValidationError) ErrorName() string {
	return "FulfillmentReportContentValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportContentValidationError{}

var _FulfillmentReportContent_ThemeColor_Pattern = regexp.MustCompile("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")

var _FulfillmentReportContent_LightThemeColor_Pattern = regexp.MustCompile("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")

// Validate checks the field values on FulfillmentReportQuestion with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportQuestion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportQuestion with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportQuestionMultiError, or nil if none found.
func (m *FulfillmentReportQuestion) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportQuestion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for FulfillmentReportId

	// no validation rules for Category

	// no validation rules for Type

	// no validation rules for Key

	// no validation rules for Title

	// no validation rules for Required

	// no validation rules for IsShow

	// no validation rules for InputText

	// no validation rules for Placeholder

	if all {
		switch v := interface{}(m.GetUrls()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportQuestionValidationError{
					field:  "Urls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportQuestionValidationError{
					field:  "Urls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUrls()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportQuestionValidationError{
				field:  "Urls",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FulfillmentReportQuestionMultiError(errors)
	}

	return nil
}

// FulfillmentReportQuestionMultiError is an error wrapping multiple validation
// errors returned by FulfillmentReportQuestion.ValidateAll() if the
// designated constraints aren't met.
type FulfillmentReportQuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportQuestionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportQuestionMultiError) AllErrors() []error { return m }

// FulfillmentReportQuestionValidationError is the validation error returned by
// FulfillmentReportQuestion.Validate if the designated constraints aren't met.
type FulfillmentReportQuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportQuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportQuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportQuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportQuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportQuestionValidationError) ErrorName() string {
	return "FulfillmentReportQuestionValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportQuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportQuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportQuestionValidationError{}

// Validate checks the field values on FulfillmentReportRecommendation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportRecommendation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportRecommendation with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FulfillmentReportRecommendationMultiError, or nil if none found.
func (m *FulfillmentReportRecommendation) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportRecommendation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FrequencyDay

	// no validation rules for FrequencyType

	// no validation rules for FrequencyText

	// no validation rules for NextAppointmentDate

	// no validation rules for NextAppointmentDateText

	if len(errors) > 0 {
		return FulfillmentReportRecommendationMultiError(errors)
	}

	return nil
}

// FulfillmentReportRecommendationMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportRecommendation.ValidateAll()
// if the designated constraints aren't met.
type FulfillmentReportRecommendationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportRecommendationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportRecommendationMultiError) AllErrors() []error { return m }

// FulfillmentReportRecommendationValidationError is the validation error
// returned by FulfillmentReportRecommendation.Validate if the designated
// constraints aren't met.
type FulfillmentReportRecommendationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportRecommendationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportRecommendationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportRecommendationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportRecommendationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportRecommendationValidationError) ErrorName() string {
	return "FulfillmentReportRecommendationValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportRecommendationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportRecommendation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportRecommendationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportRecommendationValidationError{}

// Validate checks the field values on FulfillmentReportCardSummaryInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FulfillmentReportCardSummaryInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportCardSummaryInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FulfillmentReportCardSummaryInfoMultiError, or nil if none found.
func (m *FulfillmentReportCardSummaryInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportCardSummaryInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBusinessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "BusinessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "BusinessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBusinessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfoValidationError{
				field:  "BusinessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "PetInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "PetInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfoValidationError{
				field:  "PetInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAppointmentInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "AppointmentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "AppointmentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAppointmentInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfoValidationError{
				field:  "AppointmentInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAppointmentInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "NextAppointmentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "NextAppointmentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAppointmentInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfoValidationError{
				field:  "NextAppointmentInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFulfillmentReport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "FulfillmentReport",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "FulfillmentReport",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFulfillmentReport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfoValidationError{
				field:  "FulfillmentReport",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReviewBoosterConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "ReviewBoosterConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "ReviewBoosterConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewBoosterConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfoValidationError{
				field:  "ReviewBoosterConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReviewBoosterRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "ReviewBoosterRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "ReviewBoosterRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewBoosterRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfoValidationError{
				field:  "ReviewBoosterRecord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetThemeConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "ThemeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfoValidationError{
					field:  "ThemeConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetThemeConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfoValidationError{
				field:  "ThemeConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FulfillmentReportCardSummaryInfoMultiError(errors)
	}

	return nil
}

// FulfillmentReportCardSummaryInfoMultiError is an error wrapping multiple
// validation errors returned by
// FulfillmentReportCardSummaryInfo.ValidateAll() if the designated
// constraints aren't met.
type FulfillmentReportCardSummaryInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportCardSummaryInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportCardSummaryInfoMultiError) AllErrors() []error { return m }

// FulfillmentReportCardSummaryInfoValidationError is the validation error
// returned by FulfillmentReportCardSummaryInfo.Validate if the designated
// constraints aren't met.
type FulfillmentReportCardSummaryInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportCardSummaryInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportCardSummaryInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportCardSummaryInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportCardSummaryInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportCardSummaryInfoValidationError) ErrorName() string {
	return "FulfillmentReportCardSummaryInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportCardSummaryInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportCardSummaryInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportCardSummaryInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportCardSummaryInfoValidationError{}

// Validate checks the field values on FulfillmentReportSampleValue with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportSampleValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportSampleValue with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportSampleValueMultiError, or nil if none found.
func (m *FulfillmentReportSampleValue) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportSampleValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Comment

	// no validation rules for PetAvatarUrl

	if all {
		switch v := interface{}(m.GetUrls()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSampleValueValidationError{
					field:  "Urls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSampleValueValidationError{
					field:  "Urls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUrls()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSampleValueValidationError{
				field:  "Urls",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FulfillmentReportSampleValueMultiError(errors)
	}

	return nil
}

// FulfillmentReportSampleValueMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportSampleValue.ValidateAll() if
// the designated constraints aren't met.
type FulfillmentReportSampleValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportSampleValueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportSampleValueMultiError) AllErrors() []error { return m }

// FulfillmentReportSampleValueValidationError is the validation error returned
// by FulfillmentReportSampleValue.Validate if the designated constraints
// aren't met.
type FulfillmentReportSampleValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportSampleValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportSampleValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportSampleValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportSampleValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportSampleValueValidationError) ErrorName() string {
	return "FulfillmentReportSampleValueValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportSampleValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportSampleValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportSampleValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportSampleValueValidationError{}

// Validate checks the field values on FulfillmentReportThemeConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportThemeConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportThemeConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportThemeConfigMultiError, or nil if none found.
func (m *FulfillmentReportThemeConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportThemeConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Code

	// no validation rules for Color

	// no validation rules for LightColor

	// no validation rules for ImgUrl

	// no validation rules for Icon

	// no validation rules for EmailBottomImgUrl

	// no validation rules for Recommend

	// no validation rules for Status

	// no validation rules for Tag

	if len(errors) > 0 {
		return FulfillmentReportThemeConfigMultiError(errors)
	}

	return nil
}

// FulfillmentReportThemeConfigMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportThemeConfig.ValidateAll() if
// the designated constraints aren't met.
type FulfillmentReportThemeConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportThemeConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportThemeConfigMultiError) AllErrors() []error { return m }

// FulfillmentReportThemeConfigValidationError is the validation error returned
// by FulfillmentReportThemeConfig.Validate if the designated constraints
// aren't met.
type FulfillmentReportThemeConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportThemeConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportThemeConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportThemeConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportThemeConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportThemeConfigValidationError) ErrorName() string {
	return "FulfillmentReportThemeConfigValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportThemeConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportThemeConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportThemeConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportThemeConfigValidationError{}

// Validate checks the field values on BodyViewUrl with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BodyViewUrl) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BodyViewUrl with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BodyViewUrlMultiError, or
// nil if none found.
func (m *BodyViewUrl) ValidateAll() error {
	return m.validate(true)
}

func (m *BodyViewUrl) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Left

	// no validation rules for Right

	if len(errors) > 0 {
		return BodyViewUrlMultiError(errors)
	}

	return nil
}

// BodyViewUrlMultiError is an error wrapping multiple validation errors
// returned by BodyViewUrl.ValidateAll() if the designated constraints aren't met.
type BodyViewUrlMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BodyViewUrlMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BodyViewUrlMultiError) AllErrors() []error { return m }

// BodyViewUrlValidationError is the validation error returned by
// BodyViewUrl.Validate if the designated constraints aren't met.
type BodyViewUrlValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BodyViewUrlValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BodyViewUrlValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BodyViewUrlValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BodyViewUrlValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BodyViewUrlValidationError) ErrorName() string { return "BodyViewUrlValidationError" }

// Error satisfies the builtin error interface
func (e BodyViewUrlValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBodyViewUrl.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BodyViewUrlValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BodyViewUrlValidationError{}

// Validate checks the field values on FulfillmentReportSendResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportSendResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportSendResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportSendResultMultiError, or nil if none found.
func (m *FulfillmentReportSendResult) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportSendResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FulfillmentReportId

	// no validation rules for IsSentSuccess

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return FulfillmentReportSendResultMultiError(errors)
	}

	return nil
}

// FulfillmentReportSendResultMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportSendResult.ValidateAll() if
// the designated constraints aren't met.
type FulfillmentReportSendResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportSendResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportSendResultMultiError) AllErrors() []error { return m }

// FulfillmentReportSendResultValidationError is the validation error returned
// by FulfillmentReportSendResult.Validate if the designated constraints
// aren't met.
type FulfillmentReportSendResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportSendResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportSendResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportSendResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportSendResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportSendResultValidationError) ErrorName() string {
	return "FulfillmentReportSendResultValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportSendResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportSendResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportSendResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportSendResultValidationError{}

// Validate checks the field values on FulfillmentReportSendRecord with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportSendRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FulfillmentReportSendRecord with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FulfillmentReportSendRecordMultiError, or nil if none found.
func (m *FulfillmentReportSendRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportSendRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSendContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordValidationError{
					field:  "SendContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordValidationError{
					field:  "SendContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSendContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSendRecordValidationError{
				field:  "SendContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SendMethod

	// no validation rules for ServiceDate

	// no validation rules for CareType

	if all {
		switch v := interface{}(m.GetSendTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordValidationError{
					field:  "SendTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportSendRecordValidationError{
					field:  "SendTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSendTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportSendRecordValidationError{
				field:  "SendTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsSentSuccess

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return FulfillmentReportSendRecordMultiError(errors)
	}

	return nil
}

// FulfillmentReportSendRecordMultiError is an error wrapping multiple
// validation errors returned by FulfillmentReportSendRecord.ValidateAll() if
// the designated constraints aren't met.
type FulfillmentReportSendRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportSendRecordMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportSendRecordMultiError) AllErrors() []error { return m }

// FulfillmentReportSendRecordValidationError is the validation error returned
// by FulfillmentReportSendRecord.Validate if the designated constraints
// aren't met.
type FulfillmentReportSendRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportSendRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportSendRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportSendRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportSendRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportSendRecordValidationError) ErrorName() string {
	return "FulfillmentReportSendRecordValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportSendRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportSendRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportSendRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportSendRecordValidationError{}

// Validate checks the field values on
// FulfillmentReportTemplateQuestion_ExtraInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportTemplateQuestion_ExtraInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FulfillmentReportTemplateQuestion_ExtraInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FulfillmentReportTemplateQuestion_ExtraInfoMultiError, or nil if none found.
func (m *FulfillmentReportTemplateQuestion_ExtraInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportTemplateQuestion_ExtraInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FulfillmentReportTemplateQuestion_ExtraInfoMultiError(errors)
	}

	return nil
}

// FulfillmentReportTemplateQuestion_ExtraInfoMultiError is an error wrapping
// multiple validation errors returned by
// FulfillmentReportTemplateQuestion_ExtraInfo.ValidateAll() if the designated
// constraints aren't met.
type FulfillmentReportTemplateQuestion_ExtraInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportTemplateQuestion_ExtraInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportTemplateQuestion_ExtraInfoMultiError) AllErrors() []error { return m }

// FulfillmentReportTemplateQuestion_ExtraInfoValidationError is the validation
// error returned by FulfillmentReportTemplateQuestion_ExtraInfo.Validate if
// the designated constraints aren't met.
type FulfillmentReportTemplateQuestion_ExtraInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportTemplateQuestion_ExtraInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportTemplateQuestion_ExtraInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportTemplateQuestion_ExtraInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportTemplateQuestion_ExtraInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportTemplateQuestion_ExtraInfoValidationError) ErrorName() string {
	return "FulfillmentReportTemplateQuestion_ExtraInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportTemplateQuestion_ExtraInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportTemplateQuestion_ExtraInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportTemplateQuestion_ExtraInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportTemplateQuestion_ExtraInfoValidationError{}

// Validate checks the field values on
// FulfillmentReportCardSummaryInfo_BusinessInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportCardSummaryInfo_BusinessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FulfillmentReportCardSummaryInfo_BusinessInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FulfillmentReportCardSummaryInfo_BusinessInfoMultiError, or nil if none found.
func (m *FulfillmentReportCardSummaryInfo_BusinessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportCardSummaryInfo_BusinessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BusinessId

	// no validation rules for BusinessName

	// no validation rules for AvatarPath

	if len(errors) > 0 {
		return FulfillmentReportCardSummaryInfo_BusinessInfoMultiError(errors)
	}

	return nil
}

// FulfillmentReportCardSummaryInfo_BusinessInfoMultiError is an error wrapping
// multiple validation errors returned by
// FulfillmentReportCardSummaryInfo_BusinessInfo.ValidateAll() if the
// designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_BusinessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportCardSummaryInfo_BusinessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportCardSummaryInfo_BusinessInfoMultiError) AllErrors() []error { return m }

// FulfillmentReportCardSummaryInfo_BusinessInfoValidationError is the
// validation error returned by
// FulfillmentReportCardSummaryInfo_BusinessInfo.Validate if the designated
// constraints aren't met.
type FulfillmentReportCardSummaryInfo_BusinessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportCardSummaryInfo_BusinessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportCardSummaryInfo_BusinessInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FulfillmentReportCardSummaryInfo_BusinessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportCardSummaryInfo_BusinessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportCardSummaryInfo_BusinessInfoValidationError) ErrorName() string {
	return "FulfillmentReportCardSummaryInfo_BusinessInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportCardSummaryInfo_BusinessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportCardSummaryInfo_BusinessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportCardSummaryInfo_BusinessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportCardSummaryInfo_BusinessInfoValidationError{}

// Validate checks the field values on FulfillmentReportCardSummaryInfo_PetInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FulfillmentReportCardSummaryInfo_PetInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FulfillmentReportCardSummaryInfo_PetInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FulfillmentReportCardSummaryInfo_PetInfoMultiError, or nil if none found.
func (m *FulfillmentReportCardSummaryInfo_PetInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportCardSummaryInfo_PetInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PetId

	// no validation rules for PetName

	// no validation rules for AvatarPath

	if len(errors) > 0 {
		return FulfillmentReportCardSummaryInfo_PetInfoMultiError(errors)
	}

	return nil
}

// FulfillmentReportCardSummaryInfo_PetInfoMultiError is an error wrapping
// multiple validation errors returned by
// FulfillmentReportCardSummaryInfo_PetInfo.ValidateAll() if the designated
// constraints aren't met.
type FulfillmentReportCardSummaryInfo_PetInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportCardSummaryInfo_PetInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportCardSummaryInfo_PetInfoMultiError) AllErrors() []error { return m }

// FulfillmentReportCardSummaryInfo_PetInfoValidationError is the validation
// error returned by FulfillmentReportCardSummaryInfo_PetInfo.Validate if the
// designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_PetInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportCardSummaryInfo_PetInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportCardSummaryInfo_PetInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportCardSummaryInfo_PetInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportCardSummaryInfo_PetInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportCardSummaryInfo_PetInfoValidationError) ErrorName() string {
	return "FulfillmentReportCardSummaryInfo_PetInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportCardSummaryInfo_PetInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportCardSummaryInfo_PetInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportCardSummaryInfo_PetInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportCardSummaryInfo_PetInfoValidationError{}

// Validate checks the field values on
// FulfillmentReportCardSummaryInfo_AppointmentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportCardSummaryInfo_AppointmentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FulfillmentReportCardSummaryInfo_AppointmentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// FulfillmentReportCardSummaryInfo_AppointmentInfoMultiError, or nil if none found.
func (m *FulfillmentReportCardSummaryInfo_AppointmentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportCardSummaryInfo_AppointmentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppointmentId

	// no validation rules for State

	// no validation rules for AppointmentDate

	// no validation rules for AppointmentStartTime

	// no validation rules for AppointmentEndTime

	// no validation rules for ArrivalWindowBefore

	// no validation rules for ArrivalWindowAfter

	for idx, item := range m.GetPetDetailInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError{
						field:  fmt.Sprintf("PetDetailInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError{
						field:  fmt.Sprintf("PetDetailInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError{
					field:  fmt.Sprintf("PetDetailInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FulfillmentReportCardSummaryInfo_AppointmentInfoMultiError(errors)
	}

	return nil
}

// FulfillmentReportCardSummaryInfo_AppointmentInfoMultiError is an error
// wrapping multiple validation errors returned by
// FulfillmentReportCardSummaryInfo_AppointmentInfo.ValidateAll() if the
// designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_AppointmentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportCardSummaryInfo_AppointmentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportCardSummaryInfo_AppointmentInfoMultiError) AllErrors() []error { return m }

// FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError is the
// validation error returned by
// FulfillmentReportCardSummaryInfo_AppointmentInfo.Validate if the designated
// constraints aren't met.
type FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError) ErrorName() string {
	return "FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportCardSummaryInfo_AppointmentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportCardSummaryInfo_AppointmentInfoValidationError{}

// Validate checks the field values on
// FulfillmentReportCardSummaryInfo_PetDetailInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportCardSummaryInfo_PetDetailInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FulfillmentReportCardSummaryInfo_PetDetailInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// FulfillmentReportCardSummaryInfo_PetDetailInfoMultiError, or nil if none found.
func (m *FulfillmentReportCardSummaryInfo_PetDetailInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportCardSummaryInfo_PetDetailInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PetId

	// no validation rules for ServiceId

	// no validation rules for ServiceName

	// no validation rules for ServiceType

	// no validation rules for StartTime

	// no validation rules for ServiceDuration

	if all {
		switch v := interface{}(m.GetStaffInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError{
					field:  "StaffInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError{
					field:  "StaffInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStaffInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError{
				field:  "StaffInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FulfillmentReportCardSummaryInfo_PetDetailInfoMultiError(errors)
	}

	return nil
}

// FulfillmentReportCardSummaryInfo_PetDetailInfoMultiError is an error
// wrapping multiple validation errors returned by
// FulfillmentReportCardSummaryInfo_PetDetailInfo.ValidateAll() if the
// designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_PetDetailInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportCardSummaryInfo_PetDetailInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportCardSummaryInfo_PetDetailInfoMultiError) AllErrors() []error { return m }

// FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError is the
// validation error returned by
// FulfillmentReportCardSummaryInfo_PetDetailInfo.Validate if the designated
// constraints aren't met.
type FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError) ErrorName() string {
	return "FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportCardSummaryInfo_PetDetailInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportCardSummaryInfo_PetDetailInfoValidationError{}

// Validate checks the field values on
// FulfillmentReportCardSummaryInfo_StaffInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportCardSummaryInfo_StaffInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FulfillmentReportCardSummaryInfo_StaffInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FulfillmentReportCardSummaryInfo_StaffInfoMultiError, or nil if none found.
func (m *FulfillmentReportCardSummaryInfo_StaffInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportCardSummaryInfo_StaffInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StaffId

	// no validation rules for StaffFirstName

	// no validation rules for StaffLastName

	// no validation rules for StaffAvatarPath

	if len(errors) > 0 {
		return FulfillmentReportCardSummaryInfo_StaffInfoMultiError(errors)
	}

	return nil
}

// FulfillmentReportCardSummaryInfo_StaffInfoMultiError is an error wrapping
// multiple validation errors returned by
// FulfillmentReportCardSummaryInfo_StaffInfo.ValidateAll() if the designated
// constraints aren't met.
type FulfillmentReportCardSummaryInfo_StaffInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportCardSummaryInfo_StaffInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportCardSummaryInfo_StaffInfoMultiError) AllErrors() []error { return m }

// FulfillmentReportCardSummaryInfo_StaffInfoValidationError is the validation
// error returned by FulfillmentReportCardSummaryInfo_StaffInfo.Validate if
// the designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_StaffInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportCardSummaryInfo_StaffInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FulfillmentReportCardSummaryInfo_StaffInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FulfillmentReportCardSummaryInfo_StaffInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FulfillmentReportCardSummaryInfo_StaffInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportCardSummaryInfo_StaffInfoValidationError) ErrorName() string {
	return "FulfillmentReportCardSummaryInfo_StaffInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportCardSummaryInfo_StaffInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportCardSummaryInfo_StaffInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportCardSummaryInfo_StaffInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportCardSummaryInfo_StaffInfoValidationError{}

// Validate checks the field values on
// FulfillmentReportCardSummaryInfo_ReviewBoosterConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FulfillmentReportCardSummaryInfo_ReviewBoosterConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// FulfillmentReportCardSummaryInfo_ReviewBoosterConfigMultiError, or nil if
// none found.
func (m *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportCardSummaryInfo_ReviewBoosterConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PositiveScore

	// no validation rules for PositiveYelp

	// no validation rules for PositiveFacebook

	// no validation rules for PositiveGoogle

	if len(errors) > 0 {
		return FulfillmentReportCardSummaryInfo_ReviewBoosterConfigMultiError(errors)
	}

	return nil
}

// FulfillmentReportCardSummaryInfo_ReviewBoosterConfigMultiError is an error
// wrapping multiple validation errors returned by
// FulfillmentReportCardSummaryInfo_ReviewBoosterConfig.ValidateAll() if the
// designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_ReviewBoosterConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportCardSummaryInfo_ReviewBoosterConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportCardSummaryInfo_ReviewBoosterConfigMultiError) AllErrors() []error { return m }

// FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError is the
// validation error returned by
// FulfillmentReportCardSummaryInfo_ReviewBoosterConfig.Validate if the
// designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError) ErrorName() string {
	return "FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportCardSummaryInfo_ReviewBoosterConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportCardSummaryInfo_ReviewBoosterConfigValidationError{}

// Validate checks the field values on
// FulfillmentReportCardSummaryInfo_ReviewBoosterRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FulfillmentReportCardSummaryInfo_ReviewBoosterRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// FulfillmentReportCardSummaryInfo_ReviewBoosterRecordMultiError, or nil if
// none found.
func (m *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *FulfillmentReportCardSummaryInfo_ReviewBoosterRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PositiveScore

	// no validation rules for ReviewContent

	// no validation rules for ReviewTime

	if len(errors) > 0 {
		return FulfillmentReportCardSummaryInfo_ReviewBoosterRecordMultiError(errors)
	}

	return nil
}

// FulfillmentReportCardSummaryInfo_ReviewBoosterRecordMultiError is an error
// wrapping multiple validation errors returned by
// FulfillmentReportCardSummaryInfo_ReviewBoosterRecord.ValidateAll() if the
// designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_ReviewBoosterRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FulfillmentReportCardSummaryInfo_ReviewBoosterRecordMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FulfillmentReportCardSummaryInfo_ReviewBoosterRecordMultiError) AllErrors() []error { return m }

// FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError is the
// validation error returned by
// FulfillmentReportCardSummaryInfo_ReviewBoosterRecord.Validate if the
// designated constraints aren't met.
type FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError) ErrorName() string {
	return "FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError"
}

// Error satisfies the builtin error interface
func (e FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFulfillmentReportCardSummaryInfo_ReviewBoosterRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FulfillmentReportCardSummaryInfo_ReviewBoosterRecordValidationError{}
