syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "backend/proto/fulfillment/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 服务属性值
message ServiceAttributeValue {
    // 属性值ID
    int64 id = 1;
    // 工厂ID
    int64 template_id = 2;
    // 属性名称
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: attribute is clear and appropriate --)
    string attribute = 3;
    oneof value {
      // 文本值
      string text_value = 4;
      // 整数值
      int64 int_value = 5;
      // 小数值
      double decimal_value = 6;
      // 布尔值
      bool bool_value = 7;
      // JSON字符串值
      string json_value = 8;
    }
  }
  
  // 服务可用性配置
  message ServiceAvailability {
    // 是否停用
    bool inactive = 1;
    // 是否支持所有品种
    bool is_all_breed = 2;
    // 是否支持所有体重范围
    bool is_all_weight_range = 3;
    // 体重下限
    double weight_down_limit = 4;
    // 体重上限
    double weight_up_limit = 5;
    // 是否支持所有毛发过滤
    bool is_all_coat_filter = 6;
    // 服务过滤
    bool service_filter = 7;
    // 是否支持所有宠物尺寸
    bool is_all_pet_size = 8;
    // 允许的宠物尺寸列表
    string allowed_pet_size_list = 9;
    // 是否支持所有住宿
    bool is_all_lodging = 10;
  }
  
  // 定价信息
  message Pricing {
    // 税费ID
    int64 tax_id = 1;
    // 价格
    double price = 2;
    // 价格单位
    int32 price_unit = 3;
    // 是否添加到佣金
    // (-- api-linter: core::0140::prepositions=disabled
    //     aip.dev/not-precedent: add_commission is clear and concise --)
    bool add_commission = 4;
    // 是否允许小费
    bool can_tip = 5;
  }
  
  // 服务工厂
  message ServiceTemplate {
    // 服务工厂ID
    int64 id = 1;
    // 公司ID
    int64 company_id = 2;
    // 商家ID
    int64 business_id = 3;
    // 分类ID
    int64 category_id = 4;
    // 类型ID
    CareType care_type = 5;
    // 服务名称
    string name = 6;
    // 服务描述
    string description = 7;
    // 服务时长(分钟)
    // (-- api-linter: core::0192::only-leading-comments=disabled
    //     aip.dev/not-precedent: inline comment is clear and helpful --)
    int32 duration = 8;
    // 颜色代码
    string color_code = 9;
    // 是否活跃
    bool is_active = 10;
    // 价格
    double price = 11;
    // 货币代码
    // (-- api-linter: core::0143::standardized-codes=disabled
    //     aip.dev/not-precedent: currency_code is clear and appropriate --)
    string currency_code = 12;
    // 排序
    int32 sort = 13;
    // 图片URL列表
    // (-- api-linter: core::0192::only-leading-comments=disabled
    //     aip.dev/not-precedent: inline comment is clear and helpful --)
    repeated string images = 14;
    // 来源:1-MoeGo平台;2-企业中心
    // (-- api-linter: core::0192::only-leading-comments=disabled
    //     aip.dev/not-precedent: inline comment is clear and helpful --)
    int32 source = 15;
    // 属性值列表
    // (-- api-linter: core::0192::only-leading-comments=disabled
    //     aip.dev/not-precedent: inline comment is clear and helpful --)
    repeated ServiceAttributeValue attribute_values = 18;
    // 附加选项列表
    // (-- api-linter: core::0192::only-leading-comments=disabled
    //     aip.dev/not-precedent: inline comment is clear and helpful --)
    repeated ServiceOptionTemplate options = 19;
  }
  
  // 服务选项工厂
  message ServiceOptionTemplate {
    // 选项名称
    string name = 1;
    // 选项描述
    string description = 2;
    // 选项价格
    double price = 3;
    // 选项时长
    // (-- api-linter: core::0192::only-leading-comments=disabled
    //     aip.dev/not-precedent: inline comment is clear and helpful --)
    int32 duration = 4;
    // 选项排序
    int32 sort = 5;
    // 分类ID
    int64 category_id = 6;
    // 是否激活
    bool is_active = 7;
    // 是否必需
    bool is_required = 8;
    // 最大可选数量
    int32 max_selectable = 9;
  }
