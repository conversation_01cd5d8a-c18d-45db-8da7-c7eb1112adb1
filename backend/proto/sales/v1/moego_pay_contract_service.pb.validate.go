// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/sales/v1/moego_pay_contract_service.proto

package salespb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateMoegoPayContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMoegoPayContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMoegoPayContractRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateMoegoPayContractRequestMultiError, or nil if none found.
func (m *CreateMoegoPayContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMoegoPayContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := CreateMoegoPayContractRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayContractRequest_TerminalPercentage_Pattern.MatchString(m.GetTerminalPercentage()) {
		err := CreateMoegoPayContractRequestValidationError{
			field:  "TerminalPercentage",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayContractRequest_TerminalFixed_Pattern.MatchString(m.GetTerminalFixed()) {
		err := CreateMoegoPayContractRequestValidationError{
			field:  "TerminalFixed",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayContractRequest_NonTerminalPercentage_Pattern.MatchString(m.GetNonTerminalPercentage()) {
		err := CreateMoegoPayContractRequestValidationError{
			field:  "NonTerminalPercentage",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayContractRequest_NonTerminalFixed_Pattern.MatchString(m.GetNonTerminalFixed()) {
		err := CreateMoegoPayContractRequestValidationError{
			field:  "NonTerminalFixed",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayContractRequest_MinVolume_Pattern.MatchString(m.GetMinVolume()) {
		err := CreateMoegoPayContractRequestValidationError{
			field:  "MinVolume",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCreator()) < 1 {
		err := CreateMoegoPayContractRequestValidationError{
			field:  "Creator",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateMoegoPayContractRequestMultiError(errors)
	}

	return nil
}

// CreateMoegoPayContractRequestMultiError is an error wrapping multiple
// validation errors returned by CreateMoegoPayContractRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateMoegoPayContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMoegoPayContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMoegoPayContractRequestMultiError) AllErrors() []error { return m }

// CreateMoegoPayContractRequestValidationError is the validation error
// returned by CreateMoegoPayContractRequest.Validate if the designated
// constraints aren't met.
type CreateMoegoPayContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMoegoPayContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMoegoPayContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMoegoPayContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMoegoPayContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMoegoPayContractRequestValidationError) ErrorName() string {
	return "CreateMoegoPayContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMoegoPayContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMoegoPayContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMoegoPayContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMoegoPayContractRequestValidationError{}

var _CreateMoegoPayContractRequest_TerminalPercentage_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

var _CreateMoegoPayContractRequest_TerminalFixed_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

var _CreateMoegoPayContractRequest_NonTerminalPercentage_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

var _CreateMoegoPayContractRequest_NonTerminalFixed_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

var _CreateMoegoPayContractRequest_MinVolume_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

// Validate checks the field values on GetMoegoPayContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMoegoPayContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMoegoPayContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMoegoPayContractRequestMultiError, or nil if none found.
func (m *GetMoegoPayContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMoegoPayContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := GetMoegoPayContractRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetMoegoPayContractRequestMultiError(errors)
	}

	return nil
}

// GetMoegoPayContractRequestMultiError is an error wrapping multiple
// validation errors returned by GetMoegoPayContractRequest.ValidateAll() if
// the designated constraints aren't met.
type GetMoegoPayContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMoegoPayContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMoegoPayContractRequestMultiError) AllErrors() []error { return m }

// GetMoegoPayContractRequestValidationError is the validation error returned
// by GetMoegoPayContractRequest.Validate if the designated constraints aren't met.
type GetMoegoPayContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMoegoPayContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMoegoPayContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMoegoPayContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMoegoPayContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMoegoPayContractRequestValidationError) ErrorName() string {
	return "GetMoegoPayContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMoegoPayContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMoegoPayContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMoegoPayContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMoegoPayContractRequestValidationError{}

// Validate checks the field values on ListMoegoPayContractsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMoegoPayContractsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMoegoPayContractsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMoegoPayContractsRequestMultiError, or nil if none found.
func (m *ListMoegoPayContractsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMoegoPayContractsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageSize(); val <= 0 || val > 100 {
		err := ListMoegoPayContractsRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMoegoPayContractsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMoegoPayContractsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMoegoPayContractsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMoegoPayContractsRequestMultiError(errors)
	}

	return nil
}

// ListMoegoPayContractsRequestMultiError is an error wrapping multiple
// validation errors returned by ListMoegoPayContractsRequest.ValidateAll() if
// the designated constraints aren't met.
type ListMoegoPayContractsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMoegoPayContractsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMoegoPayContractsRequestMultiError) AllErrors() []error { return m }

// ListMoegoPayContractsRequestValidationError is the validation error returned
// by ListMoegoPayContractsRequest.Validate if the designated constraints
// aren't met.
type ListMoegoPayContractsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMoegoPayContractsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMoegoPayContractsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMoegoPayContractsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMoegoPayContractsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMoegoPayContractsRequestValidationError) ErrorName() string {
	return "ListMoegoPayContractsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMoegoPayContractsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMoegoPayContractsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMoegoPayContractsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMoegoPayContractsRequestValidationError{}

// Validate checks the field values on ListMoegoPayContractsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMoegoPayContractsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMoegoPayContractsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListMoegoPayContractsResponseMultiError, or nil if none found.
func (m *ListMoegoPayContractsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMoegoPayContractsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMoegoPayContracts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMoegoPayContractsResponseValidationError{
						field:  fmt.Sprintf("MoegoPayContracts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMoegoPayContractsResponseValidationError{
						field:  fmt.Sprintf("MoegoPayContracts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMoegoPayContractsResponseValidationError{
					field:  fmt.Sprintf("MoegoPayContracts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListMoegoPayContractsResponseMultiError(errors)
	}

	return nil
}

// ListMoegoPayContractsResponseMultiError is an error wrapping multiple
// validation errors returned by ListMoegoPayContractsResponse.ValidateAll()
// if the designated constraints aren't met.
type ListMoegoPayContractsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMoegoPayContractsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMoegoPayContractsResponseMultiError) AllErrors() []error { return m }

// ListMoegoPayContractsResponseValidationError is the validation error
// returned by ListMoegoPayContractsResponse.Validate if the designated
// constraints aren't met.
type ListMoegoPayContractsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMoegoPayContractsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMoegoPayContractsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMoegoPayContractsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMoegoPayContractsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMoegoPayContractsResponseValidationError) ErrorName() string {
	return "ListMoegoPayContractsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListMoegoPayContractsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMoegoPayContractsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMoegoPayContractsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMoegoPayContractsResponseValidationError{}

// Validate checks the field values on CountMoegoPayContractsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountMoegoPayContractsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountMoegoPayContractsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CountMoegoPayContractsRequestMultiError, or nil if none found.
func (m *CountMoegoPayContractsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CountMoegoPayContractsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountMoegoPayContractsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountMoegoPayContractsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountMoegoPayContractsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CountMoegoPayContractsRequestMultiError(errors)
	}

	return nil
}

// CountMoegoPayContractsRequestMultiError is an error wrapping multiple
// validation errors returned by CountMoegoPayContractsRequest.ValidateAll()
// if the designated constraints aren't met.
type CountMoegoPayContractsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountMoegoPayContractsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountMoegoPayContractsRequestMultiError) AllErrors() []error { return m }

// CountMoegoPayContractsRequestValidationError is the validation error
// returned by CountMoegoPayContractsRequest.Validate if the designated
// constraints aren't met.
type CountMoegoPayContractsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountMoegoPayContractsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountMoegoPayContractsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountMoegoPayContractsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountMoegoPayContractsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountMoegoPayContractsRequestValidationError) ErrorName() string {
	return "CountMoegoPayContractsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CountMoegoPayContractsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountMoegoPayContractsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountMoegoPayContractsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountMoegoPayContractsRequestValidationError{}

// Validate checks the field values on CountMoegoPayContractsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountMoegoPayContractsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountMoegoPayContractsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CountMoegoPayContractsResponseMultiError, or nil if none found.
func (m *CountMoegoPayContractsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CountMoegoPayContractsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return CountMoegoPayContractsResponseMultiError(errors)
	}

	return nil
}

// CountMoegoPayContractsResponseMultiError is an error wrapping multiple
// validation errors returned by CountMoegoPayContractsResponse.ValidateAll()
// if the designated constraints aren't met.
type CountMoegoPayContractsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountMoegoPayContractsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountMoegoPayContractsResponseMultiError) AllErrors() []error { return m }

// CountMoegoPayContractsResponseValidationError is the validation error
// returned by CountMoegoPayContractsResponse.Validate if the designated
// constraints aren't met.
type CountMoegoPayContractsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountMoegoPayContractsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountMoegoPayContractsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountMoegoPayContractsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountMoegoPayContractsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountMoegoPayContractsResponseValidationError) ErrorName() string {
	return "CountMoegoPayContractsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CountMoegoPayContractsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountMoegoPayContractsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountMoegoPayContractsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountMoegoPayContractsResponseValidationError{}

// Validate checks the field values on SignMoegoPayContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SignMoegoPayContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignMoegoPayContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignMoegoPayContractRequestMultiError, or nil if none found.
func (m *SignMoegoPayContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SignMoegoPayContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := SignMoegoPayContractRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if uri, err := url.Parse(m.GetSignatureUri()); err != nil {
		err = SignMoegoPayContractRequestValidationError{
			field:  "SignatureUri",
			reason: "value must be a valid URI",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	} else if !uri.IsAbs() {
		err := SignMoegoPayContractRequestValidationError{
			field:  "SignatureUri",
			reason: "value must be absolute",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SignMoegoPayContractRequestMultiError(errors)
	}

	return nil
}

// SignMoegoPayContractRequestMultiError is an error wrapping multiple
// validation errors returned by SignMoegoPayContractRequest.ValidateAll() if
// the designated constraints aren't met.
type SignMoegoPayContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignMoegoPayContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignMoegoPayContractRequestMultiError) AllErrors() []error { return m }

// SignMoegoPayContractRequestValidationError is the validation error returned
// by SignMoegoPayContractRequest.Validate if the designated constraints
// aren't met.
type SignMoegoPayContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignMoegoPayContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignMoegoPayContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignMoegoPayContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignMoegoPayContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignMoegoPayContractRequestValidationError) ErrorName() string {
	return "SignMoegoPayContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SignMoegoPayContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignMoegoPayContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignMoegoPayContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignMoegoPayContractRequestValidationError{}

// Validate checks the field values on SignMoegoPayContractResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SignMoegoPayContractResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignMoegoPayContractResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignMoegoPayContractResponseMultiError, or nil if none found.
func (m *SignMoegoPayContractResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SignMoegoPayContractResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContract()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SignMoegoPayContractResponseValidationError{
					field:  "Contract",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SignMoegoPayContractResponseValidationError{
					field:  "Contract",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContract()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SignMoegoPayContractResponseValidationError{
				field:  "Contract",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SignMoegoPayContractResponseMultiError(errors)
	}

	return nil
}

// SignMoegoPayContractResponseMultiError is an error wrapping multiple
// validation errors returned by SignMoegoPayContractResponse.ValidateAll() if
// the designated constraints aren't met.
type SignMoegoPayContractResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignMoegoPayContractResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignMoegoPayContractResponseMultiError) AllErrors() []error { return m }

// SignMoegoPayContractResponseValidationError is the validation error returned
// by SignMoegoPayContractResponse.Validate if the designated constraints
// aren't met.
type SignMoegoPayContractResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignMoegoPayContractResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignMoegoPayContractResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignMoegoPayContractResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignMoegoPayContractResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignMoegoPayContractResponseValidationError) ErrorName() string {
	return "SignMoegoPayContractResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SignMoegoPayContractResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignMoegoPayContractResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignMoegoPayContractResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignMoegoPayContractResponseValidationError{}

// Validate checks the field values on DeleteMoegoPayContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteMoegoPayContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteMoegoPayContractRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteMoegoPayContractRequestMultiError, or nil if none found.
func (m *DeleteMoegoPayContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteMoegoPayContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := DeleteMoegoPayContractRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteMoegoPayContractRequestMultiError(errors)
	}

	return nil
}

// DeleteMoegoPayContractRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteMoegoPayContractRequest.ValidateAll()
// if the designated constraints aren't met.
type DeleteMoegoPayContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteMoegoPayContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteMoegoPayContractRequestMultiError) AllErrors() []error { return m }

// DeleteMoegoPayContractRequestValidationError is the validation error
// returned by DeleteMoegoPayContractRequest.Validate if the designated
// constraints aren't met.
type DeleteMoegoPayContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteMoegoPayContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteMoegoPayContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteMoegoPayContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteMoegoPayContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteMoegoPayContractRequestValidationError) ErrorName() string {
	return "DeleteMoegoPayContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteMoegoPayContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteMoegoPayContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteMoegoPayContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteMoegoPayContractRequestValidationError{}

// Validate checks the field values on MoegoPayContract with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MoegoPayContract) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MoegoPayContract with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MoegoPayContractMultiError, or nil if none found.
func (m *MoegoPayContract) ValidateAll() error {
	return m.validate(true)
}

func (m *MoegoPayContract) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TemplateId

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MoegoPayContractValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MoegoPayContractValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MoegoPayContractValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MoegoPayContractValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MoegoPayContractValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MoegoPayContractValidationError{
				field:  "Parameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Content

	// no validation rules for Creator

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MoegoPayContractValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MoegoPayContractValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MoegoPayContractValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MoegoPayContractValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MoegoPayContractValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MoegoPayContractValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.SignTime != nil {

		if all {
			switch v := interface{}(m.GetSignTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MoegoPayContractValidationError{
						field:  "SignTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MoegoPayContractValidationError{
						field:  "SignTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSignTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MoegoPayContractValidationError{
					field:  "SignTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.SignatureUri != nil {
		// no validation rules for SignatureUri
	}

	if len(errors) > 0 {
		return MoegoPayContractMultiError(errors)
	}

	return nil
}

// MoegoPayContractMultiError is an error wrapping multiple validation errors
// returned by MoegoPayContract.ValidateAll() if the designated constraints
// aren't met.
type MoegoPayContractMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MoegoPayContractMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MoegoPayContractMultiError) AllErrors() []error { return m }

// MoegoPayContractValidationError is the validation error returned by
// MoegoPayContract.Validate if the designated constraints aren't met.
type MoegoPayContractValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MoegoPayContractValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MoegoPayContractValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MoegoPayContractValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MoegoPayContractValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MoegoPayContractValidationError) ErrorName() string { return "MoegoPayContractValidationError" }

// Error satisfies the builtin error interface
func (e MoegoPayContractValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoegoPayContract.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MoegoPayContractValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MoegoPayContractValidationError{}

// Validate checks the field values on MoegoPayContractQueryFilters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MoegoPayContractQueryFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MoegoPayContractQueryFilters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MoegoPayContractQueryFiltersMultiError, or nil if none found.
func (m *MoegoPayContractQueryFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *MoegoPayContractQueryFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CompanyId != nil {
		// no validation rules for CompanyId
	}

	if m.AccountId != nil {
		// no validation rules for AccountId
	}

	if m.OwnerEmail != nil {
		// no validation rules for OwnerEmail
	}

	if m.Creator != nil {
		// no validation rules for Creator
	}

	if m.Signed != nil {
		// no validation rules for Signed
	}

	if len(errors) > 0 {
		return MoegoPayContractQueryFiltersMultiError(errors)
	}

	return nil
}

// MoegoPayContractQueryFiltersMultiError is an error wrapping multiple
// validation errors returned by MoegoPayContractQueryFilters.ValidateAll() if
// the designated constraints aren't met.
type MoegoPayContractQueryFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MoegoPayContractQueryFiltersMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MoegoPayContractQueryFiltersMultiError) AllErrors() []error { return m }

// MoegoPayContractQueryFiltersValidationError is the validation error returned
// by MoegoPayContractQueryFilters.Validate if the designated constraints
// aren't met.
type MoegoPayContractQueryFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MoegoPayContractQueryFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MoegoPayContractQueryFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MoegoPayContractQueryFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MoegoPayContractQueryFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MoegoPayContractQueryFiltersValidationError) ErrorName() string {
	return "MoegoPayContractQueryFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e MoegoPayContractQueryFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoegoPayContractQueryFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MoegoPayContractQueryFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MoegoPayContractQueryFiltersValidationError{}

// Validate checks the field values on MoegoPayContract_Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MoegoPayContract_Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MoegoPayContract_Metadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MoegoPayContract_MetadataMultiError, or nil if none found.
func (m *MoegoPayContract_Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *MoegoPayContract_Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for AccountId

	if len(errors) > 0 {
		return MoegoPayContract_MetadataMultiError(errors)
	}

	return nil
}

// MoegoPayContract_MetadataMultiError is an error wrapping multiple validation
// errors returned by MoegoPayContract_Metadata.ValidateAll() if the
// designated constraints aren't met.
type MoegoPayContract_MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MoegoPayContract_MetadataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MoegoPayContract_MetadataMultiError) AllErrors() []error { return m }

// MoegoPayContract_MetadataValidationError is the validation error returned by
// MoegoPayContract_Metadata.Validate if the designated constraints aren't met.
type MoegoPayContract_MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MoegoPayContract_MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MoegoPayContract_MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MoegoPayContract_MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MoegoPayContract_MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MoegoPayContract_MetadataValidationError) ErrorName() string {
	return "MoegoPayContract_MetadataValidationError"
}

// Error satisfies the builtin error interface
func (e MoegoPayContract_MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoegoPayContract_Metadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MoegoPayContract_MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MoegoPayContract_MetadataValidationError{}

// Validate checks the field values on MoegoPayContract_Parameters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MoegoPayContract_Parameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MoegoPayContract_Parameters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MoegoPayContract_ParametersMultiError, or nil if none found.
func (m *MoegoPayContract_Parameters) ValidateAll() error {
	return m.validate(true)
}

func (m *MoegoPayContract_Parameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyName

	// no validation rules for OwnerName

	// no validation rules for OwnerEmail

	// no validation rules for TerminalPercentage

	// no validation rules for TerminalFixed

	// no validation rules for NonTerminalPercentage

	// no validation rules for NonTerminalFixed

	// no validation rules for MinVolume

	if len(errors) > 0 {
		return MoegoPayContract_ParametersMultiError(errors)
	}

	return nil
}

// MoegoPayContract_ParametersMultiError is an error wrapping multiple
// validation errors returned by MoegoPayContract_Parameters.ValidateAll() if
// the designated constraints aren't met.
type MoegoPayContract_ParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MoegoPayContract_ParametersMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MoegoPayContract_ParametersMultiError) AllErrors() []error { return m }

// MoegoPayContract_ParametersValidationError is the validation error returned
// by MoegoPayContract_Parameters.Validate if the designated constraints
// aren't met.
type MoegoPayContract_ParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MoegoPayContract_ParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MoegoPayContract_ParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MoegoPayContract_ParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MoegoPayContract_ParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MoegoPayContract_ParametersValidationError) ErrorName() string {
	return "MoegoPayContract_ParametersValidationError"
}

// Error satisfies the builtin error interface
func (e MoegoPayContract_ParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoegoPayContract_Parameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MoegoPayContract_ParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MoegoPayContract_ParametersValidationError{}
