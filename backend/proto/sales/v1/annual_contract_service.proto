syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

import "backend/proto/sales/v1/sales_enums.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// AnnualContractService
service AnnualContractService {
  // CreateAnnualContract
  rpc CreateAnnualContract(CreateAnnualContractRequest) returns (AnnualContract);
  // GetAnnualContract
  rpc GetAnnualContract(GetAnnualContractRequest) returns (AnnualContract);
  // ListAnnualContracts
  rpc ListAnnualContracts(ListAnnualContractsRequest) returns (ListAnnualContractsResponse);
  // CountAnnualContracts
  rpc CountAnnualContracts(CountAnnualContractsRequest) returns (CountAnnualContractsResponse);
  // SignAnnualContract
  rpc SignAnnualContract(SignAnnualContractRequest) returns (SignAnnualContractResponse);
  // DeleteAnnualContract
  rpc DeleteAnnualContract(DeleteAnnualContractRequest) returns (google.protobuf.Empty);
}

// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: We need to do this because 没必要嵌套一层，不好用. --)
// CreateAnnualContractRequest
message CreateAnnualContractRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // subscription plan
  SubscriptionPlan subscription_plan = 2 [(validate.rules).enum = {
    defined_only: true,
    not_in: [0]
  }];
  // subscription term months
  // e.g., 12 means 12 months (1 year)
  int32 subscription_term_months = 3 [(validate.rules).int32 = {gt: 0}];
  // discount percentage of subscription
  // e.g., "10" means 10%
  string discount_percentage = 4 [(validate.rules).string = {pattern: "^[0-9]+(\\.[0-9]{1,2})?$"}];
  // number of locations
  int32 location_count = 5 [(validate.rules).int32 = {gte: 0}];
  // number of vans
  int32 van_count = 6 [(validate.rules).int32 = {gte: 0}];
  // creator
  string creator = 7  [(validate.rules).string = {min_len: 1}];
}

// GetAnnualContractRequest
message GetAnnualContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: We need to do this because we don't use parent. --)
// ListAnnualContractsRequest
message ListAnnualContractsRequest {
  // 每页返回条数，最大不超过 100，默认建议为 20
  int32 page_size = 1 [(validate.rules).int32 = {gt: 0, lte: 100}];

  // 上一页返回的分页 token（用于获取下一页）
  string page_token = 2;

  // filters
  AnnualContractQueryFilters filters = 3;
}

// ListAnnualContractsResponse
message ListAnnualContractsResponse {
  // 合同列表
  repeated AnnualContract annual_contracts = 1;

  // 下一页的分页 token，如果为空表示无更多结果
  string next_page_token = 2;
}

// CountAnnualContractsRequest
message CountAnnualContractsRequest {
  // filters
  AnnualContractQueryFilters filters = 1;
}

// CountAnnualContractsResponse
message CountAnnualContractsResponse {
  // count
  int64 count = 1;
}

// SignAnnualContractRequest
message SignAnnualContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
  // signature uri (url)
  string signature_uri = 2 [(validate.rules).string = {uri: true}];
}

// SignAnnualContractResponse
message SignAnnualContractResponse {
  // contract
  AnnualContract contract = 1;
}

// DeleteAnnualContractRequest
message DeleteAnnualContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// AnnualContract
message AnnualContract {
  // id
  string id = 1;
  // template id
  string template_id = 2;
  // metadata
  Metadata metadata = 3;
  // params
  Parameters parameters = 4;
  // content
  string content = 5;
  // creator
  string creator = 6;
  // create time
  google.protobuf.Timestamp create_time = 7;
  // update time
  google.protobuf.Timestamp update_time = 8;
  // sign time
  optional google.protobuf.Timestamp sign_time = 9;
  // signature uri (url)
  optional string signature_uri = 10;

  // metadata
  message Metadata {
    // company id
    int64 company_id = 1;
    // account id
    int64 account_id = 2;
    // level (price config)
    int32 level = 3;
    // subscription plan
    SubscriptionPlan subscription_plan = 4;
  }

  // parameters for rendering
  message Parameters {
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // company name
    string company_name = 1;
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // owner name
    string owner_name = 2;
    // owner email
    string owner_email = 3;
    // address
    string address = 4;
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // subscription plan name
    string subscription_plan_name = 5;
    // subscription term months
    int32 subscription_term_months = 6;
    // location count
    int32 location_count = 7;
    // van count
    int32 van_count = 8;
    // location unit price
    string location_unit_price = 9;
    // van unit price
    string van_unit_price = 10;
    // location discounted unit price
    string location_discounted_unit_price = 11;
    // van discounted unit price
    string van_discounted_unit_price = 12;
    // discount percentage
    string discount_percentage = 13;
    // total amount
    string total_amount = 14;
  }
}

// Query filters for AnnualContract
message AnnualContractQueryFilters {
  // company_id
  optional int64 company_id = 1;
  // account_id
  optional int64 account_id = 2;
  // owner email (prefix like)
  optional string owner_email = 3;
  // creator (prefix like)
  optional string creator = 4;
  // if the contract is signed
  optional bool signed = 5;
}
