// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/sales/v1/moego_pay_custom_fee_approval_service.proto

package salespb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateMoegoPayCustomFeeApprovalRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateMoegoPayCustomFeeApprovalRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateMoegoPayCustomFeeApprovalRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CreateMoegoPayCustomFeeApprovalRequestMultiError, or nil if none found.
func (m *CreateMoegoPayCustomFeeApprovalRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMoegoPayCustomFeeApprovalRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCreator()) < 1 {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "Creator",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCompanyId() <= 0 {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAccountId() <= 0 {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "AccountId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOwnerEmail()) < 1 {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "OwnerEmail",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayCustomFeeApprovalRequest_TerminalPercentage_Pattern.MatchString(m.GetTerminalPercentage()) {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "TerminalPercentage",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayCustomFeeApprovalRequest_TerminalFixed_Pattern.MatchString(m.GetTerminalFixed()) {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "TerminalFixed",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayCustomFeeApprovalRequest_NonTerminalPercentage_Pattern.MatchString(m.GetNonTerminalPercentage()) {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "NonTerminalPercentage",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayCustomFeeApprovalRequest_NonTerminalFixed_Pattern.MatchString(m.GetNonTerminalFixed()) {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "NonTerminalFixed",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMoegoPayCustomFeeApprovalRequest_MinVolume_Pattern.MatchString(m.GetMinVolume()) {
		err := CreateMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "MinVolume",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.Spif != nil {
		// no validation rules for Spif
	}

	if m.OpportunityId != nil {
		// no validation rules for OpportunityId
	}

	if m.ContractId != nil {
		// no validation rules for ContractId
	}

	if len(errors) > 0 {
		return CreateMoegoPayCustomFeeApprovalRequestMultiError(errors)
	}

	return nil
}

// CreateMoegoPayCustomFeeApprovalRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateMoegoPayCustomFeeApprovalRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateMoegoPayCustomFeeApprovalRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMoegoPayCustomFeeApprovalRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMoegoPayCustomFeeApprovalRequestMultiError) AllErrors() []error { return m }

// CreateMoegoPayCustomFeeApprovalRequestValidationError is the validation
// error returned by CreateMoegoPayCustomFeeApprovalRequest.Validate if the
// designated constraints aren't met.
type CreateMoegoPayCustomFeeApprovalRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMoegoPayCustomFeeApprovalRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMoegoPayCustomFeeApprovalRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMoegoPayCustomFeeApprovalRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMoegoPayCustomFeeApprovalRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMoegoPayCustomFeeApprovalRequestValidationError) ErrorName() string {
	return "CreateMoegoPayCustomFeeApprovalRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMoegoPayCustomFeeApprovalRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMoegoPayCustomFeeApprovalRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMoegoPayCustomFeeApprovalRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMoegoPayCustomFeeApprovalRequestValidationError{}

var _CreateMoegoPayCustomFeeApprovalRequest_TerminalPercentage_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

var _CreateMoegoPayCustomFeeApprovalRequest_TerminalFixed_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

var _CreateMoegoPayCustomFeeApprovalRequest_NonTerminalPercentage_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

var _CreateMoegoPayCustomFeeApprovalRequest_NonTerminalFixed_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

var _CreateMoegoPayCustomFeeApprovalRequest_MinVolume_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

// Validate checks the field values on GetMoegoPayCustomFeeApprovalRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetMoegoPayCustomFeeApprovalRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMoegoPayCustomFeeApprovalRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMoegoPayCustomFeeApprovalRequestMultiError, or nil if none found.
func (m *GetMoegoPayCustomFeeApprovalRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMoegoPayCustomFeeApprovalRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := GetMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetMoegoPayCustomFeeApprovalRequestMultiError(errors)
	}

	return nil
}

// GetMoegoPayCustomFeeApprovalRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetMoegoPayCustomFeeApprovalRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMoegoPayCustomFeeApprovalRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMoegoPayCustomFeeApprovalRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMoegoPayCustomFeeApprovalRequestMultiError) AllErrors() []error { return m }

// GetMoegoPayCustomFeeApprovalRequestValidationError is the validation error
// returned by GetMoegoPayCustomFeeApprovalRequest.Validate if the designated
// constraints aren't met.
type GetMoegoPayCustomFeeApprovalRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMoegoPayCustomFeeApprovalRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMoegoPayCustomFeeApprovalRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMoegoPayCustomFeeApprovalRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMoegoPayCustomFeeApprovalRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMoegoPayCustomFeeApprovalRequestValidationError) ErrorName() string {
	return "GetMoegoPayCustomFeeApprovalRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMoegoPayCustomFeeApprovalRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMoegoPayCustomFeeApprovalRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMoegoPayCustomFeeApprovalRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMoegoPayCustomFeeApprovalRequestValidationError{}

// Validate checks the field values on ListMoegoPayCustomFeeApprovalsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListMoegoPayCustomFeeApprovalsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMoegoPayCustomFeeApprovalsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListMoegoPayCustomFeeApprovalsRequestMultiError, or nil if none found.
func (m *ListMoegoPayCustomFeeApprovalsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMoegoPayCustomFeeApprovalsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageSize() <= 0 {
		err := ListMoegoPayCustomFeeApprovalsRequestValidationError{
			field:  "PageSize",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMoegoPayCustomFeeApprovalsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMoegoPayCustomFeeApprovalsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMoegoPayCustomFeeApprovalsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMoegoPayCustomFeeApprovalsRequestMultiError(errors)
	}

	return nil
}

// ListMoegoPayCustomFeeApprovalsRequestMultiError is an error wrapping
// multiple validation errors returned by
// ListMoegoPayCustomFeeApprovalsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListMoegoPayCustomFeeApprovalsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMoegoPayCustomFeeApprovalsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMoegoPayCustomFeeApprovalsRequestMultiError) AllErrors() []error { return m }

// ListMoegoPayCustomFeeApprovalsRequestValidationError is the validation error
// returned by ListMoegoPayCustomFeeApprovalsRequest.Validate if the
// designated constraints aren't met.
type ListMoegoPayCustomFeeApprovalsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMoegoPayCustomFeeApprovalsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMoegoPayCustomFeeApprovalsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMoegoPayCustomFeeApprovalsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMoegoPayCustomFeeApprovalsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMoegoPayCustomFeeApprovalsRequestValidationError) ErrorName() string {
	return "ListMoegoPayCustomFeeApprovalsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMoegoPayCustomFeeApprovalsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMoegoPayCustomFeeApprovalsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMoegoPayCustomFeeApprovalsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMoegoPayCustomFeeApprovalsRequestValidationError{}

// Validate checks the field values on ListMoegoPayCustomFeeApprovalsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListMoegoPayCustomFeeApprovalsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListMoegoPayCustomFeeApprovalsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ListMoegoPayCustomFeeApprovalsResponseMultiError, or nil if none found.
func (m *ListMoegoPayCustomFeeApprovalsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMoegoPayCustomFeeApprovalsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMoegoPayCustomFeeApprovals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMoegoPayCustomFeeApprovalsResponseValidationError{
						field:  fmt.Sprintf("MoegoPayCustomFeeApprovals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMoegoPayCustomFeeApprovalsResponseValidationError{
						field:  fmt.Sprintf("MoegoPayCustomFeeApprovals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMoegoPayCustomFeeApprovalsResponseValidationError{
					field:  fmt.Sprintf("MoegoPayCustomFeeApprovals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListMoegoPayCustomFeeApprovalsResponseMultiError(errors)
	}

	return nil
}

// ListMoegoPayCustomFeeApprovalsResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListMoegoPayCustomFeeApprovalsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListMoegoPayCustomFeeApprovalsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMoegoPayCustomFeeApprovalsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMoegoPayCustomFeeApprovalsResponseMultiError) AllErrors() []error { return m }

// ListMoegoPayCustomFeeApprovalsResponseValidationError is the validation
// error returned by ListMoegoPayCustomFeeApprovalsResponse.Validate if the
// designated constraints aren't met.
type ListMoegoPayCustomFeeApprovalsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMoegoPayCustomFeeApprovalsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMoegoPayCustomFeeApprovalsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMoegoPayCustomFeeApprovalsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMoegoPayCustomFeeApprovalsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMoegoPayCustomFeeApprovalsResponseValidationError) ErrorName() string {
	return "ListMoegoPayCustomFeeApprovalsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListMoegoPayCustomFeeApprovalsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMoegoPayCustomFeeApprovalsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMoegoPayCustomFeeApprovalsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMoegoPayCustomFeeApprovalsResponseValidationError{}

// Validate checks the field values on CountMoegoPayCustomFeeApprovalsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CountMoegoPayCustomFeeApprovalsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CountMoegoPayCustomFeeApprovalsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CountMoegoPayCustomFeeApprovalsRequestMultiError, or nil if none found.
func (m *CountMoegoPayCustomFeeApprovalsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CountMoegoPayCustomFeeApprovalsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountMoegoPayCustomFeeApprovalsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountMoegoPayCustomFeeApprovalsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountMoegoPayCustomFeeApprovalsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CountMoegoPayCustomFeeApprovalsRequestMultiError(errors)
	}

	return nil
}

// CountMoegoPayCustomFeeApprovalsRequestMultiError is an error wrapping
// multiple validation errors returned by
// CountMoegoPayCustomFeeApprovalsRequest.ValidateAll() if the designated
// constraints aren't met.
type CountMoegoPayCustomFeeApprovalsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountMoegoPayCustomFeeApprovalsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountMoegoPayCustomFeeApprovalsRequestMultiError) AllErrors() []error { return m }

// CountMoegoPayCustomFeeApprovalsRequestValidationError is the validation
// error returned by CountMoegoPayCustomFeeApprovalsRequest.Validate if the
// designated constraints aren't met.
type CountMoegoPayCustomFeeApprovalsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountMoegoPayCustomFeeApprovalsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountMoegoPayCustomFeeApprovalsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountMoegoPayCustomFeeApprovalsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountMoegoPayCustomFeeApprovalsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountMoegoPayCustomFeeApprovalsRequestValidationError) ErrorName() string {
	return "CountMoegoPayCustomFeeApprovalsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CountMoegoPayCustomFeeApprovalsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountMoegoPayCustomFeeApprovalsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountMoegoPayCustomFeeApprovalsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountMoegoPayCustomFeeApprovalsRequestValidationError{}

// Validate checks the field values on CountMoegoPayCustomFeeApprovalsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CountMoegoPayCustomFeeApprovalsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CountMoegoPayCustomFeeApprovalsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CountMoegoPayCustomFeeApprovalsResponseMultiError, or nil if none found.
func (m *CountMoegoPayCustomFeeApprovalsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CountMoegoPayCustomFeeApprovalsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return CountMoegoPayCustomFeeApprovalsResponseMultiError(errors)
	}

	return nil
}

// CountMoegoPayCustomFeeApprovalsResponseMultiError is an error wrapping
// multiple validation errors returned by
// CountMoegoPayCustomFeeApprovalsResponse.ValidateAll() if the designated
// constraints aren't met.
type CountMoegoPayCustomFeeApprovalsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountMoegoPayCustomFeeApprovalsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountMoegoPayCustomFeeApprovalsResponseMultiError) AllErrors() []error { return m }

// CountMoegoPayCustomFeeApprovalsResponseValidationError is the validation
// error returned by CountMoegoPayCustomFeeApprovalsResponse.Validate if the
// designated constraints aren't met.
type CountMoegoPayCustomFeeApprovalsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountMoegoPayCustomFeeApprovalsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountMoegoPayCustomFeeApprovalsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountMoegoPayCustomFeeApprovalsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountMoegoPayCustomFeeApprovalsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountMoegoPayCustomFeeApprovalsResponseValidationError) ErrorName() string {
	return "CountMoegoPayCustomFeeApprovalsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CountMoegoPayCustomFeeApprovalsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountMoegoPayCustomFeeApprovalsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountMoegoPayCustomFeeApprovalsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountMoegoPayCustomFeeApprovalsResponseValidationError{}

// Validate checks the field values on ApproveMoegoPayCustomFeeApprovalRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ApproveMoegoPayCustomFeeApprovalRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ApproveMoegoPayCustomFeeApprovalRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ApproveMoegoPayCustomFeeApprovalRequestMultiError, or nil if none found.
func (m *ApproveMoegoPayCustomFeeApprovalRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ApproveMoegoPayCustomFeeApprovalRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := ApproveMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetHandler()) < 1 {
		err := ApproveMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "Handler",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ApproveMoegoPayCustomFeeApprovalRequestMultiError(errors)
	}

	return nil
}

// ApproveMoegoPayCustomFeeApprovalRequestMultiError is an error wrapping
// multiple validation errors returned by
// ApproveMoegoPayCustomFeeApprovalRequest.ValidateAll() if the designated
// constraints aren't met.
type ApproveMoegoPayCustomFeeApprovalRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApproveMoegoPayCustomFeeApprovalRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApproveMoegoPayCustomFeeApprovalRequestMultiError) AllErrors() []error { return m }

// ApproveMoegoPayCustomFeeApprovalRequestValidationError is the validation
// error returned by ApproveMoegoPayCustomFeeApprovalRequest.Validate if the
// designated constraints aren't met.
type ApproveMoegoPayCustomFeeApprovalRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApproveMoegoPayCustomFeeApprovalRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApproveMoegoPayCustomFeeApprovalRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApproveMoegoPayCustomFeeApprovalRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApproveMoegoPayCustomFeeApprovalRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApproveMoegoPayCustomFeeApprovalRequestValidationError) ErrorName() string {
	return "ApproveMoegoPayCustomFeeApprovalRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ApproveMoegoPayCustomFeeApprovalRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApproveMoegoPayCustomFeeApprovalRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApproveMoegoPayCustomFeeApprovalRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApproveMoegoPayCustomFeeApprovalRequestValidationError{}

// Validate checks the field values on ApproveMoegoPayCustomFeeApprovalResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ApproveMoegoPayCustomFeeApprovalResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ApproveMoegoPayCustomFeeApprovalResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ApproveMoegoPayCustomFeeApprovalResponseMultiError, or nil if none found.
func (m *ApproveMoegoPayCustomFeeApprovalResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ApproveMoegoPayCustomFeeApprovalResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMoegoPayCustomFeeApproval()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApproveMoegoPayCustomFeeApprovalResponseValidationError{
					field:  "MoegoPayCustomFeeApproval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApproveMoegoPayCustomFeeApprovalResponseValidationError{
					field:  "MoegoPayCustomFeeApproval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoegoPayCustomFeeApproval()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApproveMoegoPayCustomFeeApprovalResponseValidationError{
				field:  "MoegoPayCustomFeeApproval",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApproveMoegoPayCustomFeeApprovalResponseMultiError(errors)
	}

	return nil
}

// ApproveMoegoPayCustomFeeApprovalResponseMultiError is an error wrapping
// multiple validation errors returned by
// ApproveMoegoPayCustomFeeApprovalResponse.ValidateAll() if the designated
// constraints aren't met.
type ApproveMoegoPayCustomFeeApprovalResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApproveMoegoPayCustomFeeApprovalResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApproveMoegoPayCustomFeeApprovalResponseMultiError) AllErrors() []error { return m }

// ApproveMoegoPayCustomFeeApprovalResponseValidationError is the validation
// error returned by ApproveMoegoPayCustomFeeApprovalResponse.Validate if the
// designated constraints aren't met.
type ApproveMoegoPayCustomFeeApprovalResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApproveMoegoPayCustomFeeApprovalResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApproveMoegoPayCustomFeeApprovalResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApproveMoegoPayCustomFeeApprovalResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApproveMoegoPayCustomFeeApprovalResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApproveMoegoPayCustomFeeApprovalResponseValidationError) ErrorName() string {
	return "ApproveMoegoPayCustomFeeApprovalResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ApproveMoegoPayCustomFeeApprovalResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApproveMoegoPayCustomFeeApprovalResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApproveMoegoPayCustomFeeApprovalResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApproveMoegoPayCustomFeeApprovalResponseValidationError{}

// Validate checks the field values on RejectMoegoPayCustomFeeApprovalRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RejectMoegoPayCustomFeeApprovalRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RejectMoegoPayCustomFeeApprovalRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// RejectMoegoPayCustomFeeApprovalRequestMultiError, or nil if none found.
func (m *RejectMoegoPayCustomFeeApprovalRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RejectMoegoPayCustomFeeApprovalRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := RejectMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetHandler()) < 1 {
		err := RejectMoegoPayCustomFeeApprovalRequestValidationError{
			field:  "Handler",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RejectMoegoPayCustomFeeApprovalRequestMultiError(errors)
	}

	return nil
}

// RejectMoegoPayCustomFeeApprovalRequestMultiError is an error wrapping
// multiple validation errors returned by
// RejectMoegoPayCustomFeeApprovalRequest.ValidateAll() if the designated
// constraints aren't met.
type RejectMoegoPayCustomFeeApprovalRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RejectMoegoPayCustomFeeApprovalRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RejectMoegoPayCustomFeeApprovalRequestMultiError) AllErrors() []error { return m }

// RejectMoegoPayCustomFeeApprovalRequestValidationError is the validation
// error returned by RejectMoegoPayCustomFeeApprovalRequest.Validate if the
// designated constraints aren't met.
type RejectMoegoPayCustomFeeApprovalRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RejectMoegoPayCustomFeeApprovalRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RejectMoegoPayCustomFeeApprovalRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RejectMoegoPayCustomFeeApprovalRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RejectMoegoPayCustomFeeApprovalRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RejectMoegoPayCustomFeeApprovalRequestValidationError) ErrorName() string {
	return "RejectMoegoPayCustomFeeApprovalRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RejectMoegoPayCustomFeeApprovalRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRejectMoegoPayCustomFeeApprovalRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RejectMoegoPayCustomFeeApprovalRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RejectMoegoPayCustomFeeApprovalRequestValidationError{}

// Validate checks the field values on RejectMoegoPayCustomFeeApprovalResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RejectMoegoPayCustomFeeApprovalResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RejectMoegoPayCustomFeeApprovalResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// RejectMoegoPayCustomFeeApprovalResponseMultiError, or nil if none found.
func (m *RejectMoegoPayCustomFeeApprovalResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RejectMoegoPayCustomFeeApprovalResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMoegoPayCustomFeeApproval()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RejectMoegoPayCustomFeeApprovalResponseValidationError{
					field:  "MoegoPayCustomFeeApproval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RejectMoegoPayCustomFeeApprovalResponseValidationError{
					field:  "MoegoPayCustomFeeApproval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoegoPayCustomFeeApproval()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RejectMoegoPayCustomFeeApprovalResponseValidationError{
				field:  "MoegoPayCustomFeeApproval",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RejectMoegoPayCustomFeeApprovalResponseMultiError(errors)
	}

	return nil
}

// RejectMoegoPayCustomFeeApprovalResponseMultiError is an error wrapping
// multiple validation errors returned by
// RejectMoegoPayCustomFeeApprovalResponse.ValidateAll() if the designated
// constraints aren't met.
type RejectMoegoPayCustomFeeApprovalResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RejectMoegoPayCustomFeeApprovalResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RejectMoegoPayCustomFeeApprovalResponseMultiError) AllErrors() []error { return m }

// RejectMoegoPayCustomFeeApprovalResponseValidationError is the validation
// error returned by RejectMoegoPayCustomFeeApprovalResponse.Validate if the
// designated constraints aren't met.
type RejectMoegoPayCustomFeeApprovalResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RejectMoegoPayCustomFeeApprovalResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RejectMoegoPayCustomFeeApprovalResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RejectMoegoPayCustomFeeApprovalResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RejectMoegoPayCustomFeeApprovalResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RejectMoegoPayCustomFeeApprovalResponseValidationError) ErrorName() string {
	return "RejectMoegoPayCustomFeeApprovalResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RejectMoegoPayCustomFeeApprovalResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRejectMoegoPayCustomFeeApprovalResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RejectMoegoPayCustomFeeApprovalResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RejectMoegoPayCustomFeeApprovalResponseValidationError{}

// Validate checks the field values on MoegoPayCustomFeeApproval with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MoegoPayCustomFeeApproval) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MoegoPayCustomFeeApproval with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MoegoPayCustomFeeApprovalMultiError, or nil if none found.
func (m *MoegoPayCustomFeeApproval) ValidateAll() error {
	return m.validate(true)
}

func (m *MoegoPayCustomFeeApproval) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MoegoPayCustomFeeApprovalValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MoegoPayCustomFeeApprovalValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MoegoPayCustomFeeApprovalValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CompanyId

	// no validation rules for AccountId

	// no validation rules for OwnerEmail

	// no validation rules for TerminalPercentage

	// no validation rules for TerminalFixed

	// no validation rules for NonTerminalPercentage

	// no validation rules for NonTerminalFixed

	// no validation rules for MinVolume

	// no validation rules for Creator

	// no validation rules for ApprovalState

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MoegoPayCustomFeeApprovalValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MoegoPayCustomFeeApprovalValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MoegoPayCustomFeeApprovalValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MoegoPayCustomFeeApprovalValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MoegoPayCustomFeeApprovalValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MoegoPayCustomFeeApprovalValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Handler != nil {
		// no validation rules for Handler
	}

	if m.HandleTime != nil {

		if all {
			switch v := interface{}(m.GetHandleTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MoegoPayCustomFeeApprovalValidationError{
						field:  "HandleTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MoegoPayCustomFeeApprovalValidationError{
						field:  "HandleTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHandleTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MoegoPayCustomFeeApprovalValidationError{
					field:  "HandleTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MoegoPayCustomFeeApprovalMultiError(errors)
	}

	return nil
}

// MoegoPayCustomFeeApprovalMultiError is an error wrapping multiple validation
// errors returned by MoegoPayCustomFeeApproval.ValidateAll() if the
// designated constraints aren't met.
type MoegoPayCustomFeeApprovalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MoegoPayCustomFeeApprovalMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MoegoPayCustomFeeApprovalMultiError) AllErrors() []error { return m }

// MoegoPayCustomFeeApprovalValidationError is the validation error returned by
// MoegoPayCustomFeeApproval.Validate if the designated constraints aren't met.
type MoegoPayCustomFeeApprovalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MoegoPayCustomFeeApprovalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MoegoPayCustomFeeApprovalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MoegoPayCustomFeeApprovalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MoegoPayCustomFeeApprovalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MoegoPayCustomFeeApprovalValidationError) ErrorName() string {
	return "MoegoPayCustomFeeApprovalValidationError"
}

// Error satisfies the builtin error interface
func (e MoegoPayCustomFeeApprovalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoegoPayCustomFeeApproval.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MoegoPayCustomFeeApprovalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MoegoPayCustomFeeApprovalValidationError{}

// Validate checks the field values on MoegoPayCustomFeeApprovalQueryFilters
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *MoegoPayCustomFeeApprovalQueryFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MoegoPayCustomFeeApprovalQueryFilters
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// MoegoPayCustomFeeApprovalQueryFiltersMultiError, or nil if none found.
func (m *MoegoPayCustomFeeApprovalQueryFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *MoegoPayCustomFeeApprovalQueryFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CompanyId != nil {
		// no validation rules for CompanyId
	}

	if m.AccountId != nil {
		// no validation rules for AccountId
	}

	if m.OwnerEmail != nil {
		// no validation rules for OwnerEmail
	}

	if len(errors) > 0 {
		return MoegoPayCustomFeeApprovalQueryFiltersMultiError(errors)
	}

	return nil
}

// MoegoPayCustomFeeApprovalQueryFiltersMultiError is an error wrapping
// multiple validation errors returned by
// MoegoPayCustomFeeApprovalQueryFilters.ValidateAll() if the designated
// constraints aren't met.
type MoegoPayCustomFeeApprovalQueryFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MoegoPayCustomFeeApprovalQueryFiltersMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MoegoPayCustomFeeApprovalQueryFiltersMultiError) AllErrors() []error { return m }

// MoegoPayCustomFeeApprovalQueryFiltersValidationError is the validation error
// returned by MoegoPayCustomFeeApprovalQueryFilters.Validate if the
// designated constraints aren't met.
type MoegoPayCustomFeeApprovalQueryFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MoegoPayCustomFeeApprovalQueryFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MoegoPayCustomFeeApprovalQueryFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MoegoPayCustomFeeApprovalQueryFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MoegoPayCustomFeeApprovalQueryFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MoegoPayCustomFeeApprovalQueryFiltersValidationError) ErrorName() string {
	return "MoegoPayCustomFeeApprovalQueryFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e MoegoPayCustomFeeApprovalQueryFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoegoPayCustomFeeApprovalQueryFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MoegoPayCustomFeeApprovalQueryFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MoegoPayCustomFeeApprovalQueryFiltersValidationError{}

// Validate checks the field values on MoegoPayCustomFeeApproval_Metadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *MoegoPayCustomFeeApproval_Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MoegoPayCustomFeeApproval_Metadata
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// MoegoPayCustomFeeApproval_MetadataMultiError, or nil if none found.
func (m *MoegoPayCustomFeeApproval_Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *MoegoPayCustomFeeApproval_Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Spif != nil {
		// no validation rules for Spif
	}

	if m.OpportunityId != nil {
		// no validation rules for OpportunityId
	}

	if m.ContractId != nil {
		// no validation rules for ContractId
	}

	if len(errors) > 0 {
		return MoegoPayCustomFeeApproval_MetadataMultiError(errors)
	}

	return nil
}

// MoegoPayCustomFeeApproval_MetadataMultiError is an error wrapping multiple
// validation errors returned by
// MoegoPayCustomFeeApproval_Metadata.ValidateAll() if the designated
// constraints aren't met.
type MoegoPayCustomFeeApproval_MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MoegoPayCustomFeeApproval_MetadataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MoegoPayCustomFeeApproval_MetadataMultiError) AllErrors() []error { return m }

// MoegoPayCustomFeeApproval_MetadataValidationError is the validation error
// returned by MoegoPayCustomFeeApproval_Metadata.Validate if the designated
// constraints aren't met.
type MoegoPayCustomFeeApproval_MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MoegoPayCustomFeeApproval_MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MoegoPayCustomFeeApproval_MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MoegoPayCustomFeeApproval_MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MoegoPayCustomFeeApproval_MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MoegoPayCustomFeeApproval_MetadataValidationError) ErrorName() string {
	return "MoegoPayCustomFeeApproval_MetadataValidationError"
}

// Error satisfies the builtin error interface
func (e MoegoPayCustomFeeApproval_MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoegoPayCustomFeeApproval_Metadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MoegoPayCustomFeeApproval_MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MoegoPayCustomFeeApproval_MetadataValidationError{}
