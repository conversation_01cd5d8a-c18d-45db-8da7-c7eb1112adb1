// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/sales/v1/annual_contract_service.proto

package salespb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AnnualContractService_CreateAnnualContract_FullMethodName = "/backend.proto.sales.v1.AnnualContractService/CreateAnnualContract"
	AnnualContractService_GetAnnualContract_FullMethodName    = "/backend.proto.sales.v1.AnnualContractService/GetAnnualContract"
	AnnualContractService_ListAnnualContracts_FullMethodName  = "/backend.proto.sales.v1.AnnualContractService/ListAnnualContracts"
	AnnualContractService_CountAnnualContracts_FullMethodName = "/backend.proto.sales.v1.AnnualContractService/CountAnnualContracts"
	AnnualContractService_SignAnnualContract_FullMethodName   = "/backend.proto.sales.v1.AnnualContractService/SignAnnualContract"
	AnnualContractService_DeleteAnnualContract_FullMethodName = "/backend.proto.sales.v1.AnnualContractService/DeleteAnnualContract"
)

// AnnualContractServiceClient is the client API for AnnualContractService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AnnualContractService
type AnnualContractServiceClient interface {
	// CreateAnnualContract
	CreateAnnualContract(ctx context.Context, in *CreateAnnualContractRequest, opts ...grpc.CallOption) (*AnnualContract, error)
	// GetAnnualContract
	GetAnnualContract(ctx context.Context, in *GetAnnualContractRequest, opts ...grpc.CallOption) (*AnnualContract, error)
	// ListAnnualContracts
	ListAnnualContracts(ctx context.Context, in *ListAnnualContractsRequest, opts ...grpc.CallOption) (*ListAnnualContractsResponse, error)
	// CountAnnualContracts
	CountAnnualContracts(ctx context.Context, in *CountAnnualContractsRequest, opts ...grpc.CallOption) (*CountAnnualContractsResponse, error)
	// SignAnnualContract
	SignAnnualContract(ctx context.Context, in *SignAnnualContractRequest, opts ...grpc.CallOption) (*SignAnnualContractResponse, error)
	// DeleteAnnualContract
	DeleteAnnualContract(ctx context.Context, in *DeleteAnnualContractRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type annualContractServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAnnualContractServiceClient(cc grpc.ClientConnInterface) AnnualContractServiceClient {
	return &annualContractServiceClient{cc}
}

func (c *annualContractServiceClient) CreateAnnualContract(ctx context.Context, in *CreateAnnualContractRequest, opts ...grpc.CallOption) (*AnnualContract, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnnualContract)
	err := c.cc.Invoke(ctx, AnnualContractService_CreateAnnualContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *annualContractServiceClient) GetAnnualContract(ctx context.Context, in *GetAnnualContractRequest, opts ...grpc.CallOption) (*AnnualContract, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnnualContract)
	err := c.cc.Invoke(ctx, AnnualContractService_GetAnnualContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *annualContractServiceClient) ListAnnualContracts(ctx context.Context, in *ListAnnualContractsRequest, opts ...grpc.CallOption) (*ListAnnualContractsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAnnualContractsResponse)
	err := c.cc.Invoke(ctx, AnnualContractService_ListAnnualContracts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *annualContractServiceClient) CountAnnualContracts(ctx context.Context, in *CountAnnualContractsRequest, opts ...grpc.CallOption) (*CountAnnualContractsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountAnnualContractsResponse)
	err := c.cc.Invoke(ctx, AnnualContractService_CountAnnualContracts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *annualContractServiceClient) SignAnnualContract(ctx context.Context, in *SignAnnualContractRequest, opts ...grpc.CallOption) (*SignAnnualContractResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignAnnualContractResponse)
	err := c.cc.Invoke(ctx, AnnualContractService_SignAnnualContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *annualContractServiceClient) DeleteAnnualContract(ctx context.Context, in *DeleteAnnualContractRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AnnualContractService_DeleteAnnualContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnnualContractServiceServer is the server API for AnnualContractService service.
// All implementations must embed UnimplementedAnnualContractServiceServer
// for forward compatibility.
//
// AnnualContractService
type AnnualContractServiceServer interface {
	// CreateAnnualContract
	CreateAnnualContract(context.Context, *CreateAnnualContractRequest) (*AnnualContract, error)
	// GetAnnualContract
	GetAnnualContract(context.Context, *GetAnnualContractRequest) (*AnnualContract, error)
	// ListAnnualContracts
	ListAnnualContracts(context.Context, *ListAnnualContractsRequest) (*ListAnnualContractsResponse, error)
	// CountAnnualContracts
	CountAnnualContracts(context.Context, *CountAnnualContractsRequest) (*CountAnnualContractsResponse, error)
	// SignAnnualContract
	SignAnnualContract(context.Context, *SignAnnualContractRequest) (*SignAnnualContractResponse, error)
	// DeleteAnnualContract
	DeleteAnnualContract(context.Context, *DeleteAnnualContractRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedAnnualContractServiceServer()
}

// UnimplementedAnnualContractServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAnnualContractServiceServer struct{}

func (UnimplementedAnnualContractServiceServer) CreateAnnualContract(context.Context, *CreateAnnualContractRequest) (*AnnualContract, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAnnualContract not implemented")
}
func (UnimplementedAnnualContractServiceServer) GetAnnualContract(context.Context, *GetAnnualContractRequest) (*AnnualContract, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAnnualContract not implemented")
}
func (UnimplementedAnnualContractServiceServer) ListAnnualContracts(context.Context, *ListAnnualContractsRequest) (*ListAnnualContractsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAnnualContracts not implemented")
}
func (UnimplementedAnnualContractServiceServer) CountAnnualContracts(context.Context, *CountAnnualContractsRequest) (*CountAnnualContractsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAnnualContracts not implemented")
}
func (UnimplementedAnnualContractServiceServer) SignAnnualContract(context.Context, *SignAnnualContractRequest) (*SignAnnualContractResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignAnnualContract not implemented")
}
func (UnimplementedAnnualContractServiceServer) DeleteAnnualContract(context.Context, *DeleteAnnualContractRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAnnualContract not implemented")
}
func (UnimplementedAnnualContractServiceServer) mustEmbedUnimplementedAnnualContractServiceServer() {}
func (UnimplementedAnnualContractServiceServer) testEmbeddedByValue()                               {}

// UnsafeAnnualContractServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnnualContractServiceServer will
// result in compilation errors.
type UnsafeAnnualContractServiceServer interface {
	mustEmbedUnimplementedAnnualContractServiceServer()
}

func RegisterAnnualContractServiceServer(s grpc.ServiceRegistrar, srv AnnualContractServiceServer) {
	// If the following call pancis, it indicates UnimplementedAnnualContractServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AnnualContractService_ServiceDesc, srv)
}

func _AnnualContractService_CreateAnnualContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAnnualContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnualContractServiceServer).CreateAnnualContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnnualContractService_CreateAnnualContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnualContractServiceServer).CreateAnnualContract(ctx, req.(*CreateAnnualContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnnualContractService_GetAnnualContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnnualContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnualContractServiceServer).GetAnnualContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnnualContractService_GetAnnualContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnualContractServiceServer).GetAnnualContract(ctx, req.(*GetAnnualContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnnualContractService_ListAnnualContracts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAnnualContractsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnualContractServiceServer).ListAnnualContracts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnnualContractService_ListAnnualContracts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnualContractServiceServer).ListAnnualContracts(ctx, req.(*ListAnnualContractsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnnualContractService_CountAnnualContracts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAnnualContractsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnualContractServiceServer).CountAnnualContracts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnnualContractService_CountAnnualContracts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnualContractServiceServer).CountAnnualContracts(ctx, req.(*CountAnnualContractsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnnualContractService_SignAnnualContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignAnnualContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnualContractServiceServer).SignAnnualContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnnualContractService_SignAnnualContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnualContractServiceServer).SignAnnualContract(ctx, req.(*SignAnnualContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnnualContractService_DeleteAnnualContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAnnualContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnualContractServiceServer).DeleteAnnualContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnnualContractService_DeleteAnnualContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnualContractServiceServer).DeleteAnnualContract(ctx, req.(*DeleteAnnualContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AnnualContractService_ServiceDesc is the grpc.ServiceDesc for AnnualContractService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnnualContractService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.sales.v1.AnnualContractService",
	HandlerType: (*AnnualContractServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAnnualContract",
			Handler:    _AnnualContractService_CreateAnnualContract_Handler,
		},
		{
			MethodName: "GetAnnualContract",
			Handler:    _AnnualContractService_GetAnnualContract_Handler,
		},
		{
			MethodName: "ListAnnualContracts",
			Handler:    _AnnualContractService_ListAnnualContracts_Handler,
		},
		{
			MethodName: "CountAnnualContracts",
			Handler:    _AnnualContractService_CountAnnualContracts_Handler,
		},
		{
			MethodName: "SignAnnualContract",
			Handler:    _AnnualContractService_SignAnnualContract_Handler,
		},
		{
			MethodName: "DeleteAnnualContract",
			Handler:    _AnnualContractService_DeleteAnnualContract_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/sales/v1/annual_contract_service.proto",
}
