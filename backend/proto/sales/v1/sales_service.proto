syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

import "backend/proto/sales/v1/sales_enums.proto";
import "validate/validate.proto";
import "google/type/decimal.proto";
import "google/type/money.proto";

// SalesService
service SalesService {
  // SyncOpportunity updates an opportunity.
  rpc SyncOpportunity(SyncOpportunityRequest) returns (SyncOpportunityResponse);
  // SyncSalesSubscription add subscription line items to an opportunity.
  rpc SyncSalesSubscription(SyncSalesSubscriptionRequest) returns (SyncSalesSubscriptionResponse);
  // SyncSalesHardware add hardware line items to an opportunity.
  rpc SyncSalesHardware(SyncSalesHardwareRequest) returns (SyncSalesHardwareResponse);
}

// SyncOpportunityRequest
message SyncOpportunityRequest {
  // opportunity id
  string opportunity_id = 1 [(validate.rules).string.min_len = 1];
  // email
  optional string email = 2 [(validate.rules).string.min_len = 1];
  // tier
  optional string tier = 3 [(validate.rules).string.min_len = 1];
  // terminal percentage
  optional google.type.Decimal terminal_percentage = 4;
  // terminal fixed
  optional google.type.Decimal terminal_fixed = 5;
  // non terminal percentage
  optional google.type.Decimal non_terminal_percentage = 6;
  // non terminal fixed
  optional google.type.Decimal non_terminal_fixed = 7;
  // min_volume
  optional google.type.Decimal min_volume = 8;
  // spif
  optional google.type.Decimal spif = 9;
}

// SyncOpportunityResponse
message SyncOpportunityResponse {}

// SyncSalesSubscriptionRequest
message SyncSalesSubscriptionRequest {
  // sales code
  string sales_code = 1 [(validate.rules).string.min_len = 1];
  // opportunity id
  string opportunity_id = 2 [(validate.rules).string.min_len = 1];
  // subscription plan
  SubscriptionPlan subscription_plan = 3;
  // subscription term
  SubscriptionTerm subscription_term = 4;
  // salon
  optional LineItem salon = 5;
  // van
  optional LineItem van = 6;
  // contract link
  optional string contract_link = 7;
  // contract signed
  optional bool contract_signed = 8;
}

// SyncSalesSubscriptionResponse
message SyncSalesSubscriptionResponse {}

// SyncSalesHardwareRequest
message SyncSalesHardwareRequest {
  // sales code
  string sales_code = 1 [(validate.rules).string.min_len = 1];
  // opportunity id
  string opportunity_id = 2 [(validate.rules).string.min_len = 1];
  // reader m2
  optional LineItem reader_m2 = 3;
  // bbpos
  optional LineItem bbpos = 4;
}

// SyncSalesHardwareResponse
message SyncSalesHardwareResponse {}

// line item
message LineItem {
  // quantity
  int32 quantity = 1 [(validate.rules).int32.gt = 0];
  // unit price (before discount)
  google.type.Money unit_price = 2;
  // discount percentage
  optional google.type.Decimal discount_percentage = 3;
}

// subscription term
enum SubscriptionTerm {
  // unspecified
  SUBSCRIPTION_TERM_UNSPECIFIED = 0;
  // monthly
  MONTHLY = 1;
  // 1 year
  ONE_YEAR = 2;
  // 2 years
  TWO_YEARS = 3;
  // 3 years
  THREE_YEARS = 4;
}

// ErrCode 定义错误码枚举
// 
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because 
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 993900; 
}