// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/sales/v1/sales_service.proto

package salespb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SyncOpportunityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncOpportunityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncOpportunityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncOpportunityRequestMultiError, or nil if none found.
func (m *SyncOpportunityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncOpportunityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetOpportunityId()) < 1 {
		err := SyncOpportunityRequestValidationError{
			field:  "OpportunityId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.Email != nil {

		if utf8.RuneCountInString(m.GetEmail()) < 1 {
			err := SyncOpportunityRequestValidationError{
				field:  "Email",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Tier != nil {

		if utf8.RuneCountInString(m.GetTier()) < 1 {
			err := SyncOpportunityRequestValidationError{
				field:  "Tier",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.TerminalPercentage != nil {

		if all {
			switch v := interface{}(m.GetTerminalPercentage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "TerminalPercentage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "TerminalPercentage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTerminalPercentage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncOpportunityRequestValidationError{
					field:  "TerminalPercentage",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.TerminalFixed != nil {

		if all {
			switch v := interface{}(m.GetTerminalFixed()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "TerminalFixed",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "TerminalFixed",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTerminalFixed()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncOpportunityRequestValidationError{
					field:  "TerminalFixed",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.NonTerminalPercentage != nil {

		if all {
			switch v := interface{}(m.GetNonTerminalPercentage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "NonTerminalPercentage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "NonTerminalPercentage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNonTerminalPercentage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncOpportunityRequestValidationError{
					field:  "NonTerminalPercentage",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.NonTerminalFixed != nil {

		if all {
			switch v := interface{}(m.GetNonTerminalFixed()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "NonTerminalFixed",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "NonTerminalFixed",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNonTerminalFixed()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncOpportunityRequestValidationError{
					field:  "NonTerminalFixed",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.MinVolume != nil {

		if all {
			switch v := interface{}(m.GetMinVolume()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "MinVolume",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "MinVolume",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMinVolume()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncOpportunityRequestValidationError{
					field:  "MinVolume",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Spif != nil {

		if all {
			switch v := interface{}(m.GetSpif()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "Spif",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncOpportunityRequestValidationError{
						field:  "Spif",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSpif()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncOpportunityRequestValidationError{
					field:  "Spif",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SyncOpportunityRequestMultiError(errors)
	}

	return nil
}

// SyncOpportunityRequestMultiError is an error wrapping multiple validation
// errors returned by SyncOpportunityRequest.ValidateAll() if the designated
// constraints aren't met.
type SyncOpportunityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncOpportunityRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncOpportunityRequestMultiError) AllErrors() []error { return m }

// SyncOpportunityRequestValidationError is the validation error returned by
// SyncOpportunityRequest.Validate if the designated constraints aren't met.
type SyncOpportunityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncOpportunityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncOpportunityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncOpportunityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncOpportunityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncOpportunityRequestValidationError) ErrorName() string {
	return "SyncOpportunityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncOpportunityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncOpportunityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncOpportunityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncOpportunityRequestValidationError{}

// Validate checks the field values on SyncOpportunityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncOpportunityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncOpportunityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncOpportunityResponseMultiError, or nil if none found.
func (m *SyncOpportunityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncOpportunityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SyncOpportunityResponseMultiError(errors)
	}

	return nil
}

// SyncOpportunityResponseMultiError is an error wrapping multiple validation
// errors returned by SyncOpportunityResponse.ValidateAll() if the designated
// constraints aren't met.
type SyncOpportunityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncOpportunityResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncOpportunityResponseMultiError) AllErrors() []error { return m }

// SyncOpportunityResponseValidationError is the validation error returned by
// SyncOpportunityResponse.Validate if the designated constraints aren't met.
type SyncOpportunityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncOpportunityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncOpportunityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncOpportunityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncOpportunityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncOpportunityResponseValidationError) ErrorName() string {
	return "SyncOpportunityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncOpportunityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncOpportunityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncOpportunityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncOpportunityResponseValidationError{}

// Validate checks the field values on SyncSalesSubscriptionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncSalesSubscriptionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncSalesSubscriptionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncSalesSubscriptionRequestMultiError, or nil if none found.
func (m *SyncSalesSubscriptionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncSalesSubscriptionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetSalesCode()) < 1 {
		err := SyncSalesSubscriptionRequestValidationError{
			field:  "SalesCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOpportunityId()) < 1 {
		err := SyncSalesSubscriptionRequestValidationError{
			field:  "OpportunityId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SubscriptionPlan

	// no validation rules for SubscriptionTerm

	if m.Salon != nil {

		if all {
			switch v := interface{}(m.GetSalon()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncSalesSubscriptionRequestValidationError{
						field:  "Salon",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncSalesSubscriptionRequestValidationError{
						field:  "Salon",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSalon()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncSalesSubscriptionRequestValidationError{
					field:  "Salon",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Van != nil {

		if all {
			switch v := interface{}(m.GetVan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncSalesSubscriptionRequestValidationError{
						field:  "Van",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncSalesSubscriptionRequestValidationError{
						field:  "Van",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncSalesSubscriptionRequestValidationError{
					field:  "Van",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ContractLink != nil {
		// no validation rules for ContractLink
	}

	if m.ContractSigned != nil {
		// no validation rules for ContractSigned
	}

	if len(errors) > 0 {
		return SyncSalesSubscriptionRequestMultiError(errors)
	}

	return nil
}

// SyncSalesSubscriptionRequestMultiError is an error wrapping multiple
// validation errors returned by SyncSalesSubscriptionRequest.ValidateAll() if
// the designated constraints aren't met.
type SyncSalesSubscriptionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncSalesSubscriptionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncSalesSubscriptionRequestMultiError) AllErrors() []error { return m }

// SyncSalesSubscriptionRequestValidationError is the validation error returned
// by SyncSalesSubscriptionRequest.Validate if the designated constraints
// aren't met.
type SyncSalesSubscriptionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncSalesSubscriptionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncSalesSubscriptionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncSalesSubscriptionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncSalesSubscriptionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncSalesSubscriptionRequestValidationError) ErrorName() string {
	return "SyncSalesSubscriptionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncSalesSubscriptionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncSalesSubscriptionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncSalesSubscriptionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncSalesSubscriptionRequestValidationError{}

// Validate checks the field values on SyncSalesSubscriptionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncSalesSubscriptionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncSalesSubscriptionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SyncSalesSubscriptionResponseMultiError, or nil if none found.
func (m *SyncSalesSubscriptionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncSalesSubscriptionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SyncSalesSubscriptionResponseMultiError(errors)
	}

	return nil
}

// SyncSalesSubscriptionResponseMultiError is an error wrapping multiple
// validation errors returned by SyncSalesSubscriptionResponse.ValidateAll()
// if the designated constraints aren't met.
type SyncSalesSubscriptionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncSalesSubscriptionResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncSalesSubscriptionResponseMultiError) AllErrors() []error { return m }

// SyncSalesSubscriptionResponseValidationError is the validation error
// returned by SyncSalesSubscriptionResponse.Validate if the designated
// constraints aren't met.
type SyncSalesSubscriptionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncSalesSubscriptionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncSalesSubscriptionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncSalesSubscriptionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncSalesSubscriptionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncSalesSubscriptionResponseValidationError) ErrorName() string {
	return "SyncSalesSubscriptionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncSalesSubscriptionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncSalesSubscriptionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncSalesSubscriptionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncSalesSubscriptionResponseValidationError{}

// Validate checks the field values on SyncSalesHardwareRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncSalesHardwareRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncSalesHardwareRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncSalesHardwareRequestMultiError, or nil if none found.
func (m *SyncSalesHardwareRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncSalesHardwareRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetSalesCode()) < 1 {
		err := SyncSalesHardwareRequestValidationError{
			field:  "SalesCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOpportunityId()) < 1 {
		err := SyncSalesHardwareRequestValidationError{
			field:  "OpportunityId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.ReaderM2 != nil {

		if all {
			switch v := interface{}(m.GetReaderM2()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncSalesHardwareRequestValidationError{
						field:  "ReaderM2",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncSalesHardwareRequestValidationError{
						field:  "ReaderM2",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReaderM2()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncSalesHardwareRequestValidationError{
					field:  "ReaderM2",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Bbpos != nil {

		if all {
			switch v := interface{}(m.GetBbpos()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncSalesHardwareRequestValidationError{
						field:  "Bbpos",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncSalesHardwareRequestValidationError{
						field:  "Bbpos",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBbpos()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncSalesHardwareRequestValidationError{
					field:  "Bbpos",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SyncSalesHardwareRequestMultiError(errors)
	}

	return nil
}

// SyncSalesHardwareRequestMultiError is an error wrapping multiple validation
// errors returned by SyncSalesHardwareRequest.ValidateAll() if the designated
// constraints aren't met.
type SyncSalesHardwareRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncSalesHardwareRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncSalesHardwareRequestMultiError) AllErrors() []error { return m }

// SyncSalesHardwareRequestValidationError is the validation error returned by
// SyncSalesHardwareRequest.Validate if the designated constraints aren't met.
type SyncSalesHardwareRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncSalesHardwareRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncSalesHardwareRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncSalesHardwareRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncSalesHardwareRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncSalesHardwareRequestValidationError) ErrorName() string {
	return "SyncSalesHardwareRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncSalesHardwareRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncSalesHardwareRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncSalesHardwareRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncSalesHardwareRequestValidationError{}

// Validate checks the field values on SyncSalesHardwareResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncSalesHardwareResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncSalesHardwareResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncSalesHardwareResponseMultiError, or nil if none found.
func (m *SyncSalesHardwareResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncSalesHardwareResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SyncSalesHardwareResponseMultiError(errors)
	}

	return nil
}

// SyncSalesHardwareResponseMultiError is an error wrapping multiple validation
// errors returned by SyncSalesHardwareResponse.ValidateAll() if the
// designated constraints aren't met.
type SyncSalesHardwareResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncSalesHardwareResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncSalesHardwareResponseMultiError) AllErrors() []error { return m }

// SyncSalesHardwareResponseValidationError is the validation error returned by
// SyncSalesHardwareResponse.Validate if the designated constraints aren't met.
type SyncSalesHardwareResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncSalesHardwareResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncSalesHardwareResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncSalesHardwareResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncSalesHardwareResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncSalesHardwareResponseValidationError) ErrorName() string {
	return "SyncSalesHardwareResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncSalesHardwareResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncSalesHardwareResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncSalesHardwareResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncSalesHardwareResponseValidationError{}

// Validate checks the field values on LineItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LineItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LineItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LineItemMultiError, or nil
// if none found.
func (m *LineItem) ValidateAll() error {
	return m.validate(true)
}

func (m *LineItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetQuantity() <= 0 {
		err := LineItemValidationError{
			field:  "Quantity",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUnitPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemValidationError{
					field:  "UnitPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemValidationError{
					field:  "UnitPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnitPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemValidationError{
				field:  "UnitPrice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.DiscountPercentage != nil {

		if all {
			switch v := interface{}(m.GetDiscountPercentage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LineItemValidationError{
						field:  "DiscountPercentage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LineItemValidationError{
						field:  "DiscountPercentage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDiscountPercentage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LineItemValidationError{
					field:  "DiscountPercentage",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LineItemMultiError(errors)
	}

	return nil
}

// LineItemMultiError is an error wrapping multiple validation errors returned
// by LineItem.ValidateAll() if the designated constraints aren't met.
type LineItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LineItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LineItemMultiError) AllErrors() []error { return m }

// LineItemValidationError is the validation error returned by
// LineItem.Validate if the designated constraints aren't met.
type LineItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LineItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LineItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LineItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LineItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LineItemValidationError) ErrorName() string { return "LineItemValidationError" }

// Error satisfies the builtin error interface
func (e LineItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLineItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LineItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LineItemValidationError{}
