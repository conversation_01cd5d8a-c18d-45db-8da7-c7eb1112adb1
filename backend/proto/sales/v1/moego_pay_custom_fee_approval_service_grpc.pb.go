// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/sales/v1/moego_pay_custom_fee_approval_service.proto

package salespb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MoegoPayCustomFeeApprovalService_CreateMoegoPayCustomFeeApproval_FullMethodName  = "/backend.proto.sales.v1.MoegoPayCustomFeeApprovalService/CreateMoegoPayCustomFeeApproval"
	MoegoPayCustomFeeApprovalService_GetMoegoPayCustomFeeApproval_FullMethodName     = "/backend.proto.sales.v1.MoegoPayCustomFeeApprovalService/GetMoegoPayCustomFeeApproval"
	MoegoPayCustomFeeApprovalService_ListMoegoPayCustomFeeApprovals_FullMethodName   = "/backend.proto.sales.v1.MoegoPayCustomFeeApprovalService/ListMoegoPayCustomFeeApprovals"
	MoegoPayCustomFeeApprovalService_CountMoegoPayCustomFeeApprovals_FullMethodName  = "/backend.proto.sales.v1.MoegoPayCustomFeeApprovalService/CountMoegoPayCustomFeeApprovals"
	MoegoPayCustomFeeApprovalService_ApproveMoegoPayCustomFeeApproval_FullMethodName = "/backend.proto.sales.v1.MoegoPayCustomFeeApprovalService/ApproveMoegoPayCustomFeeApproval"
	MoegoPayCustomFeeApprovalService_RejectMoegoPayCustomFeeApproval_FullMethodName  = "/backend.proto.sales.v1.MoegoPayCustomFeeApprovalService/RejectMoegoPayCustomFeeApproval"
)

// MoegoPayCustomFeeApprovalServiceClient is the client API for MoegoPayCustomFeeApprovalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// MoegoPayCustomFeeApprovalService
type MoegoPayCustomFeeApprovalServiceClient interface {
	// CreateMoegoPayCustomFeeApproval
	CreateMoegoPayCustomFeeApproval(ctx context.Context, in *CreateMoegoPayCustomFeeApprovalRequest, opts ...grpc.CallOption) (*MoegoPayCustomFeeApproval, error)
	// GetMoegoPayCustomFeeApproval
	GetMoegoPayCustomFeeApproval(ctx context.Context, in *GetMoegoPayCustomFeeApprovalRequest, opts ...grpc.CallOption) (*MoegoPayCustomFeeApproval, error)
	// ListMoegoPayCustomFeeApprovals
	ListMoegoPayCustomFeeApprovals(ctx context.Context, in *ListMoegoPayCustomFeeApprovalsRequest, opts ...grpc.CallOption) (*ListMoegoPayCustomFeeApprovalsResponse, error)
	// CountMoegoPayCustomFeeApprovals
	CountMoegoPayCustomFeeApprovals(ctx context.Context, in *CountMoegoPayCustomFeeApprovalsRequest, opts ...grpc.CallOption) (*CountMoegoPayCustomFeeApprovalsResponse, error)
	// ApproveMoegoPayCustomFeeApproval
	ApproveMoegoPayCustomFeeApproval(ctx context.Context, in *ApproveMoegoPayCustomFeeApprovalRequest, opts ...grpc.CallOption) (*ApproveMoegoPayCustomFeeApprovalResponse, error)
	// RejectMoegoPayCustomFeeApproval
	RejectMoegoPayCustomFeeApproval(ctx context.Context, in *RejectMoegoPayCustomFeeApprovalRequest, opts ...grpc.CallOption) (*RejectMoegoPayCustomFeeApprovalResponse, error)
}

type moegoPayCustomFeeApprovalServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMoegoPayCustomFeeApprovalServiceClient(cc grpc.ClientConnInterface) MoegoPayCustomFeeApprovalServiceClient {
	return &moegoPayCustomFeeApprovalServiceClient{cc}
}

func (c *moegoPayCustomFeeApprovalServiceClient) CreateMoegoPayCustomFeeApproval(ctx context.Context, in *CreateMoegoPayCustomFeeApprovalRequest, opts ...grpc.CallOption) (*MoegoPayCustomFeeApproval, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MoegoPayCustomFeeApproval)
	err := c.cc.Invoke(ctx, MoegoPayCustomFeeApprovalService_CreateMoegoPayCustomFeeApproval_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayCustomFeeApprovalServiceClient) GetMoegoPayCustomFeeApproval(ctx context.Context, in *GetMoegoPayCustomFeeApprovalRequest, opts ...grpc.CallOption) (*MoegoPayCustomFeeApproval, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MoegoPayCustomFeeApproval)
	err := c.cc.Invoke(ctx, MoegoPayCustomFeeApprovalService_GetMoegoPayCustomFeeApproval_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayCustomFeeApprovalServiceClient) ListMoegoPayCustomFeeApprovals(ctx context.Context, in *ListMoegoPayCustomFeeApprovalsRequest, opts ...grpc.CallOption) (*ListMoegoPayCustomFeeApprovalsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListMoegoPayCustomFeeApprovalsResponse)
	err := c.cc.Invoke(ctx, MoegoPayCustomFeeApprovalService_ListMoegoPayCustomFeeApprovals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayCustomFeeApprovalServiceClient) CountMoegoPayCustomFeeApprovals(ctx context.Context, in *CountMoegoPayCustomFeeApprovalsRequest, opts ...grpc.CallOption) (*CountMoegoPayCustomFeeApprovalsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountMoegoPayCustomFeeApprovalsResponse)
	err := c.cc.Invoke(ctx, MoegoPayCustomFeeApprovalService_CountMoegoPayCustomFeeApprovals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayCustomFeeApprovalServiceClient) ApproveMoegoPayCustomFeeApproval(ctx context.Context, in *ApproveMoegoPayCustomFeeApprovalRequest, opts ...grpc.CallOption) (*ApproveMoegoPayCustomFeeApprovalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApproveMoegoPayCustomFeeApprovalResponse)
	err := c.cc.Invoke(ctx, MoegoPayCustomFeeApprovalService_ApproveMoegoPayCustomFeeApproval_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayCustomFeeApprovalServiceClient) RejectMoegoPayCustomFeeApproval(ctx context.Context, in *RejectMoegoPayCustomFeeApprovalRequest, opts ...grpc.CallOption) (*RejectMoegoPayCustomFeeApprovalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RejectMoegoPayCustomFeeApprovalResponse)
	err := c.cc.Invoke(ctx, MoegoPayCustomFeeApprovalService_RejectMoegoPayCustomFeeApproval_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MoegoPayCustomFeeApprovalServiceServer is the server API for MoegoPayCustomFeeApprovalService service.
// All implementations must embed UnimplementedMoegoPayCustomFeeApprovalServiceServer
// for forward compatibility.
//
// MoegoPayCustomFeeApprovalService
type MoegoPayCustomFeeApprovalServiceServer interface {
	// CreateMoegoPayCustomFeeApproval
	CreateMoegoPayCustomFeeApproval(context.Context, *CreateMoegoPayCustomFeeApprovalRequest) (*MoegoPayCustomFeeApproval, error)
	// GetMoegoPayCustomFeeApproval
	GetMoegoPayCustomFeeApproval(context.Context, *GetMoegoPayCustomFeeApprovalRequest) (*MoegoPayCustomFeeApproval, error)
	// ListMoegoPayCustomFeeApprovals
	ListMoegoPayCustomFeeApprovals(context.Context, *ListMoegoPayCustomFeeApprovalsRequest) (*ListMoegoPayCustomFeeApprovalsResponse, error)
	// CountMoegoPayCustomFeeApprovals
	CountMoegoPayCustomFeeApprovals(context.Context, *CountMoegoPayCustomFeeApprovalsRequest) (*CountMoegoPayCustomFeeApprovalsResponse, error)
	// ApproveMoegoPayCustomFeeApproval
	ApproveMoegoPayCustomFeeApproval(context.Context, *ApproveMoegoPayCustomFeeApprovalRequest) (*ApproveMoegoPayCustomFeeApprovalResponse, error)
	// RejectMoegoPayCustomFeeApproval
	RejectMoegoPayCustomFeeApproval(context.Context, *RejectMoegoPayCustomFeeApprovalRequest) (*RejectMoegoPayCustomFeeApprovalResponse, error)
	mustEmbedUnimplementedMoegoPayCustomFeeApprovalServiceServer()
}

// UnimplementedMoegoPayCustomFeeApprovalServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMoegoPayCustomFeeApprovalServiceServer struct{}

func (UnimplementedMoegoPayCustomFeeApprovalServiceServer) CreateMoegoPayCustomFeeApproval(context.Context, *CreateMoegoPayCustomFeeApprovalRequest) (*MoegoPayCustomFeeApproval, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMoegoPayCustomFeeApproval not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalServiceServer) GetMoegoPayCustomFeeApproval(context.Context, *GetMoegoPayCustomFeeApprovalRequest) (*MoegoPayCustomFeeApproval, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMoegoPayCustomFeeApproval not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalServiceServer) ListMoegoPayCustomFeeApprovals(context.Context, *ListMoegoPayCustomFeeApprovalsRequest) (*ListMoegoPayCustomFeeApprovalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMoegoPayCustomFeeApprovals not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalServiceServer) CountMoegoPayCustomFeeApprovals(context.Context, *CountMoegoPayCustomFeeApprovalsRequest) (*CountMoegoPayCustomFeeApprovalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountMoegoPayCustomFeeApprovals not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalServiceServer) ApproveMoegoPayCustomFeeApproval(context.Context, *ApproveMoegoPayCustomFeeApprovalRequest) (*ApproveMoegoPayCustomFeeApprovalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApproveMoegoPayCustomFeeApproval not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalServiceServer) RejectMoegoPayCustomFeeApproval(context.Context, *RejectMoegoPayCustomFeeApprovalRequest) (*RejectMoegoPayCustomFeeApprovalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectMoegoPayCustomFeeApproval not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalServiceServer) mustEmbedUnimplementedMoegoPayCustomFeeApprovalServiceServer() {
}
func (UnimplementedMoegoPayCustomFeeApprovalServiceServer) testEmbeddedByValue() {}

// UnsafeMoegoPayCustomFeeApprovalServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MoegoPayCustomFeeApprovalServiceServer will
// result in compilation errors.
type UnsafeMoegoPayCustomFeeApprovalServiceServer interface {
	mustEmbedUnimplementedMoegoPayCustomFeeApprovalServiceServer()
}

func RegisterMoegoPayCustomFeeApprovalServiceServer(s grpc.ServiceRegistrar, srv MoegoPayCustomFeeApprovalServiceServer) {
	// If the following call pancis, it indicates UnimplementedMoegoPayCustomFeeApprovalServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MoegoPayCustomFeeApprovalService_ServiceDesc, srv)
}

func _MoegoPayCustomFeeApprovalService_CreateMoegoPayCustomFeeApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMoegoPayCustomFeeApprovalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).CreateMoegoPayCustomFeeApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayCustomFeeApprovalService_CreateMoegoPayCustomFeeApproval_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).CreateMoegoPayCustomFeeApproval(ctx, req.(*CreateMoegoPayCustomFeeApprovalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayCustomFeeApprovalService_GetMoegoPayCustomFeeApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMoegoPayCustomFeeApprovalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).GetMoegoPayCustomFeeApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayCustomFeeApprovalService_GetMoegoPayCustomFeeApproval_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).GetMoegoPayCustomFeeApproval(ctx, req.(*GetMoegoPayCustomFeeApprovalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayCustomFeeApprovalService_ListMoegoPayCustomFeeApprovals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMoegoPayCustomFeeApprovalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).ListMoegoPayCustomFeeApprovals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayCustomFeeApprovalService_ListMoegoPayCustomFeeApprovals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).ListMoegoPayCustomFeeApprovals(ctx, req.(*ListMoegoPayCustomFeeApprovalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayCustomFeeApprovalService_CountMoegoPayCustomFeeApprovals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountMoegoPayCustomFeeApprovalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).CountMoegoPayCustomFeeApprovals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayCustomFeeApprovalService_CountMoegoPayCustomFeeApprovals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).CountMoegoPayCustomFeeApprovals(ctx, req.(*CountMoegoPayCustomFeeApprovalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayCustomFeeApprovalService_ApproveMoegoPayCustomFeeApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApproveMoegoPayCustomFeeApprovalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).ApproveMoegoPayCustomFeeApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayCustomFeeApprovalService_ApproveMoegoPayCustomFeeApproval_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).ApproveMoegoPayCustomFeeApproval(ctx, req.(*ApproveMoegoPayCustomFeeApprovalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayCustomFeeApprovalService_RejectMoegoPayCustomFeeApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectMoegoPayCustomFeeApprovalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).RejectMoegoPayCustomFeeApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayCustomFeeApprovalService_RejectMoegoPayCustomFeeApproval_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalServiceServer).RejectMoegoPayCustomFeeApproval(ctx, req.(*RejectMoegoPayCustomFeeApprovalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MoegoPayCustomFeeApprovalService_ServiceDesc is the grpc.ServiceDesc for MoegoPayCustomFeeApprovalService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MoegoPayCustomFeeApprovalService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.sales.v1.MoegoPayCustomFeeApprovalService",
	HandlerType: (*MoegoPayCustomFeeApprovalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMoegoPayCustomFeeApproval",
			Handler:    _MoegoPayCustomFeeApprovalService_CreateMoegoPayCustomFeeApproval_Handler,
		},
		{
			MethodName: "GetMoegoPayCustomFeeApproval",
			Handler:    _MoegoPayCustomFeeApprovalService_GetMoegoPayCustomFeeApproval_Handler,
		},
		{
			MethodName: "ListMoegoPayCustomFeeApprovals",
			Handler:    _MoegoPayCustomFeeApprovalService_ListMoegoPayCustomFeeApprovals_Handler,
		},
		{
			MethodName: "CountMoegoPayCustomFeeApprovals",
			Handler:    _MoegoPayCustomFeeApprovalService_CountMoegoPayCustomFeeApprovals_Handler,
		},
		{
			MethodName: "ApproveMoegoPayCustomFeeApproval",
			Handler:    _MoegoPayCustomFeeApprovalService_ApproveMoegoPayCustomFeeApproval_Handler,
		},
		{
			MethodName: "RejectMoegoPayCustomFeeApproval",
			Handler:    _MoegoPayCustomFeeApprovalService_RejectMoegoPayCustomFeeApproval_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/sales/v1/moego_pay_custom_fee_approval_service.proto",
}
