syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

// Subscription plan
enum SubscriptionPlan {
  // Unspecified
  SUBSCRIPTION_PLAN_UNSPECIFIED = 0;
  // Growth - Grooming
  GROWTH_GROOMING = 1;
  // Ultimate - Grooming
  ULTIMATE_GROOMING = 2;
  // Growth - Boarding & Daycare
  GROWTH_BOARDING_DAYCARE = 3;
  // Ultimate - Boarding & Daycare
  ULTIMATE_BOARDING_DAYCARE = 4;
  // Enterprise - Boarding & Daycare
  ENTERPRISE_BOARDING_DAYCARE = 5;
}