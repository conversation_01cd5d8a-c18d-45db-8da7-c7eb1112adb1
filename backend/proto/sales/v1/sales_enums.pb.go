// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/sales/v1/sales_enums.proto

package salespb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Subscription plan
type SubscriptionPlan int32

const (
	// Unspecified
	SubscriptionPlan_SUBSCRIPTION_PLAN_UNSPECIFIED SubscriptionPlan = 0
	// Growth - Grooming
	SubscriptionPlan_GROWTH_GROOMING SubscriptionPlan = 1
	// Ultimate - Grooming
	SubscriptionPlan_ULTIMATE_GROOMING SubscriptionPlan = 2
	// Growth - Boarding & Daycare
	SubscriptionPlan_GROWTH_BOARDING_DAYCARE SubscriptionPlan = 3
	// Ultimate - Boarding & Daycare
	SubscriptionPlan_ULTIMATE_BOARDING_DAYCARE SubscriptionPlan = 4
	// Enterprise - Boarding & Daycare
	SubscriptionPlan_ENTERPRISE_BOARDING_DAYCARE SubscriptionPlan = 5
)

// Enum value maps for SubscriptionPlan.
var (
	SubscriptionPlan_name = map[int32]string{
		0: "SUBSCRIPTION_PLAN_UNSPECIFIED",
		1: "GROWTH_GROOMING",
		2: "ULTIMATE_GROOMING",
		3: "GROWTH_BOARDING_DAYCARE",
		4: "ULTIMATE_BOARDING_DAYCARE",
		5: "ENTERPRISE_BOARDING_DAYCARE",
	}
	SubscriptionPlan_value = map[string]int32{
		"SUBSCRIPTION_PLAN_UNSPECIFIED": 0,
		"GROWTH_GROOMING":               1,
		"ULTIMATE_GROOMING":             2,
		"GROWTH_BOARDING_DAYCARE":       3,
		"ULTIMATE_BOARDING_DAYCARE":     4,
		"ENTERPRISE_BOARDING_DAYCARE":   5,
	}
)

func (x SubscriptionPlan) Enum() *SubscriptionPlan {
	p := new(SubscriptionPlan)
	*p = x
	return p
}

func (x SubscriptionPlan) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubscriptionPlan) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_sales_v1_sales_enums_proto_enumTypes[0].Descriptor()
}

func (SubscriptionPlan) Type() protoreflect.EnumType {
	return &file_backend_proto_sales_v1_sales_enums_proto_enumTypes[0]
}

func (x SubscriptionPlan) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubscriptionPlan.Descriptor instead.
func (SubscriptionPlan) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_enums_proto_rawDescGZIP(), []int{0}
}

var File_backend_proto_sales_v1_sales_enums_proto protoreflect.FileDescriptor

const file_backend_proto_sales_v1_sales_enums_proto_rawDesc = "" +
	"\n" +
	"(backend/proto/sales/v1/sales_enums.proto\x12\x16backend.proto.sales.v1*\xbe\x01\n" +
	"\x10SubscriptionPlan\x12!\n" +
	"\x1dSUBSCRIPTION_PLAN_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fGROWTH_GROOMING\x10\x01\x12\x15\n" +
	"\x11ULTIMATE_GROOMING\x10\x02\x12\x1b\n" +
	"\x17GROWTH_BOARDING_DAYCARE\x10\x03\x12\x1d\n" +
	"\x19ULTIMATE_BOARDING_DAYCARE\x10\x04\x12\x1f\n" +
	"\x1bENTERPRISE_BOARDING_DAYCARE\x10\x05Bb\n" +
	" com.moego.backend.proto.sales.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespbb\x06proto3"

var (
	file_backend_proto_sales_v1_sales_enums_proto_rawDescOnce sync.Once
	file_backend_proto_sales_v1_sales_enums_proto_rawDescData []byte
)

func file_backend_proto_sales_v1_sales_enums_proto_rawDescGZIP() []byte {
	file_backend_proto_sales_v1_sales_enums_proto_rawDescOnce.Do(func() {
		file_backend_proto_sales_v1_sales_enums_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_sales_enums_proto_rawDesc), len(file_backend_proto_sales_v1_sales_enums_proto_rawDesc)))
	})
	return file_backend_proto_sales_v1_sales_enums_proto_rawDescData
}

var file_backend_proto_sales_v1_sales_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_sales_v1_sales_enums_proto_goTypes = []any{
	(SubscriptionPlan)(0), // 0: backend.proto.sales.v1.SubscriptionPlan
}
var file_backend_proto_sales_v1_sales_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_sales_v1_sales_enums_proto_init() }
func file_backend_proto_sales_v1_sales_enums_proto_init() {
	if File_backend_proto_sales_v1_sales_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_sales_enums_proto_rawDesc), len(file_backend_proto_sales_v1_sales_enums_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_sales_v1_sales_enums_proto_goTypes,
		DependencyIndexes: file_backend_proto_sales_v1_sales_enums_proto_depIdxs,
		EnumInfos:         file_backend_proto_sales_v1_sales_enums_proto_enumTypes,
	}.Build()
	File_backend_proto_sales_v1_sales_enums_proto = out.File
	file_backend_proto_sales_v1_sales_enums_proto_goTypes = nil
	file_backend_proto_sales_v1_sales_enums_proto_depIdxs = nil
}
