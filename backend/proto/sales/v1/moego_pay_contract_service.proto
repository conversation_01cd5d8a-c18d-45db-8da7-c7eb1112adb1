syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// MoegoPayContractService
service MoegoPayContractService {
  // CreateMoegoPayContract
  rpc CreateMoegoPayContract(CreateMoegoPayContractRequest) returns (MoegoPayContract);
  // GetMoegoPayContract
  rpc GetMoegoPayContract(GetMoegoPayContractRequest) returns (MoegoPayContract);
  // ListMoegoPayContracts
  rpc ListMoegoPayContracts(ListMoegoPayContractsRequest) returns (ListMoegoPayContractsResponse);
  // CountMoegoPayContracts
  rpc CountMoegoPayContracts(CountMoegoPayContractsRequest) returns (CountMoegoPayContractsResponse);
  // SignMoegoPayContract
  rpc SignMoegoPayContract(SignMoegoPayContractRequest) returns (SignMoegoPayContractResponse);
  // DeleteMoegoPayContract
  rpc DeleteMoegoPayContract(DeleteMoegoPayContractRequest) returns (google.protobuf.Empty);
}

// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: We need to do this because 没必要嵌套一层，不好用. --)
// CreateMoegoPayContractRequest
message CreateMoegoPayContractRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
  string terminal_percentage = 2 [(validate.rules).string = {pattern: "^[0-9]+(\\.[0-9]{1,2})?$"}];
  // 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
  string terminal_fixed = 3 [(validate.rules).string = {pattern: "^[0-9]+(\\.[0-9]{1,2})?$"}];
  // 非终端收款手续费百分比，数字格式
  string non_terminal_percentage = 4 [(validate.rules).string = {pattern: "^[0-9]+(\\.[0-9]{1,2})?$"}];
  // 非终端收款固定手续费，单位为 USD，数字格式
  string non_terminal_fixed = 5 [(validate.rules).string = {pattern: "^[0-9]+(\\.[0-9]{1,2})?$"}];
  // 每月最低交易额要求，单位为 USD，数字格式
  string min_volume = 6 [(validate.rules).string = {pattern: "^[0-9]+(\\.[0-9]{1,2})?$"}];
  // creator
  string creator = 7  [(validate.rules).string = {min_len: 1}];
}

// GetMoegoPayContractRequest
message GetMoegoPayContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: We need to do this because we don't use parent. --)
// ListMoegoPayContractsRequest
message ListMoegoPayContractsRequest {
  // 每页返回条数，最大不超过 100，默认建议为 20
  int32 page_size = 1 [(validate.rules).int32 = {gt: 0, lte: 100}];

  // 上一页返回的分页 token（用于获取下一页）
  string page_token = 2;

  // filters
  MoegoPayContractQueryFilters filters = 3;
}

// ListMoegoPayContractsResponse
message ListMoegoPayContractsResponse {
  // 合同列表
  repeated MoegoPayContract moego_pay_contracts = 1;

  // 下一页的分页 token，如果为空表示无更多结果
  string next_page_token = 2;
}

// CountMoegoPayContractsRequest
message CountMoegoPayContractsRequest {
  // filters
  MoegoPayContractQueryFilters filters = 1;
}

// CountMoegoPayContractsResponse
message CountMoegoPayContractsResponse {
  // count
  int64 count = 1;
}

// SignMoegoPayContractRequest
message SignMoegoPayContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
  // signature uri (url)
  string signature_uri = 2 [(validate.rules).string = {uri: true}];
}

// SignMoegoPayContractResponse
message SignMoegoPayContractResponse {
  // contract
  MoegoPayContract contract = 1;
}

// DeleteMoegoPayContractRequest
message DeleteMoegoPayContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// MoegoPayContract
message MoegoPayContract {
  // id
  string id = 1;
  // template id
  string template_id = 2;
  // metadata
  Metadata metadata = 3;
  // params
  Parameters parameters = 4;
  // content
  string content = 5;
  // creator
  string creator = 6;
  // create time
  google.protobuf.Timestamp create_time = 7;
  // update time
  google.protobuf.Timestamp update_time = 8;
  // sign time
  optional google.protobuf.Timestamp sign_time = 9;
  // signature uri (url)
  optional string signature_uri = 10;

  // metadata
  message Metadata {
    // company id
    int64 company_id = 1;
    // account id
    int64 account_id = 2;
  }

  // parameters for rendering
  message Parameters {
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // company name
    string company_name = 1;
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // owner name
    string owner_name = 2;
    // owner email
    string owner_email = 3;
    // 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
    string terminal_percentage = 4;
    // 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
    string terminal_fixed = 5;
    // 非终端收款手续费百分比，数字格式
    string non_terminal_percentage = 6;
    // 非终端收款固定手续费，单位为 USD，数字格式
    string non_terminal_fixed = 7;
    // 每月最低交易额要求，单位为 USD，数字格式
    string min_volume = 8;
  }
}

// Query filters for MoegoPayContract
message MoegoPayContractQueryFilters {
  // company_id
  optional int64 company_id = 1;
  // account_id
  optional int64 account_id = 2;
  // owner email (prefix like)
  optional string owner_email = 3;
  // creator (prefix like)
  optional string creator = 4;
  // if the contract is signed
  optional bool signed = 5;
}
