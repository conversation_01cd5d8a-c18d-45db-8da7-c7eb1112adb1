load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "open_platformpb_proto",
    srcs = [
        "open_platform.proto",
        "open_platform_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = ["@com_google_protobuf//:empty_proto"],
)

go_proto_library(
    name = "open_platformpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "//:pgv_plugin_go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1",
    proto = ":open_platformpb_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "open_platform",
    embed = [":open_platformpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1",
    visibility = ["//visibility:public"],
)
