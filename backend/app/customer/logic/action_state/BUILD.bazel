load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "action_state",
    srcs = [
        "action_state.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/action_state",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/action_state",
        "//backend/app/customer/repo/redis",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
