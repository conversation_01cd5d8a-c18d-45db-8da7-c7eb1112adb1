load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "task_v2",
    srcs = [
        "entity.go",
        "task.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/task_v2",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/task",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
