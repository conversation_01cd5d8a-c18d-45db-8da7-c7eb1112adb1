package taskv2

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type CreateTaskDatum struct {
	CustomerID int64
	BusinessID int64
	CompanyID  int64
	StaffID    int64

	Name            string
	AllocateStaffID *int64
	CompleteTime    *timestamppb.Timestamp
	State           customerpb.Task_State
}

type UpdateTaskDatum struct {
	TaskID  int64
	StaffID int64

	Name            *string
	AllocateStaffID *int64
	CompleteTime    *timestamppb.Timestamp
	State           *customerpb.Task_State
}

type ListTasksDatum struct {
	CustomerID int64
}

type DeleteTasksDatum struct {
	TaskID  int64
	StaffID int64
}
