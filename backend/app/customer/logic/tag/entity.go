package tag

// CreateTagDatum 创建参数
type CreateTagDatum struct {
	CompanyID  int64
	BusinessID int64
	Name       string
	Sort       int32
	StaffID    int64
}

// UpdateTagDatum 更新参数
type UpdateTagDatum struct {
	TagID   int64
	Name    *string
	Sort    *int32
	StaffID int64
}

// ListTagsDatum 查询参数
type ListTagsDatum struct {
	CompanyID  *int64
	CustomerID *int64
}

// DeleteTagDatum 删除参数
type DeleteTagDatum struct {
	TagID   int64
	StaffID int64
}
