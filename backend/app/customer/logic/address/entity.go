package address

import (
	"encoding/base64"
	"encoding/json"
	"time"

	"github.com/lib/pq"
	"google.golang.org/genproto/googleapis/type/latlng"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	addressrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Address struct {
	ID                 int64                    `json:"id"`
	CustomerID         int64                    `json:"customer_id"`
	Revision           int64                    `json:"revision"`
	RegionCode         string                   `json:"region_code"`
	LanguageCode       string                   `json:"language_code"`
	Organization       string                   `json:"organization"`
	PostalCode         string                   `json:"postal_code"`
	SortingCode        string                   `json:"sorting_code"`
	AdministrativeArea string                   `json:"administrative_area"`
	Locality           string                   `json:"locality"`
	Sublocality        string                   `json:"sublocality"`
	AddressLines       pq.StringArray           `json:"address_lines"`
	Recipients         pq.StringArray           `json:"recipients"`
	Latitude           float64                  `json:"latitude"`
	Longitude          float64                  `json:"longitude"`
	State              customerpb.Address_State `json:"state"`
	CustomFields       datatypes.JSON           `json:"custom_fields"`
	DeletedTime        *time.Time               `json:"deleted_time"`
	CreatedTime        time.Time                `json:"created_time"`
	UpdatedTime        time.Time                `json:"updated_time"`
}

func (a *Address) ToDB() *addressrepo.Address {
	return &addressrepo.Address{
		ID:                 a.ID,
		CustomerID:         a.CustomerID,
		Revision:           a.Revision,
		RegionCode:         a.RegionCode,
		LanguageCode:       a.LanguageCode,
		Organization:       a.Organization,
		PostalCode:         a.PostalCode,
		SortingCode:        a.SortingCode,
		AdministrativeArea: a.AdministrativeArea,
		Locality:           a.Locality,
		Sublocality:        a.Sublocality,
		AddressLines:       a.AddressLines,
		Recipients:         a.Recipients,
		Latitude:           a.Latitude,
		Longitude:          a.Longitude,
		State:              a.State,
		DeletedTime:        a.DeletedTime,
		CreatedTime:        a.CreatedTime,
		UpdatedTime:        a.UpdatedTime,
	}
}

func (a *Address) ToPB() *customerpb.Address {
	if a == nil {
		return nil
	}

	postalAddress := &postaladdress.PostalAddress{
		Revision:           int32(a.Revision),
		Organization:       a.Organization,
		SortingCode:        a.SortingCode,
		RegionCode:         a.RegionCode,
		LanguageCode:       a.LanguageCode,
		PostalCode:         a.PostalCode,
		AdministrativeArea: a.AdministrativeArea,
		Locality:           a.Locality,
		Sublocality:        a.Sublocality,
		AddressLines:       []string(a.AddressLines),
		Recipients:         []string(a.Recipients),
	}
	latlng := &latlng.LatLng{
		Latitude:  a.Latitude,
		Longitude: a.Longitude,
	}

	address := &customerpb.Address{
		Id:         a.ID,
		CustomerId: a.CustomerID,
		Address:    postalAddress,
		Latlng:     latlng,
		State:      a.State,
		CreateTime: &timestamppb.Timestamp{
			Seconds: a.CreatedTime.Unix(),
			Nanos:   int32(a.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: a.UpdatedTime.Unix(),
			Nanos:   int32(a.UpdatedTime.Nanosecond()),
		},
	}
	if a.DeletedTime != nil {
		address.DeleteTime = &timestamppb.Timestamp{
			Seconds: a.DeletedTime.Unix(),
			Nanos:   int32(a.DeletedTime.Nanosecond()),
		}
	}
	return address
}

// ************ ListAddresses ************
type ListAddressesRequest struct {
	Filter     *ListAddressesFilter     `json:"filter"`
	Pagination *ListAddressesPagination `json:"pagination"`
	OrderBy    *ListAddressesOrderBy    `json:"order_by"`
}

type ListAddressesPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

func (p *ListAddressesPagination) DecodeCursor() *postgres.Cursor {
	if p.Cursor == "" {
		return nil
	}
	bytes, err := base64.StdEncoding.DecodeString(p.Cursor)
	if err != nil {
		return nil
	}
	cursor := &postgres.Cursor{}
	_ = json.Unmarshal(bytes, cursor)
	return cursor
}

type ListAddressesOrderBy struct {
	Field     customerpb.ListAddressesRequest_Sorting_Field     `json:"field"`
	Direction customerpb.ListAddressesRequest_Sorting_Direction `json:"direction"`
}

type ListAddressesFilter struct {
	IDs        []int64                    `json:"ids"`
	CustomerID int64                      `json:"customer_id"`
	States     []customerpb.Address_State `json:"states"`
}

type ListAddressesResponse struct {
	Addresses []*Address `json:"addresses"`
	HasNext   bool       `json:"has_next"`
	NextToken string     `json:"next_token"`
	TotalSize *int64     `json:"total_size"`
}

// ************ CreateAddress ************
type CreateAddressRequest struct {
	CustomerID         int64          `json:"customer_id"`
	Revision           int64          `json:"revision"`
	RegionCode         string         `json:"region_code"`
	LanguageCode       string         `json:"language_code"`
	Organization       string         `json:"organization"`
	PostalCode         string         `json:"postal_code"`
	SortingCode        string         `json:"sorting_code"`
	AdministrativeArea string         `json:"administrative_area"`
	Locality           string         `json:"locality"`
	Sublocality        string         `json:"sublocality"`
	AddressLines       pq.StringArray `json:"address_lines"`
	Recipients         pq.StringArray `json:"recipients"`
	Latitude           float64        `json:"latitude"`
	Longitude          float64        `json:"longitude"`
	CustomFields       datatypes.JSON `json:"custom_fields"`
}

// ************ UpdateAddress ************
type UpdateAddressRequest struct {
	Revision           int64          `json:"revision"`
	RegionCode         string         `json:"region_code"`
	LanguageCode       string         `json:"language_code"`
	Organization       string         `json:"organization"`
	PostalCode         string         `json:"postal_code"`
	SortingCode        string         `json:"sorting_code"`
	AdministrativeArea string         `json:"administrative_area"`
	Locality           string         `json:"locality"`
	Sublocality        string         `json:"sublocality"`
	AddressLines       pq.StringArray `json:"address_lines"`
	Recipients         pq.StringArray `json:"recipients"`
	Latitude           float64        `json:"latitude"`
	Longitude          float64        `json:"longitude"`
}
