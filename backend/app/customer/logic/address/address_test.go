package address_test

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	addressrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	addressmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address/mock"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func TestNew(t *testing.T) {
	t.Run("测试创建新的Logic实例", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		addressRepo := addressmock.NewMockRepository(ctrl)

		logic := address.NewByParams(addressRepo)
		require.NotNil(t, logic)
	})

	t.Run("New方法", func(t *testing.T) {
		postgres.SetDB(&gorm.DB{})
		logic := address.New()
		require.NotNil(t, logic)
	})
}

func TestCreateAddress(t *testing.T) {
	t.Run("测试创建地址成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		// 构造请求和期望返回
		req := &address.CreateAddressRequest{
			CustomerID:   1,
			RegionCode:   "CN",
			AddressLines: []string{"北京市朝阳区"},
		}
		dbAddr := &addressrepo.Address{
			CustomerID:   1,
			RegionCode:   "CN",
			AddressLines: []string{"北京市朝阳区"},
			State:        customerpb.Address_ACTIVE,
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		}
		repo.EXPECT().Create(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *addressrepo.Address) (*addressrepo.Address, error) {
				require.Equal(t, 1, int(arg.CustomerID))
				require.Equal(t, "CN", arg.RegionCode)
				require.Equal(t, []string{"北京市朝阳区"}, []string(arg.AddressLines))
				return dbAddr, nil
			},
		)

		addr, err := logic.CreateAddress(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, addr)
		require.Equal(t, req.CustomerID, addr.CustomerID)
		require.Equal(t, req.RegionCode, addr.RegionCode)
	})

	t.Run("测试创建地址失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		req := &address.CreateAddressRequest{CustomerID: 2}
		repo.EXPECT().Create(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *addressrepo.Address) (*addressrepo.Address, error) {
				require.Equal(t, 2, int(arg.CustomerID))
				return nil, errors.New("db error")
			},
		)

		addr, err := logic.CreateAddress(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, addr)
	})
}

func TestGetAddress(t *testing.T) {
	t.Run("测试获取地址成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		dbAddr := &addressrepo.Address{ID: 1, CustomerID: 1, RegionCode: "CN"}
		repo.EXPECT().Get(context.Background(), int64(1)).Return(dbAddr, nil)

		addr, err := logic.GetAddress(context.Background(), 1)
		require.NoError(t, err)
		require.NotNil(t, addr)
		require.Equal(t, int64(1), addr.ID)
	})

	t.Run("测试获取地址-未找到", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		repo.EXPECT().Get(context.Background(), int64(2)).Return(nil, gorm.ErrRecordNotFound)

		addr, err := logic.GetAddress(context.Background(), 2)
		require.Error(t, err)
		require.Nil(t, addr)
	})

	t.Run("测试获取地址-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		repo.EXPECT().Get(context.Background(), int64(3)).Return(nil, errors.New("db error"))

		addr, err := logic.GetAddress(context.Background(), 3)
		require.Error(t, err)
		require.Nil(t, addr)
	})
}

func TestListAddresses(t *testing.T) {
	t.Run("测试获取地址列表成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		req := &address.ListAddressesRequest{
			Filter:     &address.ListAddressesFilter{CustomerID: 1},
			Pagination: &address.ListAddressesPagination{PageSize: 10},
			OrderBy:    &address.ListAddressesOrderBy{},
		}
		addrs := []*addressrepo.Address{{ID: 1, CustomerID: 1}, {ID: 2, CustomerID: 1}}
		cursorResult := &addressrepo.CursorResult{Data: addrs, HasNext: true, TotalCount: func() *int64 { i := int64(2); return &i }()}
		expectedFilter := &addressrepo.ListFilter{IDs: nil, CustomerID: 1, States: nil}
		expectedPagination := &addressrepo.Pagination{PageSize: 10, Cursor: nil, ReturnTotalSize: false}
		expectedOrderBy := &addressrepo.OrderBy{Field: req.OrderBy.Field, Direction: req.OrderBy.Direction}
		repo.EXPECT().ListByCursor(context.Background(), gomock.Eq(expectedFilter), gomock.Eq(expectedPagination), gomock.Eq(expectedOrderBy)).Return(cursorResult, nil)

		resp, err := logic.ListAddresses(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Addresses, 2)
		require.True(t, resp.HasNext)
		require.Equal(t, int64(2), *resp.TotalSize)
	})

	t.Run("测试获取地址列表-Filter为空", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		req := &address.ListAddressesRequest{Pagination: &address.ListAddressesPagination{PageSize: 10}}
		resp, err := logic.ListAddresses(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, resp)
	})

	t.Run("测试获取地址列表-OrderBy为空", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		req := &address.ListAddressesRequest{Filter: &address.ListAddressesFilter{CustomerID: 1}, Pagination: &address.ListAddressesPagination{PageSize: 10}, OrderBy: nil}
		cursorResult := &addressrepo.CursorResult{Data: []*addressrepo.Address{}, HasNext: false, TotalCount: func() *int64 { i := int64(0); return &i }()}
		expectedFilter := &addressrepo.ListFilter{IDs: nil, CustomerID: 1, States: nil}
		expectedPagination := &addressrepo.Pagination{PageSize: 10, Cursor: nil, ReturnTotalSize: false}
		repo.EXPECT().ListByCursor(context.Background(), gomock.Eq(expectedFilter), gomock.Eq(expectedPagination), gomock.Any()).Return(cursorResult, nil)

		resp, err := logic.ListAddresses(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Addresses, 0)
	})

	t.Run("测试获取地址列表-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		req := &address.ListAddressesRequest{Filter: &address.ListAddressesFilter{CustomerID: 1}, Pagination: &address.ListAddressesPagination{PageSize: 10}, OrderBy: &address.ListAddressesOrderBy{}}
		expectedFilter := &addressrepo.ListFilter{IDs: nil, CustomerID: 1, States: nil}
		expectedPagination := &addressrepo.Pagination{PageSize: 10, Cursor: nil, ReturnTotalSize: false}
		expectedOrderBy := &addressrepo.OrderBy{Field: req.OrderBy.Field, Direction: req.OrderBy.Direction}
		repo.EXPECT().ListByCursor(context.Background(), gomock.Eq(expectedFilter), gomock.Eq(expectedPagination), gomock.Eq(expectedOrderBy)).Return(nil, errors.New("db error"))

		resp, err := logic.ListAddresses(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, resp)
	})
}

func TestUpdateAddress(t *testing.T) {
	t.Run("测试更新地址成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		oldAddr := &addressrepo.Address{ID: 1, CustomerID: 1, RegionCode: "CN"}
		newAddr := &addressrepo.Address{ID: 1, CustomerID: 1, RegionCode: "US", UpdatedTime: time.Now()}
		repo.EXPECT().Get(context.Background(), int64(1)).Return(oldAddr, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *addressrepo.Address) (*addressrepo.Address, error) {
				require.Equal(t, 1, int(arg.CustomerID))
				require.Equal(t, "US", arg.RegionCode)
				return newAddr, nil
			},
		)

		updateReq := &address.UpdateAddressRequest{RegionCode: "US"}
		addr, err := logic.UpdateAddress(context.Background(), 1, updateReq)
		require.NoError(t, err)
		require.NotNil(t, addr)
		require.Equal(t, "US", addr.RegionCode)
	})

	t.Run("测试更新地址-获取失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		repo.EXPECT().Get(context.Background(), int64(2)).Return(nil, gorm.ErrRecordNotFound)

		updateReq := &address.UpdateAddressRequest{RegionCode: "US"}
		addr, err := logic.UpdateAddress(context.Background(), 2, updateReq)
		require.Error(t, err)
		require.Nil(t, addr)
	})

	t.Run("测试更新地址-更新失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		oldAddr := &addressrepo.Address{ID: 1, CustomerID: 1, RegionCode: "CN"}
		repo.EXPECT().Get(context.Background(), int64(1)).Return(oldAddr, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *addressrepo.Address) (*addressrepo.Address, error) {
				require.Equal(t, 1, int(arg.CustomerID))
				return nil, errors.New("db error")
			},
		)

		updateReq := &address.UpdateAddressRequest{RegionCode: "US"}
		addr, err := logic.UpdateAddress(context.Background(), 1, updateReq)
		require.Error(t, err)
		require.Nil(t, addr)
	})
}

func TestDeleteAddress(t *testing.T) {
	t.Run("测试逻辑删除地址", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		oldAddr := &addressrepo.Address{ID: 1, CustomerID: 1, State: customerpb.Address_ACTIVE}
		repo.EXPECT().Get(context.Background(), int64(1)).Return(oldAddr, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *addressrepo.Address) (*addressrepo.Address, error) {
				require.Equal(t, 1, int(arg.CustomerID))
				require.Equal(t, customerpb.Address_INACTIVE, arg.State)
				return oldAddr, nil
			},
		)

		err := logic.DeleteAddress(context.Background(), 1, true)
		require.NoError(t, err)
	})

	t.Run("测试物理删除地址", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		oldAddr := &addressrepo.Address{ID: 2, CustomerID: 1, State: customerpb.Address_ACTIVE}
		repo.EXPECT().Get(context.Background(), int64(2)).Return(oldAddr, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *addressrepo.Address) (*addressrepo.Address, error) {
				require.Equal(t, 1, int(arg.CustomerID))
				require.Equal(t, customerpb.Address_DELETED, arg.State)
				return oldAddr, nil
			},
		)

		err := logic.DeleteAddress(context.Background(), 2, false)
		require.NoError(t, err)
	})

	t.Run("测试删除地址-获取失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		repo.EXPECT().Get(context.Background(), int64(3)).Return(nil, gorm.ErrRecordNotFound)

		err := logic.DeleteAddress(context.Background(), 3, true)
		require.Error(t, err)
	})

	t.Run("测试删除地址-更新失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := addressmock.NewMockRepository(ctrl)
		logic := address.NewByParams(repo)

		oldAddr := &addressrepo.Address{ID: 4, CustomerID: 1, State: customerpb.Address_ACTIVE}
		repo.EXPECT().Get(context.Background(), int64(4)).Return(oldAddr, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *addressrepo.Address) (*addressrepo.Address, error) {
				require.Equal(t, 1, int(arg.CustomerID))
				return nil, errors.New("db error")
			},
		)

		err := logic.DeleteAddress(context.Background(), 4, true)
		require.Error(t, err)
	})
}

func TestAddress_ToDB_ToPB(t *testing.T) {
	t.Run("正常对象ToDB和ToPB互转", func(t *testing.T) {
		addr := &address.Address{
			ID:                 1,
			CustomerID:         2,
			Revision:           3,
			RegionCode:         "CN",
			LanguageCode:       "zh",
			Organization:       "公司",
			PostalCode:         "100000",
			SortingCode:        "sort",
			AdministrativeArea: "北京",
			Locality:           "朝阳",
			Sublocality:        "望京",
			AddressLines:       []string{"北京市朝阳区望京"},
			Recipients:         []string{"张三"},
			Latitude:           39.9,
			Longitude:          116.4,
			State:              customerpb.Address_ACTIVE,
			CreatedTime:        time.Now(),
			UpdatedTime:        time.Now(),
		}
		db := addr.ToDB()
		require.Equal(t, addr.ID, db.ID)
		require.Equal(t, addr.RegionCode, db.RegionCode)
		require.Equal(t, addr.AddressLines, db.AddressLines)
		pb := addr.ToPB()
		require.Equal(t, addr.ID, pb.Id)
		require.Equal(t, addr.CustomerID, pb.CustomerId)
		require.Equal(t, addr.RegionCode, pb.Address.RegionCode)
		require.Equal(t, addr.Latitude, pb.Latlng.Latitude)
		require.Equal(t, addr.State, pb.State)
		require.NotNil(t, pb.CreateTime)
		require.NotNil(t, pb.UpdateTime)
	})

	t.Run("ToPB nil对象", func(t *testing.T) {
		var addr *address.Address
		pb := addr.ToPB()
		require.Nil(t, pb)
	})

	t.Run("ToDB/ToPB 边界字段", func(t *testing.T) {
		addr := &address.Address{
			ID:           0,
			AddressLines: nil,
			Recipients:   nil,
			DeletedTime:  nil,
		}
		db := addr.ToDB()
		require.Equal(t, int64(0), db.ID)
		pb := addr.ToPB()
		require.Equal(t, int64(0), pb.Id)
		require.Nil(t, pb.DeleteTime)
	})

	t.Run("ToPB DeletedTime 有值", func(t *testing.T) {
		dt := time.Date(2024, 7, 16, 12, 0, 0, 0, time.UTC)
		addr := &address.Address{ID: 1, DeletedTime: &dt, CreatedTime: dt, UpdatedTime: dt}
		pb := addr.ToPB()
		require.NotNil(t, pb.DeleteTime)
		require.Equal(t, dt.Unix(), pb.DeleteTime.Seconds)
	})
}

func TestListAddressesPagination_DecodeCursor(t *testing.T) {
	t.Run("空Cursor", func(t *testing.T) {
		p := &address.ListAddressesPagination{Cursor: ""}
		cursor := p.DecodeCursor()
		require.Nil(t, cursor)
	})

	t.Run("非法base64", func(t *testing.T) {
		p := &address.ListAddressesPagination{Cursor: "!!!notbase64"}
		cursor := p.DecodeCursor()
		require.Nil(t, cursor)
	})

	t.Run("合法base64但非法json", func(t *testing.T) {
		p := &address.ListAddressesPagination{Cursor: "aW52YWxpZCBqc29u"} // "invalid json" base64
		cursor := p.DecodeCursor()
		require.NotNil(t, cursor) // 解析失败也会返回空结构体
	})

	t.Run("合法base64和合法json", func(t *testing.T) {
		type testCursor struct {
			ID        int64     `json:"id"`
			CreatedAt time.Time `json:"created_at"`
		}
		origin := testCursor{ID: 123, CreatedAt: time.Date(2024, 7, 16, 12, 0, 0, 0, time.UTC)}
		b, _ := json.Marshal(origin)
		encoded := base64.StdEncoding.EncodeToString(b)
		p := &address.ListAddressesPagination{Cursor: encoded}
		cursor := p.DecodeCursor()
		require.NotNil(t, cursor)
	})
}
