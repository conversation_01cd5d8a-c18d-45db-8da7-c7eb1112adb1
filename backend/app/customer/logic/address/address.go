package address

import (
	"context"
	"errors"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	addressrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	addressRepo addressrepo.Repository
}

func New() *Logic {
	return &Logic{
		addressRepo: addressrepo.New(),
	}
}

func NewByParams(
	addressRepo addressrepo.Repository,
) *Logic {
	return &Logic{
		addressRepo: addressRepo,
	}
}

func (l *Logic) CreateAddress(ctx context.Context, req *CreateAddressRequest) (*Address, error) {
	// create address, set output only columns
	now := time.Now().UTC()
	address := &Address{
		CustomerID:         req.CustomerID,
		Revision:           req.Revision,
		RegionCode:         req.RegionCode,
		LanguageCode:       req.LanguageCode,
		Organization:       req.Organization,
		PostalCode:         req.PostalCode,
		SortingCode:        req.SortingCode,
		AdministrativeArea: req.AdministrativeArea,
		Locality:           req.Locality,
		Sublocality:        req.Sublocality,
		AddressLines:       req.AddressLines,
		Recipients:         req.Recipients,
		Latitude:           req.Latitude,
		Longitude:          req.Longitude,
		CustomFields:       req.CustomFields,
		State:              customerpb.Address_ACTIVE,
		CreatedTime:        now,
		UpdatedTime:        now,
	}
	dbAddress := address.ToDB()

	dbAddress, err := l.addressRepo.Create(ctx, dbAddress)
	if err != nil {
		return nil, errs.Newm(codes.Internal, "failed to create address")
	}
	// convert to logic address, and return
	return convertToAddress(dbAddress), nil
}

func (l *Logic) GetAddress(ctx context.Context, id int64) (*Address, error) {
	dbAddress, err := l.addressRepo.Get(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_ADDRESS_NOT_FOUND)
		}
		return nil, err
	}
	return convertToAddress(dbAddress), nil
}

func (l *Logic) ListAddresses(ctx context.Context, req *ListAddressesRequest) (*ListAddressesResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListAddressesOrderBy{
			Field:     customerpb.ListAddressesRequest_Sorting_CREATED_TIME,
			Direction: customerpb.ListAddressesRequest_Sorting_DESC,
		}
	}
	dbAddresses, err := l.addressRepo.ListByCursor(ctx, &addressrepo.ListFilter{
		IDs:        req.Filter.IDs,
		CustomerID: req.Filter.CustomerID,
		States:     req.Filter.States,
	}, &addressrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          req.Pagination.DecodeCursor(),
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &addressrepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}

	result := &ListAddressesResponse{
		Addresses: convertToAddresses(dbAddresses.Data),
		HasNext:   dbAddresses.HasNext,
	}
	if dbAddresses.TotalCount != nil {
		result.TotalSize = dbAddresses.TotalCount
	}
	if dbAddresses.HasNext && len(dbAddresses.Data) > 0 {
		lastAddress := dbAddresses.Data[len(dbAddresses.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastAddress.ID,
			CreatedAt: lastAddress.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}
	return result, nil
}

func (l *Logic) UpdateAddress(ctx context.Context, id int64, updateRef *UpdateAddressRequest) (*Address, error) {
	// check address exists
	address, err := l.GetAddress(ctx, id)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	address.UpdatedTime = now
	address.Revision = updateRef.Revision
	address.RegionCode = updateRef.RegionCode
	address.LanguageCode = updateRef.LanguageCode
	address.Organization = updateRef.Organization
	address.PostalCode = updateRef.PostalCode
	address.SortingCode = updateRef.SortingCode
	address.AdministrativeArea = updateRef.AdministrativeArea
	address.Locality = updateRef.Locality
	address.Sublocality = updateRef.Sublocality
	address.AddressLines = updateRef.AddressLines
	address.Recipients = updateRef.Recipients
	address.Latitude = updateRef.Latitude
	address.Longitude = updateRef.Longitude

	updatedAddress, err := l.addressRepo.Update(ctx, address.ToDB())
	if err != nil {
		return nil, err
	}

	return convertToAddress(updatedAddress), nil
}

func (l *Logic) DeleteAddress(ctx context.Context, id int64, inactivate bool) error {
	dbAddress, err := l.GetAddress(ctx, id)
	if err != nil {
		return err
	}

	now := time.Now().UTC()
	dbAddress.UpdatedTime = now
	dbAddress.State = customerpb.Address_INACTIVE
	if !inactivate {
		deletedTime := now
		dbAddress.DeletedTime = &deletedTime
		dbAddress.State = customerpb.Address_DELETED
	}
	_, err = l.addressRepo.Update(ctx, dbAddress.ToDB())
	if err != nil {
		return err
	}
	return nil
}

func convertToAddresses(dbAddresses []*addressrepo.Address) []*Address {
	addresses := make([]*Address, len(dbAddresses))
	for i, dbAddress := range dbAddresses {
		addresses[i] = convertToAddress(dbAddress)
	}
	return addresses
}

func convertToAddress(dbAddress *addressrepo.Address) *Address {
	return &Address{
		ID:                 dbAddress.ID,
		CustomerID:         dbAddress.CustomerID,
		Revision:           dbAddress.Revision,
		RegionCode:         dbAddress.RegionCode,
		LanguageCode:       dbAddress.LanguageCode,
		Organization:       dbAddress.Organization,
		PostalCode:         dbAddress.PostalCode,
		SortingCode:        dbAddress.SortingCode,
		AdministrativeArea: dbAddress.AdministrativeArea,
		Locality:           dbAddress.Locality,
		Sublocality:        dbAddress.Sublocality,
		AddressLines:       dbAddress.AddressLines,
		Recipients:         dbAddress.Recipients,
		Latitude:           dbAddress.Latitude,
		Longitude:          dbAddress.Longitude,
		State:              dbAddress.State,
		DeletedTime:        dbAddress.DeletedTime,
		CreatedTime:        dbAddress.CreatedTime,
		UpdatedTime:        dbAddress.UpdatedTime,
	}
}
