package contact

import (
	"context"
	"errors"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contactrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	contactRepo contactrepo.Repository
}

func New() *Logic {
	return &Logic{
		contactRepo: contactrepo.New(),
	}
}

func NewByParams(
	contactRepo contactrepo.Repository,
) *Logic {
	return &Logic{
		contactRepo: contactRepo,
	}
}

func (l *Logic) CreateContact(ctx context.Context, contact *Contact) (*Contact, error) {
	// todo: check if the contact already exists, wait tag develop
	now := time.Now().UTC()
	contact.State = customerpb.Contact_ACTIVE
	contact.CreatedTime = now
	contact.UpdatedTime = now
	dbContact := contact.ToDB()
	dbContact, err := l.contactRepo.Create(ctx, dbContact)
	if err != nil {
		return nil, err
	}
	return convertToContact(dbContact), nil
}

func (l *Logic) GetContact(ctx context.Context, id int64) (*Contact, error) {
	dbContact, err := l.contactRepo.Get(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CONTACT_NOT_FOUND)
		}
		return nil, err
	}
	return convertToContact(dbContact), nil
}

func (l *Logic) ListContacts(ctx context.Context, req *ListContactsRequest) (*ListContactsResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListContactsOrderBy{
			Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
			Direction: customerpb.ListContactsRequest_Sorting_DESC,
		}
	}
	dbContacts, err := l.contactRepo.ListByCursor(ctx, &contactrepo.ListFilter{
		IDs:         req.Filter.IDs,
		CustomerIDs: req.Filter.CustomerIDs,
		States:      req.Filter.States,
	}, &contactrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          req.Pagination.DecodeCursor(),
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &contactrepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}

	result := &ListContactsResponse{
		Contacts: convertToContacts(dbContacts.Data),
		HasNext:  dbContacts.HasNext,
	}
	if dbContacts.TotalCount != nil {
		result.TotalSize = dbContacts.TotalCount
	}

	if dbContacts.HasNext && len(dbContacts.Data) > 0 {
		lastContact := dbContacts.Data[len(dbContacts.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastContact.ID,
			CreatedAt: lastContact.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}
	return result, nil
}

func (l *Logic) UpdateContact(ctx context.Context, updateRef *UpdateContactRequest) (*Contact, error) {
	dbContact, err := l.GetContact(ctx, updateRef.ID)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	dbContact.Email = updateRef.Email
	dbContact.Phone = updateRef.Phone
	dbContact.GivenName = updateRef.GivenName
	dbContact.FamilyName = updateRef.FamilyName
	dbContact.UpdatedTime = now
	// update contact
	updatedContact, err := l.contactRepo.Update(ctx, dbContact.ToDB())
	if err != nil {
		return nil, err
	}
	return convertToContact(updatedContact), nil
}

func (l *Logic) DeleteContact(ctx context.Context, id int64, inactivate bool) error {
	dbContact, err := l.GetContact(ctx, id)
	if err != nil {
		return err
	}
	now := time.Now().UTC()
	dbContact.UpdatedTime = now
	dbContact.State = customerpb.Contact_INACTIVE
	if !inactivate {
		deletedTime := now
		dbContact.DeletedTime = &deletedTime
		dbContact.State = customerpb.Contact_DELETED
	}

	_, err = l.contactRepo.Update(ctx, dbContact.ToDB())
	if err != nil {
		return err
	}

	return nil
}

func convertToContacts(dbContacts []*contactrepo.Contact) []*Contact {
	contacts := make([]*Contact, 0, len(dbContacts))
	for _, dbContact := range dbContacts {
		contacts = append(contacts, convertToContact(dbContact))
	}
	return contacts
}

func convertToContact(dbContact *contactrepo.Contact) *Contact {
	return &Contact{
		ID:           dbContact.ID,
		CustomerID:   dbContact.CustomerID,
		GivenName:    dbContact.GivenName,
		FamilyName:   dbContact.FamilyName,
		Email:        dbContact.Email,
		Phone:        dbContact.Phone,
		IsSelf:       dbContact.IsSelf,
		State:        dbContact.State,
		CustomFields: dbContact.CustomFields,
		DeletedTime:  dbContact.DeletedTime,
		CreatedTime:  dbContact.CreatedTime,
		UpdatedTime:  dbContact.UpdatedTime,
	}
}
