package contact

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contactrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	contactmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact/mock"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func TestNew(t *testing.T) {
	t.Run("测试创建新的Logic实例", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)

		logic := NewByParams(contactRepo)
		require.NotNil(t, logic)
	})

	t.Run("New方法", func(t *testing.T) {
		postgres.SetDB(&gorm.DB{})
		logic := New()
		require.NotNil(t, logic)
	})
}

func TestCreateContact(t *testing.T) {
	t.Run("测试创建联系人成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contact := &Contact{
			CustomerID:   123,
			GivenName:    "测试",
			FamilyName:   "联系人",
			Email:        "<EMAIL>",
			Phone:        "+1234567890",
			CustomFields: datatypes.JSON(`{"key": "value"}`),
		}

		// 设置mock期望
		mockContact := &contactrepo.Contact{
			ID:           1,
			CustomerID:   123,
			GivenName:    "测试",
			FamilyName:   "联系人",
			Email:        "<EMAIL>",
			Phone:        "+1234567890",
			State:        customerpb.Contact_ACTIVE,
			CustomFields: datatypes.JSON(`{"key": "value"}`),
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		}
		contactRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(mockContact, nil)

		// 执行测试
		result, err := logic.CreateContact(context.Background(), contact)
		require.NoError(t, err, "创建联系人时不应返回错误")
		require.NotNil(t, result, "返回的联系人结果不应为nil")
		require.Equal(t, int64(1), result.ID, "联系人ID应为1")
		require.Equal(t, customerpb.Contact_ACTIVE, result.State, "联系人状态应为ACTIVE")
		require.Equal(t, "<EMAIL>", result.Email, "邮箱应正确设置")
		require.Equal(t, "+1234567890", result.Phone, "电话应正确设置")
	})

	t.Run("测试创建联系人失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contact := &Contact{
			CustomerID: 123,
			GivenName:  "测试",
			FamilyName: "联系人",
			Email:      "<EMAIL>",
		}

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		contactRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.CreateContact(context.Background(), contact)
		require.Error(t, err, "应返回错误")
		require.Nil(t, result, "返回结果应为nil")
		require.Equal(t, expectedErr, err)
	})
}

func TestGetContact(t *testing.T) {
	t.Run("测试获取联系人成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(1)

		// 设置mock期望
		expectedContact := &contactrepo.Contact{
			ID:           1,
			CustomerID:   123,
			GivenName:    "测试",
			FamilyName:   "联系人",
			Email:        "<EMAIL>",
			Phone:        "+1234567890",
			State:        customerpb.Contact_ACTIVE,
			CustomFields: datatypes.JSON(`{"key": "value"}`),
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		}
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(expectedContact, nil)

		// 执行测试
		result, err := logic.GetContact(context.Background(), contactID)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, "测试", result.GivenName)
		require.Equal(t, "联系人", result.FamilyName)
		require.Equal(t, "<EMAIL>", result.Email)
		require.Equal(t, "+1234567890", result.Phone)
	})

	t.Run("测试获取联系人失败-联系人不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(999)

		// 设置mock期望
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		result, err := logic.GetContact(context.Background(), contactID)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "119801", "error code is not 119801")
	})

	t.Run("测试获取联系人失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(1)

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.GetContact(context.Background(), contactID)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestListContacts(t *testing.T) {
	t.Run("测试获取联系人列表成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		req := &ListContactsRequest{
			Filter: &ListContactsFilter{
				CustomerIDs: []int64{123},
				States:      []customerpb.Contact_State{customerpb.Contact_ACTIVE},
			},
			Pagination: &ListContactsPagination{
				PageSize:        10,
				ReturnTotalSize: true,
			},
			OrderBy: &ListContactsOrderBy{
				Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
				Direction: customerpb.ListContactsRequest_Sorting_DESC,
			},
		}

		// 设置mock期望
		expectedContacts := []*contactrepo.Contact{
			{
				ID:          1,
				CustomerID:  123,
				GivenName:   "测试1",
				FamilyName:  "联系人1",
				Email:       "<EMAIL>",
				State:       customerpb.Contact_ACTIVE,
				CreatedTime: time.Now(),
				UpdatedTime: time.Now(),
			},
			{
				ID:          2,
				CustomerID:  123,
				GivenName:   "测试2",
				FamilyName:  "联系人2",
				Phone:       "+1234567890",
				State:       customerpb.Contact_ACTIVE,
				CreatedTime: time.Now().Add(-time.Hour),
				UpdatedTime: time.Now().Add(-time.Hour),
			},
		}

		contactRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&contactrepo.CursorResult{
			Data:       expectedContacts,
			HasNext:    true,
			TotalCount: func() *int64 { count := int64(2); return &count }(),
		}, nil)

		// 执行测试
		result, err := logic.ListContacts(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Len(t, result.Contacts, 2)
		require.True(t, result.HasNext)
		require.NotEmpty(t, result.NextToken)
		require.Equal(t, int64(2), *result.TotalSize)
	})

	t.Run("测试获取联系人列表-空结果", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		req := &ListContactsRequest{
			Filter: &ListContactsFilter{
				CustomerIDs: []int64{123},
			},
			Pagination: &ListContactsPagination{
				PageSize:        10,
				ReturnTotalSize: true,
			},
			OrderBy: &ListContactsOrderBy{
				Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
				Direction: customerpb.ListContactsRequest_Sorting_DESC,
			},
		}

		// 设置mock期望
		contactRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&contactrepo.CursorResult{
			Data:       []*contactrepo.Contact{},
			HasNext:    false,
			TotalCount: func() *int64 { count := int64(0); return &count }(),
		}, nil)

		// 执行测试
		result, err := logic.ListContacts(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Empty(t, result.Contacts)
		require.False(t, result.HasNext)
		require.Empty(t, result.NextToken)
		require.Equal(t, int64(0), *result.TotalSize)
	})

	t.Run("测试获取联系人列表-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		req := &ListContactsRequest{
			Filter: &ListContactsFilter{
				CustomerIDs: []int64{123},
			},
			Pagination: &ListContactsPagination{
				PageSize: 10,
			},
		}

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		contactRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.ListContacts(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})

	t.Run("测试获取联系人列表-Filter为空", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		req := &ListContactsRequest{
			Pagination: &ListContactsPagination{
				PageSize: 10,
			},
		}

		// 执行测试
		result, err := logic.ListContacts(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "filter is required")
	})
}

func TestUpdateContact(t *testing.T) {
	t.Run("测试更新联系人成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(1)
		updateReq := &UpdateContactRequest{
			ID:         contactID,
			GivenName:  "更新",
			FamilyName: "姓名",
			Email:      "<EMAIL>",
		}

		// 设置mock期望 - GetContact
		existingContact := &Contact{
			ID:           1,
			CustomerID:   123,
			GivenName:    "原",
			FamilyName:   "姓名",
			Email:        "<EMAIL>",
			State:        customerpb.Contact_ACTIVE,
			CustomFields: datatypes.JSON(`{"key": "value"}`),
			CreatedTime:  time.Now().Add(-24 * time.Hour),
			UpdatedTime:  time.Now().Add(-24 * time.Hour),
		}
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(existingContact.ToDB(), nil)

		// 设置mock期望 - Update
		updatedContact := &contactrepo.Contact{
			ID:           1,
			CustomerID:   123,
			GivenName:    "更新",
			FamilyName:   "姓名",
			Email:        "<EMAIL>",
			State:        customerpb.Contact_ACTIVE,
			CustomFields: datatypes.JSON(`{"key": "value"}`),
			CreatedTime:  time.Now().Add(-24 * time.Hour),
			UpdatedTime:  time.Now(),
		}
		contactRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(updatedContact, nil)

		// 执行测试
		result, err := logic.UpdateContact(context.Background(), updateReq)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, "更新", result.GivenName)
		require.Equal(t, "姓名", result.FamilyName)
		require.Equal(t, "<EMAIL>", result.Email)
	})

	t.Run("测试更新联系人失败-联系人不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(999)
		updateReq := &UpdateContactRequest{
			ID:         contactID,
			GivenName:  "更新",
			FamilyName: "姓名",
		}

		// 设置mock期望
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		result, err := logic.UpdateContact(context.Background(), updateReq)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "119801", "error code is not 119801")
	})

	t.Run("测试更新联系人失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(1)
		updateReq := &UpdateContactRequest{
			ID:         contactID,
			GivenName:  "更新",
			FamilyName: "姓名",
		}

		// 设置mock期望 - GetContact
		existingContact := &Contact{
			ID:          1,
			CustomerID:  123,
			GivenName:   "原",
			FamilyName:  "姓名",
			State:       customerpb.Contact_ACTIVE,
			CreatedTime: time.Now().Add(-24 * time.Hour),
			UpdatedTime: time.Now().Add(-24 * time.Hour),
		}
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(existingContact.ToDB(), nil)

		expectedErr := errors.New("数据库错误")
		contactRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.UpdateContact(context.Background(), updateReq)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestDeleteContact(t *testing.T) {
	t.Run("测试删除联系人成功-软删除", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(1)
		inactivate := true

		// 设置mock期望 - GetContact
		existingContact := &Contact{
			ID:          1,
			CustomerID:  123,
			GivenName:   "测试",
			FamilyName:  "联系人",
			State:       customerpb.Contact_ACTIVE,
			CreatedTime: time.Now().Add(-24 * time.Hour),
			UpdatedTime: time.Now().Add(-24 * time.Hour),
		}
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(existingContact.ToDB(), nil)

		// 设置mock期望 - Update
		contactRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(&contactrepo.Contact{}, nil)

		// 执行测试
		err := logic.DeleteContact(context.Background(), contactID, inactivate)
		require.NoError(t, err)
	})

	t.Run("测试删除联系人成功-硬删除", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(1)
		inactivate := false

		// 设置mock期望 - GetContact
		existingContact := &Contact{
			ID:          1,
			CustomerID:  123,
			GivenName:   "测试",
			FamilyName:  "联系人",
			State:       customerpb.Contact_ACTIVE,
			CreatedTime: time.Now().Add(-24 * time.Hour),
			UpdatedTime: time.Now().Add(-24 * time.Hour),
		}
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(existingContact.ToDB(), nil)

		// 设置mock期望 - Update
		contactRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(&contactrepo.Contact{}, nil)

		// 执行测试
		err := logic.DeleteContact(context.Background(), contactID, inactivate)
		require.NoError(t, err)
	})

	t.Run("测试删除联系人失败-联系人不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(999)
		inactivate := true

		// 设置mock期望
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		err := logic.DeleteContact(context.Background(), contactID, inactivate)
		require.Error(t, err)
		require.Contains(t, err.Error(), "119801", "error code is not 119801")
	})

	t.Run("测试删除联系人失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactRepo := contactmock.NewMockRepository(ctrl)
		logic := NewByParams(contactRepo)

		// 准备测试数据
		contactID := int64(1)
		inactivate := true

		// 设置mock期望 - GetContact
		existingContact := &Contact{
			ID:          1,
			CustomerID:  123,
			GivenName:   "测试",
			FamilyName:  "联系人",
			State:       customerpb.Contact_ACTIVE,
			CreatedTime: time.Now().Add(-24 * time.Hour),
			UpdatedTime: time.Now().Add(-24 * time.Hour),
		}
		contactRepo.EXPECT().Get(gomock.Any(), contactID).Return(existingContact.ToDB(), nil)

		// 设置mock期望 - Update
		expectedErr := errors.New("数据库错误")
		contactRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		err := logic.DeleteContact(context.Background(), contactID, inactivate)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})
}

func TestConvertToContact(t *testing.T) {
	t.Run("测试转换数据库联系人到逻辑联系人", func(t *testing.T) {
		// 准备测试数据
		dbContact := &contactrepo.Contact{
			ID:           1,
			CustomerID:   123,
			GivenName:    "测试",
			FamilyName:   "联系人",
			Email:        "<EMAIL>",
			Phone:        "+1234567890",
			State:        customerpb.Contact_ACTIVE,
			CustomFields: datatypes.JSON(`{"key": "value"}`),
			DeletedTime:  nil,
			CreatedTime:  time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			UpdatedTime:  time.Date(2023, 1, 2, 0, 0, 0, 0, time.UTC),
		}

		// 执行测试
		result := convertToContact(dbContact)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, int64(123), result.CustomerID)
		require.Equal(t, "测试", result.GivenName)
		require.Equal(t, "联系人", result.FamilyName)
		require.Equal(t, "<EMAIL>", result.Email)
		require.Equal(t, "+1234567890", result.Phone)
		require.Equal(t, customerpb.Contact_ACTIVE, result.State)
		require.Equal(t, datatypes.JSON(`{"key": "value"}`), result.CustomFields)
		require.Nil(t, result.DeletedTime)
		require.Equal(t, time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC), result.CreatedTime)
		require.Equal(t, time.Date(2023, 1, 2, 0, 0, 0, 0, time.UTC), result.UpdatedTime)
	})

	t.Run("测试转换数据库联系人列表到逻辑联系人列表", func(t *testing.T) {
		// 准备测试数据
		dbContacts := []*contactrepo.Contact{
			{
				ID:          1,
				CustomerID:  123,
				GivenName:   "测试1",
				FamilyName:  "联系人1",
				Email:       "<EMAIL>",
				State:       customerpb.Contact_ACTIVE,
				CreatedTime: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				UpdatedTime: time.Date(2023, 1, 2, 0, 0, 0, 0, time.UTC),
			},
			{
				ID:          2,
				CustomerID:  123,
				GivenName:   "测试2",
				FamilyName:  "联系人2",
				Phone:       "+1234567890",
				State:       customerpb.Contact_ACTIVE,
				CreatedTime: time.Date(2023, 1, 3, 0, 0, 0, 0, time.UTC),
				UpdatedTime: time.Date(2023, 1, 4, 0, 0, 0, 0, time.UTC),
			},
		}

		// 执行测试
		result := convertToContacts(dbContacts)
		require.NotNil(t, result)
		require.Len(t, result, 2)
		require.Equal(t, int64(1), result[0].ID)
		require.Equal(t, "测试1", result[0].GivenName)
		require.Equal(t, int64(2), result[1].ID)
		require.Equal(t, "测试2", result[1].GivenName)
	})
}

func TestContact_ToPB(t *testing.T) {
	t.Run("正常转换-有邮箱", func(t *testing.T) {
		now := time.Now().UTC()
		delTime := now.Add(24 * time.Hour)
		c := &Contact{
			ID:           123,
			CustomerID:   456,
			GivenName:    "张三",
			FamilyName:   "李四",
			Email:        "<EMAIL>",
			Phone:        "",
			State:        customerpb.Contact_ACTIVE,
			CustomFields: datatypes.JSON(`{"foo": "bar"}`),
			DeletedTime:  &delTime,
			CreatedTime:  now,
			UpdatedTime:  now,
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, c.ID, pb.Id)
		require.Equal(t, c.CustomerID, pb.CustomerId)
		require.Equal(t, c.GivenName, pb.GivenName)
		require.Equal(t, c.FamilyName, pb.FamilyName)
		require.Equal(t, c.State, pb.State)
		require.Equal(t, "<EMAIL>", pb.GetEmail())
		require.NotNil(t, pb.DeleteTime)
		require.Equal(t, c.CreatedTime.Unix(), pb.CreateTime.Seconds)
		require.Equal(t, c.UpdatedTime.Unix(), pb.UpdateTime.Seconds)
		require.Equal(t, c.DeletedTime.Unix(), pb.DeleteTime.Seconds)
	})

	t.Run("正常转换-有电话", func(t *testing.T) {
		now := time.Now().UTC()
		c := &Contact{
			ID:           123,
			CustomerID:   456,
			GivenName:    "张三",
			FamilyName:   "李四",
			Email:        "",
			Phone:        "+1234567890",
			State:        customerpb.Contact_ACTIVE,
			CustomFields: datatypes.JSON(`{"foo": "bar"}`),
			DeletedTime:  nil,
			CreatedTime:  now,
			UpdatedTime:  now,
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, c.ID, pb.Id)
		require.Equal(t, c.CustomerID, pb.CustomerId)
		require.Equal(t, c.GivenName, pb.GivenName)
		require.Equal(t, c.FamilyName, pb.FamilyName)
		require.Equal(t, c.State, pb.State)
		require.Equal(t, "+1234567890", pb.GetPhone().GetE164Number())
		require.Nil(t, pb.DeleteTime)
		require.Equal(t, c.CreatedTime.Unix(), pb.CreateTime.Seconds)
		require.Equal(t, c.UpdatedTime.Unix(), pb.UpdateTime.Seconds)
	})

	t.Run("正常转换-无联系信息", func(t *testing.T) {
		now := time.Now().UTC()
		c := &Contact{
			ID:           123,
			CustomerID:   456,
			GivenName:    "张三",
			FamilyName:   "李四",
			Email:        "",
			Phone:        "",
			State:        customerpb.Contact_ACTIVE,
			CustomFields: datatypes.JSON(`{"foo": "bar"}`),
			DeletedTime:  nil,
			CreatedTime:  now,
			UpdatedTime:  now,
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, c.ID, pb.Id)
		require.Equal(t, c.CustomerID, pb.CustomerId)
		require.Equal(t, c.GivenName, pb.GivenName)
		require.Equal(t, c.FamilyName, pb.FamilyName)
		require.Equal(t, c.State, pb.State)
		require.Equal(t, "", pb.GetEmail())
		require.Equal(t, "", pb.GetPhone().GetE164Number())
		require.Nil(t, pb.DeleteTime)
		require.Equal(t, c.CreatedTime.Unix(), pb.CreateTime.Seconds)
		require.Equal(t, c.UpdatedTime.Unix(), pb.UpdateTime.Seconds)
	})

	t.Run("c为nil", func(t *testing.T) {
		var c *Contact
		pb := c.ToPB()
		require.Nil(t, pb)
	})
}

func TestListContactsPagination_DecodeCursor(t *testing.T) {
	t.Run("空Cursor", func(t *testing.T) {
		p := &ListContactsPagination{Cursor: ""}
		cursor := p.DecodeCursor()
		require.Nil(t, cursor)
	})

	t.Run("非法base64", func(t *testing.T) {
		p := &ListContactsPagination{Cursor: "!!!notbase64"}
		cursor := p.DecodeCursor()
		require.Nil(t, cursor)
	})

	t.Run("合法base64但非法json", func(t *testing.T) {
		// base64编码的字符串，但不是合法json
		p := &ListContactsPagination{Cursor: "aW52YWxpZCBqc29u"}
		cursor := p.DecodeCursor()
		require.NotNil(t, cursor) // 解析失败也会返回空结构体
	})

	t.Run("合法base64和合法json", func(t *testing.T) {
		type testCursor struct {
			ID        int64     `json:"id"`
			CreatedAt time.Time `json:"created_at"`
		}
		origin := testCursor{ID: 123, CreatedAt: time.Now()}
		b, _ := json.Marshal(origin)
		encoded := base64.StdEncoding.EncodeToString(b)
		p := &ListContactsPagination{Cursor: encoded}
		cursor := p.DecodeCursor()
		require.NotNil(t, cursor)
	})
}
