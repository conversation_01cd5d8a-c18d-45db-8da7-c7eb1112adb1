load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "life_cycle",
    srcs = [
        "entity.go",
        "life_cycle.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/life_cycle",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/life_cycle",
        "//backend/app/customer/repo/redis",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
