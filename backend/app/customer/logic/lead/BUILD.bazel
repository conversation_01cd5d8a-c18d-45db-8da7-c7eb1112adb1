load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "lead",
    srcs = [
        "entity.go",
        "lead.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/lead",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@com_github_bytedance_sonic//:sonic",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "lead_test",
    srcs = ["lead_test.go"],
    deps = [
        ":lead",
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/app/customer/repo/postgres/customer/mock",
        "//backend/proto/customer/v2:customer",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
