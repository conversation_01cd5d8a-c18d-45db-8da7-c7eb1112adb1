package lead_test

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/lead"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customermock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer/mock"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func TestNew(t *testing.T) {
	t.Run("测试创建新的Logic实例", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := customermock.NewMockRepository(ctrl)

		logic := lead.NewByParams(repo)
		require.NotNil(t, logic)
	})

	t.Run("New方法", func(t *testing.T) {
		postgres.SetDB(&gorm.DB{})
		logic := lead.New()
		require.NotNil(t, logic)
	})
}

func TestCreateLead(t *testing.T) {
	t.Run("测试创建Lead成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		leadReq := &lead.Lead{
			GivenName:  "张三",
			FamilyName: "李四",
		}
		dbLead := &customerrepo.Customer{
			GivenName:    "张三",
			FamilyName:   "李四",
			CustomerType: customerpb.CustomerType_LEAD,
			State:        customerpb.Customer_State(customerpb.Lead_ACTIVE),
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		}
		repo.EXPECT().Create(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *customerrepo.Customer) (*customerrepo.Customer, error) {
				require.Equal(t, "张三", arg.GivenName)
				require.Equal(t, "李四", arg.FamilyName)
				return dbLead, nil
			},
		)

		result, err := logic.CreateLead(context.Background(), leadReq)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, "张三", result.GivenName)
	})

	t.Run("测试创建Lead失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		leadReq := &lead.Lead{GivenName: "张三"}
		repo.EXPECT().Create(context.Background(), gomock.Any()).Return(nil, errors.New("db error"))

		result, err := logic.CreateLead(context.Background(), leadReq)
		require.Error(t, err)
		require.Nil(t, result)
	})
}

func TestGetLead(t *testing.T) {
	t.Run("测试获取Lead成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		dbLead := &customerrepo.Customer{ID: 1, GivenName: "张三", CustomerType: customerpb.CustomerType_LEAD}
		repo.EXPECT().Get(context.Background(), int64(1), customerpb.CustomerType_LEAD).Return(dbLead, nil)

		result, err := logic.GetLead(context.Background(), 1)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
	})

	t.Run("测试获取Lead-未找到", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		repo.EXPECT().Get(context.Background(), int64(2), customerpb.CustomerType_LEAD).Return(nil, gorm.ErrRecordNotFound)

		result, err := logic.GetLead(context.Background(), 2)
		require.Error(t, err)
		require.Nil(t, result)
	})

	t.Run("测试获取Lead-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		repo.EXPECT().Get(context.Background(), int64(3), customerpb.CustomerType_LEAD).Return(nil, errors.New("db error"))

		result, err := logic.GetLead(context.Background(), 3)
		require.Error(t, err)
		require.Nil(t, result)
	})
}

func TestListLeads(t *testing.T) {
	t.Run("测试列表Leads成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		req := &lead.ListLeadsRequest{
			Filter: &lead.ListLeadsFilter{
				IDs:           []int64{1, 2},
				States:        []customerpb.Lead_State{customerpb.Lead_ACTIVE},
				OwnerStaffIDs: []int64{100},
			},
			Pagination: &lead.ListLeadsPagination{PageSize: 10},
			OrderBy:    &lead.ListLeadsOrderBy{},
		}
		dbLeads := []*customerrepo.Customer{{ID: 1, GivenName: "张三"}, {ID: 2, GivenName: "李四"}}
		total := int64(2)
		repo.EXPECT().ListByCursor(context.Background(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(_ context.Context, filter *customerrepo.ListFilter, pagination *customerrepo.Pagination, orderBy *customerrepo.OrderBy) (*customerrepo.CursorResult, error) {
				require.ElementsMatch(t, []int64{1, 2}, filter.IDs)
				require.Equal(t, customerpb.CustomerType_LEAD, filter.CustomerType)
				return &customerrepo.CursorResult{
					Data:       dbLeads,
					HasNext:    false,
					TotalCount: &total,
				}, nil
			},
		)

		resp, err := logic.ListLeads(context.Background(), req)
		require.NoError(t, err)
		require.Len(t, resp.Leads, 2)
		require.Equal(t, "张三", resp.Leads[0].GivenName)
		require.Equal(t, int64(2), *resp.TotalSize)
	})

	t.Run("测试列表Leads-Filter为空", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		req := &lead.ListLeadsRequest{Filter: nil, Pagination: &lead.ListLeadsPagination{PageSize: 10}}
		resp, err := logic.ListLeads(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, resp)
	})
}

func TestListLeads_Branches(t *testing.T) {
	t.Run("OrderBy为nil自动补默认排序", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		req := &lead.ListLeadsRequest{
			Filter:     &lead.ListLeadsFilter{IDs: []int64{1}},
			Pagination: &lead.ListLeadsPagination{PageSize: 1},
			OrderBy:    nil, // 关键点
		}
		dbLeads := []*customerrepo.Customer{{ID: 1, GivenName: "张三"}}
		total := int64(1)
		repo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(_ context.Context, filter *customerrepo.ListFilter, pagination *customerrepo.Pagination, orderBy *customerrepo.OrderBy) (*customerrepo.CursorResult, error) {
				return &customerrepo.CursorResult{
					Data:       dbLeads,
					HasNext:    false,
					TotalCount: &total,
				}, nil
			},
		)
		resp, err := logic.ListLeads(context.Background(), req)
		require.NoError(t, err)
		require.Len(t, resp.Leads, 1)
	})

	t.Run("HasNext为true且有数据，生成NextToken", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		req := &lead.ListLeadsRequest{
			Filter:     &lead.ListLeadsFilter{IDs: []int64{1}},
			Pagination: &lead.ListLeadsPagination{PageSize: 1},
			OrderBy:    &lead.ListLeadsOrderBy{},
		}
		dbLeads := []*customerrepo.Customer{{ID: 1, GivenName: "张三", CreatedTime: time.Now()}}
		total := int64(1)
		repo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(_ context.Context, filter *customerrepo.ListFilter, pagination *customerrepo.Pagination, orderBy *customerrepo.OrderBy) (*customerrepo.CursorResult, error) {
				return &customerrepo.CursorResult{
					Data:       dbLeads,
					HasNext:    true,
					TotalCount: &total,
				}, nil
			},
		)
		resp, err := logic.ListLeads(context.Background(), req)
		require.NoError(t, err)
		require.True(t, resp.HasNext)
		require.NotEmpty(t, resp.NextToken)
	})

	t.Run("TotalCount为nil", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		req := &lead.ListLeadsRequest{
			Filter:     &lead.ListLeadsFilter{IDs: []int64{1}},
			Pagination: &lead.ListLeadsPagination{PageSize: 1},
			OrderBy:    &lead.ListLeadsOrderBy{},
		}
		dbLeads := []*customerrepo.Customer{{ID: 1, GivenName: "张三"}}
		repo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(_ context.Context, filter *customerrepo.ListFilter, pagination *customerrepo.Pagination, orderBy *customerrepo.OrderBy) (*customerrepo.CursorResult, error) {
				return &customerrepo.CursorResult{
					Data:       dbLeads,
					HasNext:    false,
					TotalCount: nil,
				}, nil
			},
		)
		resp, err := logic.ListLeads(context.Background(), req)
		require.NoError(t, err)
		require.Nil(t, resp.TotalSize)
	})
}

func TestUpdateLead(t *testing.T) {
	t.Run("测试更新Lead成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		// 先 mock GetLead
		dbLead := &customerrepo.Customer{ID: 1, GivenName: "张三", CustomerType: customerpb.CustomerType_LEAD}
		repo.EXPECT().Get(context.Background(), int64(1), customerpb.CustomerType_LEAD).Return(dbLead, nil)

		updateReq := &lead.UpdateLeadRequest{
			GivenName:    "王五",
			FamilyName:   "赵六",
			CustomFields: nil,
			LifeCycleID:  123,
			OwnerStaffID: 456,
		}
		repo.EXPECT().Update(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *customerrepo.Customer) (*customerrepo.Customer, error) {
				require.Equal(t, "王五", arg.GivenName)
				require.Equal(t, "赵六", arg.FamilyName)
				require.Equal(t, int64(123), arg.LifeCycleID)
				require.Equal(t, int64(456), arg.OwnerStaffID)
				return arg, nil
			},
		)

		result, err := logic.UpdateLead(context.Background(), 1, updateReq)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, "王五", result.GivenName)
	})

	t.Run("测试更新Lead-Get失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		repo.EXPECT().Get(context.Background(), int64(2), customerpb.CustomerType_LEAD).Return(nil, errors.New("not found"))
		result, err := logic.UpdateLead(context.Background(), 2, &lead.UpdateLeadRequest{})
		require.Error(t, err)
		require.Nil(t, result)
	})
}

func TestUpdateLead_UpdateError(t *testing.T) {
	t.Run("Update返回error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		dbLead := &customerrepo.Customer{ID: 1, GivenName: "张三", CustomerType: customerpb.CustomerType_LEAD}
		repo.EXPECT().Get(context.Background(), int64(1), customerpb.CustomerType_LEAD).Return(dbLead, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).Return(nil, errors.New("update error"))

		updateReq := &lead.UpdateLeadRequest{GivenName: "新名"}
		result, err := logic.UpdateLead(context.Background(), 1, updateReq)
		require.Error(t, err)
		require.Nil(t, result)
	})
}

func TestDeleteLead(t *testing.T) {
	t.Run("测试删除Lead-逻辑删除", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		dbLead := &customerrepo.Customer{ID: 1, State: customerpb.Customer_ACTIVE}
		repo.EXPECT().Get(context.Background(), int64(1), customerpb.CustomerType_LEAD).Return(dbLead, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *customerrepo.Customer) (*customerrepo.Customer, error) {
				require.Equal(t, customerpb.Lead_INACTIVE, customerpb.Lead_State(arg.State))
				return arg, nil
			},
		)
		err := logic.DeleteLead(context.Background(), 1, true)
		require.NoError(t, err)
	})

	t.Run("测试删除Lead-物理删除", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		dbLead := &customerrepo.Customer{ID: 2, State: customerpb.Customer_ACTIVE}
		repo.EXPECT().Get(context.Background(), int64(2), customerpb.CustomerType_LEAD).Return(dbLead, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).DoAndReturn(
			func(_ context.Context, arg *customerrepo.Customer) (*customerrepo.Customer, error) {
				require.Equal(t, customerpb.Lead_DELETED, customerpb.Lead_State(arg.State))
				require.NotNil(t, arg.DeletedTime)
				return arg, nil
			},
		)
		err := logic.DeleteLead(context.Background(), 2, false)
		require.NoError(t, err)
	})

	t.Run("测试删除Lead-Get失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		repo.EXPECT().Get(context.Background(), int64(3), customerpb.CustomerType_LEAD).Return(nil, errors.New("not found"))
		err := logic.DeleteLead(context.Background(), 3, true)
		require.Error(t, err)
	})
}

func TestDeleteLead_UpdateError(t *testing.T) {
	t.Run("Update返回error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customermock.NewMockRepository(ctrl)
		logic := lead.NewByParams(repo)

		dbLead := &customerrepo.Customer{ID: 1, State: customerpb.Customer_ACTIVE}
		repo.EXPECT().Get(context.Background(), int64(1), customerpb.CustomerType_LEAD).Return(dbLead, nil)
		repo.EXPECT().Update(context.Background(), gomock.Any()).Return(nil, errors.New("update error"))

		err := logic.DeleteLead(context.Background(), 1, true)
		require.Error(t, err)
	})
}

func TestLead_ToDB(t *testing.T) {
	t.Run("Lead转DB结构体", func(t *testing.T) {
		leadObj := &lead.Lead{
			ID:               1,
			OrganizationType: 0, // 如有常量可替换
			OrganizationID:   100,
			GivenName:        "张三",
			FamilyName:       "李四",
			CustomerType:     customerpb.CustomerType_LEAD,
			State:            customerpb.Lead_ACTIVE,
			CustomFields:     nil,
			LifeCycleID:      123,
			OwnerStaffID:     456,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		}
		dbObj := leadObj.ToDB()
		require.Equal(t, leadObj.ID, dbObj.ID)
		require.Equal(t, leadObj.GivenName, dbObj.GivenName)
		require.Equal(t, customerpb.Customer_State(leadObj.State), dbObj.State)
	})
}

func TestLead_ToPB(t *testing.T) {
	t.Run("Lead转PB结构体-正常", func(t *testing.T) {
		now := time.Now()
		leadObj := &lead.Lead{
			ID:               2,
			OrganizationType: 0, // 如有常量可替换
			OrganizationID:   200,
			GivenName:        "王五",
			FamilyName:       "赵六",
			CustomerType:     customerpb.CustomerType_LEAD,
			State:            customerpb.Lead_ACTIVE,
			CustomFields:     nil,
			LifeCycleID:      321,
			OwnerStaffID:     654,
			CreatedTime:      now,
			UpdatedTime:      now,
		}
		pb := leadObj.ToPB()
		require.Equal(t, leadObj.ID, pb.Id)
		require.Equal(t, leadObj.GivenName, pb.GivenName)
		require.Equal(t, int32(leadObj.State), int32(pb.State))
		require.Equal(t, leadObj.OrganizationID, pb.Organization.Id)
		require.Equal(t, leadObj.OrganizationType, pb.Organization.Type)
		require.Equal(t, leadObj.LifeCycleID, pb.LifecycleId)
		require.Equal(t, leadObj.OwnerStaffID, pb.OwnerStaffId)
		require.NotNil(t, pb.CreateTime)
		require.NotNil(t, pb.UpdateTime)
	})

	t.Run("Lead转PB结构体-DeletedTime存在", func(t *testing.T) {
		now := time.Now()
		leadObj := &lead.Lead{
			ID:          3,
			DeletedTime: &now,
			CreatedTime: now,
			UpdatedTime: now,
		}
		pb := leadObj.ToPB()
		require.NotNil(t, pb.DeleteTime)
	})

	t.Run("Lead转PB结构体-空CustomFields", func(t *testing.T) {
		leadObj := &lead.Lead{CustomFields: nil}
		pb := leadObj.ToPB()
		require.NotNil(t, pb.CustomFields)
	})
}

func TestLead_ToPB_Branches(t *testing.T) {
	t.Run("c为nil返回nil", func(t *testing.T) {
		var leadObj *lead.Lead = nil
		pb := leadObj.ToPB()
		require.Nil(t, pb)
	})

	t.Run("CustomFields非法json导致Unmarshal失败", func(t *testing.T) {
		leadObj := &lead.Lead{CustomFields: []byte("not-a-json")} // 非法json
		pb := leadObj.ToPB()
		require.NotNil(t, pb)
		// 解析失败后 customFields 应为非nil空Struct
		require.NotNil(t, pb.CustomFields)
	})

	t.Run("structpb.NewStruct返回err", func(t *testing.T) {
		// 伪造一个特殊map使 NewStruct 返回错误
		// 由于 structpb.NewStruct 只会在 map 里有 channel/func/complex 类型时报错
		leadObj := &lead.Lead{}
		leadCustomFields := map[string]any{"bad": make(chan int)}
		b, _ := json.Marshal(leadCustomFields)
		leadObj.CustomFields = b
		pb := leadObj.ToPB()
		require.NotNil(t, pb)
		// 解析失败后 customFields 应为非nil空Struct
		require.NotNil(t, pb.CustomFields)
	})
}

func TestListLeadsPagination_DecodeCursor(t *testing.T) {
	t.Run("正常解码", func(t *testing.T) {
		cursor := postgres.Cursor{ID: 123, CreatedAt: time.Now()}
		bytes, _ := json.Marshal(cursor)
		encoded := base64.StdEncoding.EncodeToString(bytes)
		p := &lead.ListLeadsPagination{Cursor: encoded}
		result := p.DecodeCursor()
		require.NotNil(t, result)
		require.Equal(t, cursor.ID, result.ID)
	})

	t.Run("空字符串", func(t *testing.T) {
		p := &lead.ListLeadsPagination{Cursor: ""}
		result := p.DecodeCursor()
		require.Nil(t, result)
	})

	t.Run("非法base64", func(t *testing.T) {
		p := &lead.ListLeadsPagination{Cursor: "!!!"}
		result := p.DecodeCursor()
		require.Nil(t, result)
	})
}
