package lead

import (
	"context"
	"errors"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	customerRepo customerrepo.Repository
}

func New() *Logic {
	return &Logic{
		customerRepo: customerrepo.New(),
	}
}

func NewByParams(
	customerRepo customerrepo.Repository,
) *Logic {
	return &Logic{
		customerRepo: customerRepo,
	}
}

func (l *Logic) CreateLead(ctx context.Context, lead *Lead) (*Lead, error) {
	// create customer, set output only columns
	now := time.Now().UTC()
	lead.CreatedTime = now
	lead.UpdatedTime = now
	lead.State = customerpb.Lead_ACTIVE
	lead.CustomerType = customerpb.CustomerType_LEAD
	dbLead := lead.ToDB()

	dbLead, err := l.customerRepo.Create(ctx, dbLead)
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_LEAD_FAILED)
	}
	// convert to logic lead, and return
	return convertToLead(dbLead), nil
}

func (l *Logic) GetLead(ctx context.Context, id int64) (*Lead, error) {
	dbLead, err := l.customerRepo.Get(ctx, id, customerpb.CustomerType_LEAD)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_LEAD_NOT_FOUND)
		}
		return nil, err
	}
	return convertToLead(dbLead), nil
}

func (l *Logic) ListLeads(ctx context.Context, req *ListLeadsRequest) (*ListLeadsResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListLeadsOrderBy{
			Field:     customerpb.ListLeadsRequest_Sorting_CREATED_TIME,
			Direction: customerpb.ListLeadsRequest_Sorting_DESC,
		}
	}
	states := make([]customerpb.Customer_State, len(req.Filter.States))
	for i, state := range req.Filter.States {
		states[i] = customerpb.Customer_State(state)
	}
	dbLeads, err := l.customerRepo.ListByCursor(ctx, &customerrepo.ListFilter{
		IDs:              req.Filter.IDs,
		OrganizationType: req.Filter.OrganizationType,
		OrganizationID:   req.Filter.OrganizationID,
		CustomerType:     customerpb.CustomerType_LEAD,
		States:           states,
		OwnerStaffIDs:    req.Filter.OwnerStaffIDs,
		LifecycleIDs:     req.Filter.LifecycleIDs,
	}, &customerrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          req.Pagination.DecodeCursor(),
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &customerrepo.OrderBy{
		Field:     customerpb.ListCustomersRequest_Sorting_Field(req.OrderBy.Field),
		Direction: customerpb.ListCustomersRequest_Sorting_Direction(req.OrderBy.Direction),
	})
	if err != nil {
		return nil, err
	}

	result := &ListLeadsResponse{
		Leads:   convertToLeads(dbLeads.Data),
		HasNext: dbLeads.HasNext,
	}
	if dbLeads.TotalCount != nil {
		result.TotalSize = dbLeads.TotalCount
	}
	if dbLeads.HasNext && len(dbLeads.Data) > 0 {
		lastLead := dbLeads.Data[len(dbLeads.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastLead.ID,
			CreatedAt: lastLead.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}
	return result, nil
}

func (l *Logic) UpdateLead(ctx context.Context, id int64, updateRef *UpdateLeadRequest) (*Lead, error) {
	// check lead exists
	lead, err := l.GetLead(ctx, id)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	lead.UpdatedTime = now
	lead.GivenName = updateRef.GivenName
	lead.FamilyName = updateRef.FamilyName
	lead.CustomFields = updateRef.CustomFields
	lead.LifeCycleID = updateRef.LifeCycleID
	lead.OwnerStaffID = updateRef.OwnerStaffID

	updatedLead, err := l.customerRepo.Update(ctx, lead.ToDB())
	if err != nil {
		return nil, err
	}

	return convertToLead(updatedLead), nil
}

func (l *Logic) DeleteLead(ctx context.Context, id int64, inactivate bool) error {
	dbLead, err := l.GetLead(ctx, id)
	if err != nil {
		return err
	}

	now := time.Now().UTC()
	dbLead.UpdatedTime = now
	dbLead.State = customerpb.Lead_INACTIVE
	if !inactivate {
		deletedTime := now
		dbLead.DeletedTime = &deletedTime
		dbLead.State = customerpb.Lead_DELETED
	}
	_, err = l.customerRepo.Update(ctx, dbLead.ToDB())
	if err != nil {
		return err
	}
	return nil
}

func convertToLeads(dbLeads []*customerrepo.Customer) []*Lead {
	leads := make([]*Lead, len(dbLeads))
	for i, dbLead := range dbLeads {
		leads[i] = convertToLead(dbLead)
	}
	return leads
}

func convertToLead(dbLead *customerrepo.Customer) *Lead {
	return &Lead{
		ID:               dbLead.ID,
		OrganizationType: dbLead.OrganizationType,
		OrganizationID:   dbLead.OrganizationID,
		GivenName:        dbLead.GivenName,
		FamilyName:       dbLead.FamilyName,
		CustomerType:     dbLead.CustomerType,
		State:            customerpb.Lead_State(dbLead.State),
		CustomFields:     dbLead.CustomFields,
		LifeCycleID:      dbLead.LifeCycleID,
		OwnerStaffID:     dbLead.OwnerStaffID,
		DeletedTime:      dbLead.DeletedTime,
		CreatedTime:      dbLead.CreatedTime,
		UpdatedTime:      dbLead.UpdatedTime,
	}
}
