package customer

import (
	"context"
	"errors"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	customerRepo customerrepo.Repository
}

func New() *Logic {
	return &Logic{
		customerRepo: customerrepo.New(),
	}
}

func NewByParams(
	customerRepo customerrepo.Repository,
) *Logic {
	return &Logic{
		customerRepo: customerRepo,
	}
}

func (l *Logic) CreateCustomer(ctx context.Context, customer *Customer) (*Customer, error) {
	// create customer, set output only columns
	now := time.Now().UTC()
	customer.CreatedTime = now
	customer.UpdatedTime = now
	customer.State = customerpb.Customer_ACTIVE
	customer.CustomerType = customerpb.CustomerType_CUSTOMER
	dbCustomer := customer.ToDB()

	dbCustomer, err := l.customerRepo.Create(ctx, dbCustomer)
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_CUSTOMER_FAILED)
	}
	// convert to logic customer, and return
	return convertToCustomer(dbCustomer), nil
}

func (l *Logic) GetCustomer(ctx context.Context, id int64) (*Customer, error) {
	dbCustomer, err := l.customerRepo.Get(ctx, id, customerpb.CustomerType_CUSTOMER)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND)
		}
		return nil, err
	}
	return convertToCustomer(dbCustomer), nil
}

func (l *Logic) ListCustomers(ctx context.Context, req *ListCustomersRequest) (*ListCustomersResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListCustomersOrderBy{
			Field:     customerpb.ListCustomersRequest_Sorting_CREATED_TIME,
			Direction: customerpb.ListCustomersRequest_Sorting_DESC,
		}
	}
	dbCustomers, err := l.customerRepo.ListByCursor(ctx, &customerrepo.ListFilter{
		IDs:              req.Filter.IDs,
		OrganizationType: req.Filter.OrganizationType,
		OrganizationID:   req.Filter.OrganizationID,
		CustomerType:     req.Filter.CustomerType,
		States:           req.Filter.States,
		OwnerStaffIDs:    req.Filter.OwnerStaffIDs,
		LifecycleIDs:     req.Filter.LifecycleIDs,
	}, &customerrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          req.Pagination.DecodeCursor(),
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &customerrepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}

	result := &ListCustomersResponse{
		Customers: convertToCustomers(dbCustomers.Data),
		HasNext:   dbCustomers.HasNext,
	}
	if dbCustomers.TotalCount != nil {
		result.TotalSize = dbCustomers.TotalCount
	}
	if dbCustomers.HasNext && len(dbCustomers.Data) > 0 {
		lastCustomer := dbCustomers.Data[len(dbCustomers.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastCustomer.ID,
			CreatedAt: lastCustomer.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}
	return result, nil
}

func (l *Logic) UpdateCustomer(ctx context.Context, id int64, updateRef *UpdateCustomerRequest) (*Customer, error) {
	// check customer exists
	customer, err := l.GetCustomer(ctx, id)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	customer.UpdatedTime = now
	customer.GivenName = updateRef.GivenName
	customer.FamilyName = updateRef.FamilyName
	customer.CustomFields = updateRef.CustomFields
	customer.LifeCycleID = updateRef.LifeCycleID
	customer.OwnerStaffID = updateRef.OwnerStaffID

	updatedCustomer, err := l.customerRepo.Update(ctx, customer.ToDB())
	if err != nil {
		return nil, err
	}

	return convertToCustomer(updatedCustomer), nil
}

func (l *Logic) DeleteCustomer(ctx context.Context, id int64, inactivate bool) error {
	dbCustomer, err := l.GetCustomer(ctx, id)
	if err != nil {
		return err
	}

	now := time.Now().UTC()
	dbCustomer.UpdatedTime = now
	dbCustomer.State = customerpb.Customer_INACTIVE
	if !inactivate {
		deletedTime := now
		dbCustomer.DeletedTime = &deletedTime
		dbCustomer.State = customerpb.Customer_DELETED
	}
	_, err = l.customerRepo.Update(ctx, dbCustomer.ToDB())
	if err != nil {
		return err
	}
	return nil
}

func convertToCustomers(dbCustomers []*customerrepo.Customer) []*Customer {
	customers := make([]*Customer, len(dbCustomers))
	for i, dbCustomer := range dbCustomers {
		customers[i] = convertToCustomer(dbCustomer)
	}
	return customers
}

func convertToCustomer(dbCustomer *customerrepo.Customer) *Customer {
	return &Customer{
		ID:               dbCustomer.ID,
		OrganizationType: dbCustomer.OrganizationType,
		OrganizationID:   dbCustomer.OrganizationID,
		GivenName:        dbCustomer.GivenName,
		FamilyName:       dbCustomer.FamilyName,
		CustomerType:     dbCustomer.CustomerType,
		State:            dbCustomer.State,
		CustomFields:     dbCustomer.CustomFields,
		LifeCycleID:      dbCustomer.LifeCycleID,
		OwnerStaffID:     dbCustomer.OwnerStaffID,
		DeletedTime:      dbCustomer.DeletedTime,
		CreatedTime:      dbCustomer.CreatedTime,
		UpdatedTime:      dbCustomer.UpdatedTime,
	}
}
