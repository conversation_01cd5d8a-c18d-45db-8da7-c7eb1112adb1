package activitylog

import customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"

type CreateActivityLogDatum struct {
	CustomerID          int64
	BusinessID          int64
	CompanyID           int64
	StaffID             int64
	CustomerName        string
	CustomerPhoneNumber string

	Action *customerpb.ActivityLog_Action
	Source *customerpb.SystemSource
}

type ListActivityLogsDatum struct {
	// filter
	CustomerID      *int64
	ActivityLogType *customerpb.ActivityLog_Type
	CompanyID       *int64

	// page
	PageSize int
	PageNum  int
}

type UpdateActivityLogDatum struct {
	LogID   int64
	StaffID int64

	Action *customerpb.ActivityLog_Action
}
