package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer"
	noterepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/note"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/staff"
	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type NoteConsumer struct {
	noteRepo     noterepo.ReadWriter
	staffRepo    staff.ReadWriter
	customerRepo customer.Repository
}

func NewNoteConsumer() *NoteConsumer {
	return &NoteConsumer{
		noteRepo:     noterepo.New(),
		staffRepo:    staff.New(),
		customerRepo: customer.New(),
	}
}

func (c *NoteConsumer) NoteEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "NoteEventHandler get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	var cdcMsg NoteMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "NoteEventHandler unmarshal error: %v", err)
		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC create op but after is nil")
			return nil
		}
		model := c.toNoteModel(ctx, cdcMsg.After)
		if err := c.noteRepo.Create(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateNote error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC CreateNote success op=%s id=%d", cdcMsg.Op, model.ID)
	case "u":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC update op but after is nil")
			return nil
		}
		model := c.toNoteModel(ctx, cdcMsg.After)
		if err := c.noteRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateNote error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC UpdateNote success op=%s id=%d", cdcMsg.Op, model.ID)
	case "d":
		var id int64
		var staffID int64
		if cdcMsg.Before != nil {
			id = cdcMsg.Before.ID
			staffID = cdcMsg.Before.StaffID
		} else if cdcMsg.After != nil {
			id = cdcMsg.After.ID
			staffID = cdcMsg.After.StaffID
		}
		if id == 0 {
			log.InfoContextf(ctx, "CDC delete op but id is 0")
			return nil
		}
		if err := c.noteRepo.Delete(ctx, id, staffID); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteNote error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, id)
	default:
		log.InfoContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}
	return nil
}

// 工具函数：将 NoteRow 转为 Note entity
func (c *NoteConsumer) toNoteModel(ctx context.Context, row *NoteRow) *noterepo.Note {
	if row == nil {
		return nil
	}
	model := &noterepo.Note{
		ID:         row.ID,
		CustomerID: row.CustomerID,
		Note:       row.Note,
	}
	customerDetail, _ := c.customerRepo.GetCustomerForCDC(ctx, row.CustomerID)
	if row.StaffID > 0 {
		staffDetail, _ := c.staffRepo.GetStaffDetail(ctx, row.StaffID, getCompanyID(customerDetail), 0)
		model.CreateSource = &customerpb.SystemSource{
			Source:     customerpb.SystemSource_STAFF,
			SourceId:   row.StaffID,
			SourceName: getName(staffDetail),
		}
	}
	if row.LastStaffID > 0 {
		staffDetail, _ := c.staffRepo.GetStaffDetail(ctx, row.LastStaffID, getCompanyID(customerDetail), 0)
		model.UpdateSource = &customerpb.SystemSource{
			Source:     customerpb.SystemSource_STAFF,
			SourceId:   row.LastStaffID,
			SourceName: getName(staffDetail),
		}
	}
	if row.Status == 2 {
		model.DeleteTime = utils.ToPointer(time.Unix(row.UpdateTime, 0))
	}

	model.CreateTime = time.Unix(row.CreateTime, 0)
	model.UpdateTime = time.Unix(row.UpdateTime, 0)
	return model
}

func getName(detail *organizationpb.StaffModel) string {
	if detail == nil {
		return ""
	}
	return detail.FirstName + " " + detail.LastName
}

func getCompanyID(detail *customer.BusinessCustomer) int64 {
	if detail == nil {
		return 0
	}
	return detail.CompanyID
}
