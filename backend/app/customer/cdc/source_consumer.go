package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	sourcerepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/source"
	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type SourceConsumer struct {
	sourceRepo sourcerepo.ReadWriter
}

func NewSourceConsumer() *SourceConsumer {
	return &SourceConsumer{
		sourceRepo: sourcerepo.New(),
	}
}

func (c *SourceConsumer) SourceEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "SourceEventHandler get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	var cdcMsg SourceMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "SourceEventHandler unmarshal error: %v", err)
		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC create op but after is nil")
			return nil
		}
		model := toSourceModel(cdcMsg.After)
		if err := c.sourceRepo.Create(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateSource error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "u":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC update op but after is nil")
			return nil
		}
		model := toSourceModel(cdcMsg.After)
		if err := c.sourceRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateSource error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "d":
		var id int64
		if cdcMsg.Before != nil {
			id = cdcMsg.Before.ID
		} else if cdcMsg.After != nil {
			id = cdcMsg.After.ID
		}
		if id == 0 {
			log.InfoContextf(ctx, "CDC delete op but id is 0")
			return nil
		}
		// staffID 可选，如有需要可补充
		if err := c.sourceRepo.Delete(ctx, id, 0); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteSource error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, id)
	default:
		log.InfoContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}
	return nil
}

// 工具函数：将 SourceRow 转为 Source entity
func toSourceModel(row *SourceRow) *sourcerepo.Source {
	if row == nil {
		return nil
	}
	model := &sourcerepo.Source{
		ID:         row.ID,
		BusinessID: row.BusinessID,
		Name:       row.SourceName,
		CompanyID:  row.CompanyID,
		Sort:       row.Sort,
	}
	if row.Status == 2 {
		model.DeleteTime = utils.ToPointer(time.Unix(row.UpdateTime, 0))
	}
	model.CreateTime = time.Unix(row.CreateTime, 0)
	model.UpdateTime = time.Unix(row.UpdateTime, 0)
	return model
}
