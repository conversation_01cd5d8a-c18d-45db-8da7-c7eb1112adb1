package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	actionstate "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/action_state"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ActionStateConsumer struct {
	actionStateRepo actionstate.ReadWriter
}

func NewActionStateConsumer() *ActionStateConsumer {
	return &ActionStateConsumer{
		actionStateRepo: actionstate.New(),
	}
}

func (c *ActionStateConsumer) ActionStateEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "ActionStateEventHandler get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// 反序列化
	var cdcMsg ActionStateMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "ActionStateEventHandler unmarshal error: %v", err)
		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC create op but after is nil")
			return nil
		}
		model := toActionStateModel(cdcMsg.After)
		if err := c.actionStateRepo.Create(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateActionState error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "u":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC update op but after is nil")
			return nil
		}
		model := toActionStateModel(cdcMsg.After)
		if err := c.actionStateRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateActionState error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, model.ID)
	case "d":
		var id int64
		var staffID int64
		if cdcMsg.Before != nil {
			id = cdcMsg.Before.ID
			staffID = cdcMsg.Before.UpdatedBy
		} else if cdcMsg.After != nil {
			id = cdcMsg.After.ID
			staffID = cdcMsg.After.UpdatedBy
		}
		if id == 0 {
			log.InfoContextf(ctx, "CDC delete op but id is 0")
			return nil
		}
		if err := c.actionStateRepo.Delete(ctx, id, staffID); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteActionState error: %v", err)
			return err
		}
		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, id)
	default:
		log.InfoContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}
	return nil
}

func toActionStateModel(row *ActionStateRow) *actionstate.CustomerActionState {
	if row == nil {
		return nil
	}
	model := &actionstate.CustomerActionState{
		ID:         row.ID,
		CompanyID:  row.CompanyID,
		BusinessID: row.BusinessID,
		CreatedBy:  row.CreatedBy,
		UpdatedBy:  row.UpdatedBy,
		DeletedBy:  row.DeletedBy,
		Name:       row.Name,
		Sort:       row.Sort,
		Color:      row.Color,
	}
	model.CreatedAt = time.UnixMilli(row.CreatedAt)
	model.UpdatedAt = time.UnixMilli(row.UpdatedAt)
	if row.DeletedAt != nil {
		t := time.UnixMilli(*row.DeletedAt)
		model.DeletedAt = &t
	}
	return model
}
