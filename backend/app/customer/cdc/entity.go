package cdc

type Source struct {
	Version   string `json:"version"`
	Connector string `json:"connector"`
	Name      string `json:"name"`
	TsMs      int64  `json:"ts_ms"`
	Snapshot  string `json:"snapshot"`
	DB        string `json:"db"`
	Table     string `json:"table"`
}

type LifeCycleMessage struct {
	Before *LifeCycleRow `json:"before"`
	After  *LifeCycleRow `json:"after"`
	Source Source        `json:"source"`
	Op     string        `json:"op"`
	TsMs   int64         `json:"ts_ms"`
}

type LifeCycleRow struct {
	ID         int64  `json:"id"`
	CompanyID  int64  `json:"company_id"`
	BusinessID int64  `json:"business_id"`
	CreatedBy  int64  `json:"created_by"`
	UpdatedBy  int64  `json:"updated_by"`
	DeletedBy  *int64 `json:"deleted_by"`
	CreatedAt  int64  `json:"created_at"`
	UpdatedAt  int64  `json:"updated_at"`
	DeletedAt  *int64 `json:"deleted_at"`
	Name       string `json:"name"`
	Sort       int32  `json:"sort"`
	IsDefault  int32  `json:"is_default"`
}

type ActionStateMessage struct {
	Before *ActionStateRow `json:"before"`
	After  *ActionStateRow `json:"after"`
	Source Source          `json:"source"`
	Op     string          `json:"op"`
	TsMs   int64           `json:"ts_ms"`
}

type ActionStateRow struct {
	ID         int64  `json:"id"`
	CompanyID  int64  `json:"company_id"`
	BusinessID int64  `json:"business_id"`
	CreatedBy  int64  `json:"created_by"`
	UpdatedBy  int64  `json:"updated_by"`
	DeletedBy  *int64 `json:"deleted_by"`
	CreatedAt  int64  `json:"created_at"`
	UpdatedAt  int64  `json:"updated_at"`
	DeletedAt  *int64 `json:"deleted_at"`
	Name       string `json:"name"`
	Sort       int32  `json:"sort"`
	Color      string `json:"color"`
}

type HistoryLogMessage struct {
	Before *HistoryLogRow `json:"before"`
	After  *HistoryLogRow `json:"after"`
	Source Source         `json:"source"`
	Op     string         `json:"op"`
	TsMs   int64          `json:"ts_ms"`
}

type HistoryLogRow struct {
	ID                  int64  `json:"id"`
	CompanyID           int64  `json:"company_id"`
	BusinessID          int64  `json:"business_id"`
	CustomerID          int64  `json:"customer_id"`
	StaffID             int64  `json:"staff_id"`
	Type                string `json:"type"`
	Action              string `json:"action"`
	CreateTime          int64  `json:"create_time"`
	UpdateTime          int64  `json:"update_time"`
	Source              string `json:"source"`
	SourceID            int64  `json:"source_id"`
	CustomerName        string `json:"customer_name"`
	CustomerPhoneNumber string `json:"customer_phone_number"`
	SourceName          string `json:"source_name"`
}

type TaskMessage struct {
	Before *TaskRow `json:"before"`
	After  *TaskRow `json:"after"`
	Source Source   `json:"source"`
	Op     string   `json:"op"`
	TsMs   int64    `json:"ts_ms"`
}

type TaskRow struct {
	ID              int64  `json:"id"`
	CompanyID       int64  `json:"company_id"`
	BusinessID      int64  `json:"business_id"`
	CustomerID      int64  `json:"customer_id"`
	Name            string `json:"name"`
	AllocateStaffID *int64 `json:"allocate_staff_id"`
	CompleteTime    *int64 `json:"complete_time"`
	State           string `json:"state"`
	CreateBy        int64  `json:"create_by"`
	UpdateBy        int64  `json:"update_by"`
	DeleteBy        *int64 `json:"delete_by"`
	CreateTime      int64  `json:"create_time"`
	UpdateTime      int64  `json:"update_time"`
	DeleteTime      *int64 `json:"delete_time"`
}

// CDC 消息体结构
type NoteMessage struct {
	Before *NoteRow `json:"before"`
	After  *NoteRow `json:"after"`
	Source Source   `json:"source"`
	Op     string   `json:"op"`
	TsMs   int64    `json:"ts_ms"`
}

type NoteRow struct {
	ID          int64  `json:"id"`
	CustomerID  int64  `json:"customer_id"`
	Note        string `json:"note"`
	StaffID     int64  `json:"staff_id"`
	LastStaffID int64  `json:"last_staff_id"`
	Status      int32  `json:"status"`
	CreateTime  int64  `json:"create_time"`
	UpdateTime  int64  `json:"update_time"`
}

// CDC 消息体结构
type SourceMessage struct {
	Before *SourceRow `json:"before"`
	After  *SourceRow `json:"after"`
	Source Source     `json:"source"`
	Op     string     `json:"op"`
	TsMs   int64      `json:"ts_ms"`
}

type SourceRow struct {
	ID         int64  `json:"id"`
	BusinessID int64  `json:"business_id"`
	SourceName string `json:"source_name"`
	Status     int32  `json:"status"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
	CompanyID  int64  `json:"company_id"`
	Sort       int32  `json:"sort"`
}

// CDC 消息体结构
type TagMessage struct {
	Before *TagRow `json:"before"`
	After  *TagRow `json:"after"`
	Source Source  `json:"source"`
	Op     string  `json:"op"`
	TsMs   int64   `json:"ts_ms"`
}

type TagRow struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	BusinessID int64  `json:"business_id"`
	Sort       int32  `json:"sort"`
	Status     int32  `json:"status"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
	CompanyID  int64  `json:"company_id"`
}

type TagBindingMessage struct {
	Before *TagBindingRow `json:"before"`
	After  *TagBindingRow `json:"after"`
	Source Source         `json:"source"`
	Op     string         `json:"op"`
	TsMs   int64          `json:"ts_ms"`
}

type TagBindingRow struct {
	ID            int64 `json:"id"`
	CustomerID    int64 `json:"customer_id"`
	CustomerTagID int64 `json:"customer_tag_id"`
}
