package service

import (
	"context"

	"github.com/lib/pq"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/datatypes"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	customerv2 "github.com/MoeGolibrary/moego/backend/app/customer/logic/customerv2"
	leadLogic "github.com/MoeGolibrary/moego/backend/app/customer/logic/lead"
	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type MetadataService struct {
	customerpb.UnimplementedMetadataServiceServer
	customerLogic *customerv2.Logic
	contactLogic  *contact.Logic
	leadLogic     *leadLogic.Logic
	addressLogic  *address.Logic
}

func NewMetadataService() *MetadataService {
	return &MetadataService{
		customerLogic: customerv2.New(),
		contactLogic:  contact.New(),
		leadLogic:     leadLogic.New(),
		addressLogic:  address.New(),
	}
}

func (s *MetadataService) CreateCustomer(ctx context.Context,
	req *customerpb.CreateCustomerRequest) (*customerpb.Customer, error) {
	customer := req.GetCustomer()
	if customer == nil {
		return nil, errs.Newm(codes.InvalidArgument, "customer is required")
	}

	if customer.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	logicCustomer, err := s.customerLogic.CreateCustomer(ctx, &customerv2.Customer{
		ID:               customer.Id,
		OrganizationType: customer.Organization.Type,
		OrganizationID:   customer.Organization.Id,
		GivenName:        customer.GivenName,
		FamilyName:       customer.FamilyName,
		CustomFields:     datatypes.JSON(utils.JSONMarshalNoErr(customer.CustomFields.AsMap())),
		LifeCycleID:      customer.LifecycleId,
		OwnerStaffID:     customer.OwnerStaffId,
	})

	if err != nil {
		return nil, err
	}

	return logicCustomer.ToPB(), nil
}

func (s *MetadataService) GetCustomer(ctx context.Context,
	req *customerpb.GetCustomerRequest) (*customerpb.Customer, error) {
	customer, err := s.customerLogic.GetCustomer(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return customer.ToPB(), nil
}

func (s *MetadataService) ListCustomers(ctx context.Context,
	req *customerpb.ListCustomersRequest) (*customerpb.ListCustomersResponse, error) {

	// 构建过滤器，添加nil检查
	filter := &customerv2.ListCustomersFilter{
		CustomerType:  customerpb.CustomerType_CUSTOMER,
		States:        req.Filter.States,
		OwnerStaffIDs: req.Filter.OwnerStaffIds,
		LifecycleIDs:  req.Filter.LifecycleIds,
	}

	// 安全地访问Organization字段
	if req.Filter.Organization != nil {
		filter.OrganizationType = &req.Filter.Organization.Type
		filter.OrganizationID = req.Filter.Organization.Id
	}

	pagination := &customerv2.ListCustomersPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}

	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &customerv2.ListCustomersOrderBy{
		Field:     customerpb.ListCustomersRequest_Sorting_ID,
		Direction: customerpb.ListCustomersRequest_Sorting_ASC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.customerLogic.ListCustomers(ctx, &customerv2.ListCustomersRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	customers := make([]*customerpb.Customer, 0, len(response.Customers))
	for _, customer := range response.Customers {
		customers = append(customers, customer.ToPB())
	}
	return &customerpb.ListCustomersResponse{
		Customers:     customers,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateCustomer(ctx context.Context,
	req *customerpb.UpdateCustomerRequest) (*customerpb.Customer, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}
	customer, err := s.customerLogic.UpdateCustomer(ctx, req.Id, &customerv2.UpdateCustomerRequest{
		GivenName:    ref.GetGivenName(),
		FamilyName:   ref.GetFamilyName(),
		CustomFields: datatypes.JSON(utils.JSONMarshalNoErr(ref.GetCustomFields().AsMap())),
		LifeCycleID:  ref.GetLifecycleId(),
		OwnerStaffID: ref.GetOwnerStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return customer.ToPB(), nil
}

func (s *MetadataService) DeleteCustomer(ctx context.Context,
	req *customerpb.DeleteCustomerRequest) (*emptypb.Empty, error) {
	err := s.customerLogic.DeleteCustomer(ctx, req.Id, req.Inactivate)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateContact(ctx context.Context,
	req *customerpb.CreateContactRequest) (*customerpb.Contact, error) {
	pbContact := req.GetContact()
	if pbContact == nil {
		return nil, errs.Newm(codes.InvalidArgument, "contact is required")
	}
	_, err := s.customerLogic.GetCustomer(ctx, pbContact.CustomerId)
	if err != nil {
		return nil, errs.Newm(customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_ID, "customer id is invalid")
	}

	contact := &contact.Contact{
		ID:         pbContact.Id,
		CustomerID: pbContact.CustomerId,
		GivenName:  pbContact.GivenName,
		FamilyName: pbContact.FamilyName,
	}
	if e := pbContact.GetEmail(); e != "" {
		contact.Email = e
	}

	if phone := pbContact.GetPhone(); phone != nil {
		contact.Phone = phone.GetE164Number()
	}

	createdContact, err := s.contactLogic.CreateContact(ctx, contact)
	if err != nil {
		return nil, err
	}
	return createdContact.ToPB(), nil
}

func (s *MetadataService) GetContact(ctx context.Context,
	req *customerpb.GetContactRequest) (*customerpb.Contact, error) {
	contact, err := s.contactLogic.GetContact(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return contact.ToPB(), nil
}

func (s *MetadataService) ListContacts(ctx context.Context,
	req *customerpb.ListContactsRequest) (*customerpb.ListContactsResponse, error) {

	// 构建过滤器，添加nil检查
	filter := &contact.ListContactsFilter{
		States: req.Filter.GetStates(),
	}
	if req.Filter != nil {
		if len(req.Filter.Ids) > 0 {
			filter.IDs = req.Filter.Ids
		}
		if len(req.Filter.CustomerIds) > 0 {
			filter.CustomerIDs = req.Filter.CustomerIds
		}
	}

	pagination := &contact.ListContactsPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}
	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &contact.ListContactsOrderBy{
		Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.ListContactsRequest_Sorting_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.contactLogic.ListContacts(ctx, &contact.ListContactsRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	contacts := make([]*customerpb.Contact, 0, len(response.Contacts))
	for _, c := range response.Contacts {
		contacts = append(contacts, c.ToPB())
	}
	return &customerpb.ListContactsResponse{
		Contacts:      contacts,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateContact(ctx context.Context,
	req *customerpb.UpdateContactRequest) (*customerpb.Contact, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}
	contact, err := s.contactLogic.UpdateContact(ctx, &contact.UpdateContactRequest{
		ID:         req.Id,
		GivenName:  ref.GetGivenName(),
		FamilyName: ref.GetFamilyName(),
		Email:      ref.GetEmail(),
		Phone:      ref.GetPhone().GetE164Number(),
		TagIDs:     ref.GetTagIds(),
	})

	if err != nil {
		return nil, err
	}
	return contact.ToPB(), nil
}

func (s *MetadataService) DeleteContact(ctx context.Context,
	req *customerpb.DeleteContactRequest) (*emptypb.Empty, error) {
	err := s.contactLogic.DeleteContact(ctx, req.Id, req.Inactivate)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateLead(ctx context.Context,
	req *customerpb.CreateLeadRequest) (*customerpb.Lead, error) {
	lead := req.GetLead()
	if lead == nil {
		return nil, errs.Newm(codes.InvalidArgument, "lead is required")
	}
	logicLead, err := s.leadLogic.CreateLead(ctx, &leadLogic.Lead{
		ID:               lead.GetId(),
		OrganizationType: lead.GetOrganization().GetType(),
		OrganizationID:   lead.GetOrganization().GetId(),
		CustomerType:     customerpb.CustomerType_LEAD,
		GivenName:        lead.GetGivenName(),
		FamilyName:       lead.GetFamilyName(),
		CustomFields:     datatypes.JSON(utils.JSONMarshalNoErr(lead.GetCustomFields().AsMap())),
		LifeCycleID:      lead.GetLifecycleId(),
		OwnerStaffID:     lead.GetOwnerStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return logicLead.ToPB(), nil
}

func (s *MetadataService) GetLead(ctx context.Context,
	req *customerpb.GetLeadRequest) (*customerpb.Lead, error) {
	lead, err := s.leadLogic.GetLead(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return lead.ToPB(), nil
}

func (s *MetadataService) ListLeads(ctx context.Context,
	req *customerpb.ListLeadsRequest) (*customerpb.ListLeadsResponse, error) {
	filter := &leadLogic.ListLeadsFilter{
		States: req.Filter.GetStates(),
	}
	if req.Filter != nil {
		if len(req.Filter.Ids) > 0 {
			filter.IDs = req.Filter.Ids
		}
	}
	pagination := &leadLogic.ListLeadsPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}
	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &leadLogic.ListLeadsOrderBy{
		Field:     customerpb.ListLeadsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.ListLeadsRequest_Sorting_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.leadLogic.ListLeads(ctx, &leadLogic.ListLeadsRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	leads := make([]*customerpb.Lead, 0, len(response.Leads))
	for _, l := range response.Leads {
		leads = append(leads, l.ToPB())
	}
	return &customerpb.ListLeadsResponse{
		Leads:         leads,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateLead(ctx context.Context,
	req *customerpb.UpdateLeadRequest) (*customerpb.Lead, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}
	lead, err := s.leadLogic.UpdateLead(ctx, req.Id, &leadLogic.UpdateLeadRequest{
		GivenName:    ref.GetGivenName(),
		FamilyName:   ref.GetFamilyName(),
		CustomFields: datatypes.JSON(utils.JSONMarshalNoErr(ref.GetCustomFields().AsMap())),
		LifeCycleID:  ref.GetLifecycleId(),
		OwnerStaffID: ref.GetOwnerStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return lead.ToPB(), nil
}

func (s *MetadataService) DeleteLead(ctx context.Context,
	req *customerpb.DeleteLeadRequest) (*emptypb.Empty, error) {
	err := s.leadLogic.DeleteLead(ctx, req.Id, req.Inactivate)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateAddress(ctx context.Context,
	req *customerpb.CreateAddressRequest) (*customerpb.Address, error) {
	pbAddress := req.GetAddress()
	if pbAddress == nil {
		return nil, errs.Newm(codes.InvalidArgument, "address is required")
	}

	// 验证客户是否存在
	_, err := s.customerLogic.GetCustomer(ctx, pbAddress.CustomerId)
	if err != nil {
		return nil, errs.Newm(customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_ID, "customer id is invalid")
	}

	// 构建地址请求
	addressReq := &address.CreateAddressRequest{
		CustomerID: pbAddress.CustomerId,
	}

	if pbAddress.Address == nil {
		return nil, errs.Newm(codes.InvalidArgument, "address is required")
	}

	// 从PostalAddress中提取地址信息
	if pbAddress.Address != nil {
		addressReq.Revision = int64(pbAddress.Address.Revision)
		addressReq.Organization = pbAddress.Address.Organization
		addressReq.SortingCode = pbAddress.Address.SortingCode
		addressReq.RegionCode = pbAddress.Address.RegionCode
		addressReq.LanguageCode = pbAddress.Address.LanguageCode
		addressReq.PostalCode = pbAddress.Address.PostalCode
		addressReq.AdministrativeArea = pbAddress.Address.AdministrativeArea
		addressReq.Locality = pbAddress.Address.Locality
		addressReq.Sublocality = pbAddress.Address.Sublocality
		addressReq.AddressLines = pq.StringArray(pbAddress.Address.AddressLines)
		addressReq.Recipients = pq.StringArray(pbAddress.Address.Recipients)
	}

	// 从LatLng中提取坐标信息
	if pbAddress.Latlng != nil {
		addressReq.Latitude = pbAddress.Latlng.Latitude
		addressReq.Longitude = pbAddress.Latlng.Longitude
	}

	createdAddress, err := s.addressLogic.CreateAddress(ctx, addressReq)
	if err != nil {
		return nil, err
	}

	return createdAddress.ToPB(), nil
}

func (s *MetadataService) GetAddress(ctx context.Context,
	req *customerpb.GetAddressRequest) (*customerpb.Address, error) {
	address, err := s.addressLogic.GetAddress(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return address.ToPB(), nil
}

func (s *MetadataService) ListAddresses(ctx context.Context,
	req *customerpb.ListAddressesRequest) (*customerpb.ListAddressesResponse, error) {
	// 构建过滤器
	filter := &address.ListAddressesFilter{
		States: req.Filter.GetStates(),
	}
	if req.Filter != nil {
		if len(req.Filter.Ids) > 0 {
			filter.IDs = req.Filter.Ids
		}
		if req.Filter.CustomerId != 0 {
			filter.CustomerID = req.Filter.CustomerId
		}
	}

	pagination := &address.ListAddressesPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}
	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &address.ListAddressesOrderBy{
		Field:     customerpb.ListAddressesRequest_Sorting_CREATED_TIME,
		Direction: customerpb.ListAddressesRequest_Sorting_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.addressLogic.ListAddresses(ctx, &address.ListAddressesRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}

	addresses := make([]*customerpb.Address, 0, len(response.Addresses))
	for _, addr := range response.Addresses {
		addresses = append(addresses, addr.ToPB())
	}

	return &customerpb.ListAddressesResponse{
		Addresses:     addresses,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateAddress(ctx context.Context,
	req *customerpb.UpdateAddressRequest) (*customerpb.Address, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}

	// 构建更新请求
	updateReq := &address.UpdateAddressRequest{}

	// 从UpdateRef中提取地址信息
	if ref.Province != nil {
		updateReq.AdministrativeArea = *ref.Province
	}
	if ref.City != nil {
		updateReq.Locality = *ref.City
	}
	if ref.District != nil {
		updateReq.Sublocality = *ref.District
	}
	if ref.Detail != nil {
		updateReq.AddressLines = pq.StringArray{*ref.Detail}
	}
	if ref.PostalCode != nil {
		updateReq.PostalCode = *ref.PostalCode
	}

	updatedAddress, err := s.addressLogic.UpdateAddress(ctx, req.Id, updateReq)
	if err != nil {
		return nil, err
	}

	return updatedAddress.ToPB(), nil
}

func (s *MetadataService) DeleteAddress(ctx context.Context,
	req *customerpb.DeleteAddressRequest) (*emptypb.Empty, error) {
	err := s.addressLogic.DeleteAddress(ctx, req.Id, req.Inactivate)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateCustomField(_ context.Context,
	_ *customerpb.CreateCustomFieldRequest) (*customerpb.CustomField, error) {
	//TODO implement me
	panic("implement me")
}

func (s *MetadataService) GetCustomField(_ context.Context,
	_ *customerpb.GetCustomFieldRequest) (*customerpb.CustomField, error) {
	//TODO implement me
	panic("implement me")
}

func (s *MetadataService) ListCustomFields(_ context.Context,
	_ *customerpb.ListCustomFieldsRequest) (*customerpb.ListCustomFieldsResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s *MetadataService) UpdateCustomField(_ context.Context,
	_ *customerpb.UpdateCustomFieldRequest) (*customerpb.CustomField, error) {
	//TODO implement me
	panic("implement me")
}

func (s *MetadataService) DeleteCustomField(_ context.Context,
	_ *customerpb.DeleteCustomFieldRequest) (*emptypb.Empty, error) {
	//TODO implement me
	panic("implement me")
}
