package service

import (
	"context"

	actionstate "github.com/MoeGolibrary/moego/backend/app/customer/logic/action_state"
	activitylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/activity_log"
	activityrel "github.com/MoeGolibrary/moego/backend/app/customer/logic/activity_rel"
	lifecycle "github.com/MoeGolibrary/moego/backend/app/customer/logic/life_cycle"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/note"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/source"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/tag"
	taskv2 "github.com/MoeGolibrary/moego/backend/app/customer/logic/task_v2"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type ActivityService struct {
	all *activitylog.Logic
	tl  *taskv2.Logic
	lcl *lifecycle.Logic
	asl *actionstate.Logic
	nl  *note.Logic
	sl  *source.Logic
	tgl *tag.Logic
	arl *activityrel.Logic
	customerpb.UnimplementedActivityServiceServer
}

func NewActivityService() *ActivityService {
	return &ActivityService{
		all: activitylog.New(),
		tl:  taskv2.New(),
		lcl: lifecycle.New(),
		asl: actionstate.New(),
		nl:  note.New(),
		sl:  source.New(),
		tgl: tag.New(),
		arl: activityrel.New(),
	}
}

func (s *ActivityService) CreateCustomerActivityLog(ctx context.Context,
	request *customerpb.CreateCustomerActivityLogRequest) (
	*customerpb.CreateCustomerActivityLogResponse, error) {
	logID, err := s.all.Create(ctx, &activitylog.CreateActivityLogDatum{
		CustomerID:          request.GetCustomerId(),
		CustomerName:        request.GetCustomerName(),
		CustomerPhoneNumber: request.GetCustomerPhoneNumber(),
		BusinessID:          request.GetBusinessId(),
		CompanyID:           request.GetCompanyId(),
		Action:              request.GetAction(),
		Source:              request.Source,
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.CreateCustomerActivityLogResponse{
		LogId: logID,
	}, nil
}

func (s *ActivityService) UpdateCustomerActivityLog(ctx context.Context,
	request *customerpb.UpdateCustomerActivityLogRequest) (
	*customerpb.UpdateCustomerActivityLogResponse, error) {
	if err := s.all.Update(ctx, &activitylog.UpdateActivityLogDatum{
		LogID:   request.GetLogId(),
		StaffID: request.GetStaffId(),
		Action:  request.Action,
	}); err != nil {
		return nil, err
	}
	return &customerpb.UpdateCustomerActivityLogResponse{}, nil
}

func (s *ActivityService) ListCustomerActivityLogs(ctx context.Context,
	request *customerpb.ListCustomerActivityLogsRequest) (
	*customerpb.ListCustomerActivityLogsResponse, error) {
	// conv list datum
	listDatum := &activitylog.ListActivityLogsDatum{
		PageSize: int(request.GetPageSize()),
		PageNum:  int(request.GetPageNum()),
	}
	if request.Filter != nil {
		listDatum.CustomerID = request.Filter.CustomerId
		listDatum.ActivityLogType = request.Filter.Type
		listDatum.CompanyID = request.Filter.CompanyId
	}

	// select
	historyLogs, total, err := s.all.List(ctx, listDatum)
	if err != nil {
		return nil, err
	}
	return &customerpb.ListCustomerActivityLogsResponse{
		ActivityLogs: historyLogs,
		Total:        total,
	}, nil
}

func (s *ActivityService) ConvertCustomer(_ context.Context, _ *customerpb.ConvertCustomerRequest) (
	*customerpb.ConvertCustomerResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s *ActivityService) ConvertCustomersAttribute(_ context.Context, _ *customerpb.ConvertCustomersAttributeRequest) (
	*customerpb.ConvertCustomersAttributeResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s *ActivityService) CreateCustomerTask(ctx context.Context, request *customerpb.CreateCustomerTaskRequest) (
	*customerpb.CreateCustomerTaskResponse, error) {
	taskID, err := s.tl.Create(ctx, &taskv2.CreateTaskDatum{
		CustomerID:      request.GetCustomerId(),
		BusinessID:      request.GetBusinessId(),
		CompanyID:       request.GetCompanyId(),
		StaffID:         request.GetStaffId(),
		Name:            request.GetName(),
		AllocateStaffID: request.AllocateStaffId,
		CompleteTime:    request.CompleteTime,
		State:           customerpb.Task_NEW,
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.CreateCustomerTaskResponse{
		TaskId: taskID,
	}, nil
}

func (s *ActivityService) UpdateCustomerTask(ctx context.Context, request *customerpb.UpdateCustomerTaskRequest) (
	*customerpb.UpdateCustomerTaskResponse, error) {
	if err := s.tl.Update(ctx, &taskv2.UpdateTaskDatum{
		TaskID:          request.GetTaskId(),
		StaffID:         request.GetStaffId(),
		Name:            request.Name,
		AllocateStaffID: request.AllocateStaffId,
		CompleteTime:    request.CompleteTime,
		State:           request.State,
	}); err != nil {
		return nil, err
	}
	return &customerpb.UpdateCustomerTaskResponse{}, nil
}

func (s *ActivityService) ListCustomerTasks(ctx context.Context, request *customerpb.ListCustomerTasksRequest) (
	*customerpb.ListCustomerTasksResponse, error) {
	tasks, err := s.tl.List(ctx, &taskv2.ListTasksDatum{
		CustomerID: request.GetCustomerId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.ListCustomerTasksResponse{
		Tasks: tasks,
	}, nil
}

func (s *ActivityService) DeleteCustomerTask(ctx context.Context, request *customerpb.DeleteCustomerTaskRequest) (
	*customerpb.DeleteCustomerTaskResponse, error) {
	err := s.tl.Delete(ctx, &taskv2.DeleteTasksDatum{
		TaskID:  request.GetTaskId(),
		StaffID: request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.DeleteCustomerTaskResponse{}, nil
}

func (s *ActivityService) CreateLifeCycle(ctx context.Context, request *customerpb.CreateLifeCycleRequest) (
	*customerpb.CreateLifeCycleResponse, error) {
	id, err := s.lcl.Create(ctx, &lifecycle.CreateLifeCycleDatum{
		BusinessID: request.GetBusinessId(),
		CompanyID:  request.GetCompanyId(),
		StaffID:    request.GetStaffId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.CreateLifeCycleResponse{
		Id: id,
	}, nil
}

func (s *ActivityService) UpdateLifeCycles(ctx context.Context, request *customerpb.UpdateLifeCyclesRequest) (
	*customerpb.UpdateLifeCyclesResponse, error) {
	datumList := make([]*lifecycle.UpdateLifeCycleDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &lifecycle.UpdateLifeCycleDatum{
			ID:   update.GetId(),
			Name: update.Name,
			Sort: update.Sort,
		})
	}
	if err := s.lcl.Update(ctx, datumList, request.GetStaffId(), request.GetCompanyId()); err != nil {
		return nil, err
	}
	return &customerpb.UpdateLifeCyclesResponse{}, nil
}

func (s *ActivityService) ListLifeCycles(ctx context.Context, request *customerpb.ListLifeCyclesRequest) (
	*customerpb.ListLifeCyclesResponse, error) {
	lifeCycles, err := s.lcl.List(ctx, &lifecycle.ListLifeCyclesDatum{
		CompanyID: request.GetCompanyId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.ListLifeCyclesResponse{
		LifeCycles: lifeCycles,
	}, nil
}

func (s *ActivityService) DeleteLifeCycle(ctx context.Context, request *customerpb.DeleteLifeCycleRequest) (
	*customerpb.DeleteLifeCycleResponse, error) {
	if err := s.lcl.Delete(ctx, &lifecycle.DeleteLifeCycleDatum{
		ID:      request.GetId(),
		StaffID: request.GetStaffId(),
	}); err != nil {
		return nil, err
	}
	return &customerpb.DeleteLifeCycleResponse{}, nil
}

func (s *ActivityService) CreateActionState(ctx context.Context, request *customerpb.CreateActionStateRequest) (
	*customerpb.CreateActionStateResponse, error) {
	id, err := s.asl.Create(ctx, &actionstate.CreateActionStateDatum{
		BusinessID: request.GetBusinessId(),
		CompanyID:  request.GetCompanyId(),
		StaffID:    request.GetStaffId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
		Color:      request.GetColor(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.CreateActionStateResponse{
		Id: id,
	}, nil
}

func (s *ActivityService) UpdateActionStates(ctx context.Context, request *customerpb.UpdateActionStatesRequest) (
	*customerpb.UpdateActionsStatesResponse, error) {
	datumList := make([]*actionstate.UpdateActionStateDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &actionstate.UpdateActionStateDatum{
			ID:    update.GetId(),
			Name:  update.Name,
			Sort:  update.Sort,
			Color: update.Color,
		})
	}
	if err := s.asl.Update(ctx, datumList, request.GetStaffId(), request.GetCompanyId()); err != nil {
		return nil, err
	}
	return &customerpb.UpdateActionsStatesResponse{}, nil
}

func (s *ActivityService) ListActionStates(ctx context.Context, request *customerpb.ListActionStatesRequest) (
	*customerpb.ListActionStatesResponse, error) {
	actionStates, err := s.asl.List(ctx, &actionstate.ListActionStatesDatum{
		CompanyID: request.GetCompanyId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.ListActionStatesResponse{
		ActionStates: actionStates,
	}, nil
}

func (s *ActivityService) DeleteActionState(ctx context.Context, request *customerpb.DeleteActionStateRequest) (
	*customerpb.DeleteActionStateResponse, error) {
	if err := s.asl.Delete(ctx, &actionstate.DeleteActionStateDatum{
		ID:      request.GetId(),
		StaffID: request.GetStaffId(),
	}); err != nil {
		return nil, err
	}
	return &customerpb.DeleteActionStateResponse{}, nil
}

func (s *ActivityService) CreateTag(ctx context.Context, request *customerpb.CreateTagRequest) (
	*customerpb.CreateTagResponse, error) {
	id, err := s.tgl.Create(ctx, &tag.CreateTagDatum{
		CompanyID:  request.GetCompanyId(),
		BusinessID: request.GetBusinessId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
		StaffID:    request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.CreateTagResponse{
		Id: id,
	}, nil
}

func (s *ActivityService) UpdateTags(ctx context.Context, request *customerpb.UpdateTagsRequest) (
	*customerpb.UpdateTagsResponse, error) {
	datumList := make([]*tag.UpdateTagDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &tag.UpdateTagDatum{
			TagID:   update.GetId(),
			Name:    update.Name,
			Sort:    update.Sort,
			StaffID: request.GetStaffId(),
		})
	}
	if err := s.tgl.Update(ctx, datumList); err != nil {
		return nil, err
	}
	return &customerpb.UpdateTagsResponse{}, nil
}

func (s *ActivityService) ListTags(ctx context.Context, request *customerpb.ListTagsRequest) (
	*customerpb.ListTagsResponse, error) {
	datum := &tag.ListTagsDatum{}
	if request.GetFilter() != nil {
		datum.CompanyID = request.GetFilter().CompanyId
		datum.CustomerID = request.GetFilter().CustomerId
	}
	tags, err := s.tgl.List(ctx, datum)
	if err != nil {
		return nil, err
	}
	return &customerpb.ListTagsResponse{
		Tags: tags,
	}, nil
}

func (s *ActivityService) DeleteTag(ctx context.Context, request *customerpb.DeleteTagRequest) (
	*customerpb.DeleteTagResponse, error) {
	err := s.tgl.Delete(ctx, &tag.DeleteTagDatum{
		TagID:   request.GetId(),
		StaffID: request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.DeleteTagResponse{}, nil
}

func (s *ActivityService) CoverCustomerTag(ctx context.Context,
	request *customerpb.CoverCustomerTagRequest) (*customerpb.CoverCustomerTagResponse, error) {
	if err := s.arl.CoverCustomerTags(ctx, &activityrel.CoverCustomerTagsDatum{
		CompanyID:  request.CompanyId,
		BusinessID: request.BusinessId,
		CustomerID: request.CustomerId,
		StaffID:    request.StaffId,
		TagIDs:     request.TagIds,
	}); err != nil {
		return nil, err
	}
	return &customerpb.CoverCustomerTagResponse{}, nil
}

func (s *ActivityService) CreateNote(ctx context.Context, request *customerpb.CreateNoteRequest) (
	*customerpb.CreateNoteResponse, error) {
	id, err := s.nl.Create(ctx, &note.CreateNoteDatum{
		CompanyID:    request.GetCompanyId(),
		BusinessID:   request.GetBusinessId(),
		CustomerID:   request.GetCustomerId(),
		Note:         request.GetText(),
		CreateSource: request.GetSource(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.CreateNoteResponse{
		Id: id,
	}, nil
}

func (s *ActivityService) UpdateNotes(ctx context.Context, request *customerpb.UpdateNotesRequest) (
	*customerpb.UpdateNotesResponse, error) {
	if err := s.nl.Update(ctx, &note.UpdateNoteDatum{
		NoteID:       request.GetId(),
		Note:         request.Text,
		UpdateSource: request.Source,
	}); err != nil {
		return nil, err
	}
	return &customerpb.UpdateNotesResponse{}, nil
}

func (s *ActivityService) ListNotes(ctx context.Context, request *customerpb.ListNotesRequest) (
	*customerpb.ListNotesResponse, error) {
	notes, err := s.nl.List(ctx, &note.ListNotesDatum{
		CustomerID: request.GetFilter().GetCustomerId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.ListNotesResponse{
		Notes: notes,
	}, nil
}

func (s *ActivityService) DeleteNote(ctx context.Context, request *customerpb.DeleteNoteRequest) (
	*customerpb.DeleteNoteResponse, error) {
	err := s.nl.Delete(ctx, &note.DeleteNoteDatum{
		NoteID:  request.GetId(),
		StaffID: request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.DeleteNoteResponse{}, nil
}

func (s *ActivityService) CreateSource(ctx context.Context, request *customerpb.CreateSourceRequest) (
	*customerpb.CreateSourceResponse, error) {
	id, err := s.sl.Create(ctx, &source.CreateSourceDatum{
		CompanyID:  request.GetCompanyId(),
		BusinessID: request.GetBusinessId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
		StaffID:    request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.CreateSourceResponse{
		Id: id,
	}, nil
}

func (s *ActivityService) UpdateSources(ctx context.Context, request *customerpb.UpdateSourcesRequest) (
	*customerpb.UpdateSourcesResponse, error) {
	datumList := make([]*source.UpdateSourceDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &source.UpdateSourceDatum{
			SourceID: update.GetId(),
			Name:     update.Name,
			Sort:     update.Sort,
			StaffID:  request.GetStaffId(),
		})
	}
	if err := s.sl.Update(ctx, datumList); err != nil {
		return nil, err
	}
	return &customerpb.UpdateSourcesResponse{}, nil
}

func (s *ActivityService) ListSources(ctx context.Context, request *customerpb.ListSourcesRequest) (
	*customerpb.ListSourcesResponse, error) {
	sources, err := s.sl.List(ctx, &source.ListSourcesDatum{
		CompanyID: request.GetFilter().GetCompanyId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.ListSourcesResponse{
		Sources: sources,
	}, nil
}

func (s *ActivityService) DeleteSource(ctx context.Context, request *customerpb.DeleteSourceRequest) (
	*customerpb.DeleteSourceResponse, error) {
	err := s.sl.Delete(ctx, &source.DeleteSourceDatum{
		SourceID: request.GetId(),
		StaffID:  request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}
	return &customerpb.DeleteSourceResponse{}, nil
}
