load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "customer_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/customer/cdc",
        "//backend/app/customer/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/database/gorm/serializer",
        "//backend/common/rpc/database/kafka",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v1:customer",
        "//backend/proto/customer/v2:customer",
    ],
)

go_binary(
    name = "customer",
    embed = [":customer_lib"],
    visibility = ["//visibility:public"],
)
