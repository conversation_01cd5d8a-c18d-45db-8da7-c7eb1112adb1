load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "contact_tag",
    srcs = [
        "contact_tag.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
    ],
)
