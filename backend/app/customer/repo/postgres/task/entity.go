package task

import (
	"time"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// Task
type Task struct {
	ID         int64 `gorm:"column:id;primary_key"`
	CompanyID  int64 `gorm:"column:company_id"`
	BusinessID int64 `gorm:"column:business_id"`
	CustomerID int64 `gorm:"column:customer_id"`

	Name            string                `gorm:"column:name"`
	AllocateStaffID *int64                `gorm:"column:allocate_staff_id"`
	CompleteTime    *time.Time            `gorm:"column:complete_time"`
	State           customerpb.Task_State `gorm:"column:state;serializer:proto_enum"`

	CreateBy   int64      `gorm:"column:create_by"`
	UpdateBy   int64      `gorm:"column:update_by"`
	DeleteBy   *int64     `gorm:"column:delete_by"`
	CreateTime time.Time  `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL"`
	UpdateTime time.Time  `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL"`
	DeleteTime *time.Time `gorm:"column:delete_time"`
}

// TableName 表名
func (m *Task) TableName() string {
	return "task"
}
