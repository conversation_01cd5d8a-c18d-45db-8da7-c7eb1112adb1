package postgres

import (
	"encoding/base64"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

var (
	database *gorm.DB
	once     sync.Once
)

type Cursor struct {
	ID        int64     `json:"id"`         // 客户ID
	CreatedAt time.Time `json:"created_at"` // 创建时间
}

func (c *Cursor) EncodeCursor() string {
	bytes, err := sonic.Marshal(c)
	if err != nil {
		return ""
	}
	return base64.StdEncoding.EncodeToString(bytes)
}

func SetDB(db *gorm.DB) {
	database = db
}

func GetDB() *gorm.DB {
	if database == nil {
		NewDB()
	}
	return database
}

func NewDB() {
	once.Do(func() {
		db, err := igorm.NewClientProxy("postgres.moego_customer")
		if err != nil {
			panic(err)
		}
		database = db
	})
}
