load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "activity_log",
    srcs = [
        "activity_log.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_log",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_zap//:zap",
    ],
)
