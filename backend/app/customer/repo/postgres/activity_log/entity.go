package activitylog

import (
	"time"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// unit test
// mockgen -destination=./mock/gorm_mock.go -package=mock -source=gorm.go

// ActivityLog 商家客户历史记录表
type ActivityLog struct {
	ID                  int64                          `gorm:"column:id;primary_key"`
	CompanyID           int64                          `gorm:"column:company_id"`
	BusinessID          int64                          `gorm:"column:business_id"`
	CustomerID          int64                          `gorm:"column:customer_id"`
	CustomerName        string                         `gorm:"column:customer_name"`
	CustomerPhoneNumber string                         `gorm:"column:customer_phone_number"`
	Type                customerpb.ActivityLog_Type    `gorm:"column:type;serializer:proto_enum"`
	Action              *customerpb.ActivityLog_Action `gorm:"column:action;serializer:proto_json"`
	Source              *customerpb.SystemSource       `gorm:"column:source;serializer:proto_json"`
	CreateTime          time.Time                      `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL'"`
	UpdateTime          time.Time                      `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL"`
}

// TableName 表名
func (m *ActivityLog) TableName() string {
	return "activity_log"
}

type ListActivityLogsDatum struct {
	// filter field
	CustomerID      *int64
	ActivityLogType *customerpb.ActivityLog_Type
	CompanyID       *int64

	// page
	PageSize int
	PageNum  int
}
