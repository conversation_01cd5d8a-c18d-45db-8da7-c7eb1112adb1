package contacttagrel

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
)

type Repository interface {
	Create(ctx context.Context, contactTagRel *ContactTagRel) (*ContactTagRel, error)
	Get(ctx context.Context, contactID, tagID int64) (*ContactTagRel, error)
	List(ctx context.Context, filter *ListFilter) ([]*ContactTagRel, error)
	Delete(ctx context.Context, contactID, tagID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (r *impl) Create(ctx context.Context, contactTagRel *ContactTagRel) (*ContactTagRel, error) {
	err := r.db.WithContext(ctx).Create(contactTagRel).Error
	if err != nil {
		return nil, err
	}
	return contactTagRel, nil
}

func (r *impl) Get(ctx context.Context, contactID, tagID int64) (*ContactTagRel, error) {
	var rel ContactTagRel
	err := r.db.WithContext(ctx).
		Where("contact_id = ? AND tag_id = ? AND deleted_time IS NULL", contactID, tagID).
		First(&rel).Error
	if err != nil {
		return nil, err
	}
	return &rel, nil
}

func (r *impl) List(ctx context.Context, filter *ListFilter) ([]*ContactTagRel, error) {
	var rels []*ContactTagRel
	db := r.db.WithContext(ctx).Where("deleted_time IS NULL")
	if filter.ContactID != 0 {
		db = db.Where("contact_id = ?", filter.ContactID)
	}
	if filter.TagID != 0 {
		db = db.Where("tag_id = ?", filter.TagID)
	}
	err := db.Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

func (r *impl) Delete(ctx context.Context, contactID, tagID int64) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&ContactTagRel{}).
		Where("contact_id = ? AND tag_id = ? AND deleted_time IS NULL", contactID, tagID).
		Update("deleted_time", now).Error
}
