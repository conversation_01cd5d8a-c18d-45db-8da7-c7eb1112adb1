load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "contact_tag_rel",
    srcs = [
        "contact_tag_rel.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_rel",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "@io_gorm_gorm//:gorm",
    ],
)
