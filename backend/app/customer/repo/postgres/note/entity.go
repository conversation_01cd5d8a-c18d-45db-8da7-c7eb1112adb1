package note

import (
	"time"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Note struct {
	ID           int64                    `gorm:"column:id"`
	CompanyID    int64                    `gorm:"column:company_id"`
	BusinessID   int64                    `gorm:"column:business_id"`
	CustomerID   int64                    `gorm:"column:customer_id"`
	Note         string                   `gorm:"column:note"`
	CreateSource *customerpb.SystemSource `gorm:"column:create_source;serializer:proto_json"`
	UpdateSource *customerpb.SystemSource `gorm:"column:update_source;serializer:proto_json"`
	CreateBy     int64                    `gorm:"column:create_by"`
	UpdateBy     int64                    `gorm:"column:update_by"`
	DeleteBy     *int64                   `gorm:"column:delete_by"`
	CreateTime   time.Time                `gorm:"column:create_time"`
	UpdateTime   time.Time                `gorm:"column:update_time"`
	DeleteTime   *time.Time               `gorm:"column:delete_time"`
}

// TableName sets the insert table name for this struct type
func (n *Note) TableName() string {
	return "note"
}
