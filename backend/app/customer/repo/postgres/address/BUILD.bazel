load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "address",
    srcs = [
        "customer.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/proto/customer/v2:customer",
        "@com_github_lib_pq//:pq",
        "@io_gorm_gorm//:gorm",
    ],
)
