load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "activity_rel",
    srcs = [
        "activity_rel.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_rel",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
