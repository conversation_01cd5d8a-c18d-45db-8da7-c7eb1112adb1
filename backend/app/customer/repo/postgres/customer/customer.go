package customer

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Repository interface {
	Create(ctx context.Context, customer *Customer) (*Customer, error)
	Get(ctx context.Context, id int64, customerType customerpb.CustomerType) (*Customer, error)
	ListByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error)

	Update(ctx context.Context, customer *Customer) (*Customer, error)
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, customer *Customer) (*Customer, error) {
	err := i.db.WithContext(ctx).Create(customer).Error
	if err != nil {
		return nil, err
	}
	return customer, nil
}

func (i *impl) Get(ctx context.Context, id int64, customerType customerpb.CustomerType) (*Customer, error) {
	var customer Customer
	err := i.db.WithContext(ctx).
		Where("id = ?", id).
		Where("customer_type = ?", customerType.String()).
		Where("state = ?", customerpb.Customer_ACTIVE.String()).
		First(&customer).Error
	if err != nil {
		return nil, err
	}
	return &customer, nil
}

// ListWithCursor 基于游标分页的列表查询
func (i *impl) ListByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}
	query := i.db.WithContext(ctx).Model(&Customer{})
	query = i.applyFilter(query, filter)
	query = i.applyCursor(query, pagination.Cursor, orderBy)
	query = i.applyOrderBy(query, orderBy)

	// 多查一条用于判断是否有下一页
	query = query.Limit(int(pagination.PageSize + 1))

	var customers []*Customer
	if err := query.Find(&customers).Error; err != nil {
		return nil, err
	}

	hasNext := len(customers) > int(pagination.PageSize)
	if hasNext {
		customers = customers[:pagination.PageSize]
	}

	return &CursorResult{
		Data:       customers,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) applyFilter(query *gorm.DB, filter *ListFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	if filter.IDs != nil {
		query = query.Where("id IN (?)", filter.IDs)
	}

	if filter.OrganizationType != nil {
		query = query.Where("organization_type = ?", filter.OrganizationType.String())
	}

	if filter.OrganizationID != 0 {
		query = query.Where("organization_id = ?", filter.OrganizationID)
	}

	if filter.CustomerType != 0 {
		query = query.Where("customer_type = ?", filter.CustomerType.String())
	}

	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN (?)", states)
	}

	if len(filter.OwnerStaffIDs) > 0 {
		query = query.Where("owner_staff_id IN (?)", filter.OwnerStaffIDs)
	}

	if len(filter.LifecycleIDs) > 0 {
		query = query.Where("life_cycle_id IN (?)", filter.LifecycleIDs)
	}

	return query
}

func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *OrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == customerpb.ListCustomersRequest_Sorting_DESC {
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func (i *impl) applyOrderBy(query *gorm.DB, orderBy *OrderBy) *gorm.DB {
	if orderBy != nil {
		query = query.
			Order(fmt.Sprintf("%s %s, id %s", orderBy.Field.String(),
				orderBy.Direction.String(), orderBy.Direction.String()))
	} else {
		query = query.Order("created_time DESC, id DESC")
	}
	return query
}

func (i *impl) getTotalCount(ctx context.Context, filter *ListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&Customer{})
	query = i.applyFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (i *impl) Update(ctx context.Context, customer *Customer) (*Customer, error) {
	err := i.db.WithContext(ctx).Updates(customer).Error
	if err != nil {
		return nil, err
	}
	return customer, nil
}
