load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "source",
    srcs = [
        "entity.go",
        "source.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/source",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
