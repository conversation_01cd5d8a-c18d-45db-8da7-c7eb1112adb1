package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/utils"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type SalesService struct {
	salespb.UnimplementedSalesServiceServer
	l *sales.Logic
}

func NewSalesService() *SalesService {
	return &SalesService{
		l: sales.NewLogic(),
	}
}

func (s *SalesService) SyncOpportunity(ctx context.Context, req *salespb.SyncOpportunityRequest) (
	*salespb.SyncOpportunityResponse, error) {
	err := s.l.SyncOpportunity(ctx, &sales.OpportunitySyncParams{
		ID:                    req.OpportunityId,
		Email:                 req.Email,
		Tier:                  req.Tier,
		TerminalPercentage:    utils.DecimalToString(req.TerminalPercentage),
		TerminalFixed:         utils.DecimalToString(req.TerminalFixed),
		NonTerminalPercentage: utils.DecimalToString(req.NonTerminalPercentage),
		NonTerminalFixed:      utils.DecimalToString(req.NonTerminalFixed),
		MinVolume:             utils.DecimalToString(req.MinVolume),
		SPIF:                  utils.DecimalToString(req.Spif),
	})
	if err != nil {
		return nil, err
	}
	return &salespb.SyncOpportunityResponse{}, nil
}

func (s *SalesService) SyncSalesSubscription(ctx context.Context, req *salespb.SyncSalesSubscriptionRequest) (
	*salespb.SyncSalesSubscriptionResponse, error) {
	err := s.l.SyncSalesSubscription(ctx, req)
	if err != nil {
		return nil, err
	}
	return &salespb.SyncSalesSubscriptionResponse{}, nil
}

func (s *SalesService) SyncSalesHardware(ctx context.Context, req *salespb.SyncSalesHardwareRequest) (
	*salespb.SyncSalesHardwareResponse, error) {
	err := s.l.SyncSalesHardware(ctx, req)
	if err != nil {
		return nil, err
	}
	return &salespb.SyncSalesHardwareResponse{}, nil
}
