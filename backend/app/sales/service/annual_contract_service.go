package service

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type AnnualContractService struct {
	salespb.UnimplementedAnnualContractServiceServer
	al *sales.AnnualContractLogic
}

func NewAnnualContractService() *AnnualContractService {
	return &AnnualContractService{
		al: sales.NewAnnualContractLogic(),
	}
}

func (s *AnnualContractService) CreateAnnualContract(ctx context.Context, req *salespb.CreateAnnualContractRequest) (
	*salespb.AnnualContract, error) {

	if req.LocationCount <= 0 && req.VanCount <= 0 {
		return nil, status.Error(codes.InvalidArgument,
			"at least one of number of locations or vans must be greater than 0")
	}

	// check location number for bd
	switch req.GetSubscriptionPlan() {
	case salespb.SubscriptionPlan_GROWTH_BOARDING_DAYCARE,
		salespb.SubscriptionPlan_ULTIMATE_BOARDING_DAYCARE,
		salespb.SubscriptionPlan_ENTERPRISE_BOARDING_DAYCARE:
		if req.LocationCount <= 0 {
			return nil, status.Error(codes.InvalidArgument, "number of locations must be greater than 0 for BD plan")
		}
	}

	return s.al.CreateAnnualContract(ctx, req)
}

func (s *AnnualContractService) GetAnnualContract(ctx context.Context, req *salespb.GetAnnualContractRequest) (
	*salespb.AnnualContract, error) {
	return s.al.GetAnnualContract(ctx, req.Id)
}

func (s *AnnualContractService) ListAnnualContracts(ctx context.Context, req *salespb.ListAnnualContractsRequest) (
	*salespb.ListAnnualContractsResponse, error) {
	return s.al.ListAnnualContracts(ctx, req)
}

func (s *AnnualContractService) CountAnnualContracts(ctx context.Context, req *salespb.CountAnnualContractsRequest) (
	*salespb.CountAnnualContractsResponse, error) {
	count, err := s.al.CountAnnualContracts(ctx, req)
	if err != nil {
		return nil, err
	}
	return &salespb.CountAnnualContractsResponse{
		Count: count,
	}, nil
}

func (s *AnnualContractService) SignAnnualContract(ctx context.Context, req *salespb.SignAnnualContractRequest) (
	*salespb.SignAnnualContractResponse, error) {
	contract, err := s.al.SignAnnualContract(ctx, req)
	if err != nil {
		return nil, err
	}

	return &salespb.SignAnnualContractResponse{
		Contract: contract,
	}, nil
}

func (s *AnnualContractService) DeleteAnnualContract(ctx context.Context, req *salespb.DeleteAnnualContractRequest) (
	*emptypb.Empty, error) {
	err := s.al.DeleteAnnualContract(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
