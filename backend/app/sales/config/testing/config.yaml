secrets:
  - name: 'moego/testing/datasource'
    prefix: 'secret.datasource.'
  - name: 'moego/testing/salesforce'
    prefix: 'secret.salesforce.'

server:
  filter:
    - opentelemetry
    - debuglog
    - recovery
    - validation
  service:
    - name: backend.proto.sales.v1.SalesService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
    - name: backend.proto.sales.v1.AnnualContractService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
    - name: backend.proto.sales.v1.MoegoPayContractService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
    - name: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 60000
  service:
    - callee: moego-server-payment
      target: http://moego-service-payment:9204
      protocol: http
    - callee: moego-svc-account
      target: dns://moego-svc-account:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: https://hooks.slack.com
      target: https://hooks.slack.com
      protocol: http
      ca_cert: none
    - callee: salesforce-api
      target: https://${secret.salesforce.instance_url}
      protocol: http
      ca_cert: none
    - callee: postgres.moego_sales
      target: dsn://postgresql://${secret.datasource.postgres.moego_sales.username}:${secret.datasource.postgres.moego_sales.password}@${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_sales
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_sales
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
