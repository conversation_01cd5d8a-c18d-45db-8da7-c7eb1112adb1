load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "sales_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/sales/config",
        "//backend/app/sales/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/sales/v1:sales",
    ],
)

go_binary(
    name = "sales",
    embed = [":sales_lib"],
    visibility = ["//visibility:public"],
)
