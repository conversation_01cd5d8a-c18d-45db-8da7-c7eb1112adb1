load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "payment",
    srcs = [
        "moego_pay_custom_fee.go",
        "payment.go",
        "price_config.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/payment",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/http",
        "//backend/proto/sales/v1:sales",
        "@com_github_shopspring_decimal//:decimal",
    ],
)
