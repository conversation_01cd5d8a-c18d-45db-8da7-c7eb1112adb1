package payment

import (
	"context"
	"strconv"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

type MoegoPayCustomFee struct {
	CompanyID             *int64   `json:"companyId"`
	MinVolume             *int64   `json:"customizedMinVol"`
	NonTerminalPercentage *float64 `json:"onlineFeeRate"`
	NonTerminalFixedCents *int64   `json:"onlineFeeCents"`
	TerminalPercentage    *float64 `json:"readerFeeRate"`
	TerminalFixedCents    *int64   `json:"readerFeeCents"`
}

type MoegoPayCustomFeeClient interface {
	GetByCompanyID(ctx context.Context, companyID int64) (*MoegoPayCustomFee, error)
	Create(ctx context.Context, customFee *MoegoPayCustomFee) error
}

type moegoPayCustomFeeClientImpl struct {
	client http.Client
}

func (m moegoPayCustomFeeClientImpl) GetByCompanyID(ctx context.Context, companyID int64) (*MoegoPayCustomFee, error) {
	customFee := &MoegoPayCustomFee{}
	path := "/service/payment/customize-payment-setting?companyId=" + strconv.FormatInt(companyID, 10)
	err := m.client.Get(ctx, path, customFee)
	if err != nil {
		return nil, err
	}
	// not exist
	if customFee.CompanyID == nil {
		return nil, nil
	}
	return customFee, nil
}

func (m moegoPayCustomFeeClientImpl) Create(ctx context.Context, customFee *MoegoPayCustomFee) error {
	return m.client.Post(ctx, "/service/payment/customize-payment-setting", customFee, nil)
}

func NewMoegoPayCustomFeeClient() MoegoPayCustomFeeClient {
	return &moegoPayCustomFeeClientImpl{
		client: GetClient(),
	}
}
