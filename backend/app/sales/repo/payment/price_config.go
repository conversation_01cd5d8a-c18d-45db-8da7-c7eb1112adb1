package payment

import (
	"context"

	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type PriceLevel int32

const (
	PriceLevelGrowthGrooming   PriceLevel = 1101
	PriceLevelUltimateGrooming PriceLevel = 1201
	PriceLevelGrowthBD         PriceLevel = 1102
	PriceLevelUltimateBD       PriceLevel = 1202
	PriceLevelEnterpriseBD     PriceLevel = 1302
)

type BusinessType int32

const (
	BusinessTypeMobile BusinessType = 0
	BusinessTypeSalon  BusinessType = 1
)

func getPriceLevel(plan salespb.SubscriptionPlan) PriceLevel {
	switch plan {
	case salespb.SubscriptionPlan_GROWTH_GROOMING:
		return PriceLevelGrowthGrooming
	case salespb.SubscriptionPlan_ULTIMATE_GROOMING:
		return PriceLevelUltimateGrooming
	case salespb.SubscriptionPlan_GROWTH_BOARDING_DAYCARE:
		return PriceLevelGrowthBD
	case salespb.SubscriptionPlan_ULTIMATE_BOARDING_DAYCARE:
		return PriceLevelUltimateBD
	case salespb.SubscriptionPlan_ENTERPRISE_BOARDING_DAYCARE:
		return PriceLevelEnterpriseBD
	}
	return 0
}

type Price struct {
	Location decimal.Decimal
	Van      decimal.Decimal
	Level    PriceLevel
}

type PriceConfig struct {
	ID           int64        `json:"id"`
	StripePlanID string       `json:"stripePlanId"`
	PlanName     string       `json:"planName"`
	Price        float64      `json:"price"`
	Title        string       `json:"title"`
	Description  string       `json:"description"`
	BusinessType BusinessType `json:"businessType"`
	Level        PriceLevel   `json:"level"`
}

type ListPriceConfigParams struct {
	IsAll bool       `json:"isAll"`
	Level PriceLevel `json:"level"`
}

type PriceConfigClient interface {
	GetPrice(ctx context.Context, plan salespb.SubscriptionPlan) (*Price, error)
}

type priceConfigClientImpl struct {
	client http.Client
}

func (i *priceConfigClientImpl) GetPrice(ctx context.Context, plan salespb.SubscriptionPlan) (*Price, error) {
	params := ListPriceConfigParams{
		Level: getPriceLevel(plan),
	}
	var configs []*PriceConfig
	err := i.client.Post(ctx, "/service/payment/subscription/plans", params, &configs)
	if err != nil {
		return nil, err
	}

	locationPrice := decimal.Zero
	vanPrice := decimal.Zero

	for _, c := range configs {
		switch c.BusinessType {
		case BusinessTypeSalon:
			locationPrice = decimal.NewFromFloat(c.Price)
		case BusinessTypeMobile:
			vanPrice = decimal.NewFromFloat(c.Price)
		}
	}
	return &Price{
		Location: locationPrice,
		Van:      vanPrice,
		Level:    params.Level,
	}, nil
}

func NewPriceConfigClient() PriceConfigClient {
	return &priceConfigClientImpl{
		client: GetClient(),
	}
}
