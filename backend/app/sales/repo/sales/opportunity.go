package sales

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	"github.com/MoeGolibrary/moego/backend/app/sales/utils"
)

type Opportunity struct {
	ID                    string     `gorm:"column:id"`
	Email                 *string    `gorm:"column:email"`
	Tier                  *string    `gorm:"column:tier"`
	TerminalPercentage    *string    `gorm:"column:terminal_percentage"`
	TerminalFixed         *string    `gorm:"column:terminal_fixed"`
	NonTerminalPercentage *string    `gorm:"column:non_terminal_percentage"`
	NonTerminalFixed      *string    `gorm:"column:non_terminal_fixed"`
	MinVolume             *string    `gorm:"column:min_volume"`
	SPIF                  *string    `gorm:"column:spif"`
	CreatedAt             *time.Time `gorm:"column:created_at"`
	UpdatedAt             *time.Time `gorm:"column:updated_at"`
}

func (*Opportunity) TableName() string {
	return "moego_sales.public.opportunity"
}

func (o *Opportunity) ToSalesforceEntity() *salesforce.Opportunity {
	return &salesforce.Opportunity{
		ID:                    &o.ID,
		Email:                 o.Email,
		Tier:                  o.Tier,
		TerminalPercentage:    utils.StringToFloat(o.TerminalPercentage),
		TerminalFixed:         utils.StringToFloat(o.TerminalFixed),
		NonTerminalPercentage: utils.StringToFloat(o.NonTerminalPercentage),
		NonTerminalFixed:      utils.StringToFloat(o.NonTerminalFixed),
		MinVolume:             utils.StringToFloat(o.MinVolume),
		SPIF:                  utils.StringToFloat(o.SPIF),
	}
}

type OpportunityReadWriter interface {
	Get(ctx context.Context, id string) (*Opportunity, error)
	Save(ctx context.Context, op *Opportunity) error
}

type opportunityImpl struct {
	db *gorm.DB
}

func NewOpportunityRW() OpportunityReadWriter {
	return &opportunityImpl{
		db: NewDB(),
	}
}

func (i *opportunityImpl) Get(ctx context.Context, id string) (*Opportunity, error) {
	op := &Opportunity{}
	if err := i.db.WithContext(ctx).Where("id = ?", id).First(op).Error; err != nil {
		return nil, err
	}
	return op, nil
}

func (i *opportunityImpl) Save(ctx context.Context, op *Opportunity) error {
	return i.db.WithContext(ctx).Save(op).Error
}
