// Code generated by MockGen. DO NOT EDIT.
// Source: ./organization/organization.go
//
// Generated by this command:
//
//	mockgen -source=./organization/organization.go -destination=mock/./organization/organization_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockOrganization is a mock of Organization interface.
type MockOrganization struct {
	ctrl     *gomock.Controller
	recorder *MockOrganizationMockRecorder
	isgomock struct{}
}

// MockOrganizationMockRecorder is the mock recorder for MockOrganization.
type MockOrganizationMockRecorder struct {
	mock *MockOrganization
}

// NewMockOrganization creates a new mock instance.
func NewMockOrganization(ctrl *gomock.Controller) *MockOrganization {
	mock := &MockOrganization{ctrl: ctrl}
	mock.recorder = &MockOrganizationMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrganization) EXPECT() *MockOrganizationMockRecorder {
	return m.recorder
}

// GetCompany mocks base method.
func (m *MockOrganization) GetCompany(ctx context.Context, id int64) (*organizationpb.CompanyModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCompany", ctx, id)
	ret0, _ := ret[0].(*organizationpb.CompanyModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCompany indicates an expected call of GetCompany.
func (mr *MockOrganizationMockRecorder) GetCompany(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompany", reflect.TypeOf((*MockOrganization)(nil).GetCompany), ctx, id)
}

// GetCompanySetting mocks base method.
func (m *MockOrganization) GetCompanySetting(ctx context.Context, id int64) (*organizationpb.CompanyPreferenceSettingModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCompanySetting", ctx, id)
	ret0, _ := ret[0].(*organizationpb.CompanyPreferenceSettingModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCompanySetting indicates an expected call of GetCompanySetting.
func (mr *MockOrganizationMockRecorder) GetCompanySetting(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompanySetting", reflect.TypeOf((*MockOrganization)(nil).GetCompanySetting), ctx, id)
}

// GetLocations mocks base method.
func (m *MockOrganization) GetLocations(ctx context.Context, companyID int64) ([]*organizationpb.LocationBriefView, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocations", ctx, companyID)
	ret0, _ := ret[0].([]*organizationpb.LocationBriefView)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocations indicates an expected call of GetLocations.
func (mr *MockOrganizationMockRecorder) GetLocations(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocations", reflect.TypeOf((*MockOrganization)(nil).GetLocations), ctx, companyID)
}
