// Code generated by MockGen. DO NOT EDIT.
// Source: ./sales/contract.go
//
// Generated by this command:
//
//	mockgen -source=./sales/contract.go -destination=mock/./sales/contract_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	sales "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	gomock "go.uber.org/mock/gomock"
)

// MockContractReadWriter is a mock of ContractReadWriter interface.
type MockContractReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockContractReadWriterMockRecorder
	isgomock struct{}
}

// MockContractReadWriterMockRecorder is the mock recorder for MockContractReadWriter.
type MockContractReadWriterMockRecorder struct {
	mock *MockContractReadWriter
}

// NewMockContractReadWriter creates a new mock instance.
func NewMockContractReadWriter(ctrl *gomock.Controller) *MockContractReadWriter {
	mock := &MockContractReadWriter{ctrl: ctrl}
	mock.recorder = &MockContractReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockContractReadWriter) EXPECT() *MockContractReadWriterMockRecorder {
	return m.recorder
}

// Count mocks base method.
func (m *MockContractReadWriter) Count(ctx context.Context, opts ...sales.ContractQueryFilter) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Count", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockContractReadWriterMockRecorder) Count(ctx any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockContractReadWriter)(nil).Count), varargs...)
}

// Delete mocks base method.
func (m *MockContractReadWriter) Delete(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockContractReadWriterMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockContractReadWriter)(nil).Delete), ctx, id)
}

// Get mocks base method.
func (m *MockContractReadWriter) Get(ctx context.Context, id string) (*sales.Contract, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*sales.Contract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockContractReadWriterMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockContractReadWriter)(nil).Get), ctx, id)
}

// List mocks base method.
func (m *MockContractReadWriter) List(ctx context.Context, page sales.Page[*sales.Contract], opts ...sales.ContractQueryFilter) ([]*sales.Contract, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, page}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].([]*sales.Contract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockContractReadWriterMockRecorder) List(ctx, page any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, page}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockContractReadWriter)(nil).List), varargs...)
}

// Save mocks base method.
func (m *MockContractReadWriter) Save(ctx context.Context, contract *sales.Contract) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, contract)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockContractReadWriterMockRecorder) Save(ctx, contract any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockContractReadWriter)(nil).Save), ctx, contract)
}
