load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "payment",
    srcs = [
        "moego_pay_custom_fee_mock.go",
        "payment_mock.go",
        "price_config_mock.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/payment",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/sales/repo/payment",
        "//backend/proto/sales/v1:sales",
        "@org_uber_go_mock//gomock",
    ],
)
