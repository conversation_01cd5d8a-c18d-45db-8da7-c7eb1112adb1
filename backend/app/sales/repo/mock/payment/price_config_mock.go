// Code generated by MockGen. DO NOT EDIT.
// Source: ./payment/price_config.go
//
// Generated by this command:
//
//	mockgen -source=./payment/price_config.go -destination=mock/./payment/price_config_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	payment "github.com/MoeGolibrary/moego/backend/app/sales/repo/payment"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockPriceConfigClient is a mock of PriceConfigClient interface.
type MockPriceConfigClient struct {
	ctrl     *gomock.Controller
	recorder *MockPriceConfigClientMockRecorder
	isgomock struct{}
}

// MockPriceConfigClientMockRecorder is the mock recorder for MockPriceConfigClient.
type MockPriceConfigClientMockRecorder struct {
	mock *MockPriceConfigClient
}

// NewMockPriceConfigClient creates a new mock instance.
func NewMockPriceConfigClient(ctrl *gomock.Controller) *MockPriceConfigClient {
	mock := &MockPriceConfigClient{ctrl: ctrl}
	mock.recorder = &MockPriceConfigClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPriceConfigClient) EXPECT() *MockPriceConfigClientMockRecorder {
	return m.recorder
}

// GetPrice mocks base method.
func (m *MockPriceConfigClient) GetPrice(ctx context.Context, plan salespb.SubscriptionPlan) (*payment.Price, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrice", ctx, plan)
	ret0, _ := ret[0].(*payment.Price)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrice indicates an expected call of GetPrice.
func (mr *MockPriceConfigClientMockRecorder) GetPrice(ctx, plan any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrice", reflect.TypeOf((*MockPriceConfigClient)(nil).GetPrice), ctx, plan)
}
