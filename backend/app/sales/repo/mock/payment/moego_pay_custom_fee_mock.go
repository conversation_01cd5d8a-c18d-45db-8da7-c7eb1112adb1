// Code generated by MockGen. DO NOT EDIT.
// Source: ./payment/moego_pay_custom_fee.go
//
// Generated by this command:
//
//	mockgen -source=./payment/moego_pay_custom_fee.go -destination=mock/./payment/moego_pay_custom_fee_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	payment "github.com/MoeGolibrary/moego/backend/app/sales/repo/payment"
	gomock "go.uber.org/mock/gomock"
)

// MockMoegoPayCustomFeeClient is a mock of MoegoPayCustomFeeClient interface.
type MockMoegoPayCustomFeeClient struct {
	ctrl     *gomock.Controller
	recorder *MockMoegoPayCustomFeeClientMockRecorder
	isgomock struct{}
}

// MockMoegoPayCustomFeeClientMockRecorder is the mock recorder for MockMoegoPayCustomFeeClient.
type MockMoegoPayCustomFeeClientMockRecorder struct {
	mock *MockMoegoPayCustomFeeClient
}

// NewMockMoegoPayCustomFeeClient creates a new mock instance.
func NewMockMoegoPayCustomFeeClient(ctrl *gomock.Controller) *MockMoegoPayCustomFeeClient {
	mock := &MockMoegoPayCustomFeeClient{ctrl: ctrl}
	mock.recorder = &MockMoegoPayCustomFeeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMoegoPayCustomFeeClient) EXPECT() *MockMoegoPayCustomFeeClientMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockMoegoPayCustomFeeClient) Create(ctx context.Context, customFee *payment.MoegoPayCustomFee) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, customFee)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockMoegoPayCustomFeeClientMockRecorder) Create(ctx, customFee any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockMoegoPayCustomFeeClient)(nil).Create), ctx, customFee)
}

// GetByCompanyID mocks base method.
func (m *MockMoegoPayCustomFeeClient) GetByCompanyID(ctx context.Context, companyID int64) (*payment.MoegoPayCustomFee, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCompanyID", ctx, companyID)
	ret0, _ := ret[0].(*payment.MoegoPayCustomFee)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCompanyID indicates an expected call of GetByCompanyID.
func (mr *MockMoegoPayCustomFeeClientMockRecorder) GetByCompanyID(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCompanyID", reflect.TypeOf((*MockMoegoPayCustomFeeClient)(nil).GetByCompanyID), ctx, companyID)
}
