package utils

import (
	"encoding/json"
	"reflect"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	decimalpb "google.golang.org/genproto/googleapis/type/decimal"
	moneypb "google.golang.org/genproto/googleapis/type/money"
	"gorm.io/datatypes"
)

func Int64ToString(i *int64) *string {
	if i == nil {
		return nil
	}
	v := strconv.FormatInt(*i, 10)
	return &v
}

func StringToFloat(s *string) *float64 {
	if s == nil {
		return nil
	}
	f, err := strconv.ParseFloat(*s, 64)
	if err != nil {
		return nil
	}
	return &f
}

func DecimalToString(d *decimalpb.Decimal) *string {
	if d == nil {
		return nil
	}
	return &d.Value
}

func DecimalToFloat(d *decimalpb.Decimal) *float64 {
	if d == nil {
		return nil
	}
	f, err := strconv.ParseFloat(d.Value, 64)
	if err != nil {
		return nil
	}
	return &f
}

func MoneyToFloat(m *moneypb.Money) float64 {
	if m == nil {
		return 0
	}
	result, _ := decimal.NewFromInt(m.Units).Add(decimal.NewFromInt32(m.Nanos).Shift(-9)).Float64()
	return result
}

func ToJSON(o any) string {
	result, err := json.Marshal(o)
	if err != nil {
		return ""
	}
	return string(result)
}

func ParseStructJSONTagNames(s interface{}) []string {
	v := reflect.TypeOf(s)

	fields := make([]string, 0, v.NumField())
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		tag := field.Tag.Get("json")
		if len(tag) == 0 || tag == "-" {
			continue
		}

		name := strings.Split(tag, ",")[0]
		if len(name) == 0 {
			continue
		}
		fields = append(fields, name)
	}
	return fields
}

func NewUUID() string {
	id, _ := uuid.NewV7()
	return strings.ReplaceAll(id.String(), "-", "")
}

func ToJSONB(v any) datatypes.JSON {
	bytes, _ := json.Marshal(v)
	return bytes
}
