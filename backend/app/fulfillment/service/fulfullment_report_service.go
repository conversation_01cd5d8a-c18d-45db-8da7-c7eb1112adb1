package service

import (
	"context"

	fulfillmentreport "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/fulfillment_report"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type FulfillmentReportService struct {
	fulfillmentReport *fulfillmentreport.Logic
	pb.UnimplementedFulfillmentReportServiceServer
}

func NewFulfillmentReportService() *FulfillmentReportService {
	return &FulfillmentReportService{
		fulfillmentReport: fulfillmentreport.New(),
	}
}

func (s *FulfillmentReportService) GetFulfillmentReportTemplate(ctx context.Context,
	req *pb.GetFulfillmentReportTemplateRequest) (*pb.GetFulfillmentReportTemplateResponse, error) {
	return s.fulfillmentReport.GetFulfillmentReportTemplate(ctx, req)
}

func (s *FulfillmentReportService) UpdateFulfillmentReportTemplate(ctx context.Context,
	req *pb.UpdateFulfillmentReportTemplateRequest) (*pb.UpdateFulfillmentReportTemplateResponse, error) {
	return s.fulfillmentReport.UpdateFulfillmentReportTemplate(ctx, req)
}
