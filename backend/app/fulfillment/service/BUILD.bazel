load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "appointment_service.go",
        "fulfillment_service.go",
        "fulfullment_report_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/logic/appointment",
        "//backend/app/fulfillment/logic/fulfillment",
        "//backend/app/fulfillment/logic/fulfillment_report",
        "//backend/common/rpc/framework/log",
        "//backend/proto/fulfillment/v1:fulfillment",
    ],
)
