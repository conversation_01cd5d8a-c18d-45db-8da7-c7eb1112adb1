package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/appointment"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type AppointmentService struct {
	// TODO(aiden): 后面需要删掉
	pb.UnimplementedAppointmentServiceServer
	appointment *appointment.Logic
}

func NewAppointmentService() *AppointmentService {
	return &AppointmentService{
		appointment: appointment.New(),
	}
}

func (a *AppointmentService) CreateAppointment(ctx context.Context,
	req *pb.CreateAppointmentRequest) (*pb.CreateAppointmentResponse, error) {
	return a.appointment.CreateAppointment(ctx, req)
}

func (a *AppointmentService) ListAppointment(ctx context.Context,
	req *pb.ListAppointmentRequest) (*pb.ListAppointmentResponse, error) {
	// TODO: 需实现，grpc需要先注册方法
	log.InfoContextf(ctx, "ListAppointment req:%+v", req)
	return &pb.ListAppointmentResponse{}, nil
}

func (a *AppointmentService) UpdateAppointment(ctx context.Context,
	req *pb.UpdateAppointmentRequest) (*pb.UpdateAppointmentResponse, error) {
	// TODO: 需实现，grpc需要先注册方法
	log.InfoContextf(ctx, "UpdateAppointment req:%+v", req)
	return &pb.UpdateAppointmentResponse{}, nil
}

func (a *AppointmentService) GetAppointmentByIDs(ctx context.Context,
	req *pb.GetAppointmentByIDsRequest) (*pb.GetAppointmentByIDsResponse, error) {
	// TODO: 需实现，grpc需要先注册方法
	log.InfoContextf(ctx, "GetAppointmentByIDs req:%+v", req)
	return &pb.GetAppointmentByIDsResponse{}, nil
}
