package main

import (
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

func main() {
	s := rpc.NewServer()

	// 这里需要注册grpc服务
	grpc.Register(s, &fulfillmentpb.FulfillmentService_ServiceDesc, service.NewFulfillmentService())
	grpc.Register(s, &fulfillmentpb.AppointmentService_ServiceDesc, service.NewAppointmentService())
	grpc.Register(s, &fulfillmentpb.FulfillmentReportService_ServiceDesc, service.NewFulfillmentReportService())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
