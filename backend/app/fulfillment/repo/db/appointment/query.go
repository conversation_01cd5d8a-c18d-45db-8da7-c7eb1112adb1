package appointment

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error) {
	if param == nil {
		return 0, nil
	}
	query := i.buildQuery(ctx, param, filter)
	var count int64
	if err := query.Count(&count).Error; err != nil {
		log.ErrorContextf(ctx, "GetAppointmentListCount err, err:%+v", err)
		return 0, err
	}
	return count, nil
}

func (i *impl) List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Appointment, error) {
	if param == nil {
		return []*Appointment{}, nil
	}
	query := i.buildQuery(ctx, param, filter)
	var appointmentList []*Appointment
	offset, limit := buildPaginationInfo(param.PaginationInfo)
	if err := query.
		Offset(int(offset)).
		Limit(int(limit)).
		Order(ColumnCreatedAt + " desc").
		Find(&appointmentList).Error; err != nil {
		log.ErrorContextf(ctx, "GetAppointmentList err, err:%+v", err)
		return nil, err
	}
	return appointmentList, nil
}

func (i *impl) GetByID(ctx context.Context, id int) (*Appointment, error) {
	var appointment Appointment
	if err := i.db.WithContext(ctx).Where(ColumnID+" = ?", id).First(&appointment).Error; err != nil {
		return nil, err
	}
	return &appointment, nil
}

func buildPaginationInfo(paginationInfo *PaginationInfo) (int32, int32) {
	if paginationInfo == nil {
		return defaultOffset, defaultLimit
	}
	return paginationInfo.Offset, paginationInfo.Limit
}

func (i *impl) buildQuery(ctx context.Context, param *BaseParam, filter *Filter) *gorm.DB {
	query := i.db.WithContext(ctx).Model(&Appointment{})
	query = i.applyBaseParamFilters(query, param)
	if filter != nil {
		query = i.applyFilterConditions(query, filter)
	}
	return query
}

// applyBaseParamFilters 应用BaseParam过滤条件
func (i *impl) applyBaseParamFilters(query *gorm.DB, param *BaseParam) *gorm.DB {
	if param.BusinessID != 0 {
		query = query.Where(ColumnBusinessID+" = ?", param.BusinessID)
	}
	if param.CompanyID != 0 {
		query = query.Where(ColumnCompanyID+" = ?", param.CompanyID)
	}
	query = query.Where(ColumnStartTime+" >= ? AND "+ColumnEndTime+" <= ?", param.StartTime, param.EndTime)
	return query
}

// applyFilterConditions 应用Filter过滤条件
func (i *impl) applyFilterConditions(query *gorm.DB, filter *Filter) *gorm.DB {
	if len(filter.CustomerIDs) > 0 {
		query = query.Where(ColumnCustomerID+" IN ?", filter.CustomerIDs)
	}
	if len(filter.Statuses) > 0 {
		query = query.Where(ColumnStatus+" IN ?", filter.Statuses)
	}
	if len(filter.ServiceItemTypes) > 0 {
		query = query.Where(ColumnServiceItemType+" IN ?", filter.ServiceItemTypes)
	}
	if len(filter.BusinessIDs) > 0 {
		query = query.Where(ColumnBusinessID+" IN ?", filter.BusinessIDs)
	}
	return query
}
