package appointment

import (
	"context"

	"gorm.io/gorm"
)

const (
	defaultLimit  = 200
	defaultOffset = 0
)

type ReadWriter interface {
	List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Appointment, error)
	Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error)
	BatchCreate(ctx context.Context, appointments []*Appointment) error
	Update(ctx context.Context, appointment *Appointment) error
	GetByID(ctx context.Context, id int) (*Appointment, error)
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	// TODO(aiden) 确认好aws数据库配置结构再添加
	db := &gorm.DB{}
	return &impl{db: db}
}
