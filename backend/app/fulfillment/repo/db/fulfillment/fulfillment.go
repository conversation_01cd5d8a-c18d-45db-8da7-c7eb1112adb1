package fulfillment

import (
	"context"

	"gorm.io/gorm"
)

const (
	defaultLimit  = 200
	defaultOffset = 0
)

type ReadWriter interface {
	List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Fulfillment, error)
	Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error)
	BatchCreate(ctx context.Context, fulfillments []*Fulfillment) error
	Update(ctx context.Context, fulfillment *Fulfillment) error
	DeleteByServiceInstanceID(ctx context.Context, serviceInstanceID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	// TODO(aiden) 确认好aws数据库配置结构再添加
	db := &gorm.DB{}
	return &impl{db: db}
}
