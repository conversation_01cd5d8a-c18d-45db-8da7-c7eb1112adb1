package serviceinstance

import (
	"context"

	"gorm.io/gorm"
)

// List 基于BaseParam和Filter查询ServiceInstance记录
func (i *impl) List(ctx context.Context, baseParam *BaseParam, filter *Filter) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance

	// 使用Model方法，让GORM自动处理表名和字段映射
	query := i.db.WithContext(ctx).Model(&ServiceInstance{})

	if baseParam == nil {
		return []*ServiceInstance{}, nil
	}

	// 应用BaseParam过滤条件
	query = i.applyBaseParamFilters(query, baseParam)

	// 应用Filter过滤条件
	if filter != nil {
		query = i.applyFilterConditions(query, filter)
	}

	// 应用分页
	query = i.applyPagination(query, baseParam.PaginationInfo)

	// 按创建时间倒序排列
	query = query.Order(ColumnCreatedAt + " DESC")

	err := query.Find(&instances).Error
	if err != nil {
		return nil, err
	}

	return instances, nil
}

// applyBaseParamFilters 应用BaseParam过滤条件
func (i *impl) applyBaseParamFilters(query *gorm.DB, baseParam *BaseParam) *gorm.DB {
	if baseParam.BusinessID != 0 {
		query = query.Where(ColumnBusinessID+" = ?", baseParam.BusinessID)
	}
	if baseParam.CompanyID != 0 {
		query = query.Where(ColumnCompanyID+" = ?", baseParam.CompanyID)
	}
	if !baseParam.StartTime.IsZero() {
		query = query.Where(ColumnCreatedAt+" >= ?", baseParam.StartTime)
	}
	if !baseParam.EndTime.IsZero() {
		query = query.Where(ColumnCreatedAt+" <= ?", baseParam.EndTime)
	}
	return query
}

// applyFilterConditions 应用Filter过滤条件
func (i *impl) applyFilterConditions(query *gorm.DB, filter *Filter) *gorm.DB {
	if len(filter.PetIDs) > 0 {
		query = query.Where(ColumnPetID+" IN ?", filter.PetIDs)
	}
	if len(filter.CustomerIDs) > 0 {
		query = query.Where(ColumnCustomerID+" IN ?", filter.CustomerIDs)
	}
	if len(filter.CareTypes) > 0 {
		query = query.Where(ColumnCareType+" IN ?", filter.CareTypes)
	}
	if len(filter.DateTypes) > 0 {
		query = query.Where(ColumnDateType+" IN ?", filter.DateTypes)
	}
	if len(filter.RootIDs) > 0 {
		query = query.Where(ColumnRootID+" IN ?", filter.RootIDs)
	}
	return query
}

// applyPagination 应用分页条件
func (i *impl) applyPagination(query *gorm.DB, paginationInfo *PaginationInfo) *gorm.DB {
	if paginationInfo != nil {
		if paginationInfo.Offset > 0 {
			query = query.Offset(int(paginationInfo.Offset))
		}
		if paginationInfo.Limit > 0 {
			query = query.Limit(int(paginationInfo.Limit))
		}
	}
	return query
}

func (i *impl) GetByID(ctx context.Context, id int) (*ServiceInstance, error) {
	var instance ServiceInstance
	if err := i.db.WithContext(ctx).Where(ColumnID+" = ?", id).First(&instance).Error; err != nil {
		return nil, err
	}
	return &instance, nil
}

func (i *impl) Delete(ctx context.Context, id int) error {
	return i.db.WithContext(ctx).Where(ColumnID+" = ?", id).Delete(&ServiceInstance{}).Error
}

func (i *impl) GetByAppointmentID(ctx context.Context, appointmentID int) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance
	if err := i.db.WithContext(ctx).Where(ColumnAppointmentID+" = ?",
		appointmentID).Find(&instances).Error; err != nil {
		return nil, err
	}
	return instances, nil
}
