package serviceinstance

import (
	"context"

	"gorm.io/gorm"
)

type ReadWriter interface {
	Create(ctx context.Context, si *ServiceInstance) (int, error)
	BatchCreate(ctx context.Context, serviceInstances []*ServiceInstance) error
	Update(ctx context.Context, si *ServiceInstance) error
	List(ctx context.Context, baseParam *BaseParam, filter *Filter) ([]*ServiceInstance, error)
	GetByID(ctx context.Context, id int) (*ServiceInstance, error)
	Delete(ctx context.Context, id int) error
	GetByAppointmentID(ctx context.Context, appointmentID int) ([]*ServiceInstance, error)
}

type impl struct {
	db *gorm.DB
}

func New(db *gorm.DB) ReadWriter {
	return &impl{db: db}
}
