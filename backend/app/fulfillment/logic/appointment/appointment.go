package appointment

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	fulfillmentRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

const (
	updateMessage = "更新成功"
)

func New() *Logic {
	// TODO(aiden) 确认好aws数据库配置结构再添加
	db := &gorm.DB{}
	return &Logic{
		appointmentCli:     appointment.New(),
		fulfillmentCli:     fulfillmentRepo.New(),
		serviceInstanceCli: serviceinstance.New(db),
		db:                 db,
	}
}

type Logic struct {
	appointmentCli     appointment.ReadWriter
	fulfillmentCli     fulfillmentRepo.ReadWriter
	serviceInstanceCli serviceinstance.ReadWriter
	db                 *gorm.DB
}

func (l *Logic) CreateAppointment(ctx context.Context, req *pb.CreateAppointmentRequest) (
	*pb.CreateAppointmentResponse, error) {
	// 校验请求
	if err := verifyCreateAppointmentRequest(req); err != nil {
		return nil, err
	}
	// 获取所有serviceTemplateID并调用第三方服务获取careType信息
	serviceTemplateIDs := l.getAllServiceTemplateIDs(req.GetPets())
	careTypeMap, err := l.getCareTypesByServiceTemplateIDs(ctx, serviceTemplateIDs)
	if err != nil {
		return nil, err
	}
	// 计算ServiceItemType（所有careType去重后相加的值）
	serviceItemType := l.calculateServiceItemType(careTypeMap)
	// 组包
	appointmentEntity := &appointment.Appointment{
		BusinessID:      int(req.GetBusinessId()),
		CustomerID:      int(req.GetCustomerId()),
		CompanyID:       int(req.GetCompanyId()),
		Status:          int(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED),
		ServiceItemType: int(serviceItemType),
		StartTime:       req.GetStartTime().AsTime(),
		EndTime:         req.GetEndTime().AsTime(),
		ColorCode:       req.GetColorCode(),
	}
	appointmentID, err := l.doCreateAppointment(ctx, req, appointmentEntity, careTypeMap)
	if err != nil {
		return nil, err
	}
	response := &pb.CreateAppointmentResponse{
		Id: int64(appointmentID),
		Appointment: &pb.Appointment{
			Id:         int64(appointmentID),
			BusinessId: req.GetBusinessId(),
			CompanyId:  req.GetCompanyId(),
			CustomerId: req.GetCustomerId(),
			StartTime:  req.GetStartTime(),
			EndTime:    req.GetEndTime(),
			ColorCode:  req.GetColorCode(),
			Pets:       req.GetPets(),
		},
	}
	return response, nil
}

func (l *Logic) doCreateAppointment(ctx context.Context, req *pb.CreateAppointmentRequest,
	appointmentEntity *appointment.Appointment, careTypeMap map[int64]pb.CareType) (int, error) {
	var appointmentID int

	// 如果db为nil（测试环境），跳过事务处理
	if l.db == nil {
		return l.createAppointmentWithoutTransaction(ctx, req, careTypeMap)
	}

	// 正常环境，使用事务处理
	err := l.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return l.createAppointmentInTransaction(ctx, tx, req, appointmentEntity, careTypeMap, &appointmentID)
	})
	if err != nil {
		return 0, err
	}
	return appointmentID, nil
}

// createAppointmentWithoutTransaction 在测试环境中创建预约（无事务）
func (l *Logic) createAppointmentWithoutTransaction(ctx context.Context, req *pb.CreateAppointmentRequest,
	careTypeMap map[int64]pb.CareType) (int, error) {
	appointmentID := 1 // 模拟ID
	serviceInstances, err := l.createServiceInstances(ctx, req, appointmentID, careTypeMap)
	if err != nil {
		return 0, err
	}

	if err := l.createFulfillments(ctx, serviceInstances); err != nil {
		return 0, err
	}

	return appointmentID, nil
}

// createAppointmentInTransaction 在事务中创建预约
func (l *Logic) createAppointmentInTransaction(ctx context.Context, tx *gorm.DB, req *pb.CreateAppointmentRequest,
	appointmentEntity *appointment.Appointment, careTypeMap map[int64]pb.CareType, appointmentID *int) error {
	// 创建 appointment
	if err := tx.Create(appointmentEntity).Error; err != nil {
		return err
	}
	*appointmentID = appointmentEntity.ID

	serviceInstances, err := l.createServiceInstances(ctx, req, *appointmentID, careTypeMap)
	if err != nil {
		return err
	}

	if err := l.createFulfillments(ctx, serviceInstances); err != nil {
		return err
	}

	return nil
}

// createServiceInstances 创建服务实例
func (l *Logic) createServiceInstances(ctx context.Context, req *pb.CreateAppointmentRequest,
	appointmentID int, careTypeMap map[int64]pb.CareType) ([]*serviceinstance.ServiceInstance, error) {
	var serviceInstances []*serviceinstance.ServiceInstance

	for _, petDetail := range req.GetPets() {
		for _, serviceInstance := range petDetail.GetServices() {
			careType := careTypeMap[serviceInstance.GetServiceTemplateId()]
			si := &serviceinstance.ServiceInstance{
				BusinessID:       int(req.GetBusinessId()),
				CustomerID:       int(req.GetCustomerId()),
				CompanyID:        int(req.GetCompanyId()),
				AppointmentID:    appointmentID,
				PetID:            int(petDetail.GetPetId()),
				CareType:         int(careType),
				DateType:         int(serviceInstance.GetDateType()),
				ServiceFactoryID: int(serviceInstance.GetServiceTemplateId()),
				StartDate:        serviceInstance.GetStartTime().AsTime(),
				EndDate:          serviceInstance.GetEndTime().AsTime(),
			}
			serviceInstances = append(serviceInstances, si)
		}
	}

	if len(serviceInstances) > 0 {
		if err := l.serviceInstanceCli.BatchCreate(ctx, serviceInstances); err != nil {
			return nil, err
		}
	}

	return serviceInstances, nil
}

// createFulfillments 创建履约记录
func (l *Logic) createFulfillments(ctx context.Context, serviceInstances []*serviceinstance.ServiceInstance) error {
	var allFulfillments []*fulfillmentRepo.Fulfillment

	for _, si := range serviceInstances {
		careType := pb.CareType(si.CareType)
		fulfillments := l.generateFulfillmentRecords(si, careType)
		allFulfillments = append(allFulfillments, fulfillments...)
	}

	if len(allFulfillments) > 0 {
		if err := l.fulfillmentCli.BatchCreate(ctx, allFulfillments); err != nil {
			return err
		}
	}

	return nil
}

// getAllServiceTemplateIDs 获取pets下所有serviceTemplateID
func (l *Logic) getAllServiceTemplateIDs(pets []*pb.PetDetail) []int64 {
	var serviceTemplateIDs []int64
	result := make(map[int64]bool) // 用于去重
	for _, petDetail := range pets {
		for _, serviceInstance := range petDetail.GetServices() {
			templateID := serviceInstance.GetServiceTemplateId()
			if !result[templateID] {
				serviceTemplateIDs = append(serviceTemplateIDs, templateID)
				result[templateID] = true
			}
		}
	}
	// 确保返回空slice而不是nil
	if len(serviceTemplateIDs) == 0 {
		return []int64{}
	}
	return serviceTemplateIDs
}

func (l *Logic) getCareTypesByServiceTemplateIDs(_ context.Context,
	serviceTemplateIDs []int64) (map[int64]pb.CareType, error) {
	// TODO: 实现调用offering服务的接口
	// 临时返回默认值，等待接口实现
	result := make(map[int64]pb.CareType)
	for _, templateID := range serviceTemplateIDs {
		result[templateID] = pb.CareType_CARE_TYPE_BOARDING
	}

	return result, nil
}

func (l *Logic) calculateServiceItemType(careTypeMap map[int64]pb.CareType) int32 {
	// 使用map来去重careType
	uniqueCareTypes := make(map[pb.CareType]bool)
	// 收集所有唯一的careType
	for _, careType := range careTypeMap {
		uniqueCareTypes[careType] = true
	}
	var serviceItemType int32
	for careType := range uniqueCareTypes {
		serviceItemType += int32(careType)
	}
	return serviceItemType
}

// generateFulfillmentRecords 根据 CareType 生成履约记录（不直接创建，只返回记录列表）
func (l *Logic) generateFulfillmentRecords(si *serviceinstance.ServiceInstance,
	careType pb.CareType) []*fulfillmentRepo.Fulfillment {
	switch careType {
	case pb.CareType_CARE_TYPE_BOARDING:
		// Boarding 类型按天维度进行履约
		return l.generateDailyFulfillmentRecords(si)
	case pb.CareType_CARE_TYPE_GROOMING, pb.CareType_CARE_TYPE_DAYCARE,
		pb.CareType_CARE_TYPE_EVALUATION, pb.CareType_CARE_TYPE_DOG_WALKING,
		pb.CareType_CARE_TYPE_GROUP_CLASS:
		// 其他类型按服务维度创建一条记录
		return l.generateSingleFulfillmentRecord(si)
	default:
		// 默认按服务维度创建
		return l.generateSingleFulfillmentRecord(si)
	}
}

// generateDailyFulfillmentRecords 按天生成履约记录（用于 Boarding 等跨天服务）
func (l *Logic) generateDailyFulfillmentRecords(si *serviceinstance.ServiceInstance) []*fulfillmentRepo.Fulfillment {
	var fulfillments []*fulfillmentRepo.Fulfillment
	startDate := si.StartDate
	endDate := si.EndDate
	// 按天循环生成履约记录
	for currentDate := startDate; !currentDate.After(endDate); currentDate = currentDate.AddDate(0, 0, 1) {
		dayStart := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(),
			0, 0, 0, 0, currentDate.Location())
		dayEnd := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(),
			23, 59, 59, 0, currentDate.Location())
		// 如果是第一天，使用实际开始时间
		if currentDate.Equal(startDate) {
			dayStart = startDate
		}
		// 如果是最后一天，使用实际结束时间
		if currentDate.Year() == endDate.Year() &&
			currentDate.Month() == endDate.Month() &&
			currentDate.Day() == endDate.Day() {
			dayEnd = endDate
		}
		fulfillment := &fulfillmentRepo.Fulfillment{
			BusinessID:        int64(si.BusinessID),
			CompanyID:         int64(si.CompanyID),
			CustomerID:        int64(si.CustomerID),
			PetID:             int64(si.PetID),
			ServiceInstanceID: int64(si.ID),
			ServiceFactoryID:  int64(si.ServiceFactoryID),
			CareType:          int32(si.CareType),
			StartTime:         dayStart,
			EndTime:           dayEnd,
			State:             int32(pb.State_STATE_UNSPECIFIED),
		}
		fulfillments = append(fulfillments, fulfillment)
	}

	return fulfillments
}

// generateSingleFulfillmentRecord 生成单条履约记录（用于 Grooming 等不跨天服务）
func (l *Logic) generateSingleFulfillmentRecord(si *serviceinstance.ServiceInstance) []*fulfillmentRepo.Fulfillment {
	fulfillment := &fulfillmentRepo.Fulfillment{
		BusinessID:        int64(si.BusinessID),
		CompanyID:         int64(si.CompanyID),
		CustomerID:        int64(si.CustomerID),
		PetID:             int64(si.PetID),
		ServiceInstanceID: int64(si.ID),
		ServiceFactoryID:  int64(si.ServiceFactoryID),
		CareType:          int32(si.CareType),
		StartTime:         si.StartDate,
		EndTime:           si.EndDate,
		State:             int32(pb.State_STATE_UNSPECIFIED),
	}
	return []*fulfillmentRepo.Fulfillment{fulfillment}
}

func verifyCreateAppointmentRequest(req *pb.CreateAppointmentRequest) error {
	if req.GetBusinessId() <= 0 {
		return status.Error(codes.InvalidArgument, "business_id must be greater than 0")
	}
	if req.GetCompanyId() <= 0 {
		return status.Error(codes.InvalidArgument, "company_id must be greater than 0")
	}
	if req.GetCustomerId() <= 0 {
		return status.Error(codes.InvalidArgument, "customer_id must be greater than 0")
	}
	startTime := req.GetStartTime().AsTime()
	endTime := req.GetEndTime().AsTime()
	if startTime.After(endTime) {
		return status.Error(codes.InvalidArgument, "start_time must be before end_time")
	}
	if len(req.GetPets()) == 0 {
		return status.Error(codes.InvalidArgument, "pets cannot be empty")
	}
	return nil
}

func (l *Logic) UpdateAppointment(ctx context.Context, req *pb.UpdateAppointmentRequest) (
	*pb.UpdateAppointmentResponse, error) {
	// 校验请求
	if err := verifyUpdateAppointmentRequest(req); err != nil {
		return nil, err
	}
	// 验证预约是否存在
	appointmentEntity, err := l.appointmentCli.GetByID(ctx, int(req.GetAppointmentId()))
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "appointment not found: %v", err)
	}
	// 执行更新操作
	success, message, err := l.doUpdateAppointment(ctx, req, appointmentEntity)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateAppointmentResponse{
		Success: success,
		Message: message,
	}, nil
}

func (l *Logic) doUpdateAppointment(ctx context.Context, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) (bool, string, error) {
	var success bool
	var message string

	// 如果db为nil（测试环境），跳过事务处理
	if l.db == nil {
		// 直接执行业务逻辑，不进行事务处理
		if err := l.updateAppointmentBasicInfo(ctx, req, appointmentEntity); err != nil {
			return false, err.Error(), err
		}
		if err := l.processServiceOperations(ctx, req.GetServiceOperations(),
			int(req.GetAppointmentId()), req, appointmentEntity); err != nil {
			return false, err.Error(), err
		}
		if err := l.processOptionOperations(ctx, req.GetOptionOperations()); err != nil {
			return false, err.Error(), err
		}
		if err := l.recalculateAndUpdateServiceItemType(ctx, int(req.GetAppointmentId())); err != nil {
			return false, err.Error(), err
		}
		success = true
		message = updateMessage
		return success, message, nil
	}

	// 正常环境，使用事务处理
	err := l.db.WithContext(ctx).Transaction(func(_ *gorm.DB) error {
		// 更新预约基本信息
		if err := l.updateAppointmentBasicInfo(ctx, req, appointmentEntity); err != nil {
			return err
		}
		// 处理服务操作
		if err := l.processServiceOperations(ctx, req.GetServiceOperations(),
			int(req.GetAppointmentId()), req, appointmentEntity); err != nil {
			return err
		}
		// 处理option(addon)操作
		if err := l.processOptionOperations(ctx, req.GetOptionOperations()); err != nil {
			return err
		}
		// 重新计算ServiceItemType并更新
		if err := l.recalculateAndUpdateServiceItemType(ctx, int(req.GetAppointmentId())); err != nil {
			return err
		}
		success = true
		message = updateMessage
		return nil
	})

	if err != nil {
		return false, err.Error(), err
	}

	return success, message, nil
}

func (l *Logic) updateAppointmentBasicInfo(ctx context.Context, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) error {
	operations := req.GetAppointmentOperations()
	if operations == nil {
		return nil
	}
	// 更新开始时间
	if operations.StartTime != nil {
		appointmentEntity.StartTime = operations.StartTime.AsTime()
	}
	// 更新结束时间
	if operations.EndTime != nil {
		appointmentEntity.EndTime = operations.EndTime.AsTime()
	}
	// 更新颜色代码
	if operations.ColorCode != nil {
		appointmentEntity.ColorCode = *operations.ColorCode
	}
	// 更新状态
	if operations.NewStatus != nil {
		appointmentEntity.Status = int(*operations.NewStatus)
	}
	return l.appointmentCli.Update(ctx, appointmentEntity)
}

func (l *Logic) processServiceOperations(ctx context.Context,
	serviceOperations []*pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, req *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment) error {
	for _, operation := range serviceOperations {
		switch operation.GetOperationMode() {
		case pb.OperationMode_OPERATION_MODE_CREATE:
			if err := l.createServiceInstance(ctx, operation, appointmentID, req, appointmentEntity); err != nil {
				return err
			}
		case pb.OperationMode_OPERATION_MODE_UPDATE:
			if err := l.updateServiceInstance(ctx, operation); err != nil {
				return err
			}
		case pb.OperationMode_OPERATION_MODE_DELETE:
			if err := l.deleteServiceInstance(ctx, operation); err != nil {
				return err
			}
		}
	}
	return nil
}

/*
		createServiceInstance
		1. 新建一个serviceInstance，在serviceOperation填充新建信息即可，不用填充serviceOperation里面的parent_service_instance_id
	    2. 新建一个subServiceInstance（additionalService）
	       a. 如果其父serviceInstance存在，则需要新建一个serviceOperation，在里面填充parent_service_instance_id和subServiceInstance内容
	       b. 如果其父serviceInstance不存在，则需要先新建一个serviceOperation，先填充父serviceInstance信息，
	          然后在serviceOperation的subServiceInstance字段
*/
func (l *Logic) createServiceInstance(ctx context.Context, operation *pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, req *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment) error {
	// 获取careType
	careType, err := l.getCareTypeByServiceTemplateID(ctx, operation.GetServiceTemplateId())
	if err != nil {
		return err
	}
	// 创建主服务实例
	serviceInstance, err := l.buildServiceInstance(operation, appointmentID, req, appointmentEntity, careType)
	if err != nil {
		return err
	}
	// 设置父子关系
	if err := l.setParentChildRelationship(ctx, serviceInstance, operation); err != nil {
		return err
	}
	// 创建服务实例
	serviceInstanceID, err := l.serviceInstanceCli.Create(ctx, serviceInstance)
	if err != nil {
		return err
	}
	// 生成履约记录
	if err := l.createFulfillmentsForServiceInstance(ctx, serviceInstance, careType); err != nil {
		return err
	}
	// 处理子服务实例
	if err := l.processSubServiceInstances(ctx, operation, serviceInstanceID,
		appointmentID, req, appointmentEntity); err != nil {
		return err
	}
	// 处理选项
	if err := l.processServiceOptions(ctx, operation, serviceInstanceID); err != nil {
		return err
	}
	return nil
}

// buildServiceInstance 构建服务实例
func (l *Logic) buildServiceInstance(operation *pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, req *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment,
	careType pb.CareType) (*serviceinstance.ServiceInstance, error) {
	serviceInstance := &serviceinstance.ServiceInstance{
		BusinessID:       int(req.GetBusinessId()),
		CustomerID:       appointmentEntity.CustomerID,
		CompanyID:        int(req.GetCompanyId()),
		AppointmentID:    appointmentID,
		PetID:            0, // TODO: 需要从operation中获取pet_id，或者从现有数据中推断
		CareType:         int(careType),
		DateType:         int(operation.GetDateType()),
		ServiceFactoryID: int(operation.GetServiceTemplateId()),
		StartDate:        operation.GetStartTime().AsTime(),
		EndDate:          operation.GetEndTime().AsTime(),
	}
	return serviceInstance, nil
}

// setParentChildRelationship 设置父子关系
func (l *Logic) setParentChildRelationship(ctx context.Context, serviceInstance *serviceinstance.ServiceInstance,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetParentServiceInstanceId() <= 0 {
		return nil
	}

	serviceInstance.ParentID = int(operation.GetParentServiceInstanceId())

	// 获取父服务的RootID，如果没有则使用父服务ID作为RootID
	parentService, err := l.serviceInstanceCli.GetByID(ctx, int(operation.GetParentServiceInstanceId()))
	if err != nil {
		return err
	}

	if parentService.RootID > 0 {
		serviceInstance.RootID = parentService.RootID
	} else {
		serviceInstance.RootID = parentService.ID
	}

	return nil
}

// createFulfillmentsForServiceInstance 为服务实例创建履约记录
func (l *Logic) createFulfillmentsForServiceInstance(ctx context.Context,
	serviceInstance *serviceinstance.ServiceInstance,
	careType pb.CareType) error {
	fulfillments := l.generateFulfillmentRecords(serviceInstance, careType)
	if len(fulfillments) > 0 {
		if err := l.fulfillmentCli.BatchCreate(ctx, fulfillments); err != nil {
			return err
		}
	}
	return nil
}

// processSubServiceInstances 处理子服务实例
func (l *Logic) processSubServiceInstances(ctx context.Context, operation *pb.UpdateAppointmentRequest_ServiceOperation,
	serviceInstanceID int, appointmentID int, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) error {
	for _, subOperation := range operation.GetSubServiceInstance() {
		subOperation.ParentServiceInstanceId = int64(serviceInstanceID)
		if err := l.createServiceInstance(ctx, subOperation, appointmentID, req, appointmentEntity); err != nil {
			return err
		}
	}
	return nil
}

// processServiceOptions 处理服务选项
func (l *Logic) processServiceOptions(ctx context.Context, operation *pb.UpdateAppointmentRequest_ServiceOperation,
	serviceInstanceID int) error {
	for _, option := range operation.GetOptions() {
		optionOperation := &pb.UpdateAppointmentRequest_OptionOperation{
			OperationMode:           pb.OperationMode_OPERATION_MODE_CREATE,
			ServiceInstanceId:       int64(serviceInstanceID),
			ServiceOptionTemplateId: option.GetServiceOptionTemplateId(),
			StartTime:               option.GetStartTime(),
			EndTime:                 option.GetEndTime(),
			Name:                    option.GetName(),
			Quantity:                option.GetQuantity(),
			Price:                   option.GetPrice(),
			Tax:                     option.GetTax(),
		}
		if err := l.createServiceOption(ctx, optionOperation); err != nil {
			return err
		}
	}
	return nil
}

func (l *Logic) updateServiceInstance(ctx context.Context,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetServiceInstanceId() <= 0 {
		return status.Error(codes.InvalidArgument, "service_instance_id is required for update operation")
	}
	// 获取现有服务实例
	serviceInstance, err := l.serviceInstanceCli.GetByID(ctx, int(operation.GetServiceInstanceId()))
	if err != nil {
		return err
	}
	// 更新字段
	if operation.GetStartTime() != nil {
		serviceInstance.StartDate = operation.GetStartTime().AsTime()
	}
	if operation.GetEndTime() != nil {
		serviceInstance.EndDate = operation.GetEndTime().AsTime()
	}
	if operation.GetDateType() != pb.DateType_DATE_TYPE_UNSPECIFIED {
		serviceInstance.DateType = int(operation.GetDateType())
	}
	return l.serviceInstanceCli.Update(ctx, serviceInstance)
}

func (l *Logic) deleteServiceInstance(ctx context.Context,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetServiceInstanceId() <= 0 {
		return status.Error(codes.InvalidArgument, "service_instance_id is required for delete operation")
	}

	// 删除相关的履约记录
	if err := l.fulfillmentCli.DeleteByServiceInstanceID(ctx, int64(operation.GetServiceInstanceId())); err != nil {
		return err
	}

	// 删除服务实例
	return l.serviceInstanceCli.Delete(ctx, int(operation.GetServiceInstanceId()))
}

func (l *Logic) processOptionOperations(ctx context.Context,
	optionOperations []*pb.UpdateAppointmentRequest_OptionOperation) error {
	for _, operation := range optionOperations {
		switch operation.GetOperationMode() {
		case pb.OperationMode_OPERATION_MODE_CREATE:
			if err := l.createServiceOption(ctx, operation); err != nil {
				return err
			}
		case pb.OperationMode_OPERATION_MODE_UPDATE:
			if err := l.updateServiceOption(ctx, operation); err != nil {
				return err
			}
		case pb.OperationMode_OPERATION_MODE_DELETE:
			if err := l.deleteServiceOption(ctx, operation); err != nil {
				return err
			}
		}
	}
	return nil
}

func (l *Logic) createServiceOption(_ context.Context, _ *pb.UpdateAppointmentRequest_OptionOperation) error {
	// TODO: 实现ServiceOption的创建逻辑
	// 这里需要先创建ServiceOption的数据库模型和repo
	// 暂时返回nil，等待ServiceOption模型创建完成
	return nil
}

func (l *Logic) updateServiceOption(_ context.Context, _ *pb.UpdateAppointmentRequest_OptionOperation) error {
	// TODO: 实现ServiceOption的更新逻辑
	return nil
}

func (l *Logic) deleteServiceOption(_ context.Context, _ *pb.UpdateAppointmentRequest_OptionOperation) error {
	// TODO: 实现ServiceOption的删除逻辑
	return nil
}

func (l *Logic) getCareTypeByServiceTemplateID(_ context.Context, _ int64) (pb.CareType, error) {
	// 调用offering服务获取careType
	// 暂时返回默认值
	return pb.CareType_CARE_TYPE_BOARDING, nil
}

func (l *Logic) recalculateAndUpdateServiceItemType(ctx context.Context, appointmentID int) error {
	// 获取预约下的所有服务实例
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentID(ctx, appointmentID)
	if err != nil {
		return err
	}

	// 收集所有唯一的careType
	uniqueCareTypes := make(map[pb.CareType]bool)
	for _, si := range serviceInstances {
		careType := pb.CareType(si.CareType)
		uniqueCareTypes[careType] = true
	}

	// 计算ServiceItemType
	var serviceItemType int32
	for careType := range uniqueCareTypes {
		serviceItemType += int32(careType)
	}

	// 更新预约的ServiceItemType
	appointmentEntity, err := l.appointmentCli.GetByID(ctx, appointmentID)
	if err != nil {
		return err
	}
	appointmentEntity.ServiceItemType = int(serviceItemType)
	return l.appointmentCli.Update(ctx, appointmentEntity)
}

func verifyUpdateAppointmentRequest(req *pb.UpdateAppointmentRequest) error {
	if req.GetAppointmentId() <= 0 {
		return status.Error(codes.InvalidArgument, "appointment_id must be greater than 0")
	}
	if req.GetCompanyId() <= 0 {
		return status.Error(codes.InvalidArgument, "company_id must be greater than 0")
	}
	if req.GetBusinessId() <= 0 {
		return status.Error(codes.InvalidArgument, "business_id must be greater than 0")
	}

	// 验证预约操作
	if operations := req.GetAppointmentOperations(); operations != nil {
		if operations.StartTime != nil && operations.EndTime != nil {
			startTime := operations.StartTime.AsTime()
			endTime := operations.EndTime.AsTime()
			if startTime.After(endTime) {
				return status.Error(codes.InvalidArgument, "start_time must be before end_time")
			}
		}
	}

	// 验证服务操作
	for _, serviceOp := range req.GetServiceOperations() {
		if err := verifyServiceOperation(serviceOp); err != nil {
			return err
		}
	}

	// 验证选项操作
	for _, optionOp := range req.GetOptionOperations() {
		if err := verifyOptionOperation(optionOp); err != nil {
			return err
		}
	}

	return nil
}

func verifyServiceOperation(operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	switch operation.GetOperationMode() {
	case pb.OperationMode_OPERATION_MODE_CREATE:
		if operation.GetServiceTemplateId() <= 0 {
			return status.Error(codes.InvalidArgument, "service_template_id is required for create operation")
		}
		if operation.GetStartTime() == nil || operation.GetEndTime() == nil {
			return status.Error(codes.InvalidArgument, "start_time and end_time are required for create operation")
		}
	case pb.OperationMode_OPERATION_MODE_UPDATE, pb.OperationMode_OPERATION_MODE_DELETE:
		if operation.GetServiceInstanceId() <= 0 {
			return status.Error(codes.InvalidArgument, "service_instance_id is required for update/delete operation")
		}
	}

	// 验证时间
	if operation.GetStartTime() != nil && operation.GetEndTime() != nil {
		startTime := operation.GetStartTime().AsTime()
		endTime := operation.GetEndTime().AsTime()
		if startTime.After(endTime) {
			return status.Error(codes.InvalidArgument, "start_time must be before end_time")
		}
	}

	return nil
}

func verifyOptionOperation(operation *pb.UpdateAppointmentRequest_OptionOperation) error {
	switch operation.GetOperationMode() {
	case pb.OperationMode_OPERATION_MODE_CREATE:
		if operation.GetServiceOptionTemplateId() <= 0 {
			return status.Error(codes.InvalidArgument, "service_option_template_id is required for create operation")
		}
		if operation.GetStartTime() == nil || operation.GetEndTime() == nil {
			return status.Error(codes.InvalidArgument, "start_time and end_time are required for create operation")
		}
	case pb.OperationMode_OPERATION_MODE_UPDATE, pb.OperationMode_OPERATION_MODE_DELETE:
		if operation.GetServiceOptionId() <= 0 {
			return status.Error(codes.InvalidArgument, "service_option_id is required for update/delete operation")
		}
	}

	// 验证时间
	if operation.GetStartTime() != nil && operation.GetEndTime() != nil {
		startTime := operation.GetStartTime().AsTime()
		endTime := operation.GetEndTime().AsTime()
		if startTime.After(endTime) {
			return status.Error(codes.InvalidArgument, "start_time must be before end_time")
		}
	}

	return nil
}
