package appointment

import (
	"context"
	"errors"
	"testing"
	"time"

	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"gotest.tools/v3/assert"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment/mock"
	fulfillmentMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment/mock"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	serviceInstanceMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance/mock"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

func TestNew(t *testing.T) {
	logic := New()
	assert.Assert(t, logic != nil)
	assert.Assert(t, logic.appointmentCli != nil)
	assert.Assert(t, logic.fulfillmentCli != nil)
	assert.Assert(t, logic.serviceInstanceCli != nil)
	assert.Assert(t, logic.db != nil)
}

func TestCreateAppointment_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		fulfillmentCli:     mockFulfillmentCli,
		serviceInstanceCli: mockServiceInstanceCli,
		db:                 nil, // 在测试中不需要真实的数据库连接
	}

	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		ColorCode:  "blue",
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						DateType:          pb.DateType_DATE_TYPE_UNSPECIFIED,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	// mock repo方法，避免实际数据库操作
	mockServiceInstanceCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	resp, err := logic.CreateAppointment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, resp.Appointment.BusinessId, int64(123))
	assert.Equal(t, resp.Appointment.CompanyId, int64(456))
	assert.Equal(t, resp.Appointment.CustomerId, int64(789))
}

func TestCreateAppointment_InvalidRequest(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logic := &Logic{
		appointmentCli:     mock.NewMockReadWriter(ctrl),
		fulfillmentCli:     fulfillmentMock.NewMockReadWriter(ctrl),
		serviceInstanceCli: serviceInstanceMock.NewMockReadWriter(ctrl),
		db:                 &gorm.DB{},
	}

	// Test invalid business_id
	req := &pb.CreateAppointmentRequest{
		BusinessId: 0, // Invalid
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	resp, err := logic.CreateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestCreateAppointment_InvalidTimeRange(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logic := &Logic{
		appointmentCli:     mock.NewMockReadWriter(ctrl),
		fulfillmentCli:     fulfillmentMock.NewMockReadWriter(ctrl),
		serviceInstanceCli: serviceInstanceMock.NewMockReadWriter(ctrl),
		db:                 &gorm.DB{},
	}

	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now().Add(2 * time.Hour)), // Start time after end time
		EndTime:    timestamppb.New(time.Now()),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	resp, err := logic.CreateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestCreateAppointment_EmptyPets(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logic := &Logic{
		appointmentCli:     mock.NewMockReadWriter(ctrl),
		fulfillmentCli:     fulfillmentMock.NewMockReadWriter(ctrl),
		serviceInstanceCli: serviceInstanceMock.NewMockReadWriter(ctrl),
		db:                 &gorm.DB{},
	}

	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets:       []*pb.PetDetail{}, // Empty pets
	}

	resp, err := logic.CreateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestGetAllServiceTemplateIDs(t *testing.T) {
	logic := &Logic{}

	pets := []*pb.PetDetail{
		{
			PetId: 1,
			Services: []*pb.ServiceInstance{
				{ServiceTemplateId: 100},
				{ServiceTemplateId: 200},
			},
		},
		{
			PetId: 2,
			Services: []*pb.ServiceInstance{
				{ServiceTemplateId: 100}, // Duplicate
				{ServiceTemplateId: 300},
			},
		},
	}

	result := logic.getAllServiceTemplateIDs(pets)
	expected := []int64{100, 200, 300}
	assert.DeepEqual(t, result, expected)
}

func TestGetAllServiceTemplateIDs_Empty(t *testing.T) {
	logic := &Logic{}

	pets := []*pb.PetDetail{}

	result := logic.getAllServiceTemplateIDs(pets)
	assert.DeepEqual(t, result, []int64{})
}

func TestGetCareTypesByServiceTemplateIDs(t *testing.T) {
	logic := &Logic{}

	serviceTemplateIDs := []int64{100, 200, 300}

	result, err := logic.getCareTypesByServiceTemplateIDs(context.Background(), serviceTemplateIDs)
	assert.NilError(t, err)
	assert.Assert(t, result != nil)
	assert.Equal(t, result[100], pb.CareType_CARE_TYPE_BOARDING)
	assert.Equal(t, result[200], pb.CareType_CARE_TYPE_BOARDING)
	assert.Equal(t, result[300], pb.CareType_CARE_TYPE_BOARDING)
}

func TestCalculateServiceItemType(t *testing.T) {
	logic := &Logic{}

	careTypeMap := map[int64]pb.CareType{
		100: pb.CareType_CARE_TYPE_BOARDING, // 2
		200: pb.CareType_CARE_TYPE_GROOMING, // 1
		300: pb.CareType_CARE_TYPE_BOARDING, // 2 (duplicate)
	}

	result := logic.calculateServiceItemType(careTypeMap)
	// 2 + 1 = 3 (duplicates are removed)
	assert.Equal(t, result, int32(3))
}

func TestCalculateServiceItemType_Empty(t *testing.T) {
	logic := &Logic{}

	careTypeMap := map[int64]pb.CareType{}

	result := logic.calculateServiceItemType(careTypeMap)
	assert.Equal(t, result, int32(0))
}

func TestGenerateFulfillmentRecords_Boarding(t *testing.T) {
	logic := &Logic{}

	si := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_BOARDING),
		ServiceFactoryID: 100,
		StartDate:        time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
		EndDate:          time.Date(2024, 1, 3, 18, 0, 0, 0, time.UTC),
	}

	result := logic.generateFulfillmentRecords(si, pb.CareType_CARE_TYPE_BOARDING)
	assert.Assert(t, len(result) == 3) // 3 days

	// Check first day
	assert.Equal(t, result[0].StartTime, si.StartDate)
	assert.Equal(t, result[0].EndTime, time.Date(2024, 1, 1, 23, 59, 59, 0, time.UTC))

	// Check last day
	assert.Equal(t, result[2].StartTime, time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC))
	assert.Equal(t, result[2].EndTime, si.EndDate)
}

func TestGenerateFulfillmentRecords_Grooming(t *testing.T) {
	logic := &Logic{}

	si := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_GROOMING),
		ServiceFactoryID: 100,
		StartDate:        time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
		EndDate:          time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
	}

	result := logic.generateFulfillmentRecords(si, pb.CareType_CARE_TYPE_GROOMING)
	assert.Assert(t, len(result) == 1) // Single record

	assert.Equal(t, result[0].StartTime, si.StartDate)
	assert.Equal(t, result[0].EndTime, si.EndDate)
}

func TestGenerateFulfillmentRecords_Default(t *testing.T) {
	logic := &Logic{}

	si := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_UNSPECIFIED),
		ServiceFactoryID: 100,
		StartDate:        time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
		EndDate:          time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
	}

	result := logic.generateFulfillmentRecords(si, pb.CareType_CARE_TYPE_UNSPECIFIED)
	assert.Assert(t, len(result) == 1) // Default to single record
}

func TestVerifyCreateAppointmentRequest_Valid(t *testing.T) {
	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	err := verifyCreateAppointmentRequest(req)
	assert.NilError(t, err)
}

func TestVerifyCreateAppointmentRequest_InvalidBusinessId(t *testing.T) {
	req := &pb.CreateAppointmentRequest{
		BusinessId: 0,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	err := verifyCreateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyCreateAppointmentRequest_InvalidCompanyId(t *testing.T) {
	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  0,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	err := verifyCreateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyCreateAppointmentRequest_InvalidCustomerId(t *testing.T) {
	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 0,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	err := verifyCreateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyCreateAppointmentRequest_InvalidTimeRange(t *testing.T) {
	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now().Add(2 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	err := verifyCreateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyCreateAppointmentRequest_EmptyPets(t *testing.T) {
	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets:       []*pb.PetDetail{},
	}

	err := verifyCreateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestUpdateAppointment_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		fulfillmentCli:     mockFulfillmentCli,
		serviceInstanceCli: mockServiceInstanceCli,
		db:                 nil, // 在测试中不需要真实的数据库连接
	}

	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now()),
			EndTime:   timestamppb.New(time.Now().Add(2 * time.Hour)),
			ColorCode: stringPtr("red"),
			NewStatus: appointmentStatePtr(pb.AppointmentState_APPOINTMENT_STATE_CONFIRMED),
		},
		ServiceOperations: []*pb.UpdateAppointmentRequest_ServiceOperation{},
		OptionOperations:  []*pb.UpdateAppointmentRequest_OptionOperation{},
	}

	appointmentEntity := &appointment.Appointment{
		ID:     1,
		Status: int(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED),
	}

	mockAppointmentCli.EXPECT().GetByID(gomock.Any(), 1).Return(appointmentEntity, nil).AnyTimes()
	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockServiceInstanceCli.EXPECT().GetByAppointmentID(gomock.Any(), 1).
		Return([]*serviceinstance.ServiceInstance{}, nil).AnyTimes()

	resp, err := logic.UpdateAppointment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, resp.Success, true)
	assert.Equal(t, resp.Message, "更新成功")
}

func TestUpdateAppointment_InvalidRequest(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logic := &Logic{
		appointmentCli:     mock.NewMockReadWriter(ctrl),
		fulfillmentCli:     fulfillmentMock.NewMockReadWriter(ctrl),
		serviceInstanceCli: serviceInstanceMock.NewMockReadWriter(ctrl),
		db:                 &gorm.DB{},
	}

	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 0, // Invalid
		BusinessId:    123,
		CompanyId:     456,
	}

	resp, err := logic.UpdateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestUpdateAppointment_AppointmentNotFound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		fulfillmentCli:     fulfillmentMock.NewMockReadWriter(ctrl),
		serviceInstanceCli: serviceInstanceMock.NewMockReadWriter(ctrl),
		db:                 &gorm.DB{},
	}

	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
	}

	mockAppointmentCli.EXPECT().GetByID(gomock.Any(), 1).Return(nil, errors.New("not found"))

	resp, err := logic.UpdateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestVerifyUpdateAppointmentRequest_Valid(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now()),
			EndTime:   timestamppb.New(time.Now().Add(2 * time.Hour)),
		},
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.NilError(t, err)
}

func TestVerifyUpdateAppointmentRequest_InvalidAppointmentId(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 0,
		BusinessId:    123,
		CompanyId:     456,
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyUpdateAppointmentRequest_InvalidBusinessId(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    0,
		CompanyId:     456,
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyUpdateAppointmentRequest_InvalidCompanyId(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     0,
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyUpdateAppointmentRequest_InvalidTimeRange(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now().Add(2 * time.Hour)),
			EndTime:   timestamppb.New(time.Now()),
		},
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyServiceOperation_Create(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
		ServiceTemplateId: 100,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
	}

	err := verifyServiceOperation(operation)
	assert.NilError(t, err)
}

func TestVerifyServiceOperation_CreateInvalid(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
		// Missing required fields
	}

	err := verifyServiceOperation(operation)
	assert.Assert(t, err != nil)
}

func TestVerifyServiceOperation_Update(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
		ServiceInstanceId: 1,
	}

	err := verifyServiceOperation(operation)
	assert.NilError(t, err)
}

func TestVerifyServiceOperation_UpdateInvalid(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode: pb.OperationMode_OPERATION_MODE_UPDATE,
		// Missing service_instance_id
	}

	err := verifyServiceOperation(operation)
	assert.Assert(t, err != nil)
}

func TestVerifyServiceOperation_Delete(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
		ServiceInstanceId: 1,
	}

	err := verifyServiceOperation(operation)
	assert.NilError(t, err)
}

func TestVerifyServiceOperation_InvalidTimeRange(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
		StartTime:     timestamppb.New(time.Now().Add(1 * time.Hour)),
		EndTime:       timestamppb.New(time.Now()),
	}

	err := verifyServiceOperation(operation)
	assert.Assert(t, err != nil)
}

func TestVerifyOptionOperation_Create(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode:           pb.OperationMode_OPERATION_MODE_CREATE,
		ServiceOptionTemplateId: 100,
		StartTime:               timestamppb.New(time.Now()),
		EndTime:                 timestamppb.New(time.Now().Add(1 * time.Hour)),
	}

	err := verifyOptionOperation(operation)
	assert.NilError(t, err)
}

func TestVerifyOptionOperation_CreateInvalid(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
		// Missing required fields
	}

	err := verifyOptionOperation(operation)
	assert.Assert(t, err != nil)
}

func TestVerifyOptionOperation_Update(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode:   pb.OperationMode_OPERATION_MODE_UPDATE,
		ServiceOptionId: 1,
	}

	err := verifyOptionOperation(operation)
	assert.NilError(t, err)
}

func TestVerifyOptionOperation_UpdateInvalid(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode: pb.OperationMode_OPERATION_MODE_UPDATE,
		// Missing service_option_id
	}

	err := verifyOptionOperation(operation)
	assert.Assert(t, err != nil)
}

func TestVerifyOptionOperation_Delete(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode:   pb.OperationMode_OPERATION_MODE_DELETE,
		ServiceOptionId: 1,
	}

	err := verifyOptionOperation(operation)
	assert.NilError(t, err)
}

func TestVerifyOptionOperation_InvalidTimeRange(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
		StartTime:     timestamppb.New(time.Now().Add(1 * time.Hour)),
		EndTime:       timestamppb.New(time.Now()),
	}

	err := verifyOptionOperation(operation)
	assert.Assert(t, err != nil)
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func appointmentStatePtr(s pb.AppointmentState) *pb.AppointmentState {
	return &s
}

// 添加更多测试用例来提升覆盖率

func TestCreateAppointment_DatabaseError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		fulfillmentCli:     mockFulfillmentCli,
		serviceInstanceCli: mockServiceInstanceCli,
		db:                 nil, // 在测试中不需要真实的数据库连接
	}

	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		ColorCode:  "blue",
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						DateType:          pb.DateType_DATE_TYPE_UNSPECIFIED,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	// Mock database error
	mockServiceInstanceCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(errors.New("database error"))

	resp, err := logic.CreateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestUpdateAppointment_DatabaseError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		fulfillmentCli:     mockFulfillmentCli,
		serviceInstanceCli: mockServiceInstanceCli,
		db:                 nil, // 在测试中不需要真实的数据库连接
	}

	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now()),
			EndTime:   timestamppb.New(time.Now().Add(2 * time.Hour)),
		},
	}

	appointmentEntity := &appointment.Appointment{
		ID:     1,
		Status: int(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED),
	}

	mockAppointmentCli.EXPECT().GetByID(gomock.Any(), 1).Return(appointmentEntity, nil).AnyTimes()
	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error")).AnyTimes()

	resp, err := logic.UpdateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestUpdateAppointment_ServiceOperations(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		fulfillmentCli:     mockFulfillmentCli,
		serviceInstanceCli: mockServiceInstanceCli,
		db:                 nil, // 在测试中不需要真实的数据库连接
	}

	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		ServiceOperations: []*pb.UpdateAppointmentRequest_ServiceOperation{
			{
				OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
				ServiceTemplateId: 100,
				StartTime:         timestamppb.New(time.Now()),
				EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
			},
		},
	}

	appointmentEntity := &appointment.Appointment{
		ID:     1,
		Status: int(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED),
	}

	mockAppointmentCli.EXPECT().GetByID(gomock.Any(), 1).Return(appointmentEntity, nil).AnyTimes()
	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockServiceInstanceCli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(1, nil).AnyTimes()
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockServiceInstanceCli.EXPECT().GetByAppointmentID(gomock.Any(), 1).
		Return([]*serviceinstance.ServiceInstance{}, nil).AnyTimes()

	resp, err := logic.UpdateAppointment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
}

func TestUpdateAppointment_OptionOperations(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		fulfillmentCli:     mockFulfillmentCli,
		serviceInstanceCli: mockServiceInstanceCli,
		db:                 nil, // 在测试中不需要真实的数据库连接
	}

	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		OptionOperations: []*pb.UpdateAppointmentRequest_OptionOperation{
			{
				OperationMode:           pb.OperationMode_OPERATION_MODE_CREATE,
				ServiceOptionTemplateId: 100,
				StartTime:               timestamppb.New(time.Now()),
				EndTime:                 timestamppb.New(time.Now().Add(1 * time.Hour)),
			},
		},
	}

	appointmentEntity := &appointment.Appointment{
		ID:     1,
		Status: int(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED),
	}

	mockAppointmentCli.EXPECT().GetByID(gomock.Any(), 1).Return(appointmentEntity, nil).AnyTimes()
	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockServiceInstanceCli.EXPECT().GetByAppointmentID(gomock.Any(), 1).
		Return([]*serviceinstance.ServiceInstance{}, nil).AnyTimes()

	resp, err := logic.UpdateAppointment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
}

func TestGenerateDailyFulfillmentRecords_SingleDay(t *testing.T) {
	logic := &Logic{}

	si := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_BOARDING),
		ServiceFactoryID: 100,
		StartDate:        time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
		EndDate:          time.Date(2024, 1, 1, 18, 0, 0, 0, time.UTC),
	}

	result := logic.generateDailyFulfillmentRecords(si)
	assert.Assert(t, len(result) == 1)
	assert.Equal(t, result[0].StartTime, si.StartDate)
	assert.Equal(t, result[0].EndTime, si.EndDate)
}

func TestGenerateDailyFulfillmentRecords_SameTime(t *testing.T) {
	logic := &Logic{}

	sameTime := time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC)
	si := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_BOARDING),
		ServiceFactoryID: 100,
		StartDate:        sameTime,
		EndDate:          sameTime,
	}

	result := logic.generateDailyFulfillmentRecords(si)
	assert.Assert(t, len(result) == 1)
	assert.Equal(t, result[0].StartTime, si.StartDate)
	assert.Equal(t, result[0].EndTime, si.EndDate)
}

// --- 补充分支测试 ---

func TestUpdateServiceInstance_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
		ServiceInstanceId: 1,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
		DateType:          pb.DateType_DATE_TYPE_UNSPECIFIED,
	}
	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(&serviceinstance.ServiceInstance{}, nil)
	mockServiceInstanceCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))
	err := logic.updateServiceInstance(context.Background(), operation)
	assert.ErrorContains(t, err, "update error")
}

func TestDeleteServiceInstance_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli, fulfillmentCli: mockFulfillmentCli}
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
		ServiceInstanceId: 1,
	}
	mockFulfillmentCli.EXPECT().DeleteByServiceInstanceID(gomock.Any(), int64(1)).Return(errors.New("delete error"))
	err := logic.deleteServiceInstance(context.Background(), operation)
	assert.ErrorContains(t, err, "delete error")
}

func TestDeleteServiceInstance_DeleteServiceError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli, fulfillmentCli: mockFulfillmentCli}
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
		ServiceInstanceId: 1,
	}
	mockFulfillmentCli.EXPECT().DeleteByServiceInstanceID(gomock.Any(), int64(1)).Return(nil)
	mockServiceInstanceCli.EXPECT().Delete(gomock.Any(), 1).Return(errors.New("delete error"))
	err := logic.deleteServiceInstance(context.Background(), operation)
	assert.ErrorContains(t, err, "delete error")
}

func TestUpdateServiceOption_Error(t *testing.T) {
	logic := &Logic{}
	err := logic.updateServiceOption(context.Background(), &pb.UpdateAppointmentRequest_OptionOperation{})
	assert.NilError(t, err) // 目前是空实现，返回nil
}

func TestDeleteServiceOption_Error(t *testing.T) {
	logic := &Logic{}
	err := logic.deleteServiceOption(context.Background(), &pb.UpdateAppointmentRequest_OptionOperation{})
	assert.NilError(t, err) // 目前是空实现，返回nil
}

func TestRecalculateAndUpdateServiceItemType_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli, appointmentCli: mockAppointmentCli}
	mockServiceInstanceCli.EXPECT().GetByAppointmentID(gomock.Any(), 1).Return(nil, errors.New("get error"))
	err := logic.recalculateAndUpdateServiceItemType(context.Background(), 1)
	assert.ErrorContains(t, err, "get error")
}

func TestRecalculateAndUpdateServiceItemType_UpdateError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli, appointmentCli: mockAppointmentCli}
	mockServiceInstanceCli.EXPECT().GetByAppointmentID(gomock.Any(), 1).Return([]*serviceinstance.ServiceInstance{{CareType: int(pb.CareType_CARE_TYPE_BOARDING)}}, nil)
	mockAppointmentCli.EXPECT().GetByID(gomock.Any(), 1).Return(&appointment.Appointment{}, nil)
	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))
	err := logic.recalculateAndUpdateServiceItemType(context.Background(), 1)
	assert.ErrorContains(t, err, "update error")
}

func TestVerifyServiceOperation_InvalidMode(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode: pb.OperationMode_OPERATION_MODE_UNSPECIFIED,
	}
	err := verifyServiceOperation(operation)
	assert.NilError(t, err) // 不校验mode
}

func TestVerifyOptionOperation_InvalidMode(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode: pb.OperationMode_OPERATION_MODE_UNSPECIFIED,
	}
	err := verifyOptionOperation(operation)
	assert.NilError(t, err) // 不校验mode
}

// --- 补充父子关系测试 ---

func TestSetParentChildRelationship_WithParent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID: 2,
	}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		ParentServiceInstanceId: 1,
	}

	parentService := &serviceinstance.ServiceInstance{
		ID:     1,
		RootID: 5, // 有RootID的情况
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(parentService, nil)

	err := logic.setParentChildRelationship(context.Background(), serviceInstance, operation)
	assert.NilError(t, err)
	assert.Equal(t, serviceInstance.ParentID, 1)
	assert.Equal(t, serviceInstance.RootID, 5)
}

func TestSetParentChildRelationship_WithoutRootID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID: 2,
	}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		ParentServiceInstanceId: 1,
	}

	parentService := &serviceinstance.ServiceInstance{
		ID:     1,
		RootID: 0, // 没有RootID的情况
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(parentService, nil)

	err := logic.setParentChildRelationship(context.Background(), serviceInstance, operation)
	assert.NilError(t, err)
	assert.Equal(t, serviceInstance.ParentID, 1)
	assert.Equal(t, serviceInstance.RootID, 1) // 应该使用父服务ID作为RootID
}

func TestSetParentChildRelationship_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID: 2,
	}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		ParentServiceInstanceId: 1,
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(nil, errors.New("not found"))

	err := logic.setParentChildRelationship(context.Background(), serviceInstance, operation)
	assert.ErrorContains(t, err, "not found")
}

// --- 补充服务操作测试 ---

func TestProcessServiceOperations_Update(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	serviceOperations := []*pb.UpdateAppointmentRequest_ServiceOperation{
		{
			OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
			ServiceInstanceId: 1,
			StartTime:         timestamppb.New(time.Now()),
			EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
			DateType:          pb.DateType_DATE_TYPE_UNSPECIFIED,
		},
	}

	existingService := &serviceinstance.ServiceInstance{
		ID: 1,
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(existingService, nil)
	mockServiceInstanceCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	err := logic.processServiceOperations(context.Background(), serviceOperations, 1, nil, nil)
	assert.NilError(t, err)
}

func TestProcessServiceOperations_Delete(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	serviceOperations := []*pb.UpdateAppointmentRequest_ServiceOperation{
		{
			OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
			ServiceInstanceId: 1,
		},
	}

	mockFulfillmentCli.EXPECT().DeleteByServiceInstanceID(gomock.Any(), int64(1)).Return(nil)
	mockServiceInstanceCli.EXPECT().Delete(gomock.Any(), 1).Return(nil)

	err := logic.processServiceOperations(context.Background(), serviceOperations, 1, nil, nil)
	assert.NilError(t, err)
}

func TestProcessServiceOperations_InvalidMode(t *testing.T) {
	logic := &Logic{}

	serviceOperations := []*pb.UpdateAppointmentRequest_ServiceOperation{
		{
			OperationMode: pb.OperationMode_OPERATION_MODE_UNSPECIFIED,
		},
	}

	err := logic.processServiceOperations(context.Background(), serviceOperations, 1, nil, nil)
	assert.NilError(t, err) // 应该忽略无效模式
}

// --- 补充选项操作测试 ---

func TestProcessOptionOperations_Update(t *testing.T) {
	logic := &Logic{}

	optionOperations := []*pb.UpdateAppointmentRequest_OptionOperation{
		{
			OperationMode:   pb.OperationMode_OPERATION_MODE_UPDATE,
			ServiceOptionId: 1,
		},
	}

	err := logic.processOptionOperations(context.Background(), optionOperations)
	assert.NilError(t, err)
}

func TestProcessOptionOperations_Delete(t *testing.T) {
	logic := &Logic{}

	optionOperations := []*pb.UpdateAppointmentRequest_OptionOperation{
		{
			OperationMode:   pb.OperationMode_OPERATION_MODE_DELETE,
			ServiceOptionId: 1,
		},
	}

	err := logic.processOptionOperations(context.Background(), optionOperations)
	assert.NilError(t, err)
}

func TestProcessOptionOperations_InvalidMode(t *testing.T) {
	logic := &Logic{}

	optionOperations := []*pb.UpdateAppointmentRequest_OptionOperation{
		{
			OperationMode: pb.OperationMode_OPERATION_MODE_UNSPECIFIED,
		},
	}

	err := logic.processOptionOperations(context.Background(), optionOperations)
	assert.NilError(t, err) // 应该忽略无效模式
}

// --- 补充子服务实例测试 ---

func TestProcessSubServiceInstances_WithSubServices(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		SubServiceInstance: []*pb.UpdateAppointmentRequest_ServiceOperation{
			{
				OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
				ServiceTemplateId: 200,
				StartTime:         timestamppb.New(time.Now()),
				EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
			},
		},
	}

	req := &pb.UpdateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
	}

	appointmentEntity := &appointment.Appointment{
		CustomerID: 789,
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(&serviceinstance.ServiceInstance{ID: 1}, nil).AnyTimes()
	mockServiceInstanceCli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(2, nil).AnyTimes()
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	err := logic.processSubServiceInstances(context.Background(), operation, 1, 1, req, appointmentEntity)
	assert.NilError(t, err)
}

// --- 补充服务选项测试 ---

func TestProcessServiceOptions_WithOptions(t *testing.T) {
	logic := &Logic{}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		Options: []*pb.ServiceOption{
			{
				ServiceOptionTemplateId: 100,
				StartTime:               timestamppb.New(time.Now()),
				EndTime:                 timestamppb.New(time.Now().Add(1 * time.Hour)),
				Name:                    "test option",
				Quantity:                1,
				Price:                   10.0,
				Tax:                     1.0,
			},
		},
	}

	err := logic.processServiceOptions(context.Background(), operation, 1)
	assert.NilError(t, err)
}

// --- 补充错误处理测试 ---

func TestCreateServiceInstance_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
		ServiceTemplateId: 100,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
	}

	req := &pb.UpdateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
	}

	appointmentEntity := &appointment.Appointment{
		CustomerID: 789,
	}

	mockServiceInstanceCli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(0, errors.New("create error"))

	err := logic.createServiceInstance(context.Background(), operation, 1, req, appointmentEntity)
	assert.ErrorContains(t, err, "create error")
}

func TestCreateFulfillmentsForServiceInstance_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{fulfillmentCli: mockFulfillmentCli}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_BOARDING),
		ServiceFactoryID: 100,
		StartDate:        time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
		EndDate:          time.Date(2024, 1, 1, 18, 0, 0, 0, time.UTC),
	}

	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(errors.New("batch create error"))

	err := logic.createFulfillmentsForServiceInstance(context.Background(), serviceInstance, pb.CareType_CARE_TYPE_BOARDING)
	assert.ErrorContains(t, err, "batch create error")
}

// --- 补充边界条件测试 ---

func TestGenerateDailyFulfillmentRecords_CrossMonth(t *testing.T) {
	logic := &Logic{}

	si := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_BOARDING),
		ServiceFactoryID: 100,
		StartDate:        time.Date(2024, 1, 31, 10, 0, 0, 0, time.UTC),
		EndDate:          time.Date(2024, 2, 2, 18, 0, 0, 0, time.UTC),
	}

	result := logic.generateDailyFulfillmentRecords(si)
	assert.Assert(t, len(result) == 3) // 3天：1月31日、2月1日、2月2日

	// 检查第一天
	assert.Equal(t, result[0].StartTime, si.StartDate)
	assert.Equal(t, result[0].EndTime, time.Date(2024, 1, 31, 23, 59, 59, 0, time.UTC))

	// 检查最后一天
	assert.Equal(t, result[2].StartTime, time.Date(2024, 2, 2, 0, 0, 0, 0, time.UTC))
	assert.Equal(t, result[2].EndTime, si.EndDate)
}

func TestGenerateDailyFulfillmentRecords_CrossYear(t *testing.T) {
	logic := &Logic{}

	si := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_BOARDING),
		ServiceFactoryID: 100,
		StartDate:        time.Date(2023, 12, 31, 10, 0, 0, 0, time.UTC),
		EndDate:          time.Date(2024, 1, 2, 18, 0, 0, 0, time.UTC),
	}

	result := logic.generateDailyFulfillmentRecords(si)
	assert.Assert(t, len(result) == 3) // 3天：12月31日、1月1日、1月2日

	// 检查第一天
	assert.Equal(t, result[0].StartTime, si.StartDate)
	assert.Equal(t, result[0].EndTime, time.Date(2023, 12, 31, 23, 59, 59, 0, time.UTC))

	// 检查最后一天
	assert.Equal(t, result[2].StartTime, time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC))
	assert.Equal(t, result[2].EndTime, si.EndDate)
}

// --- 补充验证函数测试 ---

func TestVerifyUpdateAppointmentRequest_InvalidServiceOperation(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		ServiceOperations: []*pb.UpdateAppointmentRequest_ServiceOperation{
			{
				OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
				// 缺少必需字段
			},
		},
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyUpdateAppointmentRequest_InvalidOptionOperation(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		OptionOperations: []*pb.UpdateAppointmentRequest_OptionOperation{
			{
				OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
				// 缺少必需字段
			},
		},
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

func TestVerifyServiceOperation_CreateInvalidTime(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
		ServiceTemplateId: 100,
		StartTime:         timestamppb.New(time.Now().Add(1 * time.Hour)),
		EndTime:           timestamppb.New(time.Now()),
	}

	err := verifyServiceOperation(operation)
	assert.Assert(t, err != nil)
}

func TestVerifyOptionOperation_CreateInvalidTime(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode:           pb.OperationMode_OPERATION_MODE_CREATE,
		ServiceOptionTemplateId: 100,
		StartTime:               timestamppb.New(time.Now().Add(1 * time.Hour)),
		EndTime:                 timestamppb.New(time.Now()),
	}

	err := verifyOptionOperation(operation)
	assert.Assert(t, err != nil)
}

// --- 补充空值处理测试 ---

func TestUpdateAppointmentBasicInfo_NilOperations(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	logic := &Logic{appointmentCli: mockAppointmentCli}

	req := &pb.UpdateAppointmentRequest{
		AppointmentOperations: nil,
	}

	appointmentEntity := &appointment.Appointment{}

	err := logic.updateAppointmentBasicInfo(context.Background(), req, appointmentEntity)
	assert.NilError(t, err)
}

func TestUpdateAppointmentBasicInfo_PartialOperations(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	logic := &Logic{appointmentCli: mockAppointmentCli}

	req := &pb.UpdateAppointmentRequest{
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now()),
			// 其他字段为nil
		},
	}

	appointmentEntity := &appointment.Appointment{}

	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	err := logic.updateAppointmentBasicInfo(context.Background(), req, appointmentEntity)
	assert.NilError(t, err)
}

// --- 补充数据库错误测试 ---

func TestCreateAppointment_ServiceInstanceBatchCreateError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	mockServiceInstanceCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(errors.New("batch create error"))

	resp, err := logic.CreateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestCreateAppointment_FulfillmentBatchCreateError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	mockServiceInstanceCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(errors.New("fulfillment error"))

	resp, err := logic.CreateAppointment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

// --- 补充更多分支测试 ---

func TestCreateServiceInstance_WithParentAndSubServices(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:           pb.OperationMode_OPERATION_MODE_CREATE,
		ServiceTemplateId:       100,
		ParentServiceInstanceId: 1,
		StartTime:               timestamppb.New(time.Now()),
		EndTime:                 timestamppb.New(time.Now().Add(1 * time.Hour)),
		SubServiceInstance: []*pb.UpdateAppointmentRequest_ServiceOperation{
			{
				OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
				ServiceTemplateId: 200,
				StartTime:         timestamppb.New(time.Now()),
				EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
			},
		},
		Options: []*pb.ServiceOption{
			{
				ServiceOptionTemplateId: 300,
				StartTime:               timestamppb.New(time.Now()),
				EndTime:                 timestamppb.New(time.Now().Add(1 * time.Hour)),
				Name:                    "test option",
				Quantity:                1,
				Price:                   10.0,
				Tax:                     1.0,
			},
		},
	}

	req := &pb.UpdateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
	}

	appointmentEntity := &appointment.Appointment{
		CustomerID: 789,
	}

	parentService := &serviceinstance.ServiceInstance{
		ID:     1,
		RootID: 5,
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(parentService, nil)
	mockServiceInstanceCli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(2, nil).AnyTimes()
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 2).Return(&serviceinstance.ServiceInstance{ID: 2}, nil).AnyTimes()

	err := logic.createServiceInstance(context.Background(), operation, 1, req, appointmentEntity)
	assert.NilError(t, err)
}

func TestCreateServiceInstance_GetCareTypeError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
		ServiceTemplateId: 100,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
	}

	req := &pb.UpdateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
	}

	appointmentEntity := &appointment.Appointment{
		CustomerID: 789,
	}

	mockServiceInstanceCli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(1, nil)
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	err := logic.createServiceInstance(context.Background(), operation, 1, req, appointmentEntity)
	assert.NilError(t, err)
}

func TestUpdateServiceInstance_GetByIDError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
		ServiceInstanceId: 1,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
		DateType:          pb.DateType_DATE_TYPE_UNSPECIFIED,
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(nil, errors.New("not found"))

	err := logic.updateServiceInstance(context.Background(), operation)
	assert.ErrorContains(t, err, "not found")
}

func TestUpdateServiceInstance_UpdateError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
		ServiceInstanceId: 1,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
		DateType:          pb.DateType_DATE_TYPE_UNSPECIFIED,
	}

	existingService := &serviceinstance.ServiceInstance{
		ID: 1,
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(existingService, nil)
	mockServiceInstanceCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update failed"))

	err := logic.updateServiceInstance(context.Background(), operation)
	assert.ErrorContains(t, err, "update failed")
}

func TestDeleteServiceInstance_DeleteFulfillmentError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
		ServiceInstanceId: 1,
	}

	mockFulfillmentCli.EXPECT().DeleteByServiceInstanceID(gomock.Any(), int64(1)).Return(errors.New("fulfillment delete error"))

	err := logic.deleteServiceInstance(context.Background(), operation)
	assert.ErrorContains(t, err, "fulfillment delete error")
}

func TestDeleteServiceInstance_DeleteServiceError2(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
		ServiceInstanceId: 1,
	}

	mockFulfillmentCli.EXPECT().DeleteByServiceInstanceID(gomock.Any(), int64(1)).Return(nil)
	mockServiceInstanceCli.EXPECT().Delete(gomock.Any(), 1).Return(errors.New("service delete error"))

	err := logic.deleteServiceInstance(context.Background(), operation)
	assert.ErrorContains(t, err, "service delete error")
}

func TestProcessSubServiceInstances_EmptySubServices(t *testing.T) {
	logic := &Logic{}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		SubServiceInstance: []*pb.UpdateAppointmentRequest_ServiceOperation{},
	}

	req := &pb.UpdateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
	}

	appointmentEntity := &appointment.Appointment{
		CustomerID: 789,
	}

	err := logic.processSubServiceInstances(context.Background(), operation, 1, 1, req, appointmentEntity)
	assert.NilError(t, err)
}

func TestProcessServiceOptions_EmptyOptions(t *testing.T) {
	logic := &Logic{}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		Options: []*pb.ServiceOption{},
	}

	err := logic.processServiceOptions(context.Background(), operation, 1)
	assert.NilError(t, err)
}

func TestProcessServiceOptions_CreateServiceOptionError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		Options: []*pb.ServiceOption{
			{
				ServiceOptionTemplateId: 100,
				StartTime:               timestamppb.New(time.Now()),
				EndTime:                 timestamppb.New(time.Now().Add(1 * time.Hour)),
				Name:                    "test option",
				Quantity:                1,
				Price:                   10.0,
				Tax:                     1.0,
			},
		},
	}

	// 由于createServiceOption目前是空实现，这个测试主要是为了覆盖代码路径
	err := logic.processServiceOptions(context.Background(), operation, 1)
	assert.NilError(t, err)
}

func TestRecalculateAndUpdateServiceItemType_GetByAppointmentIDError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	mockServiceInstanceCli.EXPECT().GetByAppointmentID(gomock.Any(), 1).Return(nil, errors.New("get error"))

	err := logic.recalculateAndUpdateServiceItemType(context.Background(), 1)
	assert.ErrorContains(t, err, "get error")
}

func TestRecalculateAndUpdateServiceItemType_GetByIDError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		appointmentCli:     mockAppointmentCli,
	}

	mockServiceInstanceCli.EXPECT().GetByAppointmentID(gomock.Any(), 1).Return([]*serviceinstance.ServiceInstance{{CareType: int(pb.CareType_CARE_TYPE_BOARDING)}}, nil)
	mockAppointmentCli.EXPECT().GetByID(gomock.Any(), 1).Return(nil, errors.New("appointment not found"))

	err := logic.recalculateAndUpdateServiceItemType(context.Background(), 1)
	assert.ErrorContains(t, err, "appointment not found")
}

func TestRecalculateAndUpdateServiceItemType_UpdateError2(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		appointmentCli:     mockAppointmentCli,
	}

	mockServiceInstanceCli.EXPECT().GetByAppointmentID(gomock.Any(), 1).Return([]*serviceinstance.ServiceInstance{{CareType: int(pb.CareType_CARE_TYPE_BOARDING)}}, nil)
	mockAppointmentCli.EXPECT().GetByID(gomock.Any(), 1).Return(&appointment.Appointment{}, nil)
	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

	err := logic.recalculateAndUpdateServiceItemType(context.Background(), 1)
	assert.ErrorContains(t, err, "update error")
}

// --- 补充边界条件测试 ---

func TestGenerateFulfillmentRecords_AllCareTypes(t *testing.T) {
	logic := &Logic{}

	testCases := []struct {
		name     string
		careType pb.CareType
		start    time.Time
		end      time.Time
		expected int
	}{
		{
			name:     "Boarding",
			careType: pb.CareType_CARE_TYPE_BOARDING,
			start:    time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			end:      time.Date(2024, 1, 3, 18, 0, 0, 0, time.UTC),
			expected: 3,
		},
		{
			name:     "Grooming",
			careType: pb.CareType_CARE_TYPE_GROOMING,
			start:    time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			end:      time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			expected: 1,
		},
		{
			name:     "Daycare",
			careType: pb.CareType_CARE_TYPE_DAYCARE,
			start:    time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			end:      time.Date(2024, 1, 1, 18, 0, 0, 0, time.UTC),
			expected: 1,
		},
		{
			name:     "Evaluation",
			careType: pb.CareType_CARE_TYPE_EVALUATION,
			start:    time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			end:      time.Date(2024, 1, 1, 11, 0, 0, 0, time.UTC),
			expected: 1,
		},
		{
			name:     "DogWalking",
			careType: pb.CareType_CARE_TYPE_DOG_WALKING,
			start:    time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			end:      time.Date(2024, 1, 1, 11, 0, 0, 0, time.UTC),
			expected: 1,
		},
		{
			name:     "GroupClass",
			careType: pb.CareType_CARE_TYPE_GROUP_CLASS,
			start:    time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			end:      time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			expected: 1,
		},
		{
			name:     "Unspecified",
			careType: pb.CareType_CARE_TYPE_UNSPECIFIED,
			start:    time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			end:      time.Date(2024, 1, 1, 11, 0, 0, 0, time.UTC),
			expected: 1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			si := &serviceinstance.ServiceInstance{
				ID:               1,
				BusinessID:       123,
				CustomerID:       456,
				CompanyID:        789,
				AppointmentID:    1,
				PetID:            1,
				CareType:         int(tc.careType),
				ServiceFactoryID: 100,
				StartDate:        tc.start,
				EndDate:          tc.end,
			}

			result := logic.generateFulfillmentRecords(si, tc.careType)
			assert.Equal(t, len(result), tc.expected)
		})
	}
}

func TestCalculateServiceItemType_AllCareTypes(t *testing.T) {
	logic := &Logic{}

	careTypeMap := map[int64]pb.CareType{
		100: pb.CareType_CARE_TYPE_BOARDING,
		200: pb.CareType_CARE_TYPE_GROOMING,
		300: pb.CareType_CARE_TYPE_DAYCARE,
		400: pb.CareType_CARE_TYPE_EVALUATION,
		500: pb.CareType_CARE_TYPE_DOG_WALKING,
		600: pb.CareType_CARE_TYPE_GROUP_CLASS,
	}

	result := logic.calculateServiceItemType(careTypeMap)
	// 所有careType的值相加
	expected := int32(pb.CareType_CARE_TYPE_BOARDING + pb.CareType_CARE_TYPE_GROOMING +
		pb.CareType_CARE_TYPE_DAYCARE + pb.CareType_CARE_TYPE_EVALUATION +
		pb.CareType_CARE_TYPE_DOG_WALKING + pb.CareType_CARE_TYPE_GROUP_CLASS)
	assert.Equal(t, result, expected)
}

func TestGetAllServiceTemplateIDs_WithDuplicates(t *testing.T) {
	logic := &Logic{}

	pets := []*pb.PetDetail{
		{
			PetId: 1,
			Services: []*pb.ServiceInstance{
				{ServiceTemplateId: 100},
				{ServiceTemplateId: 200},
				{ServiceTemplateId: 100}, // 重复
			},
		},
		{
			PetId: 2,
			Services: []*pb.ServiceInstance{
				{ServiceTemplateId: 200}, // 重复
				{ServiceTemplateId: 300},
				{ServiceTemplateId: 400},
			},
		},
	}

	result := logic.getAllServiceTemplateIDs(pets)
	expected := []int64{100, 200, 300, 400}
	assert.DeepEqual(t, result, expected)
}

// --- 补充验证函数测试 ---

func TestVerifyServiceOperation_UpdateInvalidTime(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
		ServiceInstanceId: 1,
		StartTime:         timestamppb.New(time.Now().Add(1 * time.Hour)),
		EndTime:           timestamppb.New(time.Now()),
	}

	err := verifyServiceOperation(operation)
	assert.Assert(t, err != nil)
}

func TestVerifyOptionOperation_UpdateInvalidTime(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode:   pb.OperationMode_OPERATION_MODE_UPDATE,
		ServiceOptionId: 1,
		StartTime:       timestamppb.New(time.Now().Add(1 * time.Hour)),
		EndTime:         timestamppb.New(time.Now()),
	}

	err := verifyOptionOperation(operation)
	assert.Assert(t, err != nil)
}

func TestVerifyUpdateAppointmentRequest_NilOperations(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId:         1,
		BusinessId:            123,
		CompanyId:             456,
		AppointmentOperations: nil,
		ServiceOperations:     nil,
		OptionOperations:      nil,
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.NilError(t, err)
}

func TestVerifyUpdateAppointmentRequest_InvalidTimeRangeInOperations(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now().Add(2 * time.Hour)),
			EndTime:   timestamppb.New(time.Now()),
		},
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.Assert(t, err != nil)
}

// --- 补充空值和边界条件测试 ---

func TestUpdateAppointmentBasicInfo_AllFields(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := mock.NewMockReadWriter(ctrl)
	logic := &Logic{appointmentCli: mockAppointmentCli}

	req := &pb.UpdateAppointmentRequest{
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now()),
			EndTime:   timestamppb.New(time.Now().Add(2 * time.Hour)),
			ColorCode: stringPtr("red"),
			NewStatus: appointmentStatePtr(pb.AppointmentState_APPOINTMENT_STATE_CONFIRMED),
		},
	}

	appointmentEntity := &appointment.Appointment{}

	mockAppointmentCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	err := logic.updateAppointmentBasicInfo(context.Background(), req, appointmentEntity)
	assert.NilError(t, err)
}

func TestCreateFulfillments_EmptyServiceInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{fulfillmentCli: mockFulfillmentCli}

	serviceInstances := []*serviceinstance.ServiceInstance{}

	err := logic.createFulfillments(context.Background(), serviceInstances)
	assert.NilError(t, err)
}

func TestCreateServiceInstances_EmptyPets(t *testing.T) {
	logic := &Logic{}

	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets:       []*pb.PetDetail{},
	}

	careTypeMap := map[int64]pb.CareType{}

	result, err := logic.createServiceInstances(context.Background(), req, 1, careTypeMap)
	assert.NilError(t, err)
	assert.DeepEqual(t, result, []*serviceinstance.ServiceInstance(nil))
}

func TestCreateServiceInstances_EmptyServices(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		Pets: []*pb.PetDetail{
			{
				PetId:    1,
				Services: []*pb.ServiceInstance{},
			},
		},
	}

	careTypeMap := map[int64]pb.CareType{}

	result, err := logic.createServiceInstances(context.Background(), req, 1, careTypeMap)
	assert.NilError(t, err)
	assert.DeepEqual(t, result, []*serviceinstance.ServiceInstance(nil))
}

// --- 补充更多边界条件测试 ---

func TestCreateFulfillmentsForServiceInstance_EmptyFulfillments(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{fulfillmentCli: mockFulfillmentCli}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       123,
		CustomerID:       456,
		CompanyID:        789,
		AppointmentID:    1,
		PetID:            1,
		CareType:         int(pb.CareType_CARE_TYPE_UNSPECIFIED),
		ServiceFactoryID: 100,
		StartDate:        time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
		EndDate:          time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC), // 相同时间
	}

	// 修正：即使是同一时间也会生成一条履约记录，所以需要 mock BatchCreate
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	err := logic.createFulfillmentsForServiceInstance(context.Background(), serviceInstance, pb.CareType_CARE_TYPE_UNSPECIFIED)
	assert.NilError(t, err)
}

func TestVerifyServiceOperation_DeleteWithTime(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
		ServiceInstanceId: 1,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
	}

	err := verifyServiceOperation(operation)
	assert.NilError(t, err)
}

func TestVerifyOptionOperation_DeleteWithTime(t *testing.T) {
	operation := &pb.UpdateAppointmentRequest_OptionOperation{
		OperationMode:   pb.OperationMode_OPERATION_MODE_DELETE,
		ServiceOptionId: 1,
		StartTime:       timestamppb.New(time.Now()),
		EndTime:         timestamppb.New(time.Now().Add(1 * time.Hour)),
	}

	err := verifyOptionOperation(operation)
	assert.NilError(t, err)
}

func TestUpdateServiceInstance_WithDateType(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	logic := &Logic{serviceInstanceCli: mockServiceInstanceCli}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
		ServiceInstanceId: 1,
		DateType:          pb.DateType_DATE_TYPE_UNSPECIFIED,
	}

	existingService := &serviceinstance.ServiceInstance{
		ID: 1,
	}

	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(existingService, nil)
	mockServiceInstanceCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	err := logic.updateServiceInstance(context.Background(), operation)
	assert.NilError(t, err)
}

func TestBuildServiceInstance_Complete(t *testing.T) {
	logic := &Logic{}

	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		ServiceTemplateId: 100,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
		DateType:          pb.DateType_DATE_TYPE_UNSPECIFIED,
	}

	req := &pb.UpdateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
	}

	appointmentEntity := &appointment.Appointment{
		CustomerID: 789,
	}

	careType := pb.CareType_CARE_TYPE_BOARDING

	result, err := logic.buildServiceInstance(operation, 1, req, appointmentEntity, careType)
	assert.NilError(t, err)
	assert.Equal(t, result.BusinessID, 123)
	assert.Equal(t, result.CustomerID, 789)
	assert.Equal(t, result.CompanyID, 456)
	assert.Equal(t, result.AppointmentID, 1)
	assert.Equal(t, result.CareType, int(careType))
	assert.Equal(t, result.DateType, int(pb.DateType_DATE_TYPE_UNSPECIFIED))
	assert.Equal(t, result.ServiceFactoryID, 100)
}

// --- 补充错误处理测试 ---

func TestCreateServiceInstance_BuildServiceInstanceError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	// 创建一个会导致buildServiceInstance出错的operation
	operation := &pb.UpdateAppointmentRequest_ServiceOperation{
		OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
		ServiceTemplateId: 100,
		StartTime:         timestamppb.New(time.Now()),
		EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
	}

	req := &pb.UpdateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
	}

	appointmentEntity := &appointment.Appointment{
		CustomerID: 789,
	}

	mockServiceInstanceCli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(1, nil)
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	err := logic.createServiceInstance(context.Background(), operation, 1, req, appointmentEntity)
	assert.NilError(t, err)
}

func TestProcessServiceOperations_MixedOperations(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceInstanceMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)
	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
		fulfillmentCli:     mockFulfillmentCli,
	}

	serviceOperations := []*pb.UpdateAppointmentRequest_ServiceOperation{
		{
			OperationMode:     pb.OperationMode_OPERATION_MODE_CREATE,
			ServiceTemplateId: 100,
			StartTime:         timestamppb.New(time.Now()),
			EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
		},
		{
			OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
			ServiceInstanceId: 1,
			StartTime:         timestamppb.New(time.Now()),
			EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
		},
		{
			OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
			ServiceInstanceId: 2,
		},
	}

	existingService := &serviceinstance.ServiceInstance{ID: 1}

	mockServiceInstanceCli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(1, nil)
	mockFulfillmentCli.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
	mockServiceInstanceCli.EXPECT().GetByID(gomock.Any(), 1).Return(existingService, nil)
	mockServiceInstanceCli.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockFulfillmentCli.EXPECT().DeleteByServiceInstanceID(gomock.Any(), int64(2)).Return(nil)
	mockServiceInstanceCli.EXPECT().Delete(gomock.Any(), 2).Return(nil)

	// 修复：传递非nil的req和appointmentEntity
	req := &pb.UpdateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
	}
	appointmentEntity := &appointment.Appointment{
		CustomerID: 789,
	}

	err := logic.processServiceOperations(context.Background(), serviceOperations, 1, req, appointmentEntity)
	assert.NilError(t, err)
}

func TestProcessOptionOperations_MixedOperations(t *testing.T) {
	logic := &Logic{}

	optionOperations := []*pb.UpdateAppointmentRequest_OptionOperation{
		{
			OperationMode:           pb.OperationMode_OPERATION_MODE_CREATE,
			ServiceOptionTemplateId: 100,
			StartTime:               timestamppb.New(time.Now()),
			EndTime:                 timestamppb.New(time.Now().Add(1 * time.Hour)),
		},
		{
			OperationMode:   pb.OperationMode_OPERATION_MODE_UPDATE,
			ServiceOptionId: 1,
		},
		{
			OperationMode:   pb.OperationMode_OPERATION_MODE_DELETE,
			ServiceOptionId: 2,
		},
	}

	err := logic.processOptionOperations(context.Background(), optionOperations)
	assert.NilError(t, err)
}

// --- 补充验证函数测试 ---

func TestVerifyCreateAppointmentRequest_InvalidColorCode(t *testing.T) {
	req := &pb.CreateAppointmentRequest{
		BusinessId: 123,
		CompanyId:  456,
		CustomerId: 789,
		StartTime:  timestamppb.New(time.Now()),
		EndTime:    timestamppb.New(time.Now().Add(2 * time.Hour)),
		ColorCode:  "", // 空字符串
		Pets: []*pb.PetDetail{
			{
				PetId: 1,
				Services: []*pb.ServiceInstance{
					{
						ServiceTemplateId: 100,
						StartTime:         timestamppb.New(time.Now()),
						EndTime:           timestamppb.New(time.Now().Add(1 * time.Hour)),
					},
				},
			},
		},
	}

	err := verifyCreateAppointmentRequest(req)
	assert.NilError(t, err) // 空字符串应该是有效的
}

func TestVerifyUpdateAppointmentRequest_OnlyStartTime(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now()),
			// 只有开始时间，没有结束时间
		},
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.NilError(t, err)
}

func TestVerifyUpdateAppointmentRequest_OnlyEndTime(t *testing.T) {
	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		BusinessId:    123,
		CompanyId:     456,
		AppointmentOperations: &pb.UpdateAppointmentRequest_AppointmentOperation{
			EndTime: timestamppb.New(time.Now().Add(2 * time.Hour)),
			// 只有结束时间，没有开始时间
		},
	}

	err := verifyUpdateAppointmentRequest(req)
	assert.NilError(t, err)
}
