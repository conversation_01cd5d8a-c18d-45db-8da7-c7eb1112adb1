package fulfillmentreport

import (
	"context"

	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

func New() *Logic {
	return &Logic{}
}

type Logic struct {
}

func (l *Logic) GetFulfillmentReportTemplate(_ context.Context,
	_ *pb.GetFulfillmentReportTemplateRequest) (*pb.GetFulfillmentReportTemplateResponse, error) {
	// TODO: implement
	return nil, nil
}

func (l *Logic) UpdateFulfillmentReportTemplate(_ context.Context,
	_ *pb.UpdateFulfillmentReportTemplateRequest) (*pb.UpdateFulfillmentReportTemplateResponse, error) {
	// TODO: implement
	return nil, nil
}
