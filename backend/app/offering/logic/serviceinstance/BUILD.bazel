load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "serviceinstance",
    srcs = ["service_instance.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/serviceinstance",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db/serviceinstance",
        "//backend/proto/offering/v1:offering",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "serviceinstance_test",
    srcs = ["service_instance_test.go"],
    embed = [":serviceinstance"],
    deps = [
        "//backend/app/offering/repo/db/serviceinstance",
        "//backend/proto/offering/v1:offering",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
