package serviceinstance

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	serviceinstance "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceinstance"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// MockServiceInstanceReadWriter 模拟 ServiceInstanceReadWriter 接口
type MockServiceInstanceReadWriter struct {
	mock.Mock
}

func (m *MockServiceInstanceReadWriter) List(ctx context.Context, baseParam *serviceinstance.BaseParam, filter *serviceinstance.Filter) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, baseParam, filter)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceReadWriter) GetByIDs(ctx context.Context, ids []int64) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, ids)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func TestNew(t *testing.T) {
	logic := New()
	assert.NotNil(t, logic)
	assert.NotNil(t, logic.serviceInstanceCli)
}

func TestListServiceInstance_Success(t *testing.T) {
	// 准备测试数据
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
		Filter: &offeringpb.ServiceInstanceFilter{
			PetIds:      []int64{1, 2},
			CustomerIds: []int64{3, 4},
			CareTypes:   []offeringpb.CareType{offeringpb.CareType_CARE_TYPE_BOARDING},
			DateTypes:   []offeringpb.DateType{offeringpb.DateType_DATE_TYPE_SPECIFIC_DATE},
			RootIds:     []int64{5, 6},
		},
	}

	// 模拟数据库返回的数据
	mockInstances := []*serviceinstance.ServiceInstance{
		{
			ID:               1,
			BusinessID:       2,
			CustomerID:       3,
			CompanyID:        1,
			AppointmentID:    10,
			PetID:            1,
			CareType:         2, // CARE_TYPE_BOARDING
			DateType:         2, // DATE_TYPE_SPECIFIC_DATE
			ServiceFactoryID: 100,
			ParentID:         0,
			RootID:           5,
			StartDate:        time.Now(),
			EndDate:          time.Now().Add(2 * time.Hour),
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
	}

	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	// 执行测试
	resp, err := logic.ListServiceInstance(context.Background(), req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.ServiceInstances, 1)
	assert.Equal(t, int64(1), resp.ServiceInstances[0].Id)
	assert.Equal(t, int64(2), resp.ServiceInstances[0].BusinessId)
	assert.Equal(t, int64(3), resp.ServiceInstances[0].CustomerId)
	assert.Equal(t, int64(1), resp.ServiceInstances[0].CompanyId)
	assert.Equal(t, int64(10), resp.ServiceInstances[0].AppointmentId)
	assert.Equal(t, int64(1), resp.ServiceInstances[0].PetId)
	assert.Equal(t, offeringpb.CareType_CARE_TYPE_BOARDING, resp.ServiceInstances[0].CareType)
	assert.Equal(t, offeringpb.DateType_DATE_TYPE_SPECIFIC_DATE, resp.ServiceInstances[0].DateType)
	assert.Equal(t, int64(100), resp.ServiceInstances[0].ServiceFactoryId)
	assert.Equal(t, int64(0), resp.ServiceInstances[0].ParentId)
	assert.Equal(t, int64(5), resp.ServiceInstances[0].RootId)
	assert.True(t, resp.IsEnd) // 返回1个实例，小于limit(10)，所以是最后一页
	assert.Equal(t, int32(1), resp.Total)

	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_WithDefaultLimit(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  0, // 使用默认限制
		},
	}

	mockInstances := []*serviceinstance.ServiceInstance{}
	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.True(t, resp.IsEnd)
	assert.Equal(t, int32(0), resp.Total)

	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_ValidationError_BusinessId(t *testing.T) {
	logic := New()

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 0, // 无效的 business_id
	}

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "business_id must be greater than 0")
}

func TestListServiceInstance_ValidationError_CompanyId(t *testing.T) {
	logic := New()

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  0, // 无效的 company_id
		BusinessId: 1,
	}

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "company_id must be greater than 0")
}

func TestListServiceInstance_ValidationError_TimeRange(t *testing.T) {
	logic := New()

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(time.Now().Add(24 * time.Hour)), // 开始时间晚于结束时间
		EndTime:    timestamppb.New(time.Now()),
	}

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "start_time must be before end_time")
}

func TestListServiceInstance_DatabaseError(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return([]*serviceinstance.ServiceInstance{}, assert.AnError)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, assert.AnError, err)

	mockCli.AssertExpectations(t)
}

func TestGetServiceInstanceByIds_Success(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &offeringpb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{1, 2, 3},
	}

	mockInstances := []*serviceinstance.ServiceInstance{
		{
			ID:               1,
			BusinessID:       2,
			CustomerID:       3,
			CompanyID:        1,
			AppointmentID:    10,
			PetID:            1,
			CareType:         1,
			DateType:         1,
			ServiceFactoryID: 100,
			ParentID:         0,
			RootID:           5,
			StartDate:        time.Now(),
			EndDate:          time.Now().Add(2 * time.Hour),
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
		{
			ID:               2,
			BusinessID:       2,
			CustomerID:       4,
			CompanyID:        1,
			AppointmentID:    11,
			PetID:            2,
			CareType:         3, // CARE_TYPE_DAYCARE
			DateType:         1, // DATE_TYPE_DATE_EVERYDAY
			ServiceFactoryID: 101,
			ParentID:         1,
			RootID:           5,
			StartDate:        time.Now().Add(1 * time.Hour),
			EndDate:          time.Now().Add(3 * time.Hour),
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
	}

	mockCli.On("GetByIDs", mock.Anything, []int64{1, 2, 3}).Return(mockInstances, nil)

	resp, err := logic.GetServiceInstanceByIDs(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.ServiceInstances, 2)
	assert.Equal(t, int64(1), resp.ServiceInstances[0].Id)
	assert.Equal(t, int64(2), resp.ServiceInstances[1].Id)

	mockCli.AssertExpectations(t)
}

func TestGetServiceInstanceByIds_ValidationError_EmptyIds(t *testing.T) {
	logic := New()

	req := &offeringpb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{}, // 空的ID列表
	}

	resp, err := logic.GetServiceInstanceByIDs(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "service_instance_ids cannot be empty")
}

func TestGetServiceInstanceByIds_DatabaseError(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &offeringpb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{1, 2, 3},
	}

	mockCli.On("GetByIDs", mock.Anything, []int64{1, 2, 3}).Return([]*serviceinstance.ServiceInstance{}, assert.AnError)

	resp, err := logic.GetServiceInstanceByIDs(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, assert.AnError, err)

	mockCli.AssertExpectations(t)
}

func TestVerifyListServiceInstanceRequest_ValidRequest(t *testing.T) {
	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
	}

	err := verifyListServiceInstanceRequest(req)
	assert.NoError(t, err)
}

func TestVerifyListServiceInstanceRequest_InvalidBusinessId(t *testing.T) {
	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 0,
	}

	err := verifyListServiceInstanceRequest(req)
	assert.Error(t, err)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "business_id must be greater than 0")
}

func TestVerifyListServiceInstanceRequest_InvalidCompanyId(t *testing.T) {
	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  0,
		BusinessId: 1,
	}

	err := verifyListServiceInstanceRequest(req)
	assert.Error(t, err)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "company_id must be greater than 0")
}

func TestVerifyListServiceInstanceRequest_InvalidTimeRange(t *testing.T) {
	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(time.Now().Add(24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
	}

	err := verifyListServiceInstanceRequest(req)
	assert.Error(t, err)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "start_time must be before end_time")
}

func TestVerifyGetServiceInstanceByIdsRequest_ValidRequest(t *testing.T) {
	req := &offeringpb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{1, 2, 3},
	}

	err := verifyGetServiceInstanceByIDsRequest(req)
	assert.NoError(t, err)
}

func TestVerifyGetServiceInstanceByIdsRequest_EmptyIds(t *testing.T) {
	req := &offeringpb.GetServiceInstanceByIDsRequest{
		ServiceInstanceIds: []int64{},
	}

	err := verifyGetServiceInstanceByIDsRequest(req)
	assert.Error(t, err)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "service_instance_ids cannot be empty")
}

func TestBuildServiceInstanceFilter_WithAllFilters(t *testing.T) {
	req := &offeringpb.ListServiceInstanceRequest{
		Filter: &offeringpb.ServiceInstanceFilter{
			PetIds:      []int64{1, 2, 3},
			CustomerIds: []int64{4, 5, 6},
			CareTypes:   []offeringpb.CareType{offeringpb.CareType_CARE_TYPE_BOARDING, offeringpb.CareType_CARE_TYPE_DAYCARE},
			DateTypes:   []offeringpb.DateType{offeringpb.DateType_DATE_TYPE_SPECIFIC_DATE, offeringpb.DateType_DATE_TYPE_DATE_EVERYDAY},
			RootIds:     []int64{7, 8, 9},
		},
	}

	filter := buildServiceInstanceFilter(req)

	assert.NotNil(t, filter)
	assert.Equal(t, []int64{1, 2, 3}, filter.PetIDs)
	assert.Equal(t, []int64{4, 5, 6}, filter.CustomerIDs)
	assert.Equal(t, []int32{2, 3}, filter.CareTypes) // CARE_TYPE_BOARDING, CARE_TYPE_DAYCARE
	assert.Equal(t, []int32{2, 1}, filter.DateTypes) // DATE_TYPE_SPECIFIC_DATE, DATE_TYPE_DATE_EVERYDAY
	assert.Equal(t, []int32{7, 8, 9}, filter.RootIDs)
}

func TestBuildServiceInstanceFilter_WithNilFilter(t *testing.T) {
	req := &offeringpb.ListServiceInstanceRequest{}

	filter := buildServiceInstanceFilter(req)

	assert.Nil(t, filter)
}

func TestBuildServiceInstanceFilter_WithEmptyFilter(t *testing.T) {
	req := &offeringpb.ListServiceInstanceRequest{
		Filter: &offeringpb.ServiceInstanceFilter{},
	}

	filter := buildServiceInstanceFilter(req)

	assert.NotNil(t, filter)
	assert.Nil(t, filter.PetIDs)
	assert.Nil(t, filter.CustomerIDs)
	assert.Nil(t, filter.CareTypes)
	assert.Nil(t, filter.DateTypes)
	assert.Nil(t, filter.RootIDs)
}

func TestConvertToProtoServiceInstance(t *testing.T) {
	now := time.Now()
	instance := &serviceinstance.ServiceInstance{
		ID:               1,
		BusinessID:       2,
		CustomerID:       3,
		CompanyID:        4,
		AppointmentID:    5,
		PetID:            6,
		CareType:         2, // CARE_TYPE_BOARDING
		DateType:         1, // DATE_TYPE_DATE_EVERYDAY
		ServiceFactoryID: 7,
		ParentID:         8,
		RootID:           9,
		StartDate:        now,
		EndDate:          now.Add(2 * time.Hour),
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	protoInstance := convertToProtoServiceInstance(instance)

	assert.Equal(t, int64(1), protoInstance.Id)
	assert.Equal(t, int64(2), protoInstance.BusinessId)
	assert.Equal(t, int64(3), protoInstance.CustomerId)
	assert.Equal(t, int64(4), protoInstance.CompanyId)
	assert.Equal(t, int64(5), protoInstance.AppointmentId)
	assert.Equal(t, int64(6), protoInstance.PetId)
	assert.Equal(t, offeringpb.CareType_CARE_TYPE_BOARDING, protoInstance.CareType)
	assert.Equal(t, offeringpb.DateType_DATE_TYPE_DATE_EVERYDAY, protoInstance.DateType)
	assert.Equal(t, int64(7), protoInstance.ServiceFactoryId)
	assert.Equal(t, int64(8), protoInstance.ParentId)
	assert.Equal(t, int64(9), protoInstance.RootId)
	assert.NotNil(t, protoInstance.StartDate)
	assert.NotNil(t, protoInstance.EndDate)
	assert.NotNil(t, protoInstance.CreatedAt)
	assert.NotNil(t, protoInstance.UpdatedAt)
}

func TestListServiceInstance_WithTimeRange(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	startTime := time.Now().Add(-24 * time.Hour)
	endTime := time.Now()
	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		StartTime:  timestamppb.New(startTime),
		EndTime:    timestamppb.New(endTime),
		Pagination: &offeringpb.PaginationRef{
			Offset: 10,
			Limit:  20,
		},
	}

	mockInstances := []*serviceinstance.ServiceInstance{}
	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.True(t, resp.IsEnd)
	assert.Equal(t, int32(0), resp.Total)

	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_WithPartialFilters(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
		Filter: &offeringpb.ServiceInstanceFilter{
			PetIds: []int64{1, 2}, // 只设置部分过滤器
		},
	}

	mockInstances := []*serviceinstance.ServiceInstance{}
	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.True(t, resp.IsEnd)
	assert.Equal(t, int32(0), resp.Total)

	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_IsEndCalculation(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  5,
		},
	}

	// 返回5个实例，等于limit，应该不是最后一页
	mockInstances := make([]*serviceinstance.ServiceInstance, 5)
	for i := range mockInstances {
		mockInstances[i] = &serviceinstance.ServiceInstance{ID: i + 1}
	}

	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.False(t, resp.IsEnd) // 返回数量等于limit，不是最后一页
	assert.Equal(t, int32(5), resp.Total)

	mockCli.AssertExpectations(t)
}

func TestListServiceInstance_IsEndCalculation_LastPage(t *testing.T) {
	mockCli := &MockServiceInstanceReadWriter{}
	logic := &Logic{serviceInstanceCli: mockCli}

	req := &offeringpb.ListServiceInstanceRequest{
		CompanyId:  1,
		BusinessId: 2,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	// 返回3个实例，小于limit，应该是最后一页
	mockInstances := make([]*serviceinstance.ServiceInstance, 3)
	for i := range mockInstances {
		mockInstances[i] = &serviceinstance.ServiceInstance{ID: i + 1}
	}

	mockCli.On("List", mock.Anything, mock.Anything, mock.Anything).Return(mockInstances, nil)

	resp, err := logic.ListServiceInstance(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.True(t, resp.IsEnd) // 返回数量小于limit，是最后一页
	assert.Equal(t, int32(3), resp.Total)

	mockCli.AssertExpectations(t)
}
