package serviceinstance

import (
	"context"

	"gorm.io/gorm"
)

type serviceInstanceImpl struct {
	db *gorm.DB
}

func NewServiceInstanceReadWriter(db *gorm.DB) ReadWriter {
	return &serviceInstanceImpl{db: db}
}

// List 基于BaseParam和Filter查询ServiceInstance记录
func (i *serviceInstanceImpl) List(ctx context.Context,
	baseParam *BaseParam, filter *Filter) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance

	// 使用Model方法，让GORM自动处理表名和字段映射
	query := i.db.WithContext(ctx).Model(&ServiceInstance{})

	if baseParam == nil {
		return []*ServiceInstance{}, nil
	}

	// 应用BaseParam过滤条件
	query = i.applyBaseParamFilters(query, baseParam)

	// 应用Filter过滤条件
	if filter != nil {
		query = i.applyFilterConditions(query, filter)
	}

	// 应用分页
	query = i.applyPagination(query, baseParam.PaginationInfo)

	// 按创建时间倒序排列
	query = query.Order(ColumnCreatedAt + " DESC")

	err := query.Find(&instances).Error
	if err != nil {
		return nil, err
	}

	return instances, nil
}

// GetByIDs 根据ID列表获取ServiceInstance记录
func (i *serviceInstanceImpl) GetByIDs(ctx context.Context, ids []int64) ([]*ServiceInstance, error) {
	if len(ids) == 0 {
		return []*ServiceInstance{}, nil
	}

	var instances []*ServiceInstance
	err := i.db.WithContext(ctx).Where(ColumnID+" IN ?", ids).Find(&instances).Error
	if err != nil {
		return nil, err
	}

	return instances, nil
}

// applyBaseParamFilters 应用BaseParam过滤条件
func (i *serviceInstanceImpl) applyBaseParamFilters(query *gorm.DB, baseParam *BaseParam) *gorm.DB {
	if baseParam.BusinessID != 0 {
		query = query.Where(ColumnBusinessID+" = ?", baseParam.BusinessID)
	}
	if baseParam.CompanyID != 0 {
		query = query.Where(ColumnCompanyID+" = ?", baseParam.CompanyID)
	}
	if !baseParam.StartTime.IsZero() {
		query = query.Where(ColumnCreatedAt+" >= ?", baseParam.StartTime)
	}
	if !baseParam.EndTime.IsZero() {
		query = query.Where(ColumnCreatedAt+" <= ?", baseParam.EndTime)
	}
	return query
}

// applyFilterConditions 应用Filter过滤条件
func (i *serviceInstanceImpl) applyFilterConditions(query *gorm.DB, filter *Filter) *gorm.DB {
	if len(filter.PetIDs) > 0 {
		query = query.Where(ColumnPetID+" IN ?", filter.PetIDs)
	}
	if len(filter.CustomerIDs) > 0 {
		query = query.Where(ColumnCustomerID+" IN ?", filter.CustomerIDs)
	}
	if len(filter.CareTypes) > 0 {
		query = query.Where(ColumnCareType+" IN ?", filter.CareTypes)
	}
	if len(filter.DateTypes) > 0 {
		query = query.Where(ColumnDateType+" IN ?", filter.DateTypes)
	}
	if len(filter.RootIDs) > 0 {
		query = query.Where(ColumnRootID+" IN ?", filter.RootIDs)
	}
	return query
}

// applyPagination 应用分页条件
func (i *serviceInstanceImpl) applyPagination(query *gorm.DB, paginationInfo *PaginationInfo) *gorm.DB {
	if paginationInfo != nil {
		if paginationInfo.Offset > 0 {
			query = query.Offset(int(paginationInfo.Offset))
		}
		if paginationInfo.Limit > 0 {
			query = query.Limit(int(paginationInfo.Limit))
		}
	}
	return query
}
