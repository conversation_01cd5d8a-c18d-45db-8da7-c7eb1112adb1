package service

import (
	"context"

	service_template "github.com/MoeGolibrary/moego/backend/app/offering/logic/servicetemplate"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type TemplateService struct {
	template *service_template.Logic
	offeringpb.UnimplementedServiceTemplateServiceServer
}

func NewTemplateService() *TemplateService {
	return &TemplateService{
		template: service_template.New(),
	}
}

func (t *TemplateService) ListServiceTemplateByCategory(_ context.Context,
	_ *offeringpb.ListServiceTemplateByCategoryRequest) (*offeringpb.ListServiceTemplateByCategoryResponse, error) {
	return &offeringpb.ListServiceTemplateByCategoryResponse{}, nil
}

func (t *TemplateService) GetServiceTemplateByIDs(_ context.Context,
	_ *offeringpb.GetServiceTemplateByIDsRequest) (*offeringpb.GetServiceTemplateByIDsResponse, error) {
	return &offeringpb.GetServiceTemplateByIDsResponse{}, nil
}

func (t *TemplateService) CreateServiceType(_ context.Context,
	_ *offeringpb.CreateServiceTypeRequest) (*offeringpb.CreateServiceTypeResponse, error) {
	// TODO，待实现
	return &offeringpb.CreateServiceTypeResponse{}, nil
}

func (t *TemplateService) CreateServiceTemplate(_ context.Context,
	_ *offeringpb.CreateServiceTemplateRequest) (*offeringpb.CreateServiceTemplateResponse, error) {
	// TODO，待实现
	return &offeringpb.CreateServiceTemplateResponse{}, nil
}

func (t *TemplateService) SetServiceAttributeValues(_ context.Context,
	_ *offeringpb.SetServiceAttributeValuesRequest) (*offeringpb.SetServiceAttributeValuesResponse, error) {
	// TODO，待实现
	return &offeringpb.SetServiceAttributeValuesResponse{}, nil
}

func (t *TemplateService) SetServiceTypeAttribute(_ context.Context,
	_ *offeringpb.SetServiceTypeAttributeRequest) (*offeringpb.SetServiceTypeAttributeResponse, error) {
	// TODO，待实现
	return &offeringpb.SetServiceTypeAttributeResponse{}, nil
}

func (t *TemplateService) GetServiceTypeAttributes(_ context.Context,
	_ *offeringpb.GetServiceTypeAttributesRequest) (*offeringpb.GetServiceTypeAttributesResponse, error) {
	// TODO，待实现
	return &offeringpb.GetServiceTypeAttributesResponse{}, nil
}

func (t *TemplateService) GetServiceAttributeValues(_ context.Context,
	_ *offeringpb.GetServiceAttributeValuesRequest) (*offeringpb.GetServiceAttributeValuesResponse, error) {
	// TODO，待实现
	return &offeringpb.GetServiceAttributeValuesResponse{}, nil
}
