package service

import (
	"context"

	serviceinstance "github.com/MoeGolibrary/moego/backend/app/offering/logic/serviceinstance"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type InstanceService struct {
	instance *serviceinstance.Logic
	offeringpb.UnimplementedInstanceServiceServer
}

func NewInstanceService() *InstanceService {
	return &InstanceService{
		instance: serviceinstance.New(),
	}
}

func (i *InstanceService) ListServiceInstance(ctx context.Context,
	req *offeringpb.ListServiceInstanceRequest) (*offeringpb.ListServiceInstanceResponse, error) {
	return i.instance.ListServiceInstance(ctx, req)
}

func (i *InstanceService) GetServiceInstanceByIDs(ctx context.Context,
	req *offeringpb.GetServiceInstanceByIDsRequest) (*offeringpb.GetServiceInstanceByIDsResponse, error) {
	return i.instance.GetServiceInstanceByIDs(ctx, req)
}
