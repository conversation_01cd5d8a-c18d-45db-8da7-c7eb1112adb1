FROM python:3.13-slim

WORKDIR /app

ARG APP_NAME
ARG BIN_DIR
ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA

ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL}
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}

COPY backend/app/${APP_NAME}/ .
# COPY . .


RUN pip install --no-cache-dir -r requirements.txt

# Set environment variables to avoid prompts during installations
# Add common user bin path to PATH for tools like uv
ENV DEBIAN_FRONTEND=noninteractive \
    PATH="/root/.local/bin:${PATH}"

# Install prerequisites (curl, gnupg for adding repos, ca-certificates)
# and Node.js (provides npm & npx) using NodeSource repository
# Using Node.js 20 LTS as an example, change NODE_MAJOR if needed (e.g., 22)
RUN apt-get update && \
    apt-get install -y --no-install-recommends git curl gnupg ca-certificates apt-transport-https && \
    \
    # Add NodeSource repository GPG key
    mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
    # Add NodeSource repository definition (Node.js 20.x)
    NODE_MAJOR=20 && \
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" > /etc/apt/sources.list.d/nodesource.list && \
    \
    # Update apt list again and install Node.js (includes npm and npx)
    apt-get update && \
    apt-get install -y nodejs && \
    \
    # Install 'uv' (assuming 'uvx' might be a typo for this)
    # This downloads and runs the official installer script
    echo "Installing uv..." && \
    curl -LsSf https://astral.sh/uv/install.sh | sh && \
    \
    # --- Placeholder for installing YOUR custom 'uvx' tool ---
    # If 'uvx' is a different, real tool you need, add its installation command(s) here.
    # For example:
    # RUN echo "Installing custom uvx..."
    # RUN pip install --no-cache-dir your-internal-uvx-package # If it's a Python package
    # RUN curl -o /usr/local/bin/uvx https://your-internal-repo/uvx && chmod +x /usr/local/bin/uvx # If it's a script/binary download
    # --- End Placeholder ---
    \
    # Clean up apt cache to reduce image size
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean

EXPOSE 8080

CMD ["gunicorn", "-w", "4","--timeout","90", "--bind", "0.0.0.0:8080", "main:app"]
