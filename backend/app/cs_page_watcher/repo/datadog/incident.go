package datadog

import "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"

type Incident struct {
	IssuePriority      string
	Tier               string
	CustomerStage      string
	CreatedBy          string
	NeedCreateIncident bool
	OncallTeam         string // 仅仅在创建Incident的时候有用，用来寻找commander
	NeedT1Slack        bool
	NeedBuzzAssignee   bool
	RefUsers           []string
	Issue              *jira.Issue
}
