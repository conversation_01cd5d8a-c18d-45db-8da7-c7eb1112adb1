server:
  filter:
    - recovery
    - debuglog
  service:
    - name: backend.proto.cs_page_watcher.v1.CSPageWatcherService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 6000
  service:
    - callee: postgres
      target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/devops_cs_page_test?sslmode=disable
      protocol: gorm
      transport: gorm
      timeout: 6000
# plugins:
#   database:
#     gorm:
#       max_idle: 20
#       max_open: 100
#       max_lifetime: 180000 # millisecond
#       service:
#         - name: postgres
#           max_idle: 10
#           max_open: 50
#           max_lifetime: 180000
