package global

// Jira 自定义字段名称常量
const (
	T1OrGeneral      = "T1 or General"
	IssuePriority    = "Issue Priority"
	CustomerStage    = "Customer Stage"
	Components       = "Components"
	SLABreach        = "SLA Breach"
	LogoName         = "[T1] Logo Name"
	DevEngineer      = "Dev Engineer"
	IssueDescription = "Issue Description"
	ResolutionTime   = "ResolutionTime"
	CreatedBy        = "Created By"
)

// Jira 优先级常量
const (
	PriorityP0 = "P0-Block"
	PriorityP1 = "P1-Critical"
)

// Jira Tier 常量
const (
	TierT1    = "T1 Ticket"
	TierOther = "Non-T1 Ticket (General)"
)

// Jira 客户阶段常量
const (
	CustomerStageGoLive    = "Go-live Day"
	CustomerStageHyperCare = "Hyper Care Week"
	CustomerStagePostLive  = "Post-live"
)

// 团队常量
const (
	TeamDedicate = "Dedicate"
)

// Slack Emoji 常量
const (
	EmojiThumbsup = "thumbsup"
	EmojiDone     = "white_check_mark"
	EmojiDoing    = "hourglass_flowing_sand"
)

// Squad 常量
const (
	SquadCRM        = "CRM"
	SquadERP        = "ERP"
	SquadFintech    = "Fintech"
	SquadBEPlatform = "BE-Platform"
	SquadFEPlatform = "FE-Platform"
	SquadZihaoTest  = "ZihaoTest"
)

type Schedule struct {
	Name       string
	ScheduleID string
	TeamHandle string
}

// ComponentsSquadsMapping 组件到团队的映射
var ComponentsSquadsMapping = map[string]string{
	"Payments, Finance & QuickBooks":           SquadFintech,
	"Account Billing & Subscriptions":          SquadFintech,
	"client/pet/leads & portal/client app":     SquadCRM,
	"Intake form":                              SquadCRM,
	"Perks(Membership, credit, package)":       SquadCRM,
	"Discount code":                            SquadCRM,
	"Ads Integration":                          SquadCRM,
	"Message&Calling(reminder/review booster)": SquadCRM,
	"Workflow":                         SquadCRM,
	"Email campaign":                   SquadCRM,
	"Branded app/Enterprise hub":       SquadCRM,
	"Insight/Report":                   SquadCRM,
	"Retail":                           SquadCRM,
	"System notification":              SquadCRM,
	"Profile&General biz settings/Nav": SquadCRM,
	"Grooming(Calendar/appt/OB/Waitlist/Grooming report)": SquadERP,
	"Mobile Grooming(SS/CACD/Map view)":                   SquadERP,
	"Self-Onboarding":                                     SquadERP,
	"Service Settings(Grooming)":                          SquadERP,
	"BD(Home/Lodging/appt/OB/waitlist/report cards)":      SquadERP,
	"Service Setting & Pricing rules (BD)":                SquadERP,
	"Staff(Payroll/Shift/permission)":                     SquadERP,
	"Agreement":                                           SquadERP,
	"Platform(Mobile App performance/System)":             SquadBEPlatform,
}

var TeamSchedules = map[string]Schedule{
	TeamDedicate: {
		Name:       "Dedicate – Primary",
		ScheduleID: "d91cf44c-b034-45eb-ae38-da98407041df", // Dedicate – Primary
		TeamHandle: "dedicate",
	},
	SquadCRM: {
		Name:       "BE CRM – Primary",
		ScheduleID: "50043a7f-4075-4d8c-b63f-61af497e23db",
		TeamHandle: "be-crm",
	},
	SquadERP: {
		Name:       "ERP CS Page Primary",
		ScheduleID: "3e9d052f-18ae-4d5a-a24c-a1eb7b058435",
		TeamHandle: "erp-oncall",
	},
	SquadFintech: {
		Name:       "Fintech-BE-Primary",
		ScheduleID: "97a0acb2-3659-4448-a2fb-5a331ef5d658",
		TeamHandle: "fintech", // be-fintech
	},
	SquadBEPlatform: {
		Name:       "BE Platform Primary",
		ScheduleID: "01754bba-2bf5-4207-9a3a-f04ed441c3c7",
		TeamHandle: "be-platform",
	},
	SquadFEPlatform: {
		Name:       "FE Platform – Default",
		ScheduleID: "199499f7-aad5-4b7e-bab0-214756e81f68",
		TeamHandle: "fe-platform",
	},
	SquadZihaoTest: {
		Name:       "Zihao’s Test Schedule",
		ScheduleID: "a5e8d984-6747-44de-aaaa-0d0ba3e7b74e",
		TeamHandle: "oncall-test",
	},
}

var MoegoContacts = map[string]string{
	"Aathi Parthiban":   "<EMAIL>",
	"Amelia Niu":        "<EMAIL>",
	"Autumn Kessler":    "<EMAIL>",
	"Dominique McNeely": "<EMAIL>",
	"Edward Li":         "<EMAIL>",
	"Elizabeth Johnson": "<EMAIL>",
	"Ellie":             "<EMAIL>",
	"Eneli Valencia":    "<EMAIL>",
	"Gelly":             "<EMAIL>",
	"James Lam":         "<EMAIL>",
	"Jerica Tarquinio":  "<EMAIL>",
	"Jonathan Ai":       "<EMAIL>",
	"Joseph Kilgore":    "<EMAIL>",
	"Kristin":           "<EMAIL>",
	"Marina Lian":       "<EMAIL>",
	"Melanie Lew":       "<EMAIL>",
	"Mia Forbes":        "<EMAIL>",
	// "Other" is excluded as it has no email
	"Rachel Shaw":    "<EMAIL>",
	"Rebekah Martin": "<EMAIL>",
	"Sai Lei":        "<EMAIL>",
	"Sam Sullivan":   "<EMAIL>",
	"Sean CP":        "<EMAIL>",
	"Stella Wang":    "<EMAIL>",
	"Tyler Huang":    "<EMAIL>",
	"Wayne Wei":      "<EMAIL>", // Note: original has uppercase 'W'
	"Lydia Lee":      "<EMAIL>",
	"Emma":           "<EMAIL>",
	"Winni Liu":      "<EMAIL>",
	"Mellie L":       "<EMAIL>",
	"Mel Petty":      "<EMAIL>",
	"Joe Marquez":    "<EMAIL>",
	"Eric Reeser":    "<EMAIL>",
	"Joshua Heffner": "<EMAIL>",
	"Brad Urena":     "<EMAIL>",
	"Nathan Gifford": "<EMAIL>",
	"Jenn Hall":      "<EMAIL>",
}
