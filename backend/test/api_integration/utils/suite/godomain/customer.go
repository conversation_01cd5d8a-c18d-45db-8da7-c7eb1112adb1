package godomain

import (
	"net/http"
	"strconv"

	businesscustomerapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
)

func (c *Context) MustGetCustomer(customerID int32) *customermodel.ComMoegoServerCustomerServiceDtoCustomerOverviewDto {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerOverviewDto()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/overview").
		AddQuery("customerId", strconv.FormatInt(int64(customerID), 10)).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	id := result.GetData().GetCustomerDetail().GetId()
	c.suite.Require().NotNil(id)

	return result.Data
}

func (c *Context) CreateCustomerWithPets(
	params *customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo) (int32, []int32) {

	// 先删除相同手机号和 email 的 client，避免冲突导致创建失败
	if params.PhoneNumber != nil && len(*params.PhoneNumber) > 0 {
		c.DeleteCustomerByKeyword(*params.PhoneNumber)
	}
	if params.Email != nil && len(*params.Email) > 0 {
		c.DeleteCustomerByKeyword(*params.Email)
	}

	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoSaveCustomerPetResultDto()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/withPet").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	customerID := result.GetData().GetId()
	c.suite.Require().NotNil(customerID)
	c.suite.Require().Greater(*customerID, int32(0))

	petCount := len(params.PetList)
	petIDs := result.GetData().GetPetIdList()
	if petCount > 0 {
		c.suite.Require().Equal(petCount, len(petIDs))
		for i := 0; i < petCount; i++ {
			c.suite.Require().Greater(petIDs[i], int32(0))
		}
	}

	return *customerID, petIDs
}

func (c *Context) DeleteCustomer(customerID int32) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer").
		SetPayload(&customermodel.ComMoegoServerCustomerServiceParamsCommonIdParams{
			Id: customerID,
		}).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	data := result.GetData()
	c.suite.Require().NotNil(data)
	c.suite.Require().True(*data)
}

func (c *Context) GetCustomerIDsFromSmartList(
	params *customermodel.ComMoegoServerCustomerParamsClientListParams) []int32 {
	result := customermodel.NewComMoegoServerCustomerWebVoClientPageClientListVO()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/smart-list").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	dataList := result.GetClientPage().GetDataList()
	c.suite.Require().NotNil(dataList)

	var customerIDs []int32
	for _, customer := range dataList {
		customerID := customer.GetCustomerId()
		c.suite.Require().NotNil(customerID)
		c.suite.Require().Greater(*customerID, int32(0))
		customerIDs = append(customerIDs, *customerID)
	}

	return customerIDs
}

func (c *Context) GetTagIDsFromListCustomerTag() []int64 {
	result := &businesscustomerapipb.ListCustomerTagResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	tagList := result.GetTags()
	c.suite.Require().NotNil(tagList)

	var tagIDs []int64
	for _, tag := range tagList {
		tagID := tag.GetId()
		c.suite.Require().NotNil(tagID)
		c.suite.Require().Greater(tagID, int64(0))
		tagIDs = append(tagIDs, tagID)
	}

	return tagIDs
}

func (c *Context) UpdateCustomer(params *customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/detail").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
	c.suite.Require().True(*result.GetSuccess())
}

func (c *Context) GetPetCodeIDsFromListPetCode() []int64 {
	result := &businesscustomerapipb.ListPetCodeResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetCodeService/ListPetCode").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	petCodeList := result.GetPetCodes()
	c.suite.Require().NotNil(petCodeList)

	var petCodeIDs []int64
	for _, petCode := range petCodeList {
		petCodeID := petCode.GetId()
		c.suite.Require().NotNil(petCodeID)
		c.suite.Require().Greater(petCodeID, int64(0))
		petCodeIDs = append(petCodeIDs, petCodeID)
	}

	return petCodeIDs
}

func (c *Context) GetVaccineIDsFromListPetVaccine() []int64 {
	result := &businesscustomerapipb.ListPetVaccineResult{}

	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetVaccineService/ListPetVaccine").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	vaccineList := result.GetVaccines()
	c.suite.Require().NotNil(vaccineList)

	var vaccineIDs []int64
	for _, vaccine := range vaccineList {
		vaccineID := vaccine.GetId()
		c.suite.Require().NotNil(vaccineID)
		c.suite.Require().Greater(vaccineID, int64(0))
		vaccineIDs = append(vaccineIDs, vaccineID)
	}

	return vaccineIDs
}

func (c *Context) GetCustomerDetailData(customerID int32) *customermodel.ComMoegoServerCustomerServiceDtoCustomerPageDetailDto {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerPageDetailDto()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/page/detail").
		AddQuery("customerId", strconv.FormatInt(int64(customerID), 10)).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.Data
}

func (c *Context) DeleteCustomerByKeyword(keyword string) {
	req := customermodel.ComMoegoServerCustomerWebParamsClientsClientListBulkParams{
		Filters: &customermodel.ComMoegoCommonParamsFilterParams{
			Filters: []customermodel.ComMoegoCommonParamsFilterParams{
				{
					Property: pointer.Get("customer_type"),
					Operator: pointer.Get("OPERATOR_IN"),
					Values:   []string{"CUSTOMER"},
				},
			},
			Type: pointer.Get("TYPE_AND"),
		},
		Queries: &customermodel.ComMoegoCommonParamsQueryParams{
			Keyword: &keyword,
		},
	}

	response, err := c.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/smart-list/deletion").
		SetPayload(req).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}

func (c *Context) GetCustomerReferralSourceIDs() []int64 {
	result := &businesscustomerapipb.ListCustomerReferralSourceResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/ListCustomerReferralSource").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	var referralSourceIDs []int64
	for _, referralSource := range result.GetReferralSources() {
		referralSourceID := referralSource.GetId()
		c.suite.Require().NotNil(referralSourceID)
		c.suite.Require().Greater(referralSourceID, int64(0))
		referralSourceIDs = append(referralSourceIDs, referralSourceID)
	}

	return referralSourceIDs
}
