package godomain

import (
	"net/http"

	"github.com/google/uuid"

	offeringapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

type lodgingOption func(lodging *offeringapipb.CreateLodgingTypeParams)

func (c *Context) CreateLodging(opts ...lodgingOption) *offeringpb.LodgingTypeView {
	// {"name":"zd test lodging type","description":"","photoList":[],"maxPetNum":15,"petSizeFilter":false,"customizedPetSizes":[],"lodgingUnitType":1,"id":""}
	params := &offeringapipb.CreateLodgingTypeParams{
		Name:            "Test Lodging Type" + uuid.New().String(),
		Description:     "",
		PhotoList:       []string{},
		MaxPetNum:       15,
		PetSizeFilter:   false,
		LodgingUnitType: 1,
	}
	for _, opt := range opts {
		opt(params)
	}

	result := &offeringapipb.CreateLodgingTypeResult{}

	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.LodgingTypeService/CreateLodgingType").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetLodgingType()
}

func (c *Context) DeleteLodging(lodgingID int64) {
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.LodgingTypeService/DeleteLodgingType").
		SetPayload(&offeringapipb.DeleteLodgingTypeParams{
			Id: lodgingID,
		}).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}
