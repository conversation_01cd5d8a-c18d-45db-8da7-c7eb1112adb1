package customer

import (
	"net/http"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	businessmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/business/model"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/clientdomain"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerAgreementTestSuite struct {
	suite.Suite
	GoCtx        godomain.Context
	ClientCtx    clientdomain.Context
	CustomerID   int32
	PetIDs       []int32
	Services     []*offeringpb.CustomizedServiceView
	setupDone    bool
	AgreementIDs []int32
}

func (s *CustomerAgreementTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.GoCtx.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.GoCtx.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerAgreementTestSuite",
	})

	s.ClientCtx.Setup(&s.Suite)

	s.CustomerID, s.PetIDs = s.GoCtx.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GoCtx.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
	})
	s.Services = s.GoCtx.ListApplicableGroomingServices(s.PetIDs[0])

	s.AgreementIDs = s.getAgreementIDs()

	s.setupDone = true
}

func (s *CustomerAgreementTestSuite) TearDownSuite() {
	defer s.GoCtx.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerAgreementTestSuite) mustTearDownSuite() {
	if s.CustomerID > 0 {
		s.GoCtx.DeleteCustomer(s.CustomerID)
	}
}

func (s *CustomerAgreementTestSuite) getAgreementIDs() []int32 {
	result := businessmodel.NewComMoegoCommonResponseResponseResultJavaUtilListComMoegoServerBusinessDtoBusinessAgreementDTO()
	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/business/agreement/list").
		SetResult(result).
		Send()
	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	var agreementIDs []int32
	for _, agreement := range result.GetData() {
		agreementID := agreement.GetId()
		s.Require().NotEmpty(agreementID)
		s.Require().Greater(*agreementID, int32(0))
		agreementIDs = append(agreementIDs, *agreementID)
	}
	return agreementIDs
}

func (s *CustomerAgreementTestSuite) createUnsignedAgreement(payload *customermodel.ComMoegoServerCustomerParamsUnsignedAgreementParams) *customermodel.ComMoegoServerCustomerDtoAddUnsignedAgreementDto {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddUnsignedAgreementDto()
	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/unsigned/agreement").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetSuccess())

	return result.GetData()
}

func (s *CustomerAgreementTestSuite) signAgreement(payload *customermodel.ComMoegoServerCustomerParamsClientCommitSignParams) *customermodel.ComMoegoServerCustomerDtoAddResultDTO {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddResultDTO()
	response, err := s.ClientCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/agreement").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetSuccess())

	return result.GetData()
}

func (s *CustomerAgreementTestSuite) getAggrementList(queries map[string]string) []customermodel.ComMoegoServerCustomerDtoCustomerAgreementRecordDTO {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaUtilListComMoegoServerCustomerDtoCustomerAgreementRecordDTO()

	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/agreement/list").
		AddQueries(queries).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result.GetData()
}

func (s *CustomerAgreementTestSuite) getAgreementInfo(agreementID int32) *customermodel.ComMoegoServerCustomerDtoCustomerAgreementInfoDTO {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoCustomerAgreementInfoDTO()

	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/agreement/info").
		AddQuery("id", strconv.Itoa(int(agreementID))).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result.GetData()
}

func (s *CustomerAgreementTestSuite) getAgreementStatus(queries *map[string]string) []customermodel.ComMoegoServerCustomerDtoCustomerSignStatus {
	result := &[]customermodel.ComMoegoServerCustomerDtoCustomerSignStatus{}
	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/agreement/status").
		AddQueries(*queries).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return *result
}

func (s *CustomerAgreementTestSuite) deleteAgreement(agreementIDs []int32) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/agreement").
		SetPayload(&customermodel.ComMoegoServerCustomerServiceParamsCommonIdsParams{
			Ids: agreementIDs,
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Equal(int32(1), *result.GetData())
}

func (s *CustomerAgreementTestSuite) TestSignedAgreement() {
	agreementData := s.createUnsignedAgreement(&customermodel.ComMoegoServerCustomerParamsUnsignedAgreementParams{
		AgreementId: s.AgreementIDs[0],
		CustomerId:  s.CustomerID,
	})
	agreementID := agreementData.GetId()
	s.Require().NotEmpty(agreementID)
	s.Require().False(*agreementData.GetIsExistingRecord())
	// 从 url 中获取最后一段路径作为 unsignedId
	url := agreementData.GetSendUrl()
	urlParts := strings.Split(*url, "/")
	unsignedId := urlParts[len(urlParts)-1]
	s.Require().NotEmpty(unsignedId)

	signResult := s.signAgreement(&customermodel.ComMoegoServerCustomerParamsClientCommitSignParams{
		UnsignedId: unsignedId,
		Signature:  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNgYAAAAAMAAWgmWQ0AAAAASUVORK5CYII=",
	})

	s.Require().Equal(*agreementID, *signResult.GetId())

	agreementList := s.getAggrementList(map[string]string{
		"customerId":   strconv.Itoa(int(s.CustomerID)),
		"servicesType": "3",
		"type":         "1",
	})
	s.Require().Equal(*agreementID, *agreementList[0].GetSignedDates()[0].GetCustomerAgreementId())
	s.Require().Equal(s.AgreementIDs[0], *agreementList[0].GetAgreementId())

	agreementInfo := s.getAgreementInfo(*agreementID)
	s.Require().Equal(s.AgreementIDs[0], *agreementInfo.GetAgreementId())
	s.Require().Equal(*agreementID, *agreementInfo.GetId())
	s.Require().Equal(s.CustomerID, *agreementInfo.GetCustomerId())
	s.Require().Equal(int32(1), *agreementInfo.GetType())

	// delete agreement
	s.deleteAgreement([]int32{*agreementID})
	agreementList = s.getAggrementList(map[string]string{
		"customerId":   strconv.Itoa(int(s.CustomerID)),
		"servicesType": "3",
		"type":         "1",
	})
	s.Require().Empty(agreementList)
}

func (s *CustomerAgreementTestSuite) TestUnsignedAgreement() {
	agreementData := s.createUnsignedAgreement(&customermodel.ComMoegoServerCustomerParamsUnsignedAgreementParams{
		AgreementId: s.AgreementIDs[0],
		CustomerId:  s.CustomerID,
	})
	agreementID := agreementData.GetId()

	result := customermodel.NewComMoegoCommonResponseResponseResultJavaUtilListComMoegoServerCustomerDtoUnsignedListInfoDTO()
	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/unsigned/agreement/list").
		AddQuery("customerId", strconv.Itoa(int(s.CustomerID))).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	s.Require().Equal(*agreementID, *result.GetData()[0].GetUnsignedId())
	s.Require().Equal(s.AgreementIDs[0], *result.GetData()[0].GetAgreementId())
}

func (s *CustomerAgreementTestSuite) TestUploadedAgreement() {
	payload := &customermodel.ComMoegoServerCustomerParamsAddCustomerAgreementUploadParams{
		CustomerId:      s.CustomerID,
		AgreementHeader: "Test agreement",
		UploadImages:    "[\"https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1752474180a329817c77dd4152908e371af6dcd3b4.jpg?name=pexels-divinetechygirl-1181244.jpg&fileSize=2001794&width=6016&height=4016\"]",
		ServicesType:    "3",
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddResultDTO()
	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/agreement/image").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	customerAgreementId := result.GetData().GetId()

	agreementList := s.getAggrementList(map[string]string{
		"customerId":   strconv.Itoa(int(s.CustomerID)),
		"servicesType": "3",
		"type":         "2",
	})
	firstAgreement := agreementList[0]
	signedDates := firstAgreement.GetSignedDates()
	s.Require().Equal(*customerAgreementId, *signedDates[0].GetCustomerAgreementId())
	s.Require().Equal("Test agreement", *signedDates[0].GetAgreementHeader())
	s.deleteAgreement([]int32{*customerAgreementId})
}

func (s *CustomerAgreementTestSuite) TestAgreementStatus() {
	today := time.Now().Format("2006-01-02")
	appointmentID := s.GoCtx.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GoCtx.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GoCtx.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	agreementStatusList := s.getAgreementStatus(&map[string]string{
		"customerId": strconv.Itoa(int(s.CustomerID)),
		"groomingId": strconv.Itoa(int(appointmentID)),
	})

	firstAggrement := agreementStatusList[0]
	s.Require().Equal(s.AgreementIDs[0], *firstAggrement.GetAgreementId())
	s.Require().Equal(int32(1), *firstAggrement.GetAgreementRequiredType())
	s.Require().Equal(s.CustomerID, *firstAggrement.GetCustomerId())
	s.Require().Equal(true, *firstAggrement.GetIsNeedSign())
}

func (s *CustomerAgreementTestSuite) TestBusinessAgreementList() {
	// signed agreement
	agreementData := s.createUnsignedAgreement(&customermodel.ComMoegoServerCustomerParamsUnsignedAgreementParams{
		AgreementId: s.AgreementIDs[0],
		CustomerId:  s.CustomerID,
	})
	agreementID := agreementData.GetId()
	s.Require().NotEmpty(agreementID)
	s.Require().False(*agreementData.GetIsExistingRecord())
	// 从 url 中获取最后一段路径作为 unsignedId
	url := agreementData.GetSendUrl()
	urlParts := strings.Split(*url, "/")
	unsignedId := urlParts[len(urlParts)-1]
	s.Require().NotEmpty(unsignedId)

	signResult := s.signAgreement(&customermodel.ComMoegoServerCustomerParamsClientCommitSignParams{
		UnsignedId: unsignedId,
		Signature:  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNgYAAAAAMAAWgmWQ0AAAAASUVORK5CYII=",
	})

	s.Require().Equal(*agreementID, *signResult.GetId())

	result := customermodel.NewComMoegoCommonResponseResponseResultComGithubPagehelperPageInfoComMoegoServerCustomerDtoBusinessAgreementRecordDTO()
	response, err := s.GoCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/business/agreement/list").
		AddQueries(map[string]string{
			"pageNum":  "1",
			"pageSize": "10",
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	signedRecordList := result.GetData().GetList()
	s.Require().Equal(s.AgreementIDs[0], *signedRecordList[0].GetAgreementId())
	s.Require().Equal("Service Agreement", *signedRecordList[0].GetAgreementHeader())
	s.Require().Equal("Joshua", *signedRecordList[0].GetClientFirstName())
	s.Require().Equal("Dewey", *signedRecordList[0].GetClientLastName())

	s.deleteAgreement([]int32{*agreementID})
}

func TestCustomerAgreementTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerAgreementTestSuite))
}
