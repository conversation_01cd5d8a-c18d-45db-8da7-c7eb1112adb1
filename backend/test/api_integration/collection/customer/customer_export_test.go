package customer

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"

	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerExportTestSuite struct {
	suite.Suite
	godomain.Context
}

func (s *CustomerExportTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerExportTestSuite",
	})
}

func (s *CustomerExportTestSuite) exportCustomer() *customermodel.ComMoegoServerCustomerWebVoClientExportVO {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerWebVoClientExportVO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/export").
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetSuccess())

	return result.GetData()
}

func (s *CustomerExportTestSuite) TestExportByFilterView() {
	exportResult := s.exportCustomer()
	s.Require().Greater(*exportResult.GetId(), int32(0))
	s.Require().True(*exportResult.GetIsNew())
	s.Require().Equal(int32(1), *exportResult.GetStatus())
}

func TestCustomerExportTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerExportTestSuite))
}
