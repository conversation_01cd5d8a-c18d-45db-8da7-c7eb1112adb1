package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type BookNewTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID int32
	PetIDs     []int32
	setupDone  bool
}

func (s *BookNewTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "BookNewTestSuite",
	})

	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
	})

	s.setupDone = true
}

func (s *BookNewTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *BookNewTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *BookNewTestSuite) getCustomerIDs(keyword string) []int32 {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerBookingSearchListDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/booknew/search").
		AddQuery("keyword", keyword).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().NotNil(result.GetData())
	var customerIDs []int32
	for _, customer := range result.GetData().GetCustomerList() {
		customerIDs = append(customerIDs, *customer.GetCustomerId())
	}
	return customerIDs
}

func (s *BookNewTestSuite) TestSearchWithName() {
	customerIDs := s.getCustomerIDs("Joshua")
	s.Require().Equal(s.CustomerID, customerIDs[0])
}

func (s *BookNewTestSuite) TestSearchWithPhoneNumber() {
	customerIDs := s.getCustomerIDs("**********")
	s.Require().Equal(s.CustomerID, customerIDs[0])
}

func (s *BookNewTestSuite) TestSearchWithEmail() {
	customerIDs := s.getCustomerIDs("tnv_5tgx")
	s.Require().Equal(s.CustomerID, customerIDs[0])
}

func (s *BookNewTestSuite) TestSearchWithPetName() {
	customerIDs := s.getCustomerIDs("Max")
	s.Require().Equal(s.CustomerID, customerIDs[0])
}

func (s *BookNewTestSuite) TestSearchWithPetBreed() {
	customerIDs := s.getCustomerIDs("Affenpinscher")
	s.Require().Equal(s.CustomerID, customerIDs[0])
}

func (s *BookNewTestSuite) TestBookNewDetail() {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerDetailDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/booknew/detail").
		AddQuery("customerId", strconv.FormatInt(int64(s.CustomerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	customerDetail := result.GetData().GetCustomerDetail()
	s.Require().Equal(s.CustomerID, *customerDetail.GetCustomerId())
	s.Require().Equal("Joshua", *customerDetail.GetFirstName())
	s.Require().Equal("Dewey", *customerDetail.GetLastName())
	s.Require().Equal("**********", *customerDetail.GetPhoneNumber())
	s.Require().Equal("<EMAIL>", *customerDetail.GetEmail())

	pet := result.GetData().GetPetList()[0].GetPetDetail()
	s.Require().Equal("Max", *pet.GetPetName())
	s.Require().Equal(int32(1), *pet.GetPetTypeId())
	s.Require().Equal("Affenpinscher", *pet.GetBreed())
}

func TestBookNewTestSuite(t *testing.T) {
	suite.Run(t, new(BookNewTestSuite))
}
