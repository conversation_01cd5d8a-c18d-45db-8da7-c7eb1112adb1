package customer

import (
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerDetailTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone  bool
	CustomerID int32
	PetIDs     []int32
	TagIDs     []int64
}

func generateRandomRGB() string {
	r := rand.Intn(256)
	g := rand.Intn(256)
	b := rand.Intn(256)
	return fmt.Sprintf("#%02x%02x%02x", r, g, b)
}

func (s *CustomerDetailTestSuite) getDetailWithOverviewData(customerID int32) *customermodel.ComMoegoServerCustomerDtoCustomerWithPetForMessageDto {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoCustomerWithPetForMessageDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/detail/withOverview").
		AddQuery("customerId", strconv.FormatInt(int64(customerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().NotNil(result.GetData())

	return result.GetData()
}

func (s *CustomerDetailTestSuite) addContact(payload *customermodel.ComMoegoServerCustomerWebVoCustomerContactAddVo) int32 {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddResultDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/contact").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetData().GetResult())

	return *result.GetData().GetId()
}

func (s *CustomerDetailTestSuite) deleteContact(contactID int32) {
	payload := &customermodel.ComMoegoServerCustomerServiceParamsCommonIdParams{
		Id: contactID,
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/contact").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetSuccess())
}

func (s *CustomerDetailTestSuite) getCustomerContactList(customerID int32) []customermodel.ComMoegoServerCustomerDtoCustomerContactDto {
	result := &[]customermodel.ComMoegoServerCustomerDtoCustomerContactDto{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/contacts").
		AddQuery("customerId", strconv.FormatInt(int64(customerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().NotNil(result)

	return *result
}

func (s *CustomerDetailTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerOverviewTestSuite",
	})

	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		Address1:               customermodel.PtrString("4313 Bylsma Circle"),
		City:                   customermodel.PtrString("Panama City"),
		Country:                customermodel.PtrString("US"),
		State:                  customermodel.PtrString("FL"),
		Zipcode:                customermodel.PtrString("32404"),
		PreferredFrequencyDay:  customermodel.PtrInt32(20),
		PreferredFrequencyType: customermodel.PtrInt32(0),
		PreferredDay:           []int32{0, 1, 2, 3},
		PreferredTime:          []int32{540, 1080},
		IsUnsubscribed:         customermodel.PtrInt32(1),
		SendAutoMessage:        customermodel.PtrInt32(1),
		SendAutoEmail:          customermodel.PtrInt32(1),
		ApptReminderByList:     []int32{},
		ClientColor:            customermodel.PtrString("#ff9800"),
	})

	s.TagIDs = s.GetTagIDsFromListCustomerTag()

	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:        s.CustomerID,
		CustomerTagIdList: []int32{int32(s.TagIDs[0])},
	})

	s.setupDone = true
}

func (s *CustomerDetailTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerDetailTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *CustomerDetailTestSuite) TestCustomerDetail() {
	result := s.GetCustomerDetailData(s.CustomerID)

	// validate address info
	addressList := result.GetAddressList()
	firstAddress := addressList[0]
	s.Require().Equal("4313 Bylsma Circle", *firstAddress.GetAddress1())
	s.Require().Equal("Panama City", *firstAddress.GetCity())
	s.Require().Equal("US", *firstAddress.GetCountry())
	s.Require().Equal("FL", *firstAddress.GetState())
	s.Require().Equal("32404", *firstAddress.GetZipcode())
	s.Require().Equal(int32(1), *firstAddress.GetIsPrimary())
	s.Require().Equal(s.CustomerID, *firstAddress.GetCustomerId())

	// validate contact info
	contactList := result.GetContactList()
	firstContact := contactList[0]
	s.Require().Equal("Joshua", *firstContact.GetFirstName())
	s.Require().Equal("Dewey", *firstContact.GetLastName())
	s.Require().Equal("Joshua Dewey", *firstContact.GetName())
	s.Require().Equal("<EMAIL>", *firstContact.GetEmail())
	s.Require().Equal("**********", *firstContact.GetPhoneNumber())
	s.Require().Equal(int32(1), *firstContact.GetIsPrimary())
	s.Require().Equal(s.CustomerID, *firstContact.GetCustomerId())

	// validate customer info
	customerInfo := result.GetCustomerInfo()
	s.Require().Equal(s.CustomerID, *customerInfo.GetCustomerId())
	s.Require().Equal("Joshua", *customerInfo.GetFirstName())
	s.Require().Equal("Dewey", *customerInfo.GetLastName())
	s.Require().Equal("<EMAIL>", *customerInfo.GetEmail())
	s.Require().Equal(int32(20), *customerInfo.GetPreferredFrequencyDay())
	s.Require().Equal(int32(0), *customerInfo.GetPreferredFrequencyType())
	s.Require().Equal([]int32{0, 1, 2, 3}, customerInfo.GetPreferredDay())
	s.Require().Equal([]int32{540, 1080}, customerInfo.GetPreferredTime())
	s.Require().Equal([]int32{}, customerInfo.GetApptReminderByList())
	s.Require().Equal(int32(1), *customerInfo.GetSendAppAutoMessage())
	s.Require().Equal(int32(1), *customerInfo.GetSendAutoEmail())
	s.Require().Equal(int32(1), *customerInfo.GetIsUnsubscribed())
	s.Require().Equal("#ff9800", *customerInfo.GetClientColor())

	// validate customer tag
	customerTagList := result.GetCustomerTagList()
	s.Require().Equal(int32(s.TagIDs[0]), *customerTagList[0].GetId())
}

func (s *CustomerDetailTestSuite) TestCustomerDetailWithOverview() {
	result := s.getDetailWithOverviewData(s.CustomerID)

	// validate contact info
	contactList := result.GetContactList()
	firstContact := contactList[0]
	s.Require().Equal("Joshua", *firstContact.GetFirstName())
	s.Require().Equal("Dewey", *firstContact.GetLastName())
	s.Require().Equal("Joshua Dewey", *firstContact.GetName())
	s.Require().Equal("<EMAIL>", *firstContact.GetEmail())
	s.Require().Equal("**********", *firstContact.GetPhoneNumber())
	s.Require().Equal(int32(1), *firstContact.GetIsPrimary())
	s.Require().Equal(s.CustomerID, *firstContact.GetCustomerId())

	// validate customer detail info
	customerDetail := result.GetCustomerDetail()
	s.Require().Equal(s.CustomerID, *customerDetail.GetCustomerId())
	s.Require().Equal("Joshua", *customerDetail.GetFirstName())
	s.Require().Equal("Dewey", *customerDetail.GetLastName())
	s.Require().Equal("<EMAIL>", *customerDetail.GetEmail())
	s.Require().Equal(int32(20), *customerDetail.GetPreferredFrequencyDay())
	s.Require().Equal(int32(0), *customerDetail.GetPreferredFrequencyType())
	s.Require().Equal([]int32{0, 1, 2, 3}, customerDetail.GetPreferredDay())
	s.Require().Equal([]int32{540, 1080}, customerDetail.GetPreferredTime())
	s.Require().Equal([]int32{}, customerDetail.GetApptReminderByList())
	s.Require().Equal(int32(1), *customerDetail.GetSendAppAutoMessage())
	s.Require().Equal(int32(1), *customerDetail.GetSendAutoEmail())
	s.Require().Equal(int32(1), *customerDetail.GetIsUnsubscribed())
	s.Require().Equal("#ff9800", *customerDetail.GetClientColor())

	// validate address
	address := customerDetail.GetAddress()
	s.Require().Equal("4313 Bylsma Circle", *address.GetAddress1())
	s.Require().Equal("Panama City", *address.GetCity())
	s.Require().Equal("US", *address.GetCountry())
	s.Require().Equal("FL", *address.GetState())
	s.Require().Equal("32404", *address.GetZipcode())
	s.Require().Equal(int32(1), *address.GetIsPrimary())
	s.Require().Equal(s.CustomerID, *address.GetCustomerId())

	// validate pet list
	petList := result.GetPetList()
	s.Require().Len(petList, 1)
	firstPet := petList[0]
	s.Require().Equal("Max", *firstPet.GetPetDetail().GetPetName())
	s.Require().Equal("Affenpinscher", *firstPet.GetPetDetail().GetBreed())
	s.Require().Equal(int32(1), *firstPet.GetPetDetail().GetPetTypeId())

	// validate customer tag
	tagList := result.GetTagList()
	s.Require().Len(tagList, 1)
	firstTag := tagList[0]
	s.Require().Equal(int32(s.TagIDs[0]), *firstTag.GetId())
}

func (s *CustomerDetailTestSuite) TestUpdateCustomerInfo() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:  s.CustomerID,
		FirstName:   customermodel.PtrString("John"),
		LastName:    customermodel.PtrString("Doe"),
		Email:       customermodel.PtrString("<EMAIL>"),
		PhoneNumber: customermodel.PtrString("**********"),
	})

	result := s.GetCustomerDetailData(s.CustomerID)

	contactList := result.GetContactList()
	firstContact := contactList[0]
	s.Require().Equal("John", *firstContact.GetFirstName())
	s.Require().Equal("Doe", *firstContact.GetLastName())
	s.Require().Equal("<EMAIL>", *firstContact.GetEmail())
	s.Require().Equal("**********", *firstContact.GetPhoneNumber())
	s.Require().Equal(s.CustomerID, *firstContact.GetCustomerId())
}

func (s *CustomerDetailTestSuite) TestMarkCustomerAsInactive() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId: s.CustomerID,
		Inactive:   customermodel.PtrInt32(1),
	})

	result := s.GetCustomerDetailData(s.CustomerID)

	customerInfo := result.GetCustomerInfo()
	s.Require().Equal(int32(1), *customerInfo.GetInactive())

	// set inactive to 0
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId: s.CustomerID,
		Inactive:   customermodel.PtrInt32(0),
	})

	updateResult := s.GetCustomerDetailData(s.CustomerID)

	isInactive := *updateResult.GetCustomerInfo().GetInactive()
	s.Require().Equal(int32(0), isInactive)
}

func (s *CustomerDetailTestSuite) TestBlockMessage() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:     s.CustomerID,
		IsBlockMessage: customermodel.PtrInt32(1),
	})

	result := s.GetCustomerDetailData(s.CustomerID)

	customerInfo := result.GetCustomerInfo()
	s.Require().Equal(int32(1), *customerInfo.GetIsBlockMessage())

	// set isBlockMessage to 0
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:     s.CustomerID,
		IsBlockMessage: customermodel.PtrInt32(0),
	})

	updateResult := s.GetCustomerDetailData(s.CustomerID)

	isBlockMessage := *updateResult.GetCustomerInfo().GetIsBlockMessage()
	s.Require().Equal(int32(0), isBlockMessage)
}

func (s *CustomerDetailTestSuite) TestAddAddress() {
	// create address
	payload := &customermodel.ComMoegoServerCustomerDtoCustomerAddressDto{
		Address1:   customermodel.PtrString("2434 South Harwood Street"),
		City:       customermodel.PtrString("Dallas"),
		Country:    customermodel.PtrString("US"),
		State:      customermodel.PtrString("TX"),
		Zipcode:    customermodel.PtrString("75215"),
		CustomerId: customermodel.PtrInt32(s.CustomerID),
		Lat:        customermodel.PtrString("32.768392399999996"),
		Lng:        customermodel.PtrString("-96.77982329999999"),
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddResultDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/address").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	addressList := customerDetail.GetAddressList()
	s.Require().Len(addressList, 2)
	secondAddress := addressList[1]
	s.Require().Equal("2434 South Harwood Street", *secondAddress.GetAddress1())
	s.Require().Equal("Dallas", *secondAddress.GetCity())
	s.Require().Equal("US", *secondAddress.GetCountry())
	s.Require().Equal("TX", *secondAddress.GetState())
	s.Require().Equal("75215", *secondAddress.GetZipcode())
	s.Require().Equal(s.CustomerID, *secondAddress.GetCustomerId())

	// delete address
	addressID := secondAddress.GetAddressId()
	deleteRes, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/address").
		SetPayload(&customermodel.ComMoegoServerCustomerServiceParamsCommonIdParams{
			Id: *addressID,
		}).
		SetResult(customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()).
		Send()

	s.Require().Nil(err)
	s.Require().True(deleteRes.IsSuccess())

	customerDetail = s.GetCustomerDetailData(s.CustomerID)
	addressList = customerDetail.GetAddressList()
	s.Require().Len(addressList, 1)
}

func (s *CustomerDetailTestSuite) TestAddAddtionalContact() {
	// create contact
	contactID := s.addContact(&customermodel.ComMoegoServerCustomerWebVoCustomerContactAddVo{
		CustomerId:  s.CustomerID,
		FirstName:   "fa",
		LastName:    "das",
		PhoneNumber: "2412412142",
		Type:        customermodel.PtrInt32(2),
	})

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	contactList := customerDetail.GetContactList()
	s.Require().Len(contactList, 2)
	primaryContact := contactList[0]
	s.Require().Equal("fa", *primaryContact.GetFirstName())
	s.Require().Equal("das", *primaryContact.GetLastName())
	s.Require().Equal("2412412142", *primaryContact.GetPhoneNumber())

	// delete contact
	s.deleteContact(contactID)
}

func (s *CustomerDetailTestSuite) TestAddClientNote() {
	// add client note
	payload := &customermodel.ComMoegoServerCustomerParamsCustomerNoteSaveVo{
		CustomerId: s.CustomerID,
		Note:       "test note",
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddResultDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/note").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	customerNoteList := customerDetail.GetCustomerNoteList()
	s.Require().Equal(s.CustomerID, *customerNoteList[0].GetCustomerId())
	s.Require().Equal("test note", *customerNoteList[0].GetNote())

	// delete client note
	noteID := *customerNoteList[0].GetCustomerNoteId()
	deleteRes, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/note").
		SetPayload(&customermodel.ComMoegoServerCustomerServiceParamsCommonIdParams{
			Id: noteID,
		}).
		SetResult(customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()).
		Send()

	s.Require().Nil(err)
	s.Require().True(deleteRes.IsSuccess())

	customerDetail = s.GetCustomerDetailData(s.CustomerID)
	customerNoteList = customerDetail.GetCustomerNoteList()
	s.Require().Empty(customerNoteList)
}

func (s *CustomerDetailTestSuite) TestPreferredTip() {
	// update auto tip
	payload := &customermodel.ComMoegoServerCustomerParamsPreferredTipConfigParams{
		CustomerId: s.CustomerID,
		Enable:     customermodel.PtrInt32(1),
		TipType:    customermodel.PtrInt32(1),
		Percentage: customermodel.PtrInt32(30),
	}
	result := customermodel.NewComMoegoServerCustomerDtoPreferredTipDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/preferredTip").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Equal(float32(0), *result.GetAmount())
	s.Require().Equal(int32(1), *result.GetEnable())
	s.Require().Equal(int32(30), *result.GetPercentage())
	s.Require().Equal(int32(1), *result.GetTipType())

	// get auto tip
	res, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/preferredTip").
		AddQuery("customerId", strconv.FormatInt(int64(s.CustomerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(res.IsSuccess())
	s.Require().Equal(float32(0), *result.GetAmount())
	s.Require().Equal(int32(1), *result.GetEnable())
	s.Require().Equal(int32(30), *result.GetPercentage())
	s.Require().Equal(int32(1), *result.GetTipType())
}

func (s *CustomerDetailTestSuite) TestAddPickupContact() {
	// create pickup contact
	firstContactID := s.addContact(&customermodel.ComMoegoServerCustomerWebVoCustomerContactAddVo{
		CustomerId:  s.CustomerID,
		FirstName:   "pickup",
		LastName:    "contact1",
		PhoneNumber: "8042860440",
		Type:        customermodel.PtrInt32(4),
	})

	customerContactList := s.getCustomerContactList(s.CustomerID)
	lastestContact := customerContactList[0]
	s.Require().Equal(firstContactID, *lastestContact.GetContactId())
	s.Require().Equal("pickup", *lastestContact.GetFirstName())
	s.Require().Equal("contact1", *lastestContact.GetLastName())
	s.Require().Equal("8042860440", *lastestContact.GetPhoneNumber())
	s.Require().Equal(int32(4), *lastestContact.GetType())

	// Validate deletion
	s.deleteContact(firstContactID)
	customerContactList = s.getCustomerContactList(s.CustomerID)
	s.Require().Len(customerContactList, 1)
}

func (s *CustomerDetailTestSuite) TestEditPickupContact() {
	// 创建初始接送联系人
	contactID := s.addContact(&customermodel.ComMoegoServerCustomerWebVoCustomerContactAddVo{
		CustomerId:  s.CustomerID,
		FirstName:   "pickup_initial",
		LastName:    "contact",
		PhoneNumber: "8000000000",
		Type:        customermodel.PtrInt32(4),
	})

	// 更新联系人信息
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/contact").
		SetPayload(&customermodel.ComMoegoServerCustomerParamsCustomerContactUpdateVo{
			CustomerContactId: customermodel.PtrInt32(contactID),
			FirstName:         customermodel.PtrString("pickup_updated"),
			LastName:          customermodel.PtrString("contact_modified"),
			PhoneNumber:       customermodel.PtrString("8000000001"),
		}).
		SetResult(customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	// 验证更新结果
	contacts := s.getCustomerContactList(s.CustomerID)
	updatedContact := contacts[0]
	s.Require().Equal("pickup_updated", *updatedContact.GetFirstName())
	s.Require().Equal("contact_modified", *updatedContact.GetLastName())
	s.Require().Equal("8000000001", *updatedContact.GetPhoneNumber())

	// 清理测试数据
	s.deleteContact(contactID)
}

func (s *CustomerDetailTestSuite) TestAddEmergencyContact() {
	// create emergency contact
	firstContactID := s.addContact(&customermodel.ComMoegoServerCustomerWebVoCustomerContactAddVo{
		CustomerId:  s.CustomerID,
		FirstName:   "em",
		LastName:    "client1",
		PhoneNumber: "8042860439",
		Type:        customermodel.PtrInt32(3),
	})

	customerContactList := s.getCustomerContactList(s.CustomerID)
	lastestContact := customerContactList[0]
	s.Require().Equal(firstContactID, *lastestContact.GetContactId())
	s.Require().Equal("em", *lastestContact.GetFirstName())
	s.Require().Equal("client1", *lastestContact.GetLastName())
	s.Require().Equal("8042860439", *lastestContact.GetPhoneNumber())
	s.Require().Equal(int32(3), *lastestContact.GetType())

	secondContactID := s.addContact(&customermodel.ComMoegoServerCustomerWebVoCustomerContactAddVo{
		CustomerId:  s.CustomerID,
		FirstName:   "em",
		LastName:    "client2",
		PhoneNumber: "2403249145",
		Type:        customermodel.PtrInt32(3),
	})

	customerContactList = s.getCustomerContactList(s.CustomerID)
	lastestContact = customerContactList[0]
	s.Require().Equal(secondContactID, *lastestContact.GetContactId())
	s.Require().Equal("em", *lastestContact.GetFirstName())
	s.Require().Equal("client2", *lastestContact.GetLastName())
	s.Require().Equal("2403249145", *lastestContact.GetPhoneNumber())
	s.Require().Equal(int32(3), *lastestContact.GetType())

	s.deleteContact(firstContactID)
	s.deleteContact(secondContactID)
}

func (s *CustomerDetailTestSuite) TestEditEmergencyContact() {
	// create emergency contact
	contactID := s.addContact(&customermodel.ComMoegoServerCustomerWebVoCustomerContactAddVo{
		CustomerId:  s.CustomerID,
		FirstName:   "emergency",
		LastName:    "client",
		PhoneNumber: "8044136719",
		Type:        customermodel.PtrInt32(2),
	})

	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/contact").
		SetPayload(&customermodel.ComMoegoServerCustomerParamsCustomerContactUpdateVo{
			CustomerContactId: customermodel.PtrInt32(contactID),
			Type:              customermodel.PtrInt32(3),
		}).
		SetResult(customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	customerContactList := s.getCustomerContactList(s.CustomerID)
	lastestContact := customerContactList[0]
	s.Require().Equal(contactID, *lastestContact.GetContactId())
	s.Require().Equal("emergency", *lastestContact.GetFirstName())
	s.Require().Equal("client", *lastestContact.GetLastName())
	s.Require().Equal("8044136719", *lastestContact.GetPhoneNumber())
	s.Require().Equal(int32(3), *lastestContact.GetType())

	s.deleteContact(contactID)
}

func (s *CustomerDetailTestSuite) TestUpdateColor() {
	randomColor := generateRandomRGB()
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:  s.CustomerID,
		ClientColor: customermodel.PtrString(randomColor),
	})

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	s.Require().Equal(randomColor, *customerDetail.GetCustomerInfo().GetClientColor())
}

func (s *CustomerDetailTestSuite) TestBlockOnlineBooking() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:           s.CustomerID,
		IsBlockOnlineBooking: customermodel.PtrInt32(1),
	})

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	s.Require().Equal(int32(1), *customerDetail.GetCustomerInfo().GetIsBlockOnlineBooking())
}

func (s *CustomerDetailTestSuite) TestUpdateReferralSource() {
	referralSourceIDs := s.GetCustomerReferralSourceIDs()
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:       s.CustomerID,
		ReferralSourceId: customermodel.PtrInt32(int32(referralSourceIDs[0])),
	})

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	s.Require().Equal(int32(referralSourceIDs[0]), *customerDetail.GetCustomerInfo().GetReferralSourceId())
}

func (s *CustomerDetailTestSuite) TestUpdateAutoSetting() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:         s.CustomerID,
		SendAutoEmail:      customermodel.PtrInt32(0),
		SendAutoMessage:    customermodel.PtrInt32(0),
		SendAppAutoMessage: customermodel.PtrInt32(0),
		ApptReminderByList: []int32{1, 2},
		IsUnsubscribed:     customermodel.PtrInt32(1),
	})

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	s.Require().Equal(int32(0), *customerDetail.GetCustomerInfo().GetSendAutoEmail())
	s.Require().Equal(int32(0), *customerDetail.GetCustomerInfo().GetSendAutoMessage())
	s.Require().Equal(int32(0), *customerDetail.GetCustomerInfo().GetSendAppAutoMessage())
	s.Require().Equal(int32(1), *customerDetail.GetCustomerInfo().GetIsUnsubscribed())
	s.Require().Equal([]int32{1, 2}, customerDetail.GetCustomerInfo().GetApptReminderByList())
}

func (s *CustomerDetailTestSuite) TestUpdateGroomingPreference() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:             s.CustomerID,
		PreferredFrequencyDay:  customermodel.PtrInt32(7),
		PreferredFrequencyType: customermodel.PtrInt32(0),
		PreferredDay:           []int32{2, 3, 4, 5, 6},
		PreferredTime:          []int32{600, 1080},
	})

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	s.Require().Equal(int32(7), *customerDetail.GetCustomerInfo().GetPreferredFrequencyDay())
	s.Require().Equal(int32(0), *customerDetail.GetCustomerInfo().GetPreferredFrequencyType())
	s.Require().Equal([]int32{2, 3, 4, 5, 6}, customerDetail.GetCustomerInfo().GetPreferredDay())
	s.Require().Equal([]int32{600, 1080}, customerDetail.GetCustomerInfo().GetPreferredTime())
}

func (s *CustomerDetailTestSuite) TestUpdateCustomerTags() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:        s.CustomerID,
		CustomerTagIdList: []int32{int32(s.TagIDs[0]), int32(s.TagIDs[1])},
	})

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	s.Require().Len(customerDetail.GetCustomerTagList(), 2)

	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:        s.CustomerID,
		CustomerTagIdList: []int32{},
	})

	customerDetail = s.GetCustomerDetailData(s.CustomerID)
	s.Require().Len(customerDetail.GetCustomerTagList(), 0)
}

func TestCustomerDetailTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerDetailTestSuite))
}
