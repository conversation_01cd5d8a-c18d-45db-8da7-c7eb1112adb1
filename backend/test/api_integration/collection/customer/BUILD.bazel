load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "customer_test",
    srcs = [
        "account_profile_test.go",
        "book_new_test.go",
        "check_exist_email_test.go",
        "customer_address_test.go",
        "customer_agreement_test.go",
        "customer_basic_test.go",
        "customer_by_contacts_test.go",
        "customer_detail_test.go",
        "customer_export_test.go",
        "customer_filter_view_test.go",
        "customer_list_test.go",
        "customer_nearby_test.go",
        "customer_overview_test.go",
        "customer_pet_test.go",
        "customer_pet_vaccine_binding_test.go",
        "customer_with_pet_note_test.go",
        "detailed_form_test.go",
        "incident_report_test.go",
        "invite_url_test.go",
        "pet_options_test.go",
        "phone_batch_check_test.go",
        "smart_list_test.go",
    ],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/def/business/model",
        "//backend/test/api_integration/def/customer/model",
        "//backend/test/api_integration/def/grooming/model",
        "//backend/test/api_integration/utils/suite/clientdomain",
        "//backend/test/api_integration/utils/suite/godomain",
        "//backend/test/api_integration/utils/suite/mydomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/business_customer/v1:business_customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
