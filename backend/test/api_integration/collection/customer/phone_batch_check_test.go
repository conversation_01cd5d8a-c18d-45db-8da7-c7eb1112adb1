package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type PhoneBatchCheckTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone  bool
	CustomerID int32
}

func (s *PhoneBatchCheckTestSuite) SetupSuite() {

	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "PhoneBatchCheckTestSuite",
	})

	s.CustomerID, _ = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
	})

	s.setupDone = true
}

func (s *PhoneBatchCheckTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *PhoneBatchCheckTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *PhoneBatchCheckTestSuite) checkPhoneExist(phoneArray []string) *map[string]bool {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaUtilMapJavaLangStringJavaLangBoolean()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/phoneNumber/batch/check").
		SetPayload(phoneArray).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result.GetData()
}

func (s *PhoneBatchCheckTestSuite) TestCheckExist() {
	phoneArray := []string{
		"**********",
	}

	existMap := s.checkPhoneExist(phoneArray)
	s.Require().True((*existMap)["**********"])
}

func (s *PhoneBatchCheckTestSuite) TestCheckNotExist() {
	phoneArray := []string{
		"**********",
	}

	existMap := s.checkPhoneExist(phoneArray)
	s.Require().False((*existMap)["**********"])
}

func (s *PhoneBatchCheckTestSuite) TestCheckMultiple() {
	phoneArray := []string{
		"**********",
		"**********",
	}

	existMap := s.checkPhoneExist(phoneArray)
	s.Require().True((*existMap)["**********"])
	s.Require().False((*existMap)["**********"])
}

func (s *PhoneBatchCheckTestSuite) TestCheckAllExist() {
	customerID, _ := s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("test"),
		LastName:            customermodel.PtrString("customer"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
	})

	phoneArray := []string{
		"**********",
		"**********",
	}

	existMap := s.checkPhoneExist(phoneArray)
	s.Require().True((*existMap)["**********"])
	s.Require().True((*existMap)["**********"])

	s.DeleteCustomer(customerID)
}

func TestPhoneBatchCheckTestSuite(t *testing.T) {
	suite.Run(t, new(PhoneBatchCheckTestSuite))
}
