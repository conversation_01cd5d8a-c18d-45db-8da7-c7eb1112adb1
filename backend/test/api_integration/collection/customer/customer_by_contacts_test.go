package customer

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"

	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerByContactTestSuite struct {
	suite.Suite
	godomain.Context
}

func (s *CustomerByContactTestSuite) SetupTest() {
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerByContactTestSuite",
	})
}

func (s *CustomerByContactTestSuite) createCustomerByContact(payload *[]customermodel.ComMoegoServerCustomerWebVoCustomerAddByContactVo) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/byContacts").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetSuccess())
}

func (s *CustomerByContactTestSuite) TestCreateCustomerByOneContact() {
	payload := &[]customermodel.ComMoegoServerCustomerWebVoCustomerAddByContactVo{
		{
			FirstName:   customermodel.PtrString("test"),
			LastName:    customermodel.PtrString("contact"),
			Email:       customermodel.PtrString("<EMAIL>"),
			PhoneNumber: "**********",
			Addresses: []customermodel.ComMoegoServerCustomerWebVoCustomerAddressAddVo{
				{
					Address1: customermodel.PtrString("456 Sunset Blvd"),
					City:     customermodel.PtrString("Los Angeles"),
					State:    customermodel.PtrString("CA"),
					Zipcode:  customermodel.PtrString("90028"),
					Country:  customermodel.PtrString("US"),
					Lat:      customermodel.PtrString("34.052235"),
					Lng:      customermodel.PtrString("-118.243683"),
				},
			},
		},
	}

	s.createCustomerByContact(payload)
	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString("**********"),
			},
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		})

	customerID := customerIDs[0]
	customerOverview := s.MustGetCustomer(customerID)
	customerDetail := s.GetCustomerDetailData(customerID)
	address := customerDetail.GetAddressList()[0]
	s.Require().Equal("test", *customerDetail.GetCustomerInfo().GetFirstName())
	s.Require().Equal("contact", *customerDetail.GetCustomerInfo().GetLastName())
	s.Require().Equal("<EMAIL>", *customerDetail.GetCustomerInfo().GetEmail())
	s.Require().Equal("**********", *customerOverview.GetCustomerDetail().GetPhoneNumber())
	s.Require().Equal("456 Sunset Blvd", *address.Address1)
	s.Require().Equal("Los Angeles", *address.City)
	s.Require().Equal("CA", *address.State)
	s.Require().Equal("90028", *address.Zipcode)
	s.Require().Equal("US", *address.Country)
	s.Require().Equal("34.052235", *address.Lat)
	s.Require().Equal("-118.243683", *address.Lng)

	s.DeleteCustomer(customerID)
}

func (s *CustomerByContactTestSuite) TestCreateCustomerByMultipleContact() {
	payload := &[]customermodel.ComMoegoServerCustomerWebVoCustomerAddByContactVo{
		{
			FirstName:   customermodel.PtrString("test"),
			LastName:    customermodel.PtrString("contact1"),
			Email:       customermodel.PtrString("<EMAIL>"),
			PhoneNumber: "8722673942",
			Addresses: []customermodel.ComMoegoServerCustomerWebVoCustomerAddressAddVo{
				{
					Address1: customermodel.PtrString("456 Sunset Blvd"),
					City:     customermodel.PtrString("Los Angeles"),
					State:    customermodel.PtrString("CA"),
					Zipcode:  customermodel.PtrString("90028"),
					Country:  customermodel.PtrString("US"),
					Lat:      customermodel.PtrString("34.052235"),
					Lng:      customermodel.PtrString("-118.243683"),
				},
			},
		},
		{
			FirstName:   customermodel.PtrString("test"),
			LastName:    customermodel.PtrString("contact2"),
			Email:       customermodel.PtrString("<EMAIL>"),
			PhoneNumber: "4193598804",
			Addresses: []customermodel.ComMoegoServerCustomerWebVoCustomerAddressAddVo{
				{
					Address1: customermodel.PtrString("789 W Madison St"),
					City:     customermodel.PtrString("Chicago"),
					State:    customermodel.PtrString("IL"),
					Zipcode:  customermodel.PtrString("90028"),
					Country:  customermodel.PtrString("US"),
					Lat:      customermodel.PtrString("41.8781"),
					Lng:      customermodel.PtrString("-87.6298"),
				},
			},
		},
	}

	s.createCustomerByContact(payload)
	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString("contact"),
			},
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		})

	s.Require().Len(customerIDs, 2)
	for _, customerID := range customerIDs {
		s.DeleteCustomer(customerID)
	}
}

func TestCustomerByContactTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerByContactTestSuite))
}
