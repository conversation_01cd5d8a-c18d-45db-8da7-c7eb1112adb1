package service

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"

	offeringapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type ServicePriceTestSuite struct {
	suite.Suite
	godomain.Context

	serviceIds []int64
	lodgingIds []int64
}

func (s *ServicePriceTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "ServicePriceTestSuite",
	})
}

func (s *ServicePriceTestSuite) TearDownSuite() {
	defer s.Context.Teardown()

	for _, serviceId := range s.serviceIds {
		s.Context.DeleteService(serviceId)
	}

	for _, lodgingId := range s.lodgingIds {
		s.Context.DeleteLodging(lodgingId)
	}
}

func (s *ServicePriceTestSuite) TestGetMaxServicePriceByLodging() {
	lodging1 := s.Context.CreateLodging()
	s.lodgingIds = append(s.lodgingIds, lodging1.GetId())

	lodging2 := s.Context.CreateLodging()
	s.lodgingIds = append(s.lodgingIds, lodging2.GetId())

	serviceWithAllLodgings := s.Context.CreateService(func(service *offeringpb.CreateServiceDef) {
		service.ServiceItemType = offeringpb.ServiceItemType_BOARDING.Enum()
		service.RequireDedicatedLodging = true
		service.LodgingFilter = false
		service.Price = 100
	})
	s.serviceIds = append(s.serviceIds, serviceWithAllLodgings.GetServiceId())

	serviceWithDedicatedLodging1 := s.Context.CreateService(func(service *offeringpb.CreateServiceDef) {
		service.ServiceItemType = offeringpb.ServiceItemType_BOARDING.Enum()
		service.RequireDedicatedLodging = true
		service.LodgingFilter = true
		service.CustomizedLodgings = []int64{lodging1.GetId()}
		service.Price = 200
	})
	s.serviceIds = append(s.serviceIds, serviceWithDedicatedLodging1.GetServiceId())

	serviceWithDedicatedLodging2 := s.Context.CreateService(func(service *offeringpb.CreateServiceDef) {
		service.ServiceItemType = offeringpb.ServiceItemType_BOARDING.Enum()
		service.RequireDedicatedLodging = true
		service.LodgingFilter = true
		service.CustomizedLodgings = []int64{lodging1.GetId(), lodging2.GetId()}
		service.Price = 300
	})
	s.serviceIds = append(s.serviceIds, serviceWithDedicatedLodging2.GetServiceId())

	serviceWithDedicatedLodging3 := s.Context.CreateService(func(service *offeringpb.CreateServiceDef) {
		service.ServiceItemType = offeringpb.ServiceItemType_BOARDING.Enum()
		service.RequireDedicatedLodging = true
		service.LodgingFilter = true
		service.CustomizedLodgings = []int64{lodging2.GetId()}
		service.Price = 400
	})
	s.serviceIds = append(s.serviceIds, serviceWithDedicatedLodging3.GetServiceId())

	result := &offeringapipb.GetMaxServicePriceByLodgingTypeResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/GetMaxServicePriceByLodgingType").
		SetPayload(offeringapipb.GetMaxServicePriceByLodgingTypeParams{
			LodgingTypeId: lodging1.GetId(),
			BusinessId:    s.Context.GetAuthInfo().BusinessID,
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Equal(int64(300), result.GetMaxPrice().GetUnits())
}

func TestServicePriceTestSuite(t *testing.T) {
	suite.Run(t, new(ServicePriceTestSuite))
}
