load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "service_test",
    srcs = [
        "ob_setting_test.go",
        "service_price_test.go",
        "service_setting_test.go",
    ],
    deps = [
        "//backend/test/api_integration/def/grooming/model",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_google_uuid//:uuid",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
    ],
)
