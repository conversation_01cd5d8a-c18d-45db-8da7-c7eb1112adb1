alter table moe_grooming.moe_business_book_online
  add display_staff_selection_page bool default true not null comment 'whether to display staff selection page, default true';

-- 修改注释
alter table moe_grooming.moe_business_book_online
  modify timeslot_format tinyint default 1 not null comment '时间间隔类型 1 Exact times，2 Arrival windows，3 Date only';

alter table moe_grooming.moe_business_book_online
  add arrival_window_before_min int default 0 not null comment '当 timeslot_format 配置为 2 时，使用这个配置';

alter table moe_grooming.moe_business_book_online
  add arrival_window_after_min int default 0 not null comment '当 timeslot_format 配置为 2 时，使用的配置';

alter table moe_grooming.moe_business_book_online
  add booking_range_start_offset int default 1 not null comment '从当前日期的偏移日期，默认值为 1，表示 next day';

alter table moe_grooming.moe_business_book_online
  add booking_range_end_type TINYINT default 1 not null comment ' 1：使用 booking_range_end_offset，2：使用 booking_range_end_date';

alter table moe_grooming.moe_business_book_online
  add booking_range_end_offset int default 180 not null comment '从当前日期的偏移日期，默认值为 180';

alter table moe_grooming.moe_business_book_online
  add booking_range_end_date varchar(20) null comment 'Custom date range 时的结束日期，例如：2023-01-01';

alter table moe_grooming.moe_business_book_online
  add is_need_send_renew_notification bool default false not null comment '是否需要发送 renew date 通知，只有当 booking_range_end_type 使用 booking_range_end_date 时才会用到该配置';

alter table moe_grooming.moe_grooming_appointment
  add no_start_time bool default false not null comment '当前 appointment 是否包含时间，新增这个字段的原因是因为 start_time 字段默认值 0 不能区分是 0 时还是没有 time';
