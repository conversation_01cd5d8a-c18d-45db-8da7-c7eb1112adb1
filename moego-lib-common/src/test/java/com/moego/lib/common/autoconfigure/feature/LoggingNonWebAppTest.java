package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.observability.logging.grpc.LoggingServerInterceptor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * {@link Logging} tester.
 */
public class LoggingNonWebAppTest {

    private final ApplicationContextRunner runner = new ApplicationContextRunner()
            .withUserConfiguration(Logging.class)
            .withBean(HttpProperties.class, () -> mock(HttpProperties.class));

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).doesNotHaveBean(WebMvcConfigurer.class);
            assertThat(context).doesNotHaveBean(LoggingServerInterceptor.class);
        });
    }
}
