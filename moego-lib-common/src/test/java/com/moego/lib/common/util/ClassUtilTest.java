package com.moego.lib.common.util;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * {@link ClassUtil} tester.
 *
 * <AUTHOR>
 */
class ClassUtilTest {

    /**
     * {@link ClassUtil#getRootPackages(List)}
     */
    @Test
    void distinct() {
        List<String> res =
                ClassUtil.getRootPackages(List.of("com.moego", "com", "moego", "com.moego", "com.moego.idl"));
        assertThat(res).containsExactly("com", "moego");
    }
}
