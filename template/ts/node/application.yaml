# 后端api-v3配置文件叫"application.yaml"。后端如果在这个文件下有新增服务，可以咨询相应功能开发的同事，添加服务配置
# 添加后，执行脚本: yarn sync-applications。命令会自动生成文件: templates/node/serviceClient.ts
# git add templates/node/serviceClient.ts templates/node/application.yaml
# git commit -m "feat: 新增服务配置"

moego:
  grpc:
    server:
      empty-server-enabled: true
      debug-enabled: true
      port: 9090
    client:
      stubs:
        - service: backend.proto.pet.v1.**
          authority: moego-pet:9090
        - service: backend.proto.customer.v1.**
          authority: moego-customer:9090
        - service: backend.proto.aistudio.v1.**
          authority: moego-aistudio:9090
        - service: backend.proto.sales.v1.**
          authority: moego-sales:9090
        - service: backend.proto.cs_page_watcher.v1.**
          authority: moego-cs-page-watcher:9090
        - service: backend.proto.open_platform.v1.**
          authority: moego-open-platform-v2:9090
        - service: backend.proto.customer.v2.**
          # TODO: change to correct url before launch
          authority: moego-customer:9090
        - service: backend.proto.fulfillment.v1.**
          authority: moego-fulfillment:9090