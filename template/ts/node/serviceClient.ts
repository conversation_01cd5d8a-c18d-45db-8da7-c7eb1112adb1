// @ts-expect-error
import { createClient } from "@connectrpc/connect";
// @ts-expect-error
import { createGrpcTransport } from "@connectrpc/connect-node";
import { PetService } from "./backend/proto/pet/v1/pet_service_pb";
import { CustomerService } from "./backend/proto/customer/v1/customer_service_pb";
import { AIStudioService } from "./backend/proto/aistudio/v1/aistudio_pb";
import { SalesService } from "./backend/proto/sales/v1/sales_service_pb";
import { MoegoPayCustomFeeApprovalService } from "./backend/proto/sales/v1/moego_pay_custom_fee_approval_service_pb";
import { MoegoPayContractService } from "./backend/proto/sales/v1/moego_pay_contract_service_pb";
import { CSPageWatcherService } from "./backend/proto/cs_page_watcher/v1/cs_page_watcher_pb";
import { OpenPlatformService } from "./backend/proto/open_platform/v1/open_platform_service_pb";
import { MetadataService as MetadataServiceV2 } from "./backend/proto/customer/v2/metadata_service_pb";
import { ActivityService as ActivityServiceV2 } from "./backend/proto/customer/v2/activity_service_pb";
import { FulfillmentReportService } from "./backend/proto/fulfillment/v1/fulfillment_report_service_pb";

export const PetServiceClient = createClient(PetService, createGrpcTransport({ baseUrl: 'http://moego-pet:9090' }));
export const CustomerServiceClient = createClient(CustomerService, createGrpcTransport({ baseUrl: 'http://moego-customer:9090' }));
export const AIStudioServiceClient = createClient(AIStudioService, createGrpcTransport({ baseUrl: 'http://moego-aistudio:9090' }));
export const SalesServiceClient = createClient(SalesService, createGrpcTransport({ baseUrl: 'http://moego-sales:9090' }));
export const MoegoPayCustomFeeApprovalServiceClient = createClient(MoegoPayCustomFeeApprovalService, createGrpcTransport({ baseUrl: 'http://moego-sales:9090' }));
export const MoegoPayContractServiceClient = createClient(MoegoPayContractService, createGrpcTransport({ baseUrl: 'http://moego-sales:9090' }));
export const CSPageWatcherServiceClient = createClient(CSPageWatcherService, createGrpcTransport({ baseUrl: 'http://moego-cs-page-watcher:9090' }));
export const OpenPlatformServiceClient = createClient(OpenPlatformService, createGrpcTransport({ baseUrl: 'http://moego-open-platform-v2:9090' }));
export const MetadataServiceV2Client = createClient(MetadataServiceV2, createGrpcTransport({ baseUrl: 'http://moego-customer:9090' }));
export const ActivityServiceV2Client = createClient(ActivityServiceV2, createGrpcTransport({ baseUrl: 'http://moego-customer:9090' }));
export const FulfillmentReportServiceClient = createClient(FulfillmentReportService, createGrpcTransport({ baseUrl: 'http://moego-fulfillment:9090' }));
