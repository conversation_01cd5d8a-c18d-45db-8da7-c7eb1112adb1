// @since 2022-05-30 17:46:23
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v1";

// limit by id
// Deprecated: force use local defined params
message Id {
  option deprecated = true;
  // the id value
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// limit by owner id
// Deprecated: force use local defined params
message Own {
  option deprecated = true;
  // owner id
  int64 owner_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// limit by owner id and id
// Deprecated: force use local defined params
message OwnId {
  option deprecated = true;
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // owner id
  int64 owner_id = 2 [(validate.rules).int64 = {gt: 0}];
}
