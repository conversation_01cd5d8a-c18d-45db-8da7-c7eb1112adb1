syntax = "proto3";

package moego.api.offering.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/datetime.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/group_class_models.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// the group class service
service GroupClassService {
  // create sessions
  rpc CreateInstance(CreateInstanceParams) returns (CreateInstanceResult);
  // count group class status
  rpc CountInstancesGroupByStatus(CountInstancesGroupByStatusParams) returns (CountInstancesGroupByStatusResult);
  // count group class statuses by group class id
  rpc CountInstancesGroupByClass(CountInstancesGroupByClassParams) returns (CountInstancesGroupByClassResult);
  // GetTrainingClassBatch
  rpc GetInstance(GetInstanceParams) returns (GetInstanceResult);
  // ListTrainingClassBatch
  rpc ListInstances(ListInstancesParams) returns (ListInstancesResult);
  // UpdateGroupClassInstance
  rpc UpdateInstance(UpdateInstanceParams) returns (UpdateInstanceResult);
  // DeleteInstanceAndSessions
  rpc DeleteInstance(DeleteInstanceParams) returns (DeleteInstanceResult);

  // edit session
  rpc UpdateSession(UpdateSessionParams) returns (UpdateSessionResult);
  // list sessions by batch id
  rpc ListSessions(ListSessionsParams) returns (ListSessionsResult);
}

// CreateTrainingClassBatch request
message CreateInstanceParams {
  // business id
  int64 business_id = 1;
  // group class id
  int64 training_group_class_id = 2;
  // trainer
  int64 staff_id = 3;
  // start date
  google.type.Date start_date = 4;
  // start time of day
  int64 start_time_of_day_minutes = 5;
  // time zone, optional, default America/Los_Angeles
  optional google.type.TimeZone time_zone = 6;
  // occurrence
  models.offering.v1.GroupClassInstance.Occurrence occurrence = 7;
}

// CreateTrainingClassBatch response
message CreateInstanceResult {
  // newly created training class batch
  GroupClassInstanceView group_class_instance = 1;
}

// GetTrainingClassBatch request
message GetInstanceParams {
  // the id
  int64 id = 1;
}

// GetTrainingClassBatch response
message GetInstanceResult {
  // the training class batch
  GroupClassInstanceView group_class_instance = 1;
}

// CountGroupClassInstanceStatus request
message CountInstancesGroupByStatusParams {
  // business id
  int64 business_id = 1;
  // trainer
  repeated int64 staff_ids = 2;
}

// CountGroupClassInstanceStatus response
message CountInstancesGroupByStatusResult {
  // the count
  message Count {
    // status
    models.offering.v1.GroupClassInstance.Status status = 1;
    // count
    int64 count = 2;
  }
  // the counts
  repeated Count counts = 1;
}

// CountGroupClassInstancesByStatus request
message CountInstancesGroupByClassParams {
  // business id
  int64 business_id = 1;
  // trainer
  repeated int64 staff_ids = 2;
  // status
  models.offering.v1.GroupClassInstance.Status status = 3;
}

// CountGroupClassInstancesByStatus response
message CountInstancesGroupByClassResult {
  // count
  message Count {
    // group class
    models.offering.v1.GroupClassModel group_class = 1;
    // count
    int64 count = 2;
  }

  // the counts
  repeated Count counts = 1;
}

// ListTrainingClassBatch request
message ListInstancesParams {
  // pagination
  utils.v2.PaginationRequest pagination = 1;
  // the business id
  int64 business_id = 2;
  // trainer
  repeated int64 staff_ids = 3;
  // the training group class id
  int64 group_class_id = 4;
  // status
  models.offering.v1.GroupClassInstance.Status status = 5;
}

// ListTrainingClassBatch response
message ListInstancesResult {
  // pagination
  utils.v2.PaginationResponse pagination = 1;
  // the training class batches
  repeated GroupClassInstanceView group_class_instances = 2;
}

// UpdateInstanceAndSessions request
message UpdateInstanceParams {
  // id
  int64 id = 1;
  // trainer
  int64 staff_id = 2;
  // start date
  google.type.Date start_date = 3;
  // start time of day
  int64 start_time_of_day_minutes = 4;
  // time zone, optional, default America/Los_Angeles
  optional google.type.TimeZone time_zone = 5;
  // occurrence
  models.offering.v1.GroupClassInstance.Occurrence occurrence = 6;
}

// UpdateInstanceAndSessions response
message UpdateInstanceResult {
  // the updated instance
  GroupClassInstanceView instance = 1;
}

// DeleteInstanceAndSessions request
message DeleteInstanceParams {
  // id
  int64 id = 1;
}

// DeleteInstanceAndSessions response
message DeleteInstanceResult {}

// UpdateSession request
message UpdateSessionParams {
  // the id
  int64 id = 1;
  // start time
  optional google.protobuf.Timestamp start_time = 2;
  // duration in minutes
  optional int32 duration_session_minutes = 3;
}

// UpdateSession response
message UpdateSessionResult {
  // the training session
  GroupClassSessionView session = 1;
}

// ListSessionsByBatchID request
message ListSessionsParams {
  // instance id
  optional int64 group_class_instance_id = 1;
}

// ListSessionsByBatchID response
message ListSessionsResult {
  // the sessions
  repeated GroupClassSessionView sessions = 1;
}

// the training class batch
message GroupClassInstanceView {
  // The unique ID
  int64 id = 1;
  // company ID
  int64 company_id = 2;
  // The ID of the training group class
  int64 training_group_class_id = 3;
  // The name
  string name = 4;
  // The start date
  google.type.Date start_date = 5;
  // start time of day
  int64 start_time_of_day_minutes = 6;
  // time zone
  google.type.TimeZone time_zone = 7;
  // capacity
  int32 capacity = 8;
  // trainer
  GroupClassTrainerView trainer = 9;
  // occurrence
  models.offering.v1.GroupClassInstance.Occurrence occurrence = 10;
  // status
  models.offering.v1.GroupClassInstance.Status status = 11;
  // sessions
  repeated GroupClassSessionView sessions = 12;
  // price
  google.type.Money price = 13;
  // enrolled pets
  repeated GroupClassPetView enrolled_pets = 14;
  // clients
  repeated GroupClassClientView clients = 15;
}

// training session view
message GroupClassSessionView {
  // The unique ID
  int64 id = 1;
  // company ID
  int64 company_id = 2;
  // training batch id
  int64 training_group_class_instance_id = 3;
  // interval
  google.type.Interval interval = 6;
  // duration in minutes
  int32 duration_session_minutes = 7;
  // is modified
  bool is_modified = 8;
  // status
  models.offering.v1.GroupClassSession.Status status = 9;
  // checked in pets
  repeated int64 checked_in_pets = 10;
}

// The pet view for group class
message GroupClassPetView {
  // The pet id
  int64 id = 1;
  // The pet name
  string pet_name = 2;
  // The pet type
  models.customer.v1.PetType pet_type = 3;
  // The pet breed
  string breed = 4;
  // The pet birthday
  optional google.type.Date birthday = 5;
  // The pet avatar
  string avatar_path = 6;
  // the client id
  int64 client_id = 7;
}

// The client view for group class
message GroupClassClientView {
  // The client id
  int64 id = 1;
  // The client first name
  string first_name = 2;
  // The client last name
  string last_name = 3;
  // The client email
  string email = 4;
}

// the trainer view
message GroupClassTrainerView {
  // the staff id
  int64 id = 1;
  // first name
  string first_name = 2;
  // last name
  string last_name = 3;
  // avatar
  string avatar_path = 4;
}
