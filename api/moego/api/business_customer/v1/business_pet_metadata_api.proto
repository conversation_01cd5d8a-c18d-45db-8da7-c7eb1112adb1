syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_metadata_defs.proto";
import "moego/models/business_customer/v1/business_pet_metadata_enums.proto";
import "moego/models/business_customer/v1/business_pet_metadata_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// Create pet metadata params
message CreatePetMetadataParams {
  // Pet metadata
  models.business_customer.v1.BusinessPetMetadataDef metadata = 1 [(validate.rules).message = {required: true}];
}

// Create pet metadata result
message CreatePetMetadataResult {
  // Pet metadata id
  int64 id = 1;
}

// Update pet metadata params
message UpdatePetMetadataParams {
  // Pet metadata id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // Pet metadata value
  string metadata_value = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // extra json data, can customize additional metadata information.
  map<string, string> extra_json = 3 [(validate.rules).map = {
    min_pairs: 0
    max_pairs: 1000
  }];
}

// Update pet metadata result
message UpdatePetMetadataResult {}

// Delete pet metadata params
message DeletePetMetadataParams {
  // Pet metadata id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
}

// Delete pet metadata result
message DeletePetMetadataResult {}

// List pet metadata params
message ListPetMetadataParams {
  // metadata name
  repeated models.business_customer.v1.BusinessPetMetadataName metadata_names = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// List pet metadata result
message ListPetMetadataResult {
  // Pet metadata
  repeated models.business_customer.v1.BusinessPetMetadataView metadata = 1;
}

// Sort pet metadata params
message SortPetMetadataParams {
  // Pet metadata name
  models.business_customer.v1.BusinessPetMetadataName metadata_name = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Pet metadata ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      int64: {gte: 0}
    }
  }];
}

// Sort pet metadata result
message SortPetMetadataResult {}

// Business pet metadata service, which provides pet metadata related operations
service BusinessPetMetadataService {
  // Create pet metadata. Below are some examples of common metadata values:
  // Feeding/Medication schedule: 540 (09:00 AM) / 1080 (06:00 PM)
  // Feeding/Medication unit: Cup / Oz
  // Feeding type: Wet food / Dry food
  // Feeding Source: Owner provide / House provide
  // Feeding Instruction: Free feed / Feed individually
  // Display rules: {Feeding schedule} {Feeding type} {Feeding amount} {Feeding unit} {Feeding instruction}
  rpc CreatePetMetadata(CreatePetMetadataParams) returns (CreatePetMetadataResult);

  // Update pet metadata
  rpc UpdatePetMetadata(UpdatePetMetadataParams) returns (UpdatePetMetadataResult);

  // Delete pet metadata
  // The metadata deleted cannot be added again, but it will not affect the original display
  rpc DeletePetMetadata(DeletePetMetadataParams) returns (DeletePetMetadataResult);

  // List pet metadata
  rpc ListPetMetadata(ListPetMetadataParams) returns (ListPetMetadataResult);

  // Sort pet metadata
  rpc SortPetMetadata(SortPetMetadataParams) returns (SortPetMetadataResult);
}
