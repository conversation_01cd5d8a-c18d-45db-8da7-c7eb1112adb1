syntax = "proto3";

package moego.client.payment.v1;

import "moego/models/payment/v1/tip_config_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/payment/v1;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.payment.v1";

// get smart tip config request
message GetTipConfigRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get smart tip config response
message GetTipConfigResponse {
  // tip config
  moego.models.payment.v1.TipConfigModelClientView config = 1;
}

// smart tip config service
service TipConfigService {
  // get credit card list
  rpc GetTipConfig(GetTipConfigRequest) returns (GetTipConfigResponse);
}
