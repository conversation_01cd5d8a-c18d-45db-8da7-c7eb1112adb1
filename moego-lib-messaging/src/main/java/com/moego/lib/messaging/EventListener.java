package com.moego.lib.messaging;

/**
 * Event listener for consuming events.
 *
 * <p> Example:
 * <pre>{@code
 * @Consumer(topics = MyEvent.TOPIC, threadCount = 3)
 * public class MyEventListener implements EventListener<MyEvent> {
 *     @Override
 *     public void onEvent(Msg<MyEvent> msg) {
 *         // do your logic here ...
 *
 *         // ack the message
 *         msg.ack();
 *     }
 * }
 * }</pre>
 *
 * @param <T> event type
 * <AUTHOR>
 * @since 2022/11/30
 */
public interface EventListener<T> {
    /**
     * Do something when event is received.
     *
     * @param message {@link Msg}
     */
    void onEvent(Msg<T> message);
}
